<!-- Full Screen Loading Overlay -->
<div *ngIf="isLoading$ | async" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 fs-5">Signing in...</div>
  </div>
</div>

<!--begin::Form-->
<form class="form w-100" [formGroup]="loginForm" novalidate="novalidate" id="kt_login_signin_form"
  (ngSubmit)="submit()">
  <!--begin::Heading-->
  <div class="text-center mb-10">
    <img src="./assets/media/logos/cropped-Pacifica-Logo.png" alt="Pacifica Engineering Services" class="h-120px logo" style="width:250px">
    <!-- <img alt="Logo" src="./assets/media/logos/Practice-Logo.png" class="h-50px logo" style="width:140px"> -->
  </div>
  <!--end::Heading-->

  <!-- begin::Alert info-->
  <!-- <ng-container *ngIf="!hasError">
    <div class="mb-10 bg-light-info p-8 rounded">
      <div class="text-info">
        Use account <strong>{{ defaultAuth.email }}</strong> and password
        <strong>{{ defaultAuth.password }}</strong> to continue.
      </div>
    </div>
  </ng-container> -->
  <!-- end::Alert info-->

  <!-- begin::Alert error-->
  <!-- <ng-container *ngIf="hasError">
    <div class="mb-lg-15 alert alert-danger">
      <div class="alert-text font-weight-bold">
        The login details are incorrect
      </div>
    </div>
  </ng-container> -->
  <!-- end::Alert error-->

  <!--begin::Form group-->
  <div class="fv-row mb-10">
    <label class="form-label  fw-bold ">Email<sup class="text-danger">*</sup></label>
    <input class="form-control form-control-sm form-control-solid" type="email" name="email" formControlName="email"
      autocomplete="off" [ngClass]="{
        'is-invalid': loginForm.controls['email'].invalid,
        'is-valid': loginForm.controls['email'].valid
      }" />
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'required',
        message: 'Email is required',
        control: loginForm.controls['email']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'email',
        message: 'Email is invalid',
        control: loginForm.controls['email']
      }"></ng-container>
    <!-- <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'minLength',
        message: 'Email should have at least 3 symbols',
        control: loginForm.controls['email']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'maxLength',
        message: 'Email should have maximum 360 symbols',
        control: loginForm.controls['email']
      }"></ng-container> -->
  </div>
  <!--end::Form group-->

  <!--begin::Form group-->
  <div class="fv-row mb-4">
    <div class="d-flex justify-content-between mt-n5">
      <div class="d-flex flex-stack mb-2">
        <label class="form-label fw-bold  mb-0">Password<sup class="text-danger">*</sup></label>
      </div>
    </div>
    <div class=" mb-0">
      <input class="form-control form-control-sm form-control-solid" [type]="passwordshown === true ?'password':'text'" name="password" autocomplete="off"
      formControlName="password" [ngClass]="{
        'is-invalid': loginForm.controls['password'].invalid,
        'is-valid': loginForm.controls['password'].valid
      }" />
      <div class="toggle-password" >
        <span [ngClass]="passwordshown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'" style="margin-top:-29px"
          (click)="passwordshown===true? showpassword(false):showpassword(true)"></span>
      </div>
    </div>

    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'required',
        message: 'Password is required',
        control: loginForm.controls['password']
      }"></ng-container>
    <!-- <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'minlength',
        message: 'Password should have at least 3 symbols',
        control: loginForm.controls['password']
      }"></ng-container>
    <ng-container [ngTemplateOutlet]="formError" [ngTemplateOutletContext]="{
        validation: 'maxLength',
        message: 'Password should have maximum 100 symbols',
        control: loginForm.controls['password']
      }"></ng-container> -->
  </div>
  <div class="d-flex flex-stack flex-wrap gap-3 fs-base fw-semibold mb-8">
    <div>
      <label class="form-check form-check-sm form-check-custom me-5">
        <input class="form-check-input" type="checkbox" formControlName="remember" />
        <span class="form-check-label ">Remember me ?</span>
      </label>
    </div>

    <!--begin::Link-->
    <a routerLink="/auth/forgot-password" class="link-primary">
      Forgot Password ?
    </a>
    <!--end::Link  -->
  </div>




  <!--end::Form group-->

  <!--begin::Action-->
  <div class="text-center">
    <button type="submit" id="kt_sign_in_submit" class="btn btn-sm btn-primary  mb-5" [disabled]="loginForm.invalid">
      <!-- <ng-container *ngIf="isLoading$ | async">
        <span class="indicator-progress" [style.display]="'block'">
          Please wait...
          <span
            class="spinner-border spinner-border-sm align-middle ms-2"
          ></span>
        </span>
      </ng-container> -->
      <ng-container>
        <span class="indicator-label">Login</span>
      </ng-container>
    </button>

    <!-- begin::Separator  -->
    <!-- <div class="text-center text-muted text-uppercase fw-bolder mb-5">or</div> -->
    <!-- end::Separator  -->

    <!-- <a
      class="
        btn btn-flex
        flex-center
        btn-light btn-lg
        w-100
        mb-5
        cursor-pointer
      "
    >
      <img
        class="h-20px me-3"
        src="./assets/media/svg/brand-logos/google-icon.svg"
      />

      Continue with Google
    </a> -->
  </div>
  <!--end::Action-->
</form>
<!--end::Form-->

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
  <ng-container *ngIf="control.hasError(validation) && (control.dirty || control.touched)">
    <div class="fv-plugins-message-container">
      <span role="alert" class="text-danger">
        {{ message }}
      </span>
    </div>
  </ng-container>
</ng-template>

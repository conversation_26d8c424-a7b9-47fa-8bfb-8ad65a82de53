import { ChangeDetectorRef, Component, HostB<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { TranslationService } from '../../../../../../modules/i18n';
import { AuthService, UserType } from '../../../../../../modules/auth';
import { ChangePasswordComponent } from 'src/app/modules/auth/components/change-password/change-password.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { AppService } from 'src/app/modules/services/app.service';

@Component({
  selector: 'app-user-inner',
  templateUrl: './user-inner.component.html',
})
export class UserInnerComponent implements OnInit, OnDestroy {
  @HostBinding('class')
  class = `menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg menu-state-primary fw-bold py-4 fs-6 w-275px`;
  @HostBinding('attr.data-kt-menu') dataKtMenu = 'true';

  user$: Observable<UserType>;
  private unsubscribe: Subscription[] = [];
  loginUser:any ={};
  userFullName:any ='';
  userEmail:any ='';
  userImage:any ='';
  initials: string = '';
  hasImage: boolean = false;
  defaultImage = './assets/media/avatars/blank.png';
  constructor(
    private auth: AuthService,
    private modalService: NgbModal,
    private translationService: TranslationService,
    public appService:AppService,
    public cdr:ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.user$ = this.auth.currentUserSubject.asObservable();

    this.loginUser = this.appService.getLoggedInUser();
        console.log(' this.loginUser  ', this.loginUser )
    this.userFullName = this.loginUser.firstName+' '+this.loginUser.lastName
    this.userEmail = this.loginUser.email;
    this.userImage = this.loginUser.imageName;
    this.hasImage = !!this.userImage;
    this.initials = this.appService.getUserInitials(this.loginUser);
    this.cdr.markForCheck();
    console.log(' this.userFullName  ', this.userFullName );
    console.log(' this.userEmail  ', this.userEmail )
    console.log(' this.userImage  ', this.userImage )
  }




  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
  changePassword(){
    var NgbModalOptions: any = {
      size: 'md', backdrop: 'static',
      keyboard: false, scrollable: true
    }
    const modalRef = this.modalService.open(ChangePasswordComponent, NgbModalOptions);
    modalRef.componentInstance.showClose = true;
    //get response from edit user modal

  }

  onImageError(event: Event) {
    // If image fails, fall back to showing initials
    this.hasImage = false;
    this.userImage = '';
    this.cdr.markForCheck();
  }

}



import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgApexchartsModule } from 'ng-apexcharts';
import { InlineSVGModule } from 'ng-inline-svg-2';

// Other
import { DropdownMenusModule } from '../dropdown-menus/dropdown-menus.module';
import { NgbDropdownModule } from '@ng-bootstrap/ng-bootstrap';

import { MixedWidget8Component } from './mixed/mixed-widget8/mixed-widget8.component';

import { TablesWidget5Component } from './tables/tables-widget5/tables-widget5.component';


import { NewChartsWidget8Component } from './_new/charts/new-charts-widget8/new-charts-widget8.component';

import { SharedModule } from "../../../shared/shared.module";
@NgModule({
  declarations: [
    MixedWidget8Component,
    TablesWidget5Component,
    NewChartsWidget8Component,
  ],
  imports: [
    CommonModule,
    DropdownMenusModule,
    InlineSVGModule,
    NgApexchartsModule,
    NgbDropdownModule,
    SharedModule
  ],
  exports: [

    MixedWidget8Component,
    TablesWidget5Component,
    NewChartsWidget8Component,

  ],
})
export class WidgetsModule {}

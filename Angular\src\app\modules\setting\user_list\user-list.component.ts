import {
  Component,
  On<PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON>roy,
  ChangeDetector<PERSON>ef,
  ViewChild,
  AfterViewInit,
} from '@angular/core';
import { SortDescriptor } from '@progress/kendo-data-query';
import {
  FilterDescriptor,
  CompositeFilterDescriptor,
  process,
} from '@progress/kendo-data-query';
import { State } from '@progress/kendo-data-query';
import {
  Subject,
  debounceTime,
  distinctUntilChanged,
  Subscription,
} from 'rxjs';
import { Router, NavigationStart, ActivatedRoute } from '@angular/router';
import { saveAs } from '@progress/kendo-file-saver';
import { AppService } from 'src/app/modules/services/app.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import { AddUserComponent } from '../add-user/user-add.component';
import { HttpUtilsService } from '../../services/http-utils.service';
import { UserService } from '../../services/user.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { KendoColumnService } from '../../services/kendo-column.service';

// Type definitions
interface UserData {
  UserId: number;
  FirstName: string;
  LastName: string;
  Email: string;
  Status: string;
  Title: string;
  PhoneNo: string;
  RoleName: string;
  LastUpdatedDate: string;
  CreatedDate: string;
  IsEmailNotificationEnabled: boolean;
  IsPasswordChanged: boolean;
  IsLocked: boolean;
  PharmacyId?: number;
  MedicalCenterId?: number;
  CreatedBy?: string;
  LastUpdatedBy?: string;
}

// Type for page configuration
interface PageConfig {
  size: number;
  pageNumber: number;
  totalElements: number;
  totalPages: number;
  orderBy: string;
  orderDir: string;
}

@Component({
  selector: 'app-user-list',
  templateUrl: './user-list.component.html',
  styleUrls: ['./user-list.component.scss'],
})
export class UserListComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('normalGrid') grid: any;

  // Data
  public serverSideRowData: any[] = [];
  public gridData: any[] = [];
  public IsListHasValue: boolean = false;

  public loading: boolean = false;
  public isLoading: boolean = false;

  loginUser: any = {};

  // Search
  public searchData: string = '';
  private searchTerms = new Subject<string>();
  private searchSubscription: Subscription;

  // Enhanced Filters for Kendo UI
  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public activeFilters: Array<{
    field: string;
    operator: string;
    value: any;
  }> = [];

  public filterOptions: Array<{ text: string; value: string | null }> = [
    { text: 'All', value: null },
    { text: 'Active', value: 'Active' },
    { text: 'Inactive', value: 'Inactive' },
  ];

  // Advanced filter options
  public advancedFilterOptions = {
    status: [
      { text: 'All', value: null },
      { text: 'Active', value: 'Active' },
      { text: 'Inactive', value: 'Inactive' },
    ] as Array<{ text: string; value: string | null }>,
    roles: [] as Array<{ text: string; value: string | null }>, // Will be populated from backend
  };

  // Filter state
  public showAdvancedFilters = false;
  public appliedFilters: {
    status?: string | null;
    role?: string | null;
  } = {};

  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one
  public kendoHide: any;
  public hiddenData: any = [];
  public kendoColOrder: any = [];
  public kendoInitColOrder: any = [];
  public hiddenFields: any = [];

  // Column configuration for the new system
  public gridColumns: string[] = [];
  public defaultColumns: string[] = [];
  public fixedColumns: string[] = [];
  public draggableColumns: string[] = [];
  public normalGrid: any;
  public expandedGrid: any;
  public isExpanded = false;

  // Enhanced Columns with Kendo UI features
  public gridColumnConfig: Array<{
    field: string;
    title: string;
    width: number;
    isFixed: boolean;
    type: string;
    filterable?: boolean;
    order: number;
  }> = [
    {
      field: 'action',
      title: 'Action',
      width: 80,
      isFixed: true,
      type: 'action',
      order: 1,
    },
    {
      field: 'userFullName',
      title: 'Name',
      width: 150,
      isFixed: true,
      type: 'text',
      filterable: true,
      order: 2,
    },
    // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },
    {
      field: 'email',
      title: 'Email',
      width: 250,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 4,
    },
    {
      field: 'title',
      title: 'Title',
      width: 120,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 5,
    },
    {
      field: 'phoneNo',
      title: 'Phone',
      width: 120,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 6,
    },
    {
      field: 'roleName',
      title: 'Role',
      width: 120,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 7,
    },
    {
      field: 'Status',
      title: 'Status',
      width: 100,
      type: 'status',
      isFixed: false,
      filterable: true,
      order: 8,
    },
    {
      field: 'lastUpdatedDate',
      title: 'Updated Date',
      width: 160,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 9,
    },
  ];

  // OLD SYSTEM - to be removed
  public columnsVisibility: Record<string, boolean> = {};

  // Old column configuration management removed - replaced with new system

  // State
  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];
  
  // Remove custom sort state tracking → rely on Kendo native

  // Router subscription for saving state on navigation
  private routerSubscription: Subscription;

  // Storage key for state persistence
  private readonly GRID_STATE_KEY = 'form-templates-grid-state';

  // Pagination
  public page: PageConfig = {
    size: 10,
    pageNumber: 0,
    totalElements: 0,
    totalPages: 0,
    orderBy: 'lastUpdatedDate',
    orderDir: 'desc',
  };
  public skip: number = 0;

  // Export options
  public exportOptions: Array<{ text: string; value: string }> = [
    { text: 'Export All', value: 'all' },
    { text: 'Export Selected', value: 'selected' },
    { text: 'Export Filtered', value: 'filtered' },
  ];

  // Selection state
  public selectedUsers: any[] = [];
  public isAllSelected: boolean = false;

  // Statistics
  public userStatistics: {
    activeUsers: number;
    inactiveUsers: number;
    suspendedUsers: number;
    lockedUsers: number;
    totalUsers: number;
  } = {
    activeUsers: 0,
    inactiveUsers: 0,
    suspendedUsers: 0,
    lockedUsers: 0,
    totalUsers: 0,
  };

  // Bulk operations
  public showBulkActions = false;
  public bulkActionStatus: string = 'Active';

  //add or edit default paramters
  public permissionArray: any = [];

  constructor(
    private usersService: UserService,
    private cdr: ChangeDetectorRef,
    private router: Router,
    private route: ActivatedRoute,
    private modalService: NgbModal, // Provides modal functionality to display modals
    public AppService: AppService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private httpUtilService: HttpUtilsService,
    private kendoColumnService: KendoColumnService
  ) {}

  ngOnInit(): void {
    this.loginUser = this.AppService.getLoggedInUser();
    console.log('Login user loaded:', this.loginUser);

    // Setup search with debounce
    this.searchSubscription = this.searchTerms
      .pipe(debounceTime(500), distinctUntilChanged())
      .subscribe((searchTerm) => {
        console.log('Search triggered with term:', searchTerm);
        this.page.pageNumber = 0;
        this.skip = 0;
        // Set loading state for search
        this.loading = true;
        this.isLoading = true;
        // Force change detection to show loader
        this.cdr.detectChanges();
        this.loadTable();
      });

    // Subscribe to router events to save state before navigation
    this.routerSubscription = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        this.saveGridState();
      }
    });

    // Load saved state if available
    this.loadGridState();

    // Load roles for advanced filters
    this.loadRoles();

    // Load user statistics
    this.loadUserStatistics();

    // Initialize with default page load
    this.onPageLoad();

    // Initialize new column visibility system
    this.initializeColumnVisibilitySystem();

    // Load column configuration after a short delay to ensure loginUser is available
    setTimeout(() => {
      this.loadColumnConfigFromDatabase();
    }, 100);
  }

  /**
   * Initialize the new column visibility system
   */
  private initializeColumnVisibilitySystem(): void {
    // Initialize default columns
    this.defaultColumns = this.gridColumnConfig.map((col) => col.field);
    this.gridColumns = [...this.defaultColumns];

    // Set fixed columns (first 3 columns)
    this.fixedColumns = ['action', 'FirstName', 'LastName'];

    // Set draggable columns (all except fixed)
    this.draggableColumns = this.defaultColumns.filter(
      (col) => !this.fixedColumns.includes(col)
    );

    // Initialize normal and expanded grid references
    this.normalGrid = this.grid;
    this.expandedGrid = this.grid;
  }

  ngAfterViewInit(): void {
    // Load the table after the view is initialized
    // Small delay to ensure the grid is properly rendered
    setTimeout(() => {
      this.loadTable();
    }, 200);
  }

  // Method to handle when the component becomes visible
  onTabActivated(): void {
    // Set loading state for tab activation
    this.loading = true;
    this.isLoading = true;
    // Refresh the data when the tab is activated
    this.loadTable();
    this.loadUserStatistics();
  }

  // Method to handle initial page load
  onPageLoad(): void {
    // Initialize the component with default data
    this.page.pageNumber = 0;
    this.skip = 0;
    this.sort = [{ field: 'LastUpdatedDate', dir: 'desc' }];
    this.filter = { logic: 'and', filters: [] };
    this.searchData = '';

    // Set loading state for initial page load
    this.loading = true;
    this.isLoading = true;
    // Load the data
    this.loadTable();
    this.loadUserStatistics();
  }

  // Refresh grid data - only refresh the grid with latest API call
  refreshGrid(): void {
    // Set loading state to show full-screen loader
    this.loading = true;
    this.isLoading = true;

    // Reset to first page and clear any applied filters
    this.page.pageNumber = 0;
    this.skip = 0;
    this.filter = { logic: 'and', filters: [] };
    this.gridFilter = { logic: 'and', filters: [] };
    this.activeFilters = [];
    this.appliedFilters = {};

    // Clear search data
    this.searchData = '';

    // Load fresh data from API
    this.loadTable();
  }

  // Old column configuration methods removed - replaced with new system

  // Old column selector methods removed - replaced with new system

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
    this.searchTerms.complete();
  }
  // New method to load data using Kendo UI specific endpoint
  loadTableWithKendoEndpoint() {
    this.loading = true;
    this.isLoading = true;

    // Enable loader
    this.httpUtilService.loadingSubject.next(true);
    
    // Force change detection to show loader
    this.cdr.detectChanges();

    // Prepare state object for Kendo UI endpoint
    // When sort is empty (3rd click), send default sort to backend
    const sortForBackend = this.sort.length > 0
      ? this.sort
      : [{ field: 'lastUpdatedDate', dir: 'desc' }];
    const state = {
      take: this.page.size,
      skip: this.skip,
      sort: sortForBackend,
      filter: this.filter.filters,
      search: this.searchData,
      loggedInUserId: this.loginUser.userId,
    };

    console.log('Loading table with search term:', this.searchData);
    console.log('Full state object:', state);
    console.log('Loading states - loading:', this.loading, 'isLoading:', this.isLoading);

    this.usersService.getUsersForKendoGrid(state).subscribe({
      next: (data: {
        isFault?: boolean;
        responseData?: {
          data: any[];
          total: number;
          errors?: string[];
          status?: number;
        };
        data?: any[];
        total?: number;
        errors?: string[];
        status?: number;
      }) => {
        // Handle the new API response structure
        if (
          data.isFault ||
          (data.responseData &&
            data.responseData.errors &&
            data.responseData.errors.length > 0)
        ) {
          const errors = data.responseData?.errors || data.errors || [];
          console.error('Kendo UI Grid errors:', errors);
          this.handleEmptyResponse();
        } else {
          // Handle both old and new response structures
          const responseData = data.responseData || data;
          const userData = responseData.data || [];
          const total = responseData.total || 0;

          this.IsListHasValue = userData.length !== 0;
          this.serverSideRowData = userData;
          this.gridData = this.serverSideRowData;
          this.page.totalElements = total;
          this.page.totalPages = Math.ceil(total / this.page.size);
        }
        this.httpUtilService.loadingSubject.next(false);
      },
      error: (error: unknown) => {
        console.error('Error loading data with Kendo UI endpoint:', error);
        this.handleEmptyResponse();
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
      },
      complete: () => {
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
        this.cdr.detectChanges();
      },
    });
  }

  // Enhanced loadTable method that can use either endpoint
  async loadTable() {
    // Use the new Kendo UI specific endpoint for better performance
    this.loadTableWithKendoEndpoint();
  }

  private handleEmptyResponse(): void {
    this.IsListHasValue = false;
    this.serverSideRowData = [];
    this.gridData = [];
    this.page.totalElements = 0;
    this.page.totalPages = 0;
  }

  // Enhanced search handling
  clearSearch(): void {
    // Clear search data and trigger search
    this.searchData = '';
    // Set loading state for clear search
    this.loading = true;
    this.isLoading = true;
    this.searchTerms.next('');
  }

  // Clear all filters and search
  clearAllFilters(): void {
    this.searchData = '';
    this.filter = { logic: 'and', filters: [] };
    this.gridFilter = { logic: 'and', filters: [] };
    this.activeFilters = [];
    this.appliedFilters = {};
    this.page.pageNumber = 0;
    this.skip = 0;
    // Set loading state for clear all filters
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  // Apply advanced filters
  applyAdvancedFilters(): void {
    this.page.pageNumber = 0;
    this.skip = 0;
    // Set loading state for advanced filters
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  // Toggle advanced filters panel
  toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  // Load roles for advanced filters
  loadRoles(): void {
    const queryParams: {
      pageSize: number;
      sortOrder: string;
      sortField: string;
      pageNumber: number;
    } = {
      pageSize: 1000,
      sortOrder: 'ASC',
      sortField: 'roleName',
      pageNumber: 0,
    };

    this.usersService.getAllRoles(queryParams).subscribe({
      next: (data: {
        responseData?: {
          content: Array<{ roleName: string }>;
        };
      }) => {
        if (data && data.responseData && data.responseData.content) {
          this.advancedFilterOptions.roles = [
            { text: 'All Roles', value: null },
            ...data.responseData.content.map((role: { roleName: string }) => ({
              text: role.roleName,
              value: role.roleName,
            })),
          ];
        }
      },
      error: (error: unknown) => {
        console.error('Error loading roles:', error);
        // Set default roles if loading fails
        this.advancedFilterOptions.roles = [{ text: 'All Roles', value: null }];
      },
    });
    this.usersService
      .getDefaultPermissions({})
      .subscribe((permissions: any) => {
        this.permissionArray = permissions.responseData;
      });
  }

  // Load user statistics
  loadUserStatistics(): void {
    this.usersService.getUserStatistics().subscribe({
      next: (data: any) => {
        if (data && data.statistics) {
          this.userStatistics = data.statistics;
        }
      },
      error: (error: unknown) => {
        console.error('Error loading user statistics:', error);
      },
    });
  }

  // Selection handling
  onSelectionChange(selection: any): void {
    this.selectedUsers = selection.selectedRows || [];
    this.isAllSelected =
      this.selectedUsers.length === this.serverSideRowData.length;
    this.showBulkActions = this.selectedUsers.length > 0;
  }

  // Select all users
  selectAllUsers(): void {
    if (this.isAllSelected) {
      this.selectedUsers = [];
      this.isAllSelected = false;
    } else {
      this.selectedUsers = [...this.serverSideRowData];
      this.isAllSelected = true;
    }
    this.showBulkActions = this.selectedUsers.length > 0;
  }

  // Delete user
  deleteUser(user: any): void {
    if (
      confirm(
        `Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`
      )
    ) {
      // Show loading state
      this.loading = true;
      this.isLoading = true;

      const deleteData = {
        userId: user.userId,
        loggedInUserId: this.loginUser.userId || 0,
      };

      this.usersService.deleteUser(deleteData).subscribe({
        next: (response: any) => {
          if (response && response.message) {
            //alert(response.message);
                                        this.customLayoutUtilsService.showSuccess(response.message, '');

            this.loadTable(); // Reload the table
            this.loadUserStatistics(); // Reload statistics
          }
        },
        error: (error: unknown) => {
          console.error('Error deleting user:', error);
                            this.customLayoutUtilsService.showError('Error deleting user', '');

          //alert('Error deleting user. Please try again.');
          // Reset loading state on error
          this.loading = false;
          this.isLoading = false;
        },
      });
    }
  }

  // Bulk update user status
  bulkUpdateUserStatus(): void {
    if (this.selectedUsers.length === 0) {
      //alert('Please select users to update.');
                                  this.customLayoutUtilsService.showSuccess('Please select users to complete', '');

      return;
    }

    if (
      confirm(
        `Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`
      )
    ) {
      // Show loading state
      this.loading = true;
      this.isLoading = true;

      const bulkUpdateData = {
        userIds: this.selectedUsers.map((user) => user.userId),
        status: this.bulkActionStatus,
        loggedInUserId: this.loginUser.userId || 0,
      };

      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({
        next: (response: any) => {
          if (response && response.message) {
            //alert(response.message);
            this.loadTable(); // Reload the table
            this.loadUserStatistics(); // Reload statistics
            this.selectedUsers = []; // Clear selection
            this.showBulkActions = false;
          }
        },
        error: (error: unknown) => {
          console.error('Error updating users:', error);
          //alert('Error updating users. Please try again.');
                                      this.customLayoutUtilsService.showError('Error updating users. Please try again', '');

          // Reset loading state on error
          this.loading = false;
          this.isLoading = false;
        },
      });
    }
  }

  // Unlock user
  unlockUser(user: any): void {
    if (
      confirm(
        `Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`
      )
    ) {
      // Show loading state
      this.loading = true;
      this.isLoading = true;

      const unlockData = {
        userId: user.userId,
        loggedInUserId: this.loginUser.userId || 0,
      };

      this.usersService.unlockUser(unlockData).subscribe({
        next: (response: any) => {
          if (response && response.message) {
                                        this.customLayoutUtilsService.showSuccess(response.message, '');

            //alert(response.message);
            this.loadTable(); // Reload the table
            this.loadUserStatistics(); // Reload statistics
          }
        },
        error: (error: unknown) => {
          console.error('Error unlocking user:', error);
                                      this.customLayoutUtilsService.showError('Error unlocking user', '');

          //alert('Error unlocking user. Please try again.');
          // Reset loading state on error
          this.loading = false;
          this.isLoading = false;
        },
      });
    }
  }

  onSearchKeyDown(event: KeyboardEvent): void {
    console.log('Search keydown event:', event.key, 'Search data:', this.searchData);
    if (event.key === 'Enter') {
      // Trigger search immediately on Enter key
      console.log('Triggering search on Enter key');
      this.searchTerms.next(this.searchData || '');
    }
  }

  // Handle search model changes
  onSearchChange(): void {
    // Trigger search when model changes with debouncing
    console.log('Search model changed:', this.searchData);
    console.log('Triggering search with debounce');
    // Ensure search is triggered even for empty strings
    this.searchTerms.next(this.searchData || '');
  }

  // Enhanced function to filter data from search and advanced filters
  filterConfiguration(): {
    paginate: boolean;
    search: string;
    columnFilter: Array<{
      field: string;
      operator: string;
      value: any;
    }>;
  } {
    let filter: {
      paginate: boolean;
      search: string;
      columnFilter: Array<{
        field: string;
        operator: string;
        value: any;
      }>;
    } = {
      paginate: true,
      search: '',
      columnFilter: [],
    };

    // Handle search text
    let searchText: string;
    if (this.searchData === null || this.searchData === undefined) {
      searchText = '';
    } else {
      searchText = this.searchData;
    }
    filter.search = searchText.trim();

    // Handle Kendo UI grid filters
    if (this.activeFilters && this.activeFilters.length > 0) {
      filter.columnFilter = [...this.activeFilters];
    }

    // Add advanced filters
    if (this.appliedFilters.status && this.appliedFilters.status !== null) {
      filter.columnFilter.push({
        field: 'userStatus',
        operator: 'eq',
        value: this.appliedFilters.status,
      });
    }

    if (this.appliedFilters.role && this.appliedFilters.role !== null) {
      filter.columnFilter.push({
        field: 'roleName',
        operator: 'eq',
        value: this.appliedFilters.role,
      });
    }

    return filter;
  }

  // Grid event handlers
  public pageChange(event: { skip: number; take: number }): void {
    this.skip = event.skip;
    this.page.pageNumber = event.skip / event.take;
    this.page.size = event.take;
    // Set loading state for pagination
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  public onSortChange(sort: SortDescriptor[]): void {
    // Check if this is the 3rd click (dir is undefined)
    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;

    if (isThirdClick) {
      // 3rd click - clear sort and use default
      this.sort = [];
      this.page.orderBy = 'lastUpdatedDate';
      this.page.orderDir = 'desc';
    } else if (sort.length > 0 && sort[0] && sort[0].dir) {
      // Valid sort with direction
      this.sort = sort;
      this.page.orderBy = sort[0].field || 'lastUpdatedDate';
      this.page.orderDir = sort[0].dir;
    } else {
      // Empty sort array or invalid sort
      this.sort = [];
      this.page.orderBy = 'lastUpdatedDate';
      this.page.orderDir = 'desc';
    }

    // Set loading state for sorting
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  public filterChange(filter: CompositeFilterDescriptor): void {
    this.filter = filter;
    this.gridFilter = filter;
    this.activeFilters = this.flattenFilters(filter);
    this.page.pageNumber = 0;
    this.skip = 0;
    // Set loading state for filtering
    this.loading = true;
    this.isLoading = true;
    this.loadTable();
  }

  // Old column visibility methods removed - replaced with new system

  // Fix 2: More robust getFilterValue method
  public getFilterValue(
    filter: CompositeFilterDescriptor,
    column: { field: string }
  ): any {
    if (!filter || !filter.filters || !column) {
      return null;
    }
    const predicate = filter.filters.find(
      (f: any) => f && 'field' in f && f.field === column.field
    );
    return predicate && 'value' in predicate ? predicate.value : null;
  }

  // Fix 3: More robust onStatusFilterChange method
  public onStatusFilterChange(
    value: string | null,
    filter: CompositeFilterDescriptor,
    column: { field: string }
  ): void {
    if (!filter || !filter.filters || !column) {
      console.error('Invalid filter or column:', { filter, column });
      return;
    }

    const exists = filter.filters.findIndex(
      (f: any) => f && 'field' in f && f.field === column.field
    );
    if (exists > -1) {
      filter.filters.splice(exists, 1);
    }

    if (value !== null) {
      filter.filters.push({
        field: column.field,
        operator: 'eq',
        value: value,
      });
    }

    this.filterChange(filter);
  }

  // Fix 4: More robust flattenFilters method
  private flattenFilters(filter: CompositeFilterDescriptor): Array<{
    field: string;
    operator: string;
    value: any;
  }> {
    const filters: Array<{
      field: string;
      operator: string;
      value: any;
    }> = [];

    if (!filter || !filter.filters) {
      return filters;
    }

    filter.filters.forEach((f: any) => {
      if (f && 'field' in f) {
        // It's a FilterDescriptor
        filters.push({
          field: f.field,
          operator: f.operator,
          value: f.value,
        });
      } else if (f && 'filters' in f) {
        // It's a CompositeFilterDescriptor
        filters.push(...this.flattenFilters(f));
      }
    });

    return filters;
  }

  // Fix 5: More robust loadGridState method
  private loadGridState(): void {
    try {
      const savedState = localStorage.getItem(this.GRID_STATE_KEY);

      if (!savedState) {
        return;
      }

      const state: {
        sort?: SortDescriptor[];
        filter?: CompositeFilterDescriptor;
        page?: {
          size: number;
          pageNumber: number;
          totalElements: number;
          totalPages: number;
          orderBy: string;
          orderDir: string;
        };
        skip?: number;
        columnsVisibility?: Record<string, boolean>;
        searchData?: string;
        activeFilters?: Array<{
          field: string;
          operator: string;
          value: any;
        }>;
        appliedFilters?: {
          status?: string | null;
          role?: string | null;
        };
        showAdvancedFilters?: boolean;
      } = JSON.parse(savedState);

      // Restore sort state
      if (state && state.sort) {
        this.sort = state.sort;
        if (this.sort && this.sort.length > 0 && this.sort[0]) {
          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';
          this.page.orderDir = this.sort[0].dir || 'desc';
        }
      }

      // Restore filter state
      if (state && state.filter) {
        this.filter = state.filter;
        this.gridFilter = state.filter;
        this.activeFilters = state.activeFilters || [];
      }

      // Restore pagination state
      if (state && state.page) {
        this.page = state.page;
      }

      if (state && state.skip !== undefined) {
        this.skip = state.skip;
      }

      // Restore column visibility
      if (state && state.columnsVisibility) {
        this.columnsVisibility = state.columnsVisibility;
      }

      // Restore search state
      if (state && state.searchData) {
        this.searchData = state.searchData;
      }

      // Restore advanced filter states
      if (state && state.appliedFilters) {
        this.appliedFilters = state.appliedFilters;
      }

      if (state && state.showAdvancedFilters !== undefined) {
        this.showAdvancedFilters = state.showAdvancedFilters;
      }
    } catch (error) {
      console.error('Error loading grid state:', error);
      // If there's an error, use default state
    }
  }

  // Old getHiddenField method removed - replaced with new system

  // Grid state persistence methods
  private saveGridState(): void {
    const state: {
      sort: SortDescriptor[];
      filter: CompositeFilterDescriptor;
      page: {
        size: number;
        pageNumber: number;
        totalElements: number;
        totalPages: number;
        orderBy: string;
        orderDir: string;
      };
      skip: number;
      columnsVisibility: Record<string, boolean>;
      searchData: string;
      activeFilters: Array<{
        field: string;
        operator: string;
        value: any;
      }>;
      appliedFilters: {
        status?: string | null;
        role?: string | null;
      };
      showAdvancedFilters: boolean;
    } = {
      sort: this.sort,
      filter: this.filter,
      page: this.page,
      skip: this.skip,
      columnsVisibility: this.columnsVisibility,
      searchData: this.searchData,
      activeFilters: this.activeFilters,
      appliedFilters: this.appliedFilters,
      showAdvancedFilters: this.showAdvancedFilters,
    };

    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));
  }

  // Function to add a new company (calls edit function with ID 0)
  add() {
    this.edit(0);
  }

  // Function to open the edit modal for adding/editing a company
  edit(id: number) {
    console.log('Line: 413', 'call edit function: ', id);
    // Configuration options for the modal dialog
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };

    // Open the modal and load the AddCompaniesComponent
    const modalRef = this.modalService.open(AddUserComponent, NgbModalOptions);
    // Pass the selected ID to the modal component (0 for new, existing ID for edit)
    modalRef.componentInstance.id = id;
    modalRef.componentInstance.defaultPermissions = this.permissionArray;
    // Subscribe to the modal event when data is updated
    modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {
      if (receivedEntry === true) {
        // Reload the table data after a successful update
        this.loadTable();
      }
    });
  }

  public deleteTemplate(item: UserData): void {
    console.log('Delete template:', item);
    // Implement delete functionality
  }
  public toggleExpand(): void {
    // Find grid container element and toggle fullscreen class
    const gridContainer = document.querySelector(
      '.grid-container'
    ) as HTMLElement;
    if (gridContainer) {
      gridContainer.classList.toggle('fullscreen-grid');
      this.isExpanded = !this.isExpanded;
      // Refresh grid after resize to ensure proper rendering
      if (this.grid) {
        this.grid.refresh();
      }
    }
  }

  // Enhanced export functionality
  public onExportClick(event: { item: { value: string } }): void {
    switch (event.item.value) {
      case 'all':
        this.exportAllUsers();
        break;
      case 'selected':
        this.exportSelectedUsers();
        break;
      case 'filtered':
        this.exportFilteredUsers();
        break;
      default:
        console.warn('Unknown export option:', event.item.value);
    }
  }

  private exportAllUsers(): void {
    const exportParams = {
      filters: {},
      format: 'excel',
    };

    this.usersService.exportUsers(exportParams).subscribe({
      next: (response: any) => {
        if (response && response.exportData) {
          this.downloadExcel(response.exportData, 'All_Users');
        }
      },
      error: (error: unknown) => {
        console.error('Error exporting users:', error);
                                    this.customLayoutUtilsService.showError('Error exporting users', '');

        //alert('Error exporting users. Please try again.');
      },
    });
  }

  private exportSelectedUsers(): void {
    if (this.selectedUsers.length === 0) {
                                  this.customLayoutUtilsService.showError('Please select users to export', '');

      //alert('Please select users to export.');
      return;
    }

    const exportParams = {
      filters: {
        userIds: this.selectedUsers.map((user) => user.UserId),
      },
      format: 'excel',
    };

    this.usersService.exportUsers(exportParams).subscribe({
      next: (response: any) => {
        if (response && response.exportData) {
          this.downloadExcel(response.exportData, 'Selected_Users');
        }
      },
      error: (error: unknown) => {
        console.error('Error exporting selected users:', error);
                                    this.customLayoutUtilsService.showError('Error exporting selected users', '');

        //alert('Error exporting selected users. Please try again.');
      },
    });
  }

  private exportFilteredUsers(): void {
    const exportParams = {
      filters: {
        status: this.appliedFilters.status,
        role: this.appliedFilters.role,
        searchTerm: this.searchData,
      },
      format: 'excel',
    };

    this.usersService.exportUsers(exportParams).subscribe({
      next: (response: any) => {
        if (response && response.exportData) {
          this.downloadExcel(response.exportData, 'Filtered_Users');
        }
      },
      error: (error: unknown) => {
        console.error('Error exporting filtered users:', error);
                                    this.customLayoutUtilsService.showError('Error exporting filtered users', '');

        //alert('Error exporting filtered users. Please try again.');
      },
    });
  }

  private downloadExcel(data: any[], filename: string): void {
    // This would typically use a library like xlsx or similar
    // For now, we'll create a simple CSV download
    const csvContent = this.convertToCSV(data);
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute(
      'download',
      `${filename}_${new Date().toISOString().split('T')[0]}.csv`
    );
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }

  private convertToCSV(data: any[]): string {
    if (data.length === 0) return '';

    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];

    for (const row of data) {
      const values = headers.map((header) => {
        const value = row[header];
        return typeof value === 'string' && value.includes(',')
          ? `"${value}"`
          : value;
      });
      csvRows.push(values.join(','));
    }

    return csvRows.join('\n');
  }

  // NEW COLUMN VISIBILITY SYSTEM METHODS

  /**
   * Saves the current state of column visibility and order in the grid.
   * This function categorizes columns into visible and hidden columns, records their titles,
   * fields, and visibility status, and also captures the order of draggable columns.
   * After gathering the necessary data, it sends this information to the backend for saving.
   */
  saveHead(): void {
    // Check if loginUser is available
    if (!this.loginUser || !this.loginUser.userId) {
      console.error('loginUser not available:', this.loginUser);
      this.customLayoutUtilsService.showError(
        'User not logged in. Please refresh the page.',
        ''
      );
      return;
    }

    const nonHiddenColumns: any[] = [];
    const hiddenColumns: any[] = [];

    if (this.grid && this.grid.columns) {
      this.grid.columns.forEach((column: any) => {
        if (!column.hidden) {
          const columnData = {
            title: column.title,
            field: column.field,
            hidden: column.hidden,
          };
          nonHiddenColumns.push(columnData);
        } else {
          const columnData = {
            title: column.title,
            field: column.field,
            hidden: column.hidden,
          };
          hiddenColumns.push(columnData);
        }
      });
    }

    const draggableColumnsOrder = this.gridColumns
      .filter((col) => !this.fixedColumns.includes(col))
      .map((field, index) => ({
        field,
        orderIndex: index,
      }));

    // Prepare data for backend
    const userData = {
      pageName: 'Users',
      userID: this.loginUser.userId,
      hiddenData: hiddenColumns,
      kendoColOrder: draggableColumnsOrder,
      LoggedId: this.loginUser.userId,
    };

    // Show loading state
    this.httpUtilService.loadingSubject.next(true);

    // Save to backend
    this.kendoColumnService.createHideFields(userData).subscribe({
      next: (res) => {
        this.httpUtilService.loadingSubject.next(false);
        if (!res.isFault) {
          // Update local state
          this.hiddenData = hiddenColumns;
          this.kendoColOrder = draggableColumnsOrder;
          this.hiddenFields = this.hiddenData.map((col: any) => col.field);

          // Also save to localStorage as backup
          this.kendoColumnService.saveToLocalStorage(userData);

          this.customLayoutUtilsService.showSuccess(
            res.message || 'Column settings saved successfully.',
            ''
          );
        } else {
          this.customLayoutUtilsService.showError(
            res.message || 'Failed to save column settings.',
            ''
          );
        }
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.httpUtilService.loadingSubject.next(false);
        console.error('Error saving column settings:', error);

        // Fallback to localStorage on error
        this.kendoColumnService.saveToLocalStorage(userData);

        // Update local state
        this.hiddenData = hiddenColumns;
        this.kendoColOrder = draggableColumnsOrder;
        this.hiddenFields = this.hiddenData.map((col: any) => col.field);

        this.customLayoutUtilsService.showError(
          'Failed to save to server. Settings saved locally.',
          ''
        );
        this.cdr.markForCheck();
      },
    });
  }

  /**
   * Reset the current state of column visibility and order in the grid to its original state.
   * This function resets columns to default visibility and order, and saves the reset state.
   */
  resetTable(): void {
    // Check if loginUser is available
    if (!this.loginUser || !this.loginUser.userId) {
      console.error('loginUser not available:', this.loginUser);
      this.customLayoutUtilsService.showError(
        'User not logged in. Please refresh the page and try again.',
        ''
      );
      return;
    }

    // Double-check authentication token
    const token = this.AppService.getLocalStorageItem('permitToken', true);
    if (!token) {
      console.error('Authentication token not found');
      this.customLayoutUtilsService.showError(
        'Authentication token not found. Please login again.',
        ''
      );
      return;
    }

    // Reset all state variables
    this.searchData = '';
    this.activeFilters = [];
    this.filter = { logic: 'and', filters: [] };
    this.skip = 0;
    this.page.pageNumber = 0;
    this.gridColumns = [...this.defaultColumns];

    // Reset sort state to default
    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
    this.page.orderBy = 'lastUpdatedDate';
    this.page.orderDir = 'desc';

    // Reset advanced filters
    this.appliedFilters = {};

    // Reset advanced filters visibility
    this.showAdvancedFilters = false;

    // Reset column order index
    if (this.grid && this.grid.columns) {
      this.grid.columns.forEach((column: any) => {
        const index = this.gridColumns.indexOf(column.field);
        if (index !== -1) {
          column.orderIndex = index;
        }
        // Reset column visibility - show all columns
        if (column.field && column.field !== 'action') {
          column.hidden = false;
        }
      });
    }

    // Clear hidden columns
    this.hiddenData = [];
    this.kendoColOrder = [];
    this.hiddenFields = [];

    // Reset the Kendo Grid's internal state
    if (this.grid) {
      // Clear all filters
      this.grid.filter = { logic: 'and', filters: [] };
      
      // Reset sorting
      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
      
      // Reset to first page
      this.grid.skip = 0;
      this.grid.pageSize = this.page.size;
    }

    // Prepare reset data
    const userData = {
      pageName: 'Users',
      userID: this.loginUser.userId,
      hiddenData: [],
      kendoColOrder: [],
      LoggedId: this.loginUser.userId,
    };

    // Only clear local settings; do not call server
    this.kendoColumnService.clearFromLocalStorage('Users');

    // Show loader and refresh grid
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);
    this.cdr.detectChanges();

    // Force grid refresh to apply all changes
    if (this.grid) {
      setTimeout(() => {
        this.grid.refresh();
        this.grid.reset();
      }, 100);
    }

    this.loadTable();
  }

  /**
   * Loads and applies the saved column order from the user preferences or configuration.
   * This function updates the grid column order, ensuring the fixed columns remain in place
   * and the draggable columns are ordered according to the saved preferences.
   */
  loadSavedColumnOrder(kendoColOrder: any): void {
    try {
      const savedOrder = kendoColOrder;
      if (savedOrder) {
        const parsedOrder = savedOrder;
        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {
          // Get only the draggable columns from saved order
          const savedDraggableColumns = parsedOrder
            .sort((a, b) => a.orderIndex - b.orderIndex)
            .map((col) => col.field)
            .filter((field) => !this.fixedColumns.includes(field));

          // Add any missing draggable columns at the end
          const missingColumns = this.draggableColumns.filter(
            (col) => !savedDraggableColumns.includes(col)
          );

          // Combine fixed columns with saved draggable columns
          this.gridColumns = [
            ...this.fixedColumns,
            ...savedDraggableColumns,
            ...missingColumns,
          ];
        } else {
          this.gridColumns = [...this.defaultColumns];
        }
      } else {
        this.gridColumns = [...this.defaultColumns];
      }
    } catch (error) {
      this.gridColumns = [...this.defaultColumns];
    }
  }

  /**
   * Checks if a given column is marked as hidden.
   * This function searches the `hiddenFields` array to determine if the column should be hidden.
   */
  getHiddenField(columnName: any): boolean {
    return this.hiddenFields.indexOf(columnName) > -1;
  }

  /**
   * Handles the column reordering event triggered when a column is moved by the user.
   * The function checks if the column being moved is in the fixed columns and prevents reordering
   * of fixed columns.
   */
  onColumnReorder(event: any): void {
    const { columns, newIndex, oldIndex } = event;

    // Prevent reordering of fixed columns
    if (
      this.fixedColumns.includes(columns[oldIndex].field) ||
      this.fixedColumns.includes(columns[newIndex].field)
    ) {
      return;
    }

    // Update the gridColumns array
    const reorderedColumns = [...this.gridColumns];
    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);
    reorderedColumns.splice(newIndex, 0, movedColumn);

    this.gridColumns = reorderedColumns;
    this.cdr.markForCheck();
  }

  /**
   * Handles column visibility changes from the Kendo Grid.
   * Updates the local state when columns are shown or hidden.
   */
  updateColumnVisibility(event: any): void {
    if (this.isExpanded === false) {
      if (this.grid && this.grid.columns) {
        this.grid.columns.forEach((column: any) => {
          const columnData = {
            title: column.title,
            field: column.field,
            hidden: column.hidden,
          };
          if (column.hidden) {
            const exists = this.hiddenData.some(
              (item: any) =>
                item.field === columnData.field && item.hidden === true
            );
            if (!exists) {
              this.hiddenData.push(columnData);
            }
          } else {
            let indexExists = this.hiddenData.findIndex(
              (item: any) =>
                item.field === columnData.field && item.hidden === true
            );
            if (indexExists !== -1) {
              this.hiddenData.splice(indexExists, 1);
            }
          }
        });
        this.hiddenFields = this.hiddenData.map((col: any) => col.field);
        this.cdr.markForCheck();
      }
    }
  }

  /**
   * Loads the saved column configuration from the backend or localStorage as fallback.
   * This method is called during component initialization to restore user preferences.
   */
  private loadColumnConfigFromDatabase(): void {
    try {
      // First try to load from backend
      if (this.loginUser && this.loginUser.userId) {
        this.kendoColumnService
          .getHideFields({
            pageName: 'Users',
            userID: this.loginUser.userId,
          })
          .subscribe({
            next: (res) => {
              if (!res.isFault && res.Data) {
                this.kendoHide = res.Data;
                this.hiddenData = res.Data.hideData
                  ? JSON.parse(res.Data.hideData)
                  : [];
                this.kendoInitColOrder = res.Data.kendoColOrder
                  ? JSON.parse(res.Data.kendoColOrder)
                  : [];
                this.hiddenFields = this.hiddenData.map(
                  (col: any) => col.field
                );

                // Update grid columns based on the hidden fields
                if (this.grid && this.grid.columns) {
                  this.grid.columns.forEach((column: any) => {
                    if (
                      this.hiddenData.some(
                        (item: any) =>
                          item.title === column.title && item.hidden
                      )
                    ) {
                      column.includeInChooser = true;
                      column.hidden = true;
                    } else {
                      column.hidden = false;
                    }
                  });
                }

                // Load saved column order and update grid
                this.loadSavedColumnOrder(this.kendoInitColOrder);

                // Also save to localStorage as backup
                this.kendoColumnService.saveToLocalStorage({
                  pageName: 'Users',
                  userID: this.loginUser.userId,
                  hiddenData: this.hiddenData,
                  kendoColOrder: this.kendoInitColOrder,
                });
              }
            },
            error: (error) => {
              console.error(
                'Error loading from backend, falling back to localStorage:',
                error
              );
              this.loadFromLocalStorageFallback();
            },
          });
      } else {
        // Fallback to localStorage if no user ID
        this.loadFromLocalStorageFallback();
      }
    } catch (error) {
      console.error('Error loading column configuration:', error);
      this.loadFromLocalStorageFallback();
    }
  }

  /**
   * Fallback method to load column configuration from localStorage
   */
  private loadFromLocalStorageFallback(): void {
    try {
      const savedConfig = this.kendoColumnService.getFromLocalStorage(
        'Users',
        this.loginUser?.UserId || 0
      );
      if (savedConfig) {
        this.kendoHide = savedConfig;
        this.hiddenData = savedConfig.hiddenData || [];
        this.kendoInitColOrder = savedConfig.kendoColOrder || [];
        this.hiddenFields = this.hiddenData.map((col: any) => col.field);

        // Update grid columns based on the hidden fields
        if (this.grid && this.grid.columns) {
          this.grid.columns.forEach((column: any) => {
            if (
              this.hiddenData.some(
                (item: any) => item.title === column.title && item.hidden
              )
            ) {
              column.includeInChooser = true;
              column.hidden = true;
            } else {
              column.hidden = false;
            }
          });
        }

        // Load saved column order and update grid
        this.loadSavedColumnOrder(this.kendoInitColOrder);
      }
    } catch (error) {
      console.error('Error loading from localStorage fallback:', error);
    }
  }
}

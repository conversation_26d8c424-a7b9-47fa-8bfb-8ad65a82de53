// Modal sizing and scrolling
:host ::ng-deep .modal-dialog {
  max-width: 800px;
  width: 90%;
}

:host ::ng-deep .modal-body {
  max-height: 70vh;
  overflow-y: auto;
  padding: 0;
}
:host ::ng-deep .modal {
  --bs-modal-padding: 1rem !important;
}
// Header Banner
.review-header-banner {
  padding: 1rem 1.5rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0.375rem 0.375rem 0 0;
  margin-top: 2px;
  margin-left: 4px;
}

.banner-content {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  flex: 1;
}

.banner-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-icon {
  font-size: 0.75rem;
  
  &.status-requires-resubmit {
    color: #ffc107;
  }
  
  &.status-approved {
    color: #28a745;
  }
  
  &.status-under-review {
    color: #17a2b8;
  }
  
  &.status-pending {
    color: #ffc107;
  }
}

.banner-text {
  white-space: nowrap;
}

.envelope-icon {
  color: #007bff;
  margin-left: 0.5rem;
}

.square-icon {
  color: #007bff;
  margin-left: 0.25rem;
}

// Dates Section
.dates-section {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5eaee;
  background-color: #f8f9fa;
}

.date-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 2rem;
}

.date-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  
  label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c7293;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
  }
  
  .date-value {
    font-size: 0.875rem;
    color: #3f4254;
    font-weight: 500;
  }
}

// Comment Section
.comment-section {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5eaee;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #3f4254;
  margin-bottom: 0.75rem;
  padding-bottom: 0.25rem;
  border-bottom: 1px solid #e5eaee;
}

.contact-info {
  p {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    color: #3f4254;
    line-height: 1.4;
  }
  
  .contact-name {
    font-weight: 600;
    color: #2c3e50;
  }
  
  .contact-title {
    color: #6c7293;
  }
  
  .contact-id,
  .contact-license,
  .contact-certification {
    color: #6c7293;
    font-family: 'Courier New', monospace;
  }
  
  .contact-organization {
    font-weight: 500;
    color: #495057;
  }
  
  .contact-phone {
    color: #6c7293;
  }
  
  .contact-email {
    color: #007bff;
    font-weight: 500;
  }
}

// Corrections Section
.corrections-section {
  padding: 1rem 1.5rem;
}

.correction-item {
  border: 1px solid #e5eaee;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  background: #fff;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.correction-header {
  background-color: #f8f9fa;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e5eaee;
  border-radius: 0.375rem 0.375rem 0 0;
  display: flex;
  align-items: center;
  gap: 1rem;

  .correction-number {
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: 600;
    flex-shrink: 0;
  }

  .correction-type {
    font-size: 0.875rem;
    font-weight: 600;
    color: #3f4254;
  }

  .correction-action {
    font-size: 0.875rem;
    font-weight: 500;
    color: #3f4254;
  }
}

.correction-content {
  padding: 1rem;
}

.correction-field {
  margin-bottom: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  label {
    display: block;
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c7293;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    margin-bottom: 0.25rem;
  }
  
  .correction-value {
    font-size: 0.875rem;
    color: #3f4254;
    font-weight: 500;
  }
  
  .correction-comment {
    font-size: 0.875rem;
    color: #3f4254;
    line-height: 1.5;
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.25rem;
    border-left: 3px solid #007bff;
  }
  
  .correction-response {
    font-size: 0.875rem;
    color: #28a745;
    line-height: 1.5;
    background-color: #f8fff9;
    padding: 0.75rem;
    border-radius: 0.25rem;
    border-left: 3px solid #28a745;
  }
  
  .correction-resolved {
    font-size: 0.875rem;
    color: #6c7293;
    font-weight: 500;
  }
}

// Comments Section (fallback when no corrections)
.comments-section {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5eaee;
}

.comment-content {
  .comment-text {
    font-size: 0.875rem;
    color: #3f4254;
    line-height: 1.6;
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.375rem;
    border-left: 4px solid #17a2b8;
    white-space: pre-wrap;
    word-wrap: break-word;
  }
}

// No Data Section
.no-data-section {
  padding: 2rem 1.5rem;
  text-align: center;
}

.no-data-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: #6c7293;
  
  i {
    font-size: 2rem;
    color: #dee2e6;
  }
  
  span {
    font-size: 0.875rem;
    font-weight: 500;
  }
}

.correction-contact-info {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e5eaee;
  
  .contact-label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c7293;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
    margin-bottom: 0.5rem;
  }
  
  .contact-details {
    background-color: #f8f9fa;
    padding: 0.75rem;
    border-radius: 0.25rem;
    
    p {
      margin: 0 0 0.25rem 0;
      font-size: 0.875rem;
      color: #3f4254;
      line-height: 1.4;
    }
    
    .contact-name {
      font-weight: 600;
      color: #2c3e50;
    }
    
    .contact-title {
      color: #6c7293;
    }
    
    .contact-id,
    .contact-license,
    .contact-certification {
      color: #6c7293;
      font-family: 'Courier New', monospace;
    }
    
    .contact-organization {
      font-weight: 500;
      color: #495057;
    }
    
    .contact-phone {
      color: #6c7293;
    }
    
    .contact-email {
      color: #007bff;
      font-weight: 500;
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .date-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .banner-content {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .banner-item {
    font-size: 0.8rem;
  }
}

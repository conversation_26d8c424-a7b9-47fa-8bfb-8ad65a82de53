import { Component, Input, Output, EventEmitter } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-response-modal',
  templateUrl: './response-modal.component.html',
  styleUrls: ['./response-modal.component.scss']
})
export class ResponseModalComponent {
  @Input() correction: any;
  @Input() review: any;
  @Input() permitId: any;
  @Input() loggedInUserId: any;
  @Input() isAdmin: boolean = false;
  @Output() responseSubmitted = new EventEmitter<any>();
  @Output() responseCompleted = new EventEmitter<boolean>();

  public responseForm: any = {
    EORAOROwner_Response: '',
    commentResponsedBy: '',
    lockResponse: false
  };
  public isLoading: boolean = false;

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit(): void {
    if (this.correction) {
      this.responseForm = {
        EORAOROwner_Response: this.correction.EORAOROwner_Response || '',
        commentResponsedBy: this.correction.commentResponsedBy || '',
        lockResponse: this.correction.lockResponse || false
      };
    }
  }

  public onLockResponseChange(): void {
    // This method is called when the checkbox state changes
    // The form fields will be automatically enabled/disabled based on the lockResponse value
  }

  public submitResponse(): void {
    if (!this.correction || !this.review) {
      return;
    }

    this.isLoading = true;
    const formData = {
      EORAOROwner_Response: this.responseForm.EORAOROwner_Response,
      commentResponsedBy: this.responseForm.commentResponsedBy,
      lockResponse: this.responseForm.lockResponse,
      permitId: this.permitId,
      correctionId: this.correction.CorrectionID,
      commentsId: this.review.commentsId,
      loggedInUserId: this.loggedInUserId
    };

    // Emit the form data to the parent component
    this.responseSubmitted.emit(formData);
  }

  public closeModal(): void {
    this.activeModal.dismiss();
  }
}

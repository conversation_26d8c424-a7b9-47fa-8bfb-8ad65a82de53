import { NgModule,CUSTOM_ELEMENTS_SCHEMA ,NO_ERRORS_SCHEMA} from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';



import { HttpClientModule } from '@angular/common/http';


// import { QuillModule } from 'ngx-quill';

import { GridModule } from '@progress/kendo-angular-grid';
import { InputsModule } from '@progress/kendo-angular-inputs';
import { ButtonsModule } from '@progress/kendo-angular-buttons';
import { DialogModule } from '@progress/kendo-angular-dialog';
import { DropDownListModule} from '@progress/kendo-angular-dropdowns';
import { DateInputsModule } from '@progress/kendo-angular-dateinputs';
import { ExcelExportModule } from '@progress/kendo-angular-excel-export';
import { PopupModule } from '@progress/kendo-angular-popup';
import { NavigationModule } from '@progress/kendo-angular-navigation';
import { InlineSVGModule } from 'ng-inline-svg-2';
import{IgxIconModule, IgxInputGroupModule, IgxMaskModule,}from'igniteui-angular';

import { LayoutModule } from 'src/app/_metronic/layout';
import { SharedModule } from '../shared/shared.module';
import { SettingRoutingModule } from './setting-routing.module';
import { UserListComponent } from './user_list/user-list.component';
import { SettingComponent } from './setting.component';
import { AddUserComponent } from './add-user/user-add.component';
import { RoleEditComponent } from './role-edit/role-edit.component';
import { RoleListComponent } from './role-list/role-list.component';
import { ActivityLogListComponent } from './activity-log-list/activity-log-list.component';
import { ActivityLogViewComponent } from './activity-log-view/activity-log-view.component';
import { EmailTemplatesEditComponent } from './email-templates-edit/email-templates-edit.component';
import { EmailTemplatesListComponent } from './email-templates-list/email-templates-list.component';
import { SettingsViewComponent } from './settings-view/settings-view.component';



@NgModule({
    declarations: [
        UserListComponent,
        SettingComponent,
        AddUserComponent,
       RoleEditComponent,
       RoleListComponent,
       EmailTemplatesListComponent,
       ActivityLogListComponent,
       ActivityLogViewComponent,
       EmailTemplatesEditComponent
    ],

    imports: [
       CommonModule,
           FormsModule,
           ReactiveFormsModule,
           SettingRoutingModule,
           HttpClientModule,
            NgbModule,
            LayoutModule,
           NgSelectModule,
        //    QuillModule.forRoot(),
           IgxIconModule, IgxInputGroupModule, IgxMaskModule,
           GridModule,
           InputsModule,
           ButtonsModule,
           DialogModule,
           DateInputsModule,
           ExcelExportModule,
           PopupModule,
           NavigationModule,
           InlineSVGModule,
           DropDownListModule,
           SettingsViewComponent
            ,SharedModule
    ],
    schemas: [ CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA ],
})
export class SettingModule { }

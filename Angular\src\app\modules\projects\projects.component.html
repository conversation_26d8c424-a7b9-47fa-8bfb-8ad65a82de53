<div class="card mb-5 mb-xl-5">
    <div class="card-body pb-0 pt-0" *ngIf="showNavBar">
        <div class="d-flex">
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-3x border-transparent fs-5 fw-bold flex-nowrap">
                <li class="nav-item">
                    <a class="nav-link text-active-primary me-6 cursor-pointer"
                        [ngClass]="{'active': selectedTab === 'Projects'}" 
                        (click)="onNavChange('Projects')">
                        Projects
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <div *ngIf="selectedTab==='Projects'">
        <router-outlet></router-outlet>
    </div>
</div>

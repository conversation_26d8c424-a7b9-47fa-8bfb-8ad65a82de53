import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Observable, Subscription } from 'rxjs';
import { AuthService } from '../../services/auth.service';
import { first } from 'rxjs/operators';
import { CustomLayoutUtilsService } from 'src/app/modules/services/custom-layout.utils.service';

import { ActivatedRoute, Router } from '@angular/router';
import { RxwebValidators } from '@rxweb/reactive-form-validators';
enum ErrorStates {
  NotSubmitted,
  HasError,
  NoError,
}

@Component({
  selector: 'app-forgot-password',
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.scss'],
})
export class ForgotPasswordComponent implements OnInit {
  forgotPasswordForm: FormGroup;
  loading = false;
  errorState: ErrorStates = ErrorStates.NotSubmitted;
  errorStates = ErrorStates;
  isLoading$: Observable<boolean>;

  // private fields
  private unsubscribe: Subscription[] = []; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/
  constructor(private fb: FormBuilder, private authService: AuthService,
    private layoutUtilService: CustomLayoutUtilsService,
    private route: ActivatedRoute,
    private router: Router,) {
    this.isLoading$ = this.authService.isLoading$;
  }

  ngOnInit(): void {
    this.initForm();
  }

  // convenience getter for easy access to form fields
  get f() {
    return this.forgotPasswordForm.controls;
  }

  initForm() {
    this.forgotPasswordForm = this.fb.group({
      email: [
        '',
        Validators.compose([
          Validators.required,
          // Validators.email,
          RxwebValidators.email(),
          // Validators.minLength(3),
          // Validators.maxLength(320), // https://stackoverflow.com/questions/386294/what-is-the-maximum-length-of-a-valid-email-address
        ]),
      ],
    });
  }

  submit() {
    const controls = this.forgotPasswordForm.controls;
		this.loading = true;
    //this.httpUtilService.loadingSubject.next(true);
		const user ={ email: controls.email.value};
    this.authService.forgotPassword(user).subscribe((res: { isFault: any; responseData: { message: string; }; }) => {
      if (!res.isFault) {
        this.layoutUtilService.showSuccess(res.responseData.message,'');
        this.loading = false;
        //this.httpUtilService.loadingSubject.next(false);
        this.router.navigate(['/auth/login'], {relativeTo: this.route});
      } else {
        this.layoutUtilService.showError(res.responseData.message,'');
        this.loading = false;
        //this.httpUtilService.loadingSubject.next(false);
      }
    });

  }
}

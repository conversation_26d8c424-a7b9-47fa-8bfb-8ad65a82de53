{"ast": null, "code": "import { each } from 'lodash';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/permits.service\";\nimport * as i3 from \"../../services/http-utils.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"../../services/kendo-column.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"../../services/app.service\";\nimport * as i8 from \"../../services/exceljs.service\";\nimport * as i9 from \"src/app/_metronic/layout/core/page-info.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"@progress/kendo-angular-grid\";\nimport * as i13 from \"@progress/kendo-angular-inputs\";\nimport * as i14 from \"@progress/kendo-angular-dropdowns\";\nimport * as i15 from \"@progress/kendo-angular-buttons\";\nimport * as i16 from \"ng-inline-svg-2\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nconst _c7 = () => ({\n  text: \"All\",\n  value: null\n});\nfunction PermitListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"span\", 12);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitListComponent_ng_template_4_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_div_18_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.onExportClick({\n        value: \"all\"\n      });\n      ctx_r2.closeExcelDropdown();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(2, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_div_18_Template_a_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.onExportClick({\n        value: \"selected\"\n      });\n      ctx_r2.closeExcelDropdown();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(4, \"Page Results\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"top\", ctx_r2.dropdownTop, \"px\")(\"left\", ctx_r2.dropdownLeft, \"px\");\n  }\n}\nfunction PermitListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"kendo-textbox\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function PermitListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"span\", 17);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 20);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 23, 1)(15, \"button\", 24, 2);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_15_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExcelDropdown($event));\n    });\n    i0.ɵɵelement(17, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, PermitListComponent_ng_template_4_div_18_Template, 5, 4, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(20, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(22, \"i\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"show\", ctx_r2.isExcelDropdownOpen);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isExcelDropdownOpen);\n  }\n}\nfunction PermitListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36)(3, \"label\", 37);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 36)(7, \"label\", 37);\n    i0.ɵɵtext(8, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.category, $event) || (ctx_r2.appliedFilters.category = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 40)(11, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵtext(12, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAdvancedFilters());\n    });\n    i0.ɵɵtext(14, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showAdvancedFilters = false);\n    });\n    i0.ɵɵtext(16, \" Hide Filters \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.categories);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.category);\n  }\n}\nfunction PermitListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PermitListComponent_ng_template_5_div_0_Template, 17, 4, \"div\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction PermitListComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showAdvancedFilters = !ctx_r2.showAdvancedFilters);\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.showAdvancedFilters ? \"Hide\" : \"Show\", \" Advanced Filters \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"a\", 65);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener() {\n      const dataItem_r8 = i0.ɵɵrestoreView(_r7).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r8.permitId));\n    });\n    i0.ɵɵelement(2, \"i\", 66);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template, 3, 0, \"ng-template\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 80)(\"sortable\", false)(\"filterable\", false)(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r9 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r9.permitNumber);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r10 = ctx.$implicit;\n    const column_r11 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r11)(\"filter\", filter_r10)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 67);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template, 2, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"permitNumber\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r12 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r12.permitName);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r13 = ctx.$implicit;\n    const column_r14 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r14)(\"filter\", filter_r13)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 71);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template, 2, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"permitName\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r15 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r15.projectName, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r16 = ctx.$implicit;\n    const column_r17 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r17)(\"filter\", filter_r16)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 72);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"projectName\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r18 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r18.permitType, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r19 = ctx.$implicit;\n    const column_r20 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r20)(\"filter\", filter_r19)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 73);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 200)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"permitType\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r21 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getCategoryClass(dataItem_r21.permitCategory));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r21.permitCategory, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r22 = i0.ɵɵrestoreView(_r22);\n      const filterService_r24 = ctx_r22.filterService;\n      const column_r25 = ctx_r22.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r25.field, filterService_r24));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r25 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r25)(\"filter\", filter_r26)(\"data\", ctx_r2.advancedFilterOptions.categories);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 74);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 120);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r27 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r27.internalReviewStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r27.internalReviewStatus, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.internalReviewStatus, $event) || (ctx_r2.appliedFilters.internalReviewStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r28 = i0.ɵɵrestoreView(_r28);\n      const filterService_r30 = ctx_r28.filterService;\n      const column_r31 = ctx_r28.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r31.field, filterService_r30));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r32 = ctx.$implicit;\n    const column_r31 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r31)(\"filter\", filter_r32)(\"data\", ctx_r2.advancedFilterOptions.categoriess);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.internalReviewStatus);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 77);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r33 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r33.permitReviewType));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r33.permitReviewType, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.internalReviewStatus, $event) || (ctx_r2.appliedFilters.internalReviewStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r34 = i0.ɵɵrestoreView(_r34);\n      const filterService_r36 = ctx_r34.filterService;\n      const column_r37 = ctx_r34.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r37.field, filterService_r36));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r38 = ctx.$implicit;\n    const column_r37 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r37)(\"filter\", filter_r38)(\"data\", ctx_r2.advancedFilterOptions.categoriess);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.internalReviewStatus);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 78);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r39 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r39.permitStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r39.permitStatus, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r40 = i0.ɵɵrestoreView(_r40);\n      const filterService_r42 = ctx_r40.filterService;\n      const column_r43 = ctx_r40.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r43.field, filterService_r42));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r44 = ctx.$implicit;\n    const column_r43 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r43)(\"filter\", filter_r44)(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 79);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r45 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r45.location, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r46 = ctx.$implicit;\n    const column_r47 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r47)(\"filter\", filter_r46)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 80);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 200);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r48 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r48.permitAppliedDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r49 = ctx.$implicit;\n    const column_r50 = ctx.column;\n    const filterService_r51 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r50)(\"filter\", filter_r49)(\"filterService\", filterService_r51);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 81);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r52 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r52.permitExpirationDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r53 = ctx.$implicit;\n    const column_r54 = ctx.column;\n    const filterService_r55 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r54)(\"filter\", filter_r53)(\"filterService\", filterService_r55);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 83);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r56 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r56.permitFinalDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r57 = ctx.$implicit;\n    const column_r58 = ctx.column;\n    const filterService_r59 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r58)(\"filter\", filter_r57)(\"filterService\", filterService_r59);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 84);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r60 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r60.permitCompleteDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r61 = ctx.$implicit;\n    const column_r62 = ctx.column;\n    const filterService_r63 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r62)(\"filter\", filter_r61)(\"filterService\", filterService_r63);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 85);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r64 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r64.attentionReason || \"\", \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r65 = ctx.$implicit;\n    const column_r66 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r66)(\"filter\", filter_r65)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 86);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 180);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r67 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r67.lastUpdatedDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r68 = ctx.$implicit;\n    const column_r69 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r69)(\"filter\", filter_r68)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 87);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_1_Template, 2, 6, \"kendo-grid-column\", 46)(2, PermitListComponent_ng_container_7_kendo_grid_column_2_Template, 3, 5, \"kendo-grid-column\", 47)(3, PermitListComponent_ng_container_7_kendo_grid_column_3_Template, 3, 5, \"kendo-grid-column\", 48)(4, PermitListComponent_ng_container_7_kendo_grid_column_4_Template, 3, 4, \"kendo-grid-column\", 49)(5, PermitListComponent_ng_container_7_kendo_grid_column_5_Template, 3, 5, \"kendo-grid-column\", 50)(6, PermitListComponent_ng_container_7_kendo_grid_column_6_Template, 3, 1, \"kendo-grid-column\", 51)(7, PermitListComponent_ng_container_7_kendo_grid_column_7_Template, 3, 1, \"kendo-grid-column\", 52)(8, PermitListComponent_ng_container_7_kendo_grid_column_8_Template, 3, 1, \"kendo-grid-column\", 53)(9, PermitListComponent_ng_container_7_kendo_grid_column_9_Template, 3, 1, \"kendo-grid-column\", 54)(10, PermitListComponent_ng_container_7_kendo_grid_column_10_Template, 3, 1, \"kendo-grid-column\", 55)(11, PermitListComponent_ng_container_7_kendo_grid_column_11_Template, 3, 1, \"kendo-grid-column\", 56)(12, PermitListComponent_ng_container_7_kendo_grid_column_12_Template, 3, 1, \"kendo-grid-column\", 57)(13, PermitListComponent_ng_container_7_kendo_grid_column_13_Template, 3, 1, \"kendo-grid-column\", 58)(14, PermitListComponent_ng_container_7_kendo_grid_column_14_Template, 3, 1, \"kendo-grid-column\", 59)(15, PermitListComponent_ng_container_7_kendo_grid_column_15_Template, 3, 1, \"kendo-grid-column\", 60)(16, PermitListComponent_ng_container_7_kendo_grid_column_16_Template, 3, 1, \"kendo-grid-column\", 61);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r70 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitNumber\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"projectName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitType\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitCategory\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"internalReviewStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitReviewType\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"location\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitAppliedDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitExpirationDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitFinalDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitCompleteDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"attentionReason\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"lastUpdatedDate\");\n  }\n}\nfunction PermitListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"i\", 89);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Permits Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No permits match your current search criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.clearSearch();\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵtext(8, \" Clear Search \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport class PermitListComponent {\n  router;\n  route;\n  permitsService;\n  httpUtilService;\n  customLayoutUtilsService;\n  kendoColumnService;\n  modalService;\n  cdr;\n  appService;\n  ExceljsService;\n  pageInfo;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  onMenuDropdownChange(value, field, filterService) {\n    const next = value == null ? {\n      filters: [],\n      logic: 'and'\n    } : {\n      filters: [{\n        field,\n        operator: 'eq',\n        value\n      }],\n      logic: 'and'\n    };\n    filterService.filter(next);\n  }\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Requires Resubmit',\n      value: 'Requires Resubmit'\n    }, {\n      text: 'On Hold',\n      value: 'On Hold'\n    }, {\n      text: 'Approved',\n      value: 'Approved'\n    }, {\n      text: 'Pending',\n      value: 'Pending'\n    }, {\n      text: 'Canceled',\n      value: 'Canceled'\n    }, {\n      text: 'Complete',\n      value: 'Complete'\n    }, {\n      text: 'Expired',\n      value: 'Expired'\n    }, {\n      text: 'Fees Due',\n      value: 'Fees Due'\n    }, {\n      text: 'In Review',\n      value: 'In Review'\n    }, {\n      text: 'Issued',\n      value: 'Issued'\n    }, {\n      text: 'Requires Resubmit for Prescreen',\n      value: 'Requires Resubmit for Prescreen'\n    }, {\n      text: 'Submitted - Online',\n      value: 'Submitted - Online'\n    }, {\n      text: 'Void',\n      value: 'Void'\n    }],\n    categories: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Primary',\n      value: 'Primary'\n    }, {\n      text: 'Sub Permit',\n      value: 'Sub Permit'\n    }, {\n      text: 'Industrial',\n      value: 'Industrial'\n    }, {\n      text: 'Municipal',\n      value: 'Municipal'\n    }, {\n      text: 'Environmental',\n      value: 'Environmental'\n    }],\n    categoriess: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Approved',\n      value: 'Approved'\n    }, {\n      text: 'Pacifica Verification',\n      value: 'Pacifica Verification'\n    }, {\n      text: 'Dis-Approved',\n      value: 'Dis-Approved'\n    }, {\n      text: 'Pending',\n      value: 'Pending'\n    }, {\n      text: 'Not Required',\n      value: 'Not Required'\n    }, {\n      text: 'In Review',\n      value: 'In Review'\n    }, {\n      text: '1 Cycle Completed',\n      value: '1 Cycle Completed'\n    }\n    // 'Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed'\n    ]\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // Column visibility system\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration\n  gridColumns = [];\n  defaultColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Custom reorderable configuration that prevents fixed column reordering\n  customReorderableConfig = false;\n  // Enhanced Columns with Kendo UI features - adapted for permits\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Action',\n    width: 100,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'permitName',\n    title: 'Permit/Sub Project Name',\n    width: 180,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  }, {\n    field: 'permitNumber',\n    title: 'Permit #',\n    width: 180,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 3\n  }, {\n    field: 'projectName',\n    title: 'Project Name',\n    width: 180,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'permitCategory',\n    title: 'Category',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'permitType',\n    title: 'Permit Type',\n    width: 200,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'permitStatus',\n    title: 'Permit Status',\n    width: 150,\n    isFixed: false,\n    type: 'status',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'internalReviewStatus',\n    title: 'Internal Review Status',\n    width: 150,\n    isFixed: false,\n    type: 'status',\n    filterable: true,\n    order: 8\n  }, {\n    field: 'permitReviewType',\n    title: 'Type',\n    width: 150,\n    isFixed: false,\n    type: 'status',\n    filterable: true,\n    order: 9\n  }, {\n    field: 'location',\n    title: 'Location',\n    width: 200,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 10\n  }, {\n    field: 'permitAppliedDate',\n    title: 'Applied Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 11\n  }, {\n    field: 'permitExpirationDate',\n    title: 'Expiration Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 12\n  }, {\n    field: 'permitFinalDate',\n    title: 'Final Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 13\n  }, {\n    field: 'permitCompleteDate',\n    title: 'Complete Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 14\n  }, {\n    field: 'attentionReason',\n    title: 'Attention Reason',\n    width: 180,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 15\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 130,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 16\n  }];\n  statusList = [{\n    text: 'Approved',\n    value: 'Approved'\n  }, {\n    text: 'Pending',\n    value: 'Pending'\n  }, {\n    text: 'Rejected',\n    value: 'Rejected'\n  }];\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  page = {\n    size: 15,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Selection\n  selectedRows = [];\n  isAllSelected = false;\n  // Export options\n  exportOptions = [{\n    text: 'All',\n    value: 'all'\n  }, {\n    text: 'Page Results',\n    value: 'selected'\n  }];\n  // Custom dropdown state\n  isExcelDropdownOpen = false;\n  dropdownTop = 0;\n  dropdownLeft = 0;\n  columnJSONFormat;\n  permitId;\n  singlePermit;\n  resetToDefaultSettings() {\n    console.log('Resetting to default settings...');\n    // Reset column visibility - show all columns\n    this.hiddenFields = [];\n    this.gridColumns = [...this.defaultColumns];\n    this.kendoColOrder = [...this.defaultColumns];\n    // Reset sort state to default\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    // Reset page state\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    // Reset all filters - clear everything\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    // Reset advanced filters\n    this.appliedFilters = {};\n    // Reset search\n    this.searchData = '';\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n    console.log('Reset completed:', {\n      hiddenFields: this.hiddenFields,\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      sort: this.sort,\n      filter: this.filter,\n      searchData: this.searchData\n    });\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset column visibility - show all columns\n      this.grid.columns.forEach(column => {\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Trigger change detection\n    this.cdr.detectChanges();\n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        // Also try to reset the grid state completely\n        this.grid.reset();\n      }, 100);\n    }\n    // Reload data with clean state\n    this.loadTable();\n  }\n  // private saveColumnState(): void {\n  //   try {\n  //     const columnState = {\n  //       columns: this.gridColumns,\n  //       hidden: this.hiddenFields,\n  //       order: this.kendoColOrder,\n  //     };\n  //     localStorage.setItem(\n  //       `${this.GRID_STATE_KEY}-columns`,\n  //       JSON.stringify(columnState)\n  //     );\n  //   } catch (error) {\n  //     console.warn('Error saving column state:', error);\n  //   }\n  // }\n  constructor(router, route, permitsService, httpUtilService, customLayoutUtilsService, kendoColumnService, modalService, cdr, appService, ExceljsService, pageInfo) {\n    this.router = router;\n    this.route = route;\n    this.permitsService = permitsService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.kendoColumnService = kendoColumnService;\n    this.modalService = modalService;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.ExceljsService = ExceljsService;\n    this.pageInfo = pageInfo;\n    // Initialize search subscription\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      this.loadTable();\n    });\n  }\n  ngOnInit() {\n    this.pageInfo.updateTitle('Permits');\n    this.initializeComponent();\n    this.loadTable();\n  }\n  ngAfterViewInit() {\n    this.initializeGrid();\n  }\n  ngOnDestroy() {\n    if (this.searchSubscription) {\n      this.searchTerms.complete();\n    }\n  }\n  initializeComponent() {\n    // Get login user info\n    this.loginUser = this.appService.getLoggedInUser();\n    // Initialize column visibility system\n    this.initializeColumnVisibility();\n  }\n  initializeColumnVisibility() {\n    // Set up column arrays first\n    this.setupColumnArrays();\n    // Try to load from local storage first\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('permits', this.loginUser.userId);\n    if (savedConfig) {\n      // Load saved settings from local storage\n      this.kendoHide = savedConfig.hiddenData || [];\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.kendoColOrder];\n    } else {\n      // Initialize with default values\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.defaultColumns];\n    }\n    // Apply settings\n    this.applySavedColumnSettings();\n  }\n  loadColumnSettingsFromServer() {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.getHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false && response.Data) {\n          // Parse the saved settings\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.kendoColOrder];\n          // Apply the settings\n          this.applySavedColumnSettings();\n          console.log('Column settings loaded from server:', {\n            kendoHide: this.kendoHide,\n            kendoColOrder: this.kendoColOrder\n          });\n        } else {\n          // No saved settings, use defaults\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      },\n      error: error => {\n        console.error('Error loading column settings:', error);\n        // Use defaults on error\n        this.kendoHide = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n        this.applySavedColumnSettings();\n      }\n    });\n  }\n  setupColumnArrays() {\n    this.gridColumns = this.gridColumnConfig.map(col => col.field);\n    this.defaultColumns = [...this.gridColumns];\n  }\n  initializeGrid() {\n    if (this.grid) {\n      // Apply saved column settings\n      this.applySavedColumnSettings();\n    }\n  }\n  applySavedColumnSettings() {\n    if (this.kendoHide && this.kendoHide.length > 0) {\n      this.hiddenFields = this.kendoHide;\n    }\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n      // Apply column order\n      this.gridColumnConfig.sort((a, b) => {\n        const aOrder = this.kendoColOrder.indexOf(a.field);\n        const bOrder = this.kendoColOrder.indexOf(b.field);\n        return aOrder - bOrder;\n      });\n    }\n  }\n  // Load table data\n  loadTable() {\n    this.loadTableWithKendoEndpoint();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Safety timeout to prevent loader from getting stuck\n    const loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached, resetting loading states');\n      this.resetLoadingStates();\n    }, 30000); // 30 seconds timeout\n    // Prepare state object for Kendo UI endpoint\n    // When sort is empty (3rd click), send default sort to backend\n    const sortForBackend = this.sort.length > 0 ? this.sort : [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: sortForBackend,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId\n    };\n    console.log('Loading table with state:', state);\n    this.permitsService.getPermitsForKendoGrid(state).subscribe({\n      next: data => {\n        // Clear the safety timeout since we got a response\n        clearTimeout(loadingTimeout);\n        console.log('API Response:', data);\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          // Check if this is an authentication error\n          if (data.responseData?.status === 401 || data.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n          this.handleEmptyResponse();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const permitData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = permitData.length !== 0;\n          this.serverSideRowData = permitData;\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: permitData,\n            total: total\n          };\n          console.log('this.serverSideRowData ', this.serverSideRowData);\n          console.log('this.gridData ', this.gridData);\n          console.log('this.IsListHasValue ', this.IsListHasValue);\n          console.log('this.page ', this.page);\n          console.log('Total elements set to:', this.page.totalElements);\n          console.log('Total pages calculated:', this.page.totalPages);\n          console.log('Current skip:', this.skip, 'Page size:', this.page.size);\n          console.log('Expected items range:', this.skip + 1, '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n          // Debug grid state after data load\n          setTimeout(() => {\n            if (this.grid) {\n              console.log('Grid total:', this.grid.total);\n              console.log('Grid pageSize:', this.grid.pageSize);\n              console.log('Grid skip:', this.grid.skip);\n              console.log('Grid pageIndex:', this.grid.pageIndex);\n              console.log('Grid data length:', this.grid.data ? this.grid.data.length : 'no data');\n              // Force grid to update its total and maintain pagination state\n              this.grid.total = this.page.totalElements;\n              this.grid.skip = this.skip;\n              this.grid.pageIndex = this.page.pageNumber;\n              console.log('Forced grid total to:', this.grid.total, 'skip to:', this.skip, 'pageIndex to:', this.page.pageNumber);\n            }\n          }, 100);\n          this.cdr.markForCheck();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      },\n      error: error => {\n        // Clear the safety timeout since we got an error\n        clearTimeout(loadingTimeout);\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        // Check if this is an authentication error\n        if (error && typeof error === 'object' && 'status' in error) {\n          const httpError = error;\n          if (httpError.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n        }\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        // Clear the safety timeout\n        clearTimeout(loadingTimeout);\n        // Ensure loading states are reset in complete block as well\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      }\n    });\n  }\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    // Ensure loading states are reset when handling empty response\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Method to manually reset loading states if they get stuck\n  resetLoadingStates() {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  // Public method to manually refresh the grid and reset any stuck loading states\n  refreshGrid() {\n    console.log('Manually refreshing grid...');\n    this.resetLoadingStates();\n    this.loadTable();\n  }\n  // Search functionality\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      this.loadTable();\n    }\n  }\n  onSearchChange() {\n    console.log('Search changed:', this.searchData);\n    // Trigger search with debounce\n    this.searchTerms.next(this.searchData || '');\n  }\n  applySearch() {\n    this.loadTable();\n  }\n  clearSearch() {\n    this.searchTerms.next(this.searchData);\n  }\n  // Filter functionality\n  filterChange(filter) {\n    console.log('filter', filter);\n    this.filter = filter;\n    this.loadTable();\n  }\n  applyAdvancedFilters() {\n    console.log('yes it came here');\n    // Apply status filter\n    if (this.appliedFilters.status) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'permitStatus';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    // Apply category filter\n    if (this.appliedFilters.category) {\n      this.filter.filters = this.filter.filters.filter(f => {\n        if ('field' in f) {\n          return f.field !== 'permitCategory';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitCategory',\n        operator: 'eq',\n        value: this.appliedFilters.category\n      });\n    }\n    this.loadTable();\n  }\n  clearAdvancedFilters() {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    this.loadTable();\n  }\n  // Sorting functionality\n  onSortChange(sort) {\n    // Check if this is the 3rd click (dir is undefined)\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n    if (isThirdClick) {\n      // 3rd click - clear sort and use default\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n      // Valid sort with direction\n      this.sort = sort;\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = sort[0].dir;\n    } else {\n      // Empty sort array or invalid sort\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.loadTable();\n  }\n  // Pagination functionality\n  pageChange(event) {\n    console.log('Page change event:', event);\n    console.log('Current page size:', this.page.size);\n    console.log('Event page size:', event.pageSize);\n    console.log('Event page index:', event.pageIndex);\n    console.log('Event skip:', event.skip);\n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n    console.log('Updated skip:', this.skip, 'page size:', this.page.size, 'page number:', this.page.pageNumber);\n    console.log('Expected items range:', this.skip + 1, '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n    this.loadTable();\n  }\n  // Handle page size change specifically\n  onPageSizeChange(event) {\n    console.log('Page size change event:', event);\n    console.log('New page size:', event.pageSize);\n    if (event.pageSize && event.pageSize !== this.page.size) {\n      console.log('Page size changing from', this.page.size, 'to', event.pageSize);\n      this.page.size = event.pageSize;\n      this.page.pageNumber = 0; // Reset to first page when changing page size\n      this.skip = 0;\n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total to:', this.grid.total);\n      }\n      console.log('Updated page size:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n  // Handle data state changes (includes page size changes)\n  onDataStateChange(event) {\n    console.log('Data state change event:', event);\n    // Check if page size changed\n    if (event.take && event.take !== this.page.size) {\n      console.log('Page size changing via data state from', this.page.size, 'to', event.take);\n      this.page.size = event.take;\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages via data state:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total via data state to:', this.grid.total);\n      }\n      console.log('Updated page size via data state:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n  updateColumnVisibility(event) {\n    // Handle column visibility changes\n    const hiddenColumns = event.hiddenColumns || [];\n    this.hiddenFields = hiddenColumns;\n  }\n  // Selection functionality\n  onSelectionChange(event) {\n    this.selectedRows = event.selectedRows || [];\n    this.isAllSelected = this.selectedRows.length === this.serverSideRowData.length;\n  }\n  selectAll() {\n    if (this.isAllSelected) {\n      this.selectedRows = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedRows = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n  }\n  // Grid expansion\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Export functionality\n  onExportClick(event) {\n    const selectedOption = event.value; // Get selected option\n    let prdItems = [];\n    if (selectedOption === 'selected') {\n      prdItems = this.serverSideRowData;\n      // declare the title and header data for excel\n      // get the data for excel in a array format\n      this.exportExcel(prdItems);\n    } else if (selectedOption === 'all') {\n      const queryparamsExcel = {\n        pageSize: this.page.totalElements,\n        sortOrder: this.page.orderDir,\n        sortField: this.page.orderBy,\n        pageNumber: this.page.pageNumber\n        // filter: this.filterConfiguration()\n      };\n      // Enable loading indicator\n      this.httpUtilService.loadingSubject.next(true);\n      // API call\n      this.permitsService.getAllPermits(queryparamsExcel)\n      // .pipe(map((data: any) => data as any))\n      .subscribe(data => {\n        // Disable loading indicator\n        this.httpUtilService.loadingSubject.next(false);\n        if (data.isFault) {\n          this.IsListHasValue = false;\n          this.cdr.markForCheck();\n          return; // Exit early if the response has a fault\n        }\n        this.IsListHasValue = true;\n        prdItems = data.responseData.data || [];\n        this.cdr.detectChanges(); // Manually trigger UI update\n        this.exportExcel(prdItems);\n      });\n    }\n  }\n  exportExcel(listOfItems) {\n    // Define local variables for the items and current date\n    let prdItems = listOfItems;\n    let currentDate = this.appService.formatMonthDate(new Date());\n    console.log('prdItems', prdItems);\n    // Check if the data exists and is not empty\n    if (prdItems !== undefined && prdItems.length > 0) {\n      // Define the title for the Excel file\n      const tableTitle = 'Events';\n      // Filter out hidden columns and sort by order\n      // const visibleColumns = this.columnJSONFormat\n      //   .filter((col: any) => !col.hidden)\n      //   .sort((a: any, b: any) => a.order - b.order);\n      // Create header from visible columns\n      const headerArray = ['Permit Number', 'Permit Type', 'Category', 'Status', 'Location', 'Project Name', 'Applied Date', 'Expiration Date', 'Attention Reason'];\n      // ...visibleColumns.map((col: any) => col.title),\n      // Define which columns should have currency and percentage formatting\n      // const currencyColumns: any = [\n      //   'Pending',\n      //   'ACAT',\n      //   'Annuity',\n      //   'AUM',\n      //   'Total Assets',\n      //   'Event Cost',\n      //   'Gross Profit',\n      // ].filter((col) => headerArray.includes(col));\n      const percentageColumns = [];\n      // Get the data for excel in an array format\n      const respResult = [];\n      // Prepare the data for export based on visible columns\n      each(prdItems, prdItem => {\n        // Create an array with the same length as headerArray\n        const respData = Array(headerArray.length).fill(null);\n        respData[0] = prdItem.eventDescription;\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n        // Fill in data for each visible column\n        headerArray.forEach((col, i) => {\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n          switch (col) {\n            case 'Permit Number':\n              respData[adjustedIndex] = prdItem.permitNumber;\n              break;\n            case 'Permit Type':\n              respData[adjustedIndex] = prdItem.permitType;\n              break;\n            case 'Category':\n              respData[adjustedIndex] = prdItem.permitCategory;\n              break;\n            case 'Status':\n              respData[adjustedIndex] = prdItem.permitStatus;\n              break;\n            case 'Location':\n              respData[adjustedIndex] = prdItem.location;\n              break;\n            case 'Project Name':\n              respData[adjustedIndex] = prdItem.projectName;\n              break;\n            case 'Applied Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitAppliedDate);\n              break;\n            case 'Expiration Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitExpirationDate);\n              break;\n            case 'Attention Reason':\n              respData[adjustedIndex] = prdItem.attentionReason;\n              break;\n            // case 'kept_appointments':\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\n            //   break;\n            // case 'kept_appt_ratio':\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n            //   break;\n            // case 'apptKeptNo':\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\n            //   break;\n            // case 'has_assets':\n            //   respData[adjustedIndex] = prdItem.has_assets;\n            //   break;\n            // case 'prospects_closed':\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\n            //   break;\n            // case 'closing_ratio':\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\n            //   break;\n            // case 'totalPending':\n            //   respData[adjustedIndex] = prdItem.totalPending;\n            //   break;\n            // case 'acatproduction':\n            //   respData[adjustedIndex] = prdItem.acatproduction;\n            //   break;\n            // case 'annuityproduction':\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\n            //   break;\n            // case 'aumproduction':\n            //   respData[adjustedIndex] = prdItem.aumproduction;\n            //   break;\n            // case 'totalAssets':\n            //   respData[adjustedIndex] = prdItem.totalAssets;\n            //   break;\n            // case 'eventCost':\n            //   respData[adjustedIndex] = prdItem.eventCost;\n            //   break;\n            // case 'grossProfit':\n            //   respData[adjustedIndex] = prdItem.grossProfit;\n            //   break;\n            // case 'status':\n            //   respData[adjustedIndex] = prdItem.status;\n            //   break;\n          }\n        });\n        respResult.push(respData);\n      });\n      // Define column sizes for the Excel file\n      const colSize = headerArray.map((header, index) => ({\n        id: index + 1,\n        width: 20\n      }));\n      // Generate the Excel file using the exceljsService\n      this.ExceljsService.generateExcel(tableTitle, headerArray, respResult, colSize\n      // currencyColumns,\n      // percentageColumns\n      );\n    } else {\n      const message = 'There are no records available to export.';\n      // this.layoutUtilService.showError(message, '');\n    }\n  }\n  // public onExportClick(event: any): void {\n  //   const exportType = event.item.value;\n  //   let selectedIds: number[] = [];\n  //   switch (exportType) {\n  //     case 'selected':\n  //       selectedIds = this.selectedRows.map((row) => row.permitId);\n  //       if (selectedIds.length === 0) {\n  //         //alert('Please select permits to export');\n  //         return;\n  //       }\n  //       break;\n  //     case 'filtered':\n  //       // Export filtered data\n  //       break;\n  //     case 'all':\n  //     default:\n  //       // Export all data\n  //       break;\n  //   }\n  //   this.exportPermits(exportType, selectedIds);\n  // }\n  // private exportPermits(exportType: string, selectedIds: number[]): void {\n  //   this.permitsService.exportPermits(exportType, selectedIds).subscribe({\n  //     next: (response: any) => {\n  //       if (response.data) {\n  //         const blob = new Blob([response.data], {\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  //         });\n  //         saveAs(\n  //           blob,\n  //           `permits_${exportType}_${\n  //             new Date().toISOString().split('T')[0]\n  //           }.xlsx`\n  //         );\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.error('Export error:', error);\n  //       //alert('Error exporting permits data');\n  //     },\n  //   });\n  // }\n  // Column settings management\n  saveHead() {\n    const settings = {\n      kendoHide: this.hiddenFields,\n      kendoColOrder: this.kendoColOrder,\n      kendoInitColOrder: this.kendoInitColOrder\n    };\n    // Save to local storage only\n    this.kendoColumnService.saveToLocalStorage({\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    });\n    console.log('Column settings saved locally:', settings);\n    this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n    //alert('Column settings saved locally');\n  }\n  saveColumnSettingsToServer(settings) {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    };\n    this.kendoColumnService.createHideFields(config).subscribe({\n      next: response => {\n        if (response.isFault === false) {\n          console.log('Column settings saved successfully:', response);\n          this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n          //alert('Column settings saved successfully');\n        } else {\n          console.error('Failed to save column settings:', response.message);\n          this.customLayoutUtilsService.showError(response.message, '');\n          //alert('Failed to save column settings: ' + response.message);\n        }\n      },\n      error: error => {\n        console.error('Error saving column settings:', error);\n        this.customLayoutUtilsService.showError('Error saving column setting', '');\n        //alert('Error saving column settings. Please try again.');\n      }\n    });\n  }\n  saveResetToServer() {\n    // First delete existing settings\n    const deleteConfig = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n      next: response => {\n        console.log('Existing settings deleted:', response);\n        // Then save the reset state (all columns visible)\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      },\n      error: error => {\n        console.error('Error deleting existing settings:', error);\n        // Still try to save the reset state\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      }\n    });\n  }\n  resetTable() {\n    console.log('Resetting Kendo settings for permits');\n    // Clear all saved settings first\n    this.kendoHide = [];\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.kendoInitColOrder = [];\n    // Clear local storage\n    this.kendoColumnService.clearFromLocalStorage('permits');\n    // Reset to default settings\n    this.resetToDefaultSettings();\n    // Trigger change detection to update the template\n    this.cdr.detectChanges();\n    // Force grid refresh to show all columns\n    if (this.grid) {\n      this.grid.refresh();\n    }\n    // Show success message\n    console.log('Table reset to default settings');\n    //alert('Table reset to default settings - all columns restored');\n  }\n  // Navigation\n  add() {\n    // this.router.navigate(['/permits/add']);\n    this.edit(0);\n  }\n  view(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'permit-list'\n      }\n    });\n  }\n  edit(permitId) {\n    if (permitId == 0) {\n      const permit = this.serverSideRowData.find(p => p.permitId === permitId);\n      const NgbModalOptions = {\n        size: 'lg',\n        // Large modal size\n        backdrop: 'static',\n        // Prevents closing when clicking outside\n        keyboard: false,\n        // Disables closing with the Escape key\n        scrollable: true // Allows scrolling inside the modal\n      };\n      // Trigger global loader BEFORE opening modal to ensure full-page overlay\n      this.httpUtilService.loadingSubject.next(true);\n      // Open the modal and load the ProjectPopup\n      const modalRef = this.modalService.open(PermitPopupComponent, NgbModalOptions);\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = permitId;\n      modalRef.componentInstance.permit = permit;\n      // Subscribe to the modal event when data is updated\n      modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n        if (receivedEntry === true) {\n          // Reload the table data after a successful update\n          this.loadTable();\n        }\n      });\n    } else {\n      this.router.navigate(['/permits/view', permitId], {\n        queryParams: {\n          from: 'permit-list'\n        }\n      });\n    }\n  }\n  deletePop(content) {\n    this.modalService.open(content, {\n      centered: true\n    });\n  }\n  confirmDelete() {\n    console.log('Item deleted ✅');\n    // your delete logic here\n  }\n  delete(permitId) {\n    if (confirm('Are you sure you want to delete this permit?')) {\n      this.permitsService.deletePermit({\n        permitId\n      }).subscribe({\n        next: response => {\n          if (response.message) {\n            //alert('Permit deleted successfully');\n            this.customLayoutUtilsService.showSuccess('Permit deleted successfully', '');\n            this.loadTable();\n          }\n        },\n        error: error => {\n          console.error('Delete error:', error);\n          this.customLayoutUtilsService.showError('Error deleting permit', '');\n          //alert('Error deleting permit');\n        }\n      });\n    }\n  }\n  // Utility methods\n  formatDate(dateString) {\n    if (!dateString) return '';\n    return new Date(dateString).toLocaleDateString();\n  }\n  getStatusClass(status) {\n    switch (status) {\n      case 'Approved':\n        return 'badge-light-success';\n      case 'Requires Resubmit':\n        return 'badge-light-warning';\n      case 'On Hold':\n        return 'badge-light-danger';\n      case 'Pending':\n        return 'badge-light-info';\n      default:\n        return 'badge-light-secondary';\n    }\n  }\n  getCategoryClass(category) {\n    return category === 'Primary' ? 'badge-light-primary' : 'badge-light-secondary';\n  }\n  syncPermits(i) {\n    this.isLoading = true;\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({\n      municipalityId: 1,\n      singlePermit: this.singlePermit,\n      autoLogin: true\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Sync response:', res);\n        // Handle wrapped response structure from interceptor\n        const responseData = res?.responseData || res;\n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n          this.customLayoutUtilsService.showError(responseData.faultMessage || 'Failed to sync permit', '');\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          } else if (responseData.message === 'No permits found for any keywords') {\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          } else {\n            this.customLayoutUtilsService.showError(responseData.message || 'Failed to sync permit', '');\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else {\n          this.customLayoutUtilsService.showSuccess('✅ Permit synced successfully', '');\n          //alert('✅ Permit synced successfully');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.error.message}`)\n            // ;\n          }\n        } else if (err?.status === 404) {\n          this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n          this.customLayoutUtilsService.showError('❌ Error syncing permit', '');\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  onTabActivated() {\n    // This method is called when the tab is activated\n    // You can add any specific logic here if needed\n    console.log('Permits tab activated');\n  }\n  // Custom dropdown methods\n  toggleExcelDropdown(event) {\n    this.isExcelDropdownOpen = !this.isExcelDropdownOpen;\n    console.log('Excel dropdown toggled:', this.isExcelDropdownOpen);\n    if (this.isExcelDropdownOpen && event) {\n      const button = event.target;\n      const rect = button.getBoundingClientRect();\n      this.dropdownTop = rect.bottom + window.scrollY;\n      this.dropdownLeft = rect.left + window.scrollX;\n      console.log('Dropdown position:', this.dropdownTop, this.dropdownLeft);\n    }\n  }\n  closeExcelDropdown() {\n    this.isExcelDropdownOpen = false;\n    console.log('Excel dropdown closed');\n  }\n  onDocumentClick(event) {\n    const target = event.target;\n    const dropdown = target.closest('.custom-dropdown');\n    if (!dropdown && this.isExcelDropdownOpen) {\n      this.closeExcelDropdown();\n    }\n  }\n  getHiddenField(fieldName) {\n    return this.hiddenFields.includes(fieldName);\n  }\n  static ɵfac = function PermitListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.PermitsService), i0.ɵɵdirectiveInject(i3.HttpUtilsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i5.KendoColumnService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.AppService), i0.ɵɵdirectiveInject(i8.ExceljsService), i0.ɵɵdirectiveInject(i9.PageInfoService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitListComponent,\n    selectors: [[\"app-permit-list\"]],\n    viewQuery: function PermitListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    hostBindings: function PermitListComponent_HostBindings(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵlistener(\"click\", function PermitListComponent_click_HostBindingHandler($event) {\n          return ctx.onDocumentClick($event);\n        }, false, i0.ɵɵresolveDocument);\n      }\n    },\n    decls: 9,\n    vars: 24,\n    consts: [[\"normalGrid\", \"\"], [\"excelDropdown\", \"\"], [\"excelButton\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"selectionChange\", \"filterChange\", \"pageChange\", \"pageSizeChange\", \"dataStateChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [1, \"custom-dropdown\", \"me-2\"], [\"type\", \"button\", \"title\", \"Export Excel\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-file-excel\", \"text-success\"], [\"class\", \"custom-dropdown-menu\", 3, \"top\", \"left\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"custom-dropdown-menu\"], [\"href\", \"#\", 1, \"custom-dropdown-item\", 3, \"click\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Category\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-6\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", \"themeColor\", \"primary\", 1, \"me-2\", 3, \"click\"], [\"kendoButton\", \"\", \"themeColor\", \"secondary\", 1, \"me-2\", 3, \"click\"], [\"kendoButton\", \"\", \"themeColor\", \"light\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Toggle Advanced Filters\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\", \"includeInChooser\", \"columnMenu\", \"hidden\", 4, \"ngIf\"], [\"field\", \"permitNumber\", \"title\", \"Permit #\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"permitName\", \"title\", \"Permit/Sub Project Name\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"permitType\", \"title\", \"Permit Type\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"permitCategory\", \"title\", \"Category\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"internalReviewStatus\", \"title\", \"Internal Review Status\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitReviewType\", \"title\", \"Type\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitStatus\", \"title\", \"Permit Status\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"location\", \"title\", \"Location\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitAppliedDate\", \"title\", \"Applied Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitExpirationDate\", \"title\", \"Expiration Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitFinalDate\", \"title\", \"Final Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitCompleteDate\", \"title\", \"Complete Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"attentionReason\", \"title\", \"Attention Reason\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [1, \"d-flex\", \"gap-1\"], [\"title\", \"View\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-pencil\", 2, \"color\", \"var(--bs-primary, #0d6efd)\", \"font-size\", \"1rem\"], [\"field\", \"permitNumber\", \"title\", \"Permit #\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bold\"], [\"operator\", \"contains\", 3, \"column\", \"filter\", \"extra\"], [\"field\", \"permitName\", \"title\", \"Permit/Sub Project Name\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"field\", \"permitType\", \"title\", \"Permit Type\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"permitCategory\", \"title\", \"Category\", 3, \"width\"], [1, \"badge\", 3, \"ngClass\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"ngModelChange\", \"valueChange\", \"column\", \"filter\", \"data\", \"ngModel\", \"valuePrimitive\", \"defaultItem\"], [\"field\", \"internalReviewStatus\", \"title\", \"Internal Review Status\", 3, \"width\"], [\"field\", \"permitReviewType\", \"title\", \"Type\", 3, \"width\"], [\"field\", \"permitStatus\", \"title\", \"Permit Status\", 3, \"width\"], [\"field\", \"location\", \"title\", \"Location\", 3, \"width\"], [\"field\", \"permitAppliedDate\", \"title\", \"Applied Date\", 3, \"width\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"field\", \"permitExpirationDate\", \"title\", \"Expiration Date\", 3, \"width\"], [\"field\", \"permitFinalDate\", \"title\", \"Final Date\", 3, \"width\"], [\"field\", \"permitCompleteDate\", \"title\", \"Complete Date\", 3, \"width\"], [\"field\", \"attentionReason\", \"title\", \"Attention Reason\", 3, \"width\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-inbox\", \"fa-3x\", \"mb-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"]],\n    template: function PermitListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, PermitListComponent_div_0_Template, 7, 0, \"div\", 3);\n        i0.ɵɵelementStart(1, \"div\", 4)(2, \"kendo-grid\", 5, 0);\n        i0.ɵɵlistener(\"selectionChange\", function PermitListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function PermitListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function PermitListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"pageSizeChange\", function PermitListComponent_Template_kendo_grid_pageSizeChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onPageSizeChange($event));\n        })(\"dataStateChange\", function PermitListComponent_Template_kendo_grid_dataStateChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onDataStateChange($event));\n        })(\"sortChange\", function PermitListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function PermitListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, PermitListComponent_ng_template_4_Template, 23, 13, \"ng-template\", 6)(5, PermitListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 6)(6, PermitListComponent_ng_template_6_Template, 3, 1, \"ng-template\", 6)(7, PermitListComponent_ng_container_7_Template, 17, 16, \"ng-container\", 7);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(8, PermitListComponent_div_8_Template, 9, 0, \"div\", 8);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(19, _c2, i0.ɵɵpureFunction0(18, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", i0.ɵɵpureFunction0(21, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(22, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(23, _c5))(\"loading\", false);\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", !ctx.IsListHasValue);\n      }\n    },\n    dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i11.NgControlStatus, i11.NgModel, i12.GridComponent, i12.ToolbarTemplateDirective, i12.GridSpacerComponent, i12.ColumnComponent, i12.CellTemplateDirective, i12.ContainsFilterOperatorComponent, i12.EqualFilterOperatorComponent, i12.NotEqualFilterOperatorComponent, i12.AfterFilterOperatorComponent, i12.AfterEqFilterOperatorComponent, i12.BeforeEqFilterOperatorComponent, i12.BeforeFilterOperatorComponent, i12.StringFilterMenuComponent, i12.FilterMenuTemplateDirective, i12.DateFilterMenuComponent, i13.TextBoxComponent, i14.DropDownListComponent, i15.ButtonComponent, i16.InlineSVGDirective],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  z-index: 9999;\\n  background: white;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #afc7dd;\\n  box-shadow: 0 0 6px rgba(59, 83, 135, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  border-radius: 8px;\\n  margin-bottom: 1rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #495057;\\n  margin-bottom: 0.5rem;\\n}\\n.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 6px;\\n}\\n\\n.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem !important;\\n  font-weight: 500 !important;\\n  font-size: 0.75rem !important;\\n  padding: 0.25rem 0.5rem !important;\\n  background-color: #6c757d !important;\\n  border-color: #6c757d !important;\\n  color: #fff !important;\\n  margin-right: 0.5rem !important;\\n  transition: all 0.2s ease !important;\\n  height: auto !important;\\n  min-height: 31px !important;\\n}\\n.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #5a6268 !important;\\n  border-color: #545b62 !important;\\n  transform: translateY(-1px) !important;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;\\n}\\n.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;\\n}\\n\\n[_nghost-%COMP%]     .k-grid {\\n  border-radius: 8px;\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header {\\n  background: #f8f9fa;\\n  border-bottom: 2px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header {\\n  background: #f8f9fa;\\n  border-color: #dee2e6;\\n  font-weight: 600;\\n  color: #495057;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar {\\n  background: #f8f9fa;\\n  border-bottom: 1px solid #dee2e6;\\n  padding: 1rem;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary {\\n  background: #007bff;\\n  border-color: #007bff;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary:hover {\\n  background: #0056b3;\\n  border-color: #0056b3;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt {\\n  background: #f8f9fa;\\n}\\n[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager {\\n  background: #f8f9fa;\\n  border-top: 1px solid #dee2e6;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-info {\\n  color: #6c757d;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link {\\n  border-radius: 4px;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link:hover {\\n  background: #e9ecef;\\n}\\n[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected {\\n  background: #007bff;\\n  color: white;\\n}\\n[_nghost-%COMP%]     .custom-dropdown .k-button {\\n  border-radius: 6px;\\n  font-weight: 500;\\n}\\n[_nghost-%COMP%]     .k-textbox {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-textbox:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n[_nghost-%COMP%]     .k-dropdownlist {\\n  border-radius: 6px;\\n}\\n[_nghost-%COMP%]     .k-dropdownlist:focus {\\n  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\\n}\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 0.5em 0.75em;\\n  font-size: 0.75em;\\n  font-weight: 600;\\n  border-radius: 6px;\\n}\\n.badge.badge-light-success[_ngcontent-%COMP%] {\\n  background: #d4edda;\\n  color: #155724;\\n}\\n.badge.badge-light-warning[_ngcontent-%COMP%] {\\n  background: #fff3cd;\\n  color: #856404;\\n}\\n.badge.badge-light-danger[_ngcontent-%COMP%] {\\n  background: #f8d7da;\\n  color: #721c24;\\n}\\n.badge.badge-light-info[_ngcontent-%COMP%] {\\n  background: #d1ecf1;\\n  color: #0c5460;\\n}\\n.badge.badge-light-secondary[_ngcontent-%COMP%] {\\n  background: #e2e3e5;\\n  color: #383d41;\\n}\\n.badge.badge-light-primary[_ngcontent-%COMP%] {\\n  background: #cce7ff;\\n  color: #004085;\\n}\\n\\n.btn[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n  font-weight: 500;\\n  transition: all 0.2s ease;\\n}\\n.btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.btn.btn-success[_ngcontent-%COMP%] {\\n  background: #28a745;\\n  border-color: #28a745;\\n}\\n.btn.btn-success[_ngcontent-%COMP%]:hover {\\n  background: #1e7e34;\\n  border-color: #1e7e34;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%] {\\n  background: #ffc107;\\n  border-color: #ffc107;\\n  color: #212529;\\n}\\n.btn.btn-warning[_ngcontent-%COMP%]:hover {\\n  background: #e0a800;\\n  border-color: #d39e00;\\n}\\n.btn.btn-info[_ngcontent-%COMP%] {\\n  background: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n.btn.btn-info[_ngcontent-%COMP%]:hover {\\n  background: #138496;\\n  border-color: #138496;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  border-color: #6c757d;\\n}\\n.btn.btn-outline-secondary[_ngcontent-%COMP%]:hover {\\n  background: #6c757d;\\n  color: white;\\n}\\n\\n.btn-icon[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  padding: 0;\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  border-radius: 6px;\\n  transition: all 0.2s ease;\\n}\\n.btn-icon[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n}\\n\\n.text-muted[_ngcontent-%COMP%]   .fas[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n}\\n.text-muted[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  color: #495057;\\n  margin-top: 1rem;\\n}\\n.text-muted[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c757d;\\n  margin-bottom: 1.5rem;\\n}\\n\\n@media (max-width: 768px) {\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%] {\\n    width: 100%;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n    width: 100% !important;\\n  }\\n  .grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    justify-content: center;\\n  }\\n  .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], \\n   .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%] {\\n    margin-bottom: 1rem;\\n  }\\n}\\n.grid-container[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n.grid-container.fullscreen-grid[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_expandGrid 0.3s ease-out;\\n}\\n\\n@keyframes _ngcontent-%COMP%_expandGrid {\\n  from {\\n    opacity: 0.8;\\n    transform: scale(0.95);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n}\\n\\n\\n[_nghost-%COMP%]     .dropdown {\\n  z-index: 999999 !important;\\n  position: relative !important;\\n}\\n\\n[_nghost-%COMP%]     .excel-dropdown-menu {\\n  z-index: 999999 !important;\\n  position: absolute !important;\\n}\\n\\n\\n\\n[_nghost-%COMP%]     .k-grid, \\n[_nghost-%COMP%]     .k-grid-header, \\n[_nghost-%COMP%]     .k-grid-header-wrap {\\n  z-index: 1 !important;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["each", "Subject", "debounceTime", "distinctUntilChanged", "PermitPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PermitListComponent_ng_template_4_div_18_Template_a_click_1_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r2", "ɵɵnextContext", "onExportClick", "value", "closeExcelDropdown", "ɵɵresetView", "preventDefault", "PermitListComponent_ng_template_4_div_18_Template_a_click_3_listener", "ɵɵstyleProp", "dropdownTop", "dropdownLeft", "ɵɵtwoWayListener", "PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "_r2", "ɵɵtwoWayBindingSet", "searchData", "PermitListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "ɵɵelement", "PermitListComponent_ng_template_4_Template_button_click_8_listener", "add", "PermitListComponent_ng_template_4_Template_button_click_11_listener", "toggleExpand", "PermitListComponent_ng_template_4_Template_button_click_15_listener", "toggleExcelDropdown", "ɵɵtemplate", "PermitListComponent_ng_template_4_div_18_Template", "PermitListComponent_ng_template_4_Template_button_click_19_listener", "resetTable", "PermitListComponent_ng_template_4_Template_button_click_21_listener", "refreshGrid", "ɵɵadvance", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "isExcelDropdownOpen", "PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r5", "appliedFilters", "status", "PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener", "category", "PermitListComponent_ng_template_5_div_0_Template_button_click_11_listener", "applyAdvancedFilters", "PermitListComponent_ng_template_5_div_0_Template_button_click_13_listener", "clearAdvancedFilters", "PermitListComponent_ng_template_5_div_0_Template_button_click_15_listener", "showAdvancedFilters", "advancedFilterOptions", "categories", "PermitListComponent_ng_template_5_div_0_Template", "PermitListComponent_ng_template_6_Template_button_click_0_listener", "_r6", "ɵɵtextInterpolate1", "PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener", "dataItem_r8", "_r7", "dataItem", "edit", "permitId", "PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template", "getHiddenField", "dataItem_r9", "permitNumber", "column_r11", "filter_r10", "PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template", "ɵɵpureFunction0", "_c6", "dataItem_r12", "permitName", "column_r14", "filter_r13", "PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template", "dataItem_r15", "projectName", "column_r17", "filter_r16", "PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template", "dataItem_r18", "permitType", "column_r20", "filter_r19", "PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template", "getCategoryClass", "dataItem_r21", "permitCategory", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener", "_r22", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r22", "filterService_r24", "filterService", "column_r25", "column", "onMenuDropdownChange", "field", "filter_r26", "_c7", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template", "getStatusClass", "dataItem_r27", "internalReviewStatus", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener", "_r28", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r28", "filterService_r30", "column_r31", "filter_r32", "categoriess", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template", "dataItem_r33", "permitReviewType", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener", "_r34", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r34", "filterService_r36", "column_r37", "filter_r38", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template", "dataItem_r39", "permitStatus", "PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener", "_r40", "PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r40", "filterService_r42", "column_r43", "filter_r44", "PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template", "dataItem_r45", "location", "column_r47", "filter_r46", "PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template", "formatDate", "dataItem_r48", "permitAppliedDate", "column_r50", "filter_r49", "filterService_r51", "PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template", "dataItem_r52", "permitExpirationDate", "column_r54", "filter_r53", "filterService_r55", "PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template", "dataItem_r56", "permitFinalDate", "column_r58", "filter_r57", "filterService_r59", "PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template", "dataItem_r60", "permitCompleteDate", "column_r62", "filter_r61", "filterService_r63", "PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template", "dataItem_r64", "attentionReason", "column_r66", "filter_r65", "PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template", "dataItem_r67", "lastUpdatedDate", "column_r69", "filter_r68", "PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_2_Template", "ɵɵelementContainerStart", "PermitListComponent_ng_container_7_kendo_grid_column_1_Template", "PermitListComponent_ng_container_7_kendo_grid_column_2_Template", "PermitListComponent_ng_container_7_kendo_grid_column_3_Template", "PermitListComponent_ng_container_7_kendo_grid_column_4_Template", "PermitListComponent_ng_container_7_kendo_grid_column_5_Template", "PermitListComponent_ng_container_7_kendo_grid_column_6_Template", "PermitListComponent_ng_container_7_kendo_grid_column_7_Template", "PermitListComponent_ng_container_7_kendo_grid_column_8_Template", "PermitListComponent_ng_container_7_kendo_grid_column_9_Template", "PermitListComponent_ng_container_7_kendo_grid_column_10_Template", "PermitListComponent_ng_container_7_kendo_grid_column_11_Template", "PermitListComponent_ng_container_7_kendo_grid_column_12_Template", "PermitListComponent_ng_container_7_kendo_grid_column_13_Template", "PermitListComponent_ng_container_7_kendo_grid_column_14_Template", "PermitListComponent_ng_container_7_kendo_grid_column_15_Template", "PermitListComponent_ng_container_7_kendo_grid_column_16_Template", "column_r70", "PermitListComponent_div_8_Template_button_click_7_listener", "_r71", "clearSearch", "loadTable", "PermitListComponent", "router", "route", "permitsService", "httpUtilService", "customLayoutUtilsService", "kendoColumnService", "modalService", "cdr", "appService", "ExceljsService", "pageInfo", "grid", "serverSideRowData", "gridData", "IsListHasValue", "loading", "isLoading", "loginUser", "searchTerms", "searchSubscription", "next", "filters", "logic", "operator", "filter", "gridFilter", "activeFilters", "filterOptions", "text", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "normalGrid", "expandedGrid", "customReorderableConfig", "gridColumnConfig", "title", "width", "isFixed", "type", "order", "filterable", "statusList", "sort", "dir", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "selectedRows", "isAllSelected", "exportOptions", "columnJSONFormat", "singlePermit", "resetToDefaultSettings", "console", "log", "columns", "for<PERSON>ach", "hidden", "pageSize", "detectChanges", "setTimeout", "refresh", "reset", "constructor", "pipe", "subscribe", "loadingSubject", "ngOnInit", "updateTitle", "initializeComponent", "ngAfterViewInit", "initializeGrid", "ngOnDestroy", "complete", "getLoggedInUser", "initializeColumnVisibility", "setupColumnArrays", "savedConfig", "getFromLocalStorage", "userId", "applySavedColumnSettings", "loadColumnSettingsFromServer", "config", "pageName", "userID", "getHideFields", "response", "<PERSON><PERSON><PERSON>", "Data", "hideData", "JSON", "parse", "error", "map", "col", "length", "a", "b", "aOrder", "indexOf", "b<PERSON>rder", "loadTableWithKendoEndpoint", "loadingTimeout", "warn", "resetLoadingStates", "sortForBackend", "state", "take", "search", "loggedInUserId", "getPermitsForKendoGrid", "data", "clearTimeout", "responseData", "errors", "handleEmptyResponse", "permitData", "total", "Math", "ceil", "min", "pageIndex", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "httpError", "event", "key", "applySearch", "filterChange", "f", "push", "onSortChange", "isThirdClick", "undefined", "pageChange", "floor", "onPageSizeChange", "onDataStateChange", "updateColumnVisibility", "hiddenColumns", "onSelectionChange", "selectAll", "gridContainer", "document", "querySelector", "classList", "toggle", "selectedOption", "prdItems", "exportExcel", "queryparamsExcel", "sortOrder", "sortField", "getAllPermits", "listOfItems", "currentDate", "formatMonthDate", "Date", "tableTitle", "headerArray", "percentageColumns", "respResult", "prdItem", "respData", "Array", "fill", "eventDescription", "event_date", "i", "adjustedIndex", "colSize", "header", "index", "id", "generateExcel", "message", "saveHead", "settings", "saveToLocalStorage", "LoggedId", "showSuccess", "saveColumnSettingsToServer", "createHideFields", "showError", "saveResetToServer", "deleteConfig", "deleteHideFields", "clearFromLocalStorage", "view", "navigate", "queryParams", "from", "permit", "find", "p", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "passEntry", "receivedEntry", "deletePop", "content", "centered", "confirmDelete", "delete", "confirm", "deletePermit", "dateString", "toLocaleDateString", "syncPermits", "municipalityId", "autoLogin", "res", "faultMessage", "success", "err", "Object", "keys", "onTabActivated", "button", "target", "rect", "getBoundingClientRect", "bottom", "window", "scrollY", "left", "scrollX", "onDocumentClick", "dropdown", "closest", "fieldName", "includes", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "PermitsService", "i3", "HttpUtilsService", "i4", "CustomLayoutUtilsService", "i5", "KendoColumnService", "i6", "NgbModal", "ChangeDetectorRef", "i7", "AppService", "i8", "i9", "PageInfoService", "selectors", "viewQuery", "PermitListComponent_Query", "rf", "ctx", "PermitListComponent_click_HostBindingHandler", "ɵɵresolveDocument", "PermitListComponent_div_0_Template", "PermitListComponent_Template_kendo_grid_selectionChange_2_listener", "_r1", "PermitListComponent_Template_kendo_grid_filterChange_2_listener", "PermitListComponent_Template_kendo_grid_pageChange_2_listener", "PermitListComponent_Template_kendo_grid_pageSizeChange_2_listener", "PermitListComponent_Template_kendo_grid_dataStateChange_2_listener", "PermitListComponent_Template_kendo_grid_sortChange_2_listener", "PermitListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "PermitListComponent_ng_template_4_Template", "PermitListComponent_ng_template_5_Template", "PermitListComponent_ng_template_6_Template", "PermitListComponent_ng_container_7_Template", "PermitListComponent_div_8_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-list\\permit-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-list\\permit-list.component.html"], "sourcesContent": ["import { formatDate } from '@angular/common';\nimport {\n  AfterViewInit,\n  ChangeDetectorRef,\n  Component,\n  HostListener,\n  OnDestroy,\n  OnInit,\n  ViewChild,\n} from '@angular/core';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport {  FilterService } from '@progress/kendo-angular-grid';\n\nimport { saveAs } from '@progress/kendo-file-saver';\nimport { add, each } from 'lodash';\nimport {\n  Subject,\n  Subscription,\n  debounceTime,\n  distinctUntilChanged,\n  filter,\n} from 'rxjs';\nimport { AppService } from '../../services/app.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { KendoColumnService } from '../../services/kendo-column.service';\nimport {\n  CompositeFilterDescriptor,\n  SortDescriptor,\n} from '@progress/kendo-data-query';\nimport { PermitsService } from '../../services/permits.service';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport { ExceljsService } from '../../services/exceljs.service';\nimport { PageInfoService } from 'src/app/_metronic/layout/core/page-info.service';\n\n@Component({\n  selector: 'app-permit-list',\n  templateUrl: './permit-list.component.html',\n  styleUrl: './permit-list.component.scss',\n})\nexport class PermitListComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('normalGrid') grid: any;\n\n  // Data\n  public serverSideRowData: any[] = [];\n  public gridData: any = [];\n  public IsListHasValue: boolean = false;\n\n  public loading: boolean = false;\n  public isLoading: boolean = false;\n\n  loginUser: any = {};\n\n  // Search\n  public searchData: string = '';\n  private searchTerms = new Subject<string>();\n  private searchSubscription: Subscription;\nonMenuDropdownChange(value: any, field: string, filterService: FilterService): void {\n  const next: CompositeFilterDescriptor =\n    value == null\n      ? { filters: [], logic: 'and' }\n      : { filters: [{ field, operator: 'eq', value }], logic: 'and' };\n  filterService.filter(next);\n}\n  // Enhanced Filters for Kendo UI\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\n  public activeFilters: Array<{\n    field: string;\n    operator: string;\n    value: any;\n  }> = [];\n\n  public filterOptions: Array<{ text: string; value: string | null }> = [\n    { text: 'All', value: null },\n    { text: 'Active', value: 'Active' },\n    { text: 'Inactive', value: 'Inactive' },\n  ];\n\n  // Advanced filter options\n  public advancedFilterOptions = {\n    status: [\n       { text: 'All', value: null },\n  { text: 'Requires Resubmit', value: 'Requires Resubmit' },\n  { text: 'On Hold', value: 'On Hold' },\n  { text: 'Approved', value: 'Approved' },\n  { text: 'Pending', value: 'Pending' },\n  { text: 'Canceled', value: 'Canceled' },\n  { text: 'Complete', value: 'Complete' },\n  { text: 'Expired', value: 'Expired' },\n  { text: 'Fees Due', value: 'Fees Due' },\n  { text: 'In Review', value: 'In Review' },\n  { text: 'Issued', value: 'Issued' },\n  { text: 'Requires Resubmit for Prescreen', value: 'Requires Resubmit for Prescreen' },\n  { text: 'Submitted - Online', value: 'Submitted - Online' },\n  { text: 'Void', value: 'Void' },\n    ] as Array<{ text: string; value: string | null }>,\n    categories: [\n      { text: 'All', value: null },\n      { text: 'Primary', value: 'Primary' },\n      { text: 'Sub Permit', value: 'Sub Permit' },\n      { text: 'Industrial', value: 'Industrial' },\n      { text: 'Municipal', value: 'Municipal' },\n      { text: 'Environmental', value: 'Environmental' },\n    \n    ] as Array<{ text: string; value: string | null }>,\n    categoriess: [\n      { text: 'All', value: null },\n      { text: 'Approved', value: 'Approved' },\n      { text: 'Pacifica Verification', value: 'Pacifica Verification' },\n      { text: 'Dis-Approved', value: 'Dis-Approved' },\n      { text: 'Pending', value: 'Pending' },\n      { text: 'Not Required', value: 'Not Required' },\n      { text: 'In Review', value: 'In Review' },\n      { text: '1 Cycle Completed', value: '1 Cycle Completed' },\n      \n      // 'Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed'\n    \n    ] as Array<{ text: string; value: string | null }>,\n  };\n\n  // Filter state\n  public showAdvancedFilters = false;\n  public appliedFilters: {\n    status?: string | null;\n    category?: string | null;\n    internalReviewStatus?: string | null;\n  } = {};\n\n  // Column visibility system\n  public kendoHide: any;\n  public hiddenData: any = [];\n  public kendoColOrder: any = [];\n  public kendoInitColOrder: any = [];\n  public hiddenFields: any = [];\n\n  // Column configuration\n  public gridColumns: string[] = [];\n  public defaultColumns: string[] = [];\n  public normalGrid: any;\n  public expandedGrid: any;\n  public isExpanded = false;\n\n  // Custom reorderable configuration that prevents fixed column reordering\n  public customReorderableConfig = false;\n\n  // Enhanced Columns with Kendo UI features - adapted for permits\n  public gridColumnConfig: Array<{\n    field: string;\n    title: string;\n    width: number;\n    isFixed: boolean;\n    type: string;\n    filterable?: boolean;\n    order: number;\n  }> = [\n    {\n      field: 'action',\n      title: 'Action',\n      width: 100,\n      isFixed: true,\n      type: 'action',\n      order: 1,\n    },\n     {\n      field: 'permitName',\n      title: 'Permit/Sub Project Name',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2,\n    },\n    {\n      field: 'permitNumber',\n      title: 'Permit #',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 3,\n    },\n   \n      {\n      field: 'projectName',\n      title: 'Project Name',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 4,\n    },\n       {\n      field: 'permitCategory',\n      title: 'Category',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5,\n    },\n    {\n      field: 'permitType',\n      title: 'Permit Type',\n      width: 200,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6,\n    },\n \n    {\n      field: 'permitStatus',\n      title: 'Permit Status',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 7,\n    },\n    {\n      field: 'internalReviewStatus',\n      title: 'Internal Review Status',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 8,\n    },\n    {\n      field: 'permitReviewType',\n      title: 'Type',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 9,\n    },\n    {\n      field: 'location',\n      title: 'Location',\n      width: 200,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 10,\n    },\n  \n    {\n      field: 'permitAppliedDate',\n      title: 'Applied Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 11,\n    },\n    {\n      field: 'permitExpirationDate',\n      title: 'Expiration Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 12,\n    },\n    {\n      field: 'permitFinalDate',\n      title: 'Final Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 13,\n    },\n    {\n      field: 'permitCompleteDate',\n      title: 'Complete Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 14,\n    },\n    {\n      field: 'attentionReason',\n      title: 'Attention Reason',\n      width: 180,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 15,\n    },\n    {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 16,\n    },\n  ];\npublic statusList:any[] = [\n  { text: 'Approved', value: 'Approved' },\n  { text: 'Pending', value: 'Pending' },\n  { text: 'Rejected', value: 'Rejected' }\n];\n  // State\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n\n  public page: any = {\n    size: 15,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc',\n  };\n\n  public skip: number = 0;\n\n  // Selection\n  public selectedRows: any[] = [];\n  public isAllSelected: boolean = false;\n\n  // Export options\n  public exportOptions = [\n    { text: 'All', value: 'all' },\n    { text: 'Page Results', value: 'selected' },\n  ];\n\n  // Custom dropdown state\n  public isExcelDropdownOpen: boolean = false;\n  public dropdownTop: number = 0;\n  public dropdownLeft: number = 0;\n\n  columnJSONFormat: any;\n  permitId: number;\n  singlePermit: any;\n  private resetToDefaultSettings(): void {\n    console.log('Resetting to default settings...');\n    \n    // Reset column visibility - show all columns\n    this.hiddenFields = [];\n    this.gridColumns = [...this.defaultColumns];\n    this.kendoColOrder = [...this.defaultColumns];\n\n    // Reset sort state to default\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n    this.page.orderBy = 'lastUpdatedDate';\n    this.page.orderDir = 'desc';\n    \n    // Reset page state\n    this.page.pageNumber = 0;\n    this.skip = 0;\n\n    // Reset all filters - clear everything\n    this.filter = { logic: 'and', filters: [] };\n    this.activeFilters = [];\n\n    // Reset advanced filters\n    this.appliedFilters = {};\n\n    // Reset search\n    this.searchData = '';\n\n    // Reset advanced filters visibility\n    this.showAdvancedFilters = false;\n\n    console.log('Reset completed:', {\n      hiddenFields: this.hiddenFields,\n      gridColumns: this.gridColumns,\n      defaultColumns: this.defaultColumns,\n      sort: this.sort,\n      filter: this.filter,\n      searchData: this.searchData\n    });\n\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = { logic: 'and', filters: [] };\n      \n      // Reset sorting\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\n      \n      // Reset column visibility - show all columns\n      this.grid.columns.forEach((column: any) => {\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n      \n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n\n    // Trigger change detection\n    this.cdr.detectChanges();\n    \n    // Force grid refresh to apply all changes\n    if (this.grid) {\n      setTimeout(() => {\n        this.grid.refresh();\n        // Also try to reset the grid state completely\n        this.grid.reset();\n      }, 100);\n    }\n    \n    // Reload data with clean state\n    this.loadTable();\n  }\n  // private saveColumnState(): void {\n  //   try {\n  //     const columnState = {\n  //       columns: this.gridColumns,\n  //       hidden: this.hiddenFields,\n  //       order: this.kendoColOrder,\n  //     };\n  //     localStorage.setItem(\n  //       `${this.GRID_STATE_KEY}-columns`,\n  //       JSON.stringify(columnState)\n  //     );\n  //   } catch (error) {\n  //     console.warn('Error saving column state:', error);\n  //   }\n  // }\n  constructor(\n    private router: Router,\n    private route: ActivatedRoute,\n    private permitsService: PermitsService,\n    private httpUtilService: HttpUtilsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private kendoColumnService: KendoColumnService,\n    private modalService: NgbModal,\n    private cdr: ChangeDetectorRef,\n    public appService: AppService,\n    public ExceljsService: ExceljsService,\n    private pageInfo: PageInfoService\n  ) {\n    // Initialize search subscription\n    this.searchSubscription = this.searchTerms\n      .pipe(debounceTime(500), distinctUntilChanged())\n      .subscribe(() => {\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        this.httpUtilService.loadingSubject.next(true);\n        this.loadTable();\n      });\n  }\n\n  ngOnInit(): void {\n    this.pageInfo.updateTitle('Permits');\n    this.initializeComponent();\n    this.loadTable();\n  }\n\n  ngAfterViewInit(): void {\n    this.initializeGrid();\n  }\n\n  ngOnDestroy(): void {\n    if (this.searchSubscription) {\n      this.searchTerms.complete();\n    }\n  }\n\n  private initializeComponent(): void {\n    // Get login user info\n    this.loginUser = this.appService.getLoggedInUser();\n    // Initialize column visibility system\n    this.initializeColumnVisibility();\n  }\n\n  private initializeColumnVisibility(): void {\n    // Set up column arrays first\n    this.setupColumnArrays();\n    \n    // Try to load from local storage first\n    const savedConfig = this.kendoColumnService.getFromLocalStorage('permits', this.loginUser.userId);\n    \n    if (savedConfig) {\n      // Load saved settings from local storage\n      this.kendoHide = savedConfig.hiddenData || [];\n      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.kendoColOrder];\n    } else {\n      // Initialize with default values\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [...this.defaultColumns];\n      this.kendoInitColOrder = [...this.defaultColumns];\n    }\n    \n    // Apply settings\n    this.applySavedColumnSettings();\n  }\n\n  private loadColumnSettingsFromServer(): void {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n\n    this.kendoColumnService.getHideFields(config).subscribe({\n      next: (response) => {\n        if (response.isFault === false && response.Data) {\n          // Parse the saved settings\n          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.kendoColOrder];\n          \n          // Apply the settings\n          this.applySavedColumnSettings();\n          \n          console.log('Column settings loaded from server:', {\n            kendoHide: this.kendoHide,\n            kendoColOrder: this.kendoColOrder\n          });\n        } else {\n          // No saved settings, use defaults\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      },\n      error: (error) => {\n        console.error('Error loading column settings:', error);\n        // Use defaults on error\n        this.kendoHide = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n        this.applySavedColumnSettings();\n      }\n    });\n  }\n\n  private setupColumnArrays(): void {\n    this.gridColumns = this.gridColumnConfig.map((col) => col.field);\n    this.defaultColumns = [...this.gridColumns];\n  }\n\n  private initializeGrid(): void {\n    if (this.grid) {\n      // Apply saved column settings\n      this.applySavedColumnSettings();\n    }\n  }\n\n  private applySavedColumnSettings(): void {\n    if (this.kendoHide && this.kendoHide.length > 0) {\n      this.hiddenFields = this.kendoHide;\n    }\n\n    if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n      // Apply column order\n      this.gridColumnConfig.sort((a, b) => {\n        const aOrder = this.kendoColOrder.indexOf(a.field);\n        const bOrder = this.kendoColOrder.indexOf(b.field);\n        return aOrder - bOrder;\n      });\n    }\n  }\n\n  // Load table data\n  public loadTable(): void {\n    this.loadTableWithKendoEndpoint();\n  }\n\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n\n    // Safety timeout to prevent loader from getting stuck\n    const loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached, resetting loading states');\n      this.resetLoadingStates();\n    }, 30000); // 30 seconds timeout\n\n    // Prepare state object for Kendo UI endpoint\n    // When sort is empty (3rd click), send default sort to backend\n    const sortForBackend = this.sort.length > 0\n      ? this.sort\n      : [{ field: 'lastUpdatedDate', dir: 'desc' }];\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: sortForBackend,\n      filter: this.filter.filters,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId,\n    };\n    \n    console.log('Loading table with state:', state);\n\n    this.permitsService.getPermitsForKendoGrid(state).subscribe({\n      next: (data: {\n        isFault?: boolean;\n        responseData?: {\n          data: any[];\n          total: number;\n          errors?: string[];\n          status?: number;\n        };\n        data?: any[];\n        total?: number;\n        errors?: string[];\n        status?: number;\n      }) => {\n        // Clear the safety timeout since we got a response\n        clearTimeout(loadingTimeout);\n\n        console.log('API Response:', data);\n\n        // Handle the new API response structure\n        if (\n          data.isFault ||\n          (data.responseData &&\n            data.responseData.errors &&\n            data.responseData.errors.length > 0)\n        ) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n\n          // Check if this is an authentication error\n          if (data.responseData?.status === 401 || data.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n\n          this.handleEmptyResponse();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const permitData = responseData.data || [];\n          const total = responseData.total || 0;\n\n          this.IsListHasValue = permitData.length !== 0;\n          this.serverSideRowData = permitData;\n          this.gridData = this.serverSideRowData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          \n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: permitData,\n            total: total\n          };\n          console.log('this.serverSideRowData ', this.serverSideRowData);\n          console.log('this.gridData ', this.gridData);\n          console.log('this.IsListHasValue ', this.IsListHasValue);\n          console.log('this.page ', this.page);\n          console.log('Total elements set to:', this.page.totalElements);\n          console.log('Total pages calculated:', this.page.totalPages);\n          console.log('Current skip:', this.skip, 'Page size:', this.page.size);\n          console.log('Expected items range:', (this.skip + 1), '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n          \n          // Debug grid state after data load\n          setTimeout(() => {\n            if (this.grid) {\n              console.log('Grid total:', this.grid.total);\n              console.log('Grid pageSize:', this.grid.pageSize);\n              console.log('Grid skip:', this.grid.skip);\n              console.log('Grid pageIndex:', this.grid.pageIndex);\n              console.log('Grid data length:', this.grid.data ? this.grid.data.length : 'no data');\n              \n              // Force grid to update its total and maintain pagination state\n              this.grid.total = this.page.totalElements;\n              this.grid.skip = this.skip;\n              this.grid.pageIndex = this.page.pageNumber;\n              console.log('Forced grid total to:', this.grid.total, 'skip to:', this.skip, 'pageIndex to:', this.page.pageNumber);\n            }\n          }, 100);\n          \n          this.cdr.markForCheck();\n          // Always reset loading states regardless of data content\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      },\n      error: (error: unknown) => {\n        // Clear the safety timeout since we got an error\n        clearTimeout(loadingTimeout);\n\n        console.error('Error loading data with Kendo UI endpoint:', error);\n\n        // Check if this is an authentication error\n        if (error && typeof error === 'object' && 'status' in error) {\n          const httpError = error as any;\n          if (httpError.status === 401) {\n            console.warn('Authentication error - token may be expired');\n            // Don't handle empty response here, let the interceptor handle auth\n            return;\n          }\n        }\n\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        // Clear the safety timeout\n        clearTimeout(loadingTimeout);\n\n        // Ensure loading states are reset in complete block as well\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n    });\n  }\n\n  private handleEmptyResponse(): void {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n\n    // Ensure loading states are reset when handling empty response\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n\n  // Method to manually reset loading states if they get stuck\n  private resetLoadingStates(): void {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n  }\n\n  // Public method to manually refresh the grid and reset any stuck loading states\n  public refreshGrid(): void {\n    console.log('Manually refreshing grid...');\n    this.resetLoadingStates();\n    this.loadTable();\n  }\n\n  // Search functionality\n  public onSearchKeyDown(event: KeyboardEvent): void {\n    if (event.key === 'Enter') {\n      this.loadTable();\n    }\n  }\n\n  public onSearchChange(): void {\n    console.log('Search changed:', this.searchData);\n    // Trigger search with debounce\n    this.searchTerms.next(this.searchData || '');\n  }\n\n  private applySearch(): void {\n    this.loadTable();\n  }\n\n  public clearSearch(): void {\n    this.searchTerms.next(this.searchData);\n  }\n\n  // Filter functionality\n  public filterChange(filter: CompositeFilterDescriptor): void {\n    console.log('filter', filter);\n    this.filter = filter;\n    this.loadTable();\n  }\n\n  public applyAdvancedFilters(): void {\n    console.log('yes it came here');\n    // Apply status filter\n    if (this.appliedFilters.status) {\n      this.filter.filters = this.filter.filters.filter((f) => {\n        if ('field' in f) {\n          return f.field !== 'permitStatus';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status,\n      });\n    }\n\n    // Apply category filter\n    if (this.appliedFilters.category) {\n      this.filter.filters = this.filter.filters.filter((f) => {\n        if ('field' in f) {\n          return f.field !== 'permitCategory';\n        }\n        return true;\n      });\n      this.filter.filters.push({\n        field: 'permitCategory',\n        operator: 'eq',\n        value: this.appliedFilters.category,\n      });\n    }\n\n    this.loadTable();\n  }\n\n  public clearAdvancedFilters(): void {\n    this.appliedFilters = {};\n    this.filter.filters = [];\n    this.loadTable();\n  }\n\n  // Sorting functionality\n  public onSortChange(sort: SortDescriptor[]): void {\n    // Check if this is the 3rd click (dir is undefined)\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n\n    if (isThirdClick) {\n      // 3rd click - clear sort and use default\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n      // Valid sort with direction\n      this.sort = sort;\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = sort[0].dir;\n    } else {\n      // Empty sort array or invalid sort\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.loadTable();\n  }\n\n  // Pagination functionality\n  public pageChange(event: any): void {\n    console.log('Page change event:', event);\n    console.log('Current page size:', this.page.size);\n    console.log('Event page size:', event.pageSize);\n    console.log('Event page index:', event.pageIndex);\n    console.log('Event skip:', event.skip);\n    \n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n    \n    console.log('Updated skip:', this.skip, 'page size:', this.page.size, 'page number:', this.page.pageNumber);\n    console.log('Expected items range:', (this.skip + 1), '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n    \n    this.loadTable();\n  }\n\n  // Handle page size change specifically\n  public onPageSizeChange(event: any): void {\n    console.log('Page size change event:', event);\n    console.log('New page size:', event.pageSize);\n    \n    if (event.pageSize && event.pageSize !== this.page.size) {\n      console.log('Page size changing from', this.page.size, 'to', event.pageSize);\n      this.page.size = event.pageSize;\n      this.page.pageNumber = 0; // Reset to first page when changing page size\n      this.skip = 0;\n      \n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      \n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total to:', this.grid.total);\n      }\n      \n      console.log('Updated page size:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n\n  // Handle data state changes (includes page size changes)\n  public onDataStateChange(event: any): void {\n    console.log('Data state change event:', event);\n    \n    // Check if page size changed\n    if (event.take && event.take !== this.page.size) {\n      console.log('Page size changing via data state from', this.page.size, 'to', event.take);\n      this.page.size = event.take;\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      \n      // Recalculate total pages based on new page size\n      if (this.page.totalElements > 0) {\n        this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n        console.log('Recalculated total pages via data state:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n      }\n      \n      // Force grid to update its page size and reset to first page\n      if (this.grid) {\n        this.grid.pageSize = this.page.size;\n        this.grid.skip = 0;\n        this.grid.pageIndex = 0;\n        this.grid.total = this.page.totalElements;\n        console.log('Updated grid total via data state to:', this.grid.total);\n      }\n      \n      console.log('Updated page size via data state:', this.page.size, 'skip:', this.skip);\n      this.loadTable();\n    }\n  }\n\n\n\n  public updateColumnVisibility(event: any): void {\n    // Handle column visibility changes\n    const hiddenColumns = event.hiddenColumns || [];\n    this.hiddenFields = hiddenColumns;\n  }\n\n  // Selection functionality\n  public onSelectionChange(event: any): void {\n    this.selectedRows = event.selectedRows || [];\n    this.isAllSelected =\n      this.selectedRows.length === this.serverSideRowData.length;\n  }\n\n  public selectAll(): void {\n    if (this.isAllSelected) {\n      this.selectedRows = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedRows = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n  }\n\n  // Grid expansion\n  public toggleExpand(): void {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector(\n      '.grid-container'\n    ) as HTMLElement;\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n\n  // Export functionality\n  onExportClick(event: any) {\n    const selectedOption = event.value; // Get selected option\n\n    let prdItems: any = [];\n    if (selectedOption === 'selected') {\n      prdItems = this.serverSideRowData;\n\n      // declare the title and header data for excel\n      // get the data for excel in a array format\n      this.exportExcel(prdItems);\n    } else if (selectedOption === 'all') {\n      const queryparamsExcel = {\n        pageSize: this.page.totalElements,\n        sortOrder: this.page.orderDir,\n        sortField: this.page.orderBy,\n        pageNumber: this.page.pageNumber,\n        // filter: this.filterConfiguration()\n      };\n\n      // Enable loading indicator\n      this.httpUtilService.loadingSubject.next(true);\n      // API call\n      this.permitsService\n        .getAllPermits(queryparamsExcel)\n        // .pipe(map((data: any) => data as any))\n        .subscribe((data) => {\n          // Disable loading indicator\n          this.httpUtilService.loadingSubject.next(false);\n          if (data.isFault) {\n            this.IsListHasValue = false;\n            this.cdr.markForCheck();\n            return; // Exit early if the response has a fault\n          }\n\n          this.IsListHasValue = true;\n          prdItems = data.responseData.data || [];\n\n          this.cdr.detectChanges(); // Manually trigger UI update\n          this.exportExcel(prdItems);\n        });\n    }\n  }\n\n  exportExcel(listOfItems: any): void {\n    // Define local variables for the items and current date\n    let prdItems: any = listOfItems;\n    let currentDate: Date = this.appService.formatMonthDate(new Date());\n\n    console.log('prdItems', prdItems);\n\n    // Check if the data exists and is not empty\n    if (prdItems !== undefined && prdItems.length > 0) {\n      // Define the title for the Excel file\n      const tableTitle = 'Events';\n\n      // Filter out hidden columns and sort by order\n      // const visibleColumns = this.columnJSONFormat\n      //   .filter((col: any) => !col.hidden)\n      //   .sort((a: any, b: any) => a.order - b.order);\n\n      // Create header from visible columns\n\n      const headerArray = [\n        'Permit Number',\n        'Permit Type',\n        'Category',\n        'Status',\n        'Location',\n        'Project Name',\n        'Applied Date',\n\n        'Expiration Date',\n        'Attention Reason',\n      ];\n      // ...visibleColumns.map((col: any) => col.title),\n\n      // Define which columns should have currency and percentage formatting\n      // const currencyColumns: any = [\n      //   'Pending',\n      //   'ACAT',\n      //   'Annuity',\n      //   'AUM',\n      //   'Total Assets',\n      //   'Event Cost',\n      //   'Gross Profit',\n      // ].filter((col) => headerArray.includes(col));\n\n      const percentageColumns: any = [];\n\n      // Get the data for excel in an array format\n      const respResult: any = [];\n\n      // Prepare the data for export based on visible columns\n      each(prdItems, (prdItem: any) => {\n        // Create an array with the same length as headerArray\n        const respData = Array(headerArray.length).fill(null);\n        respData[0] = prdItem.eventDescription;\n        respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n        // Fill in data for each visible column\n        headerArray.forEach((col: any, i: number) => {\n          const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n          switch (col) {\n            case 'Permit Number':\n              respData[adjustedIndex] = prdItem.permitNumber;\n              break;\n            case 'Permit Type':\n              respData[adjustedIndex] = prdItem.permitType;\n              break;\n            case 'Category':\n              respData[adjustedIndex] = prdItem.permitCategory;\n              break;\n            case 'Status':\n              respData[adjustedIndex] = prdItem.permitStatus;\n              break;\n            case 'Location':\n              respData[adjustedIndex] = prdItem.location;\n              break;\n            case 'Project Name':\n              respData[adjustedIndex] = prdItem.projectName;\n              break;\n            case 'Applied Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitAppliedDate);\n              break;\n            case 'Expiration Date':\n              respData[adjustedIndex] = this.appService.formatDate(prdItem.permitExpirationDate);\n              break;\n            case 'Attention Reason':\n              respData[adjustedIndex] = prdItem.attentionReason;\n              break;\n            // case 'kept_appointments':\n            //   respData[adjustedIndex] = prdItem.kept_appointments;\n            //   break;\n            // case 'kept_appt_ratio':\n            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n            //   break;\n            // case 'apptKeptNo':\n            //   respData[adjustedIndex] = prdItem.apptKeptNo;\n            //   break;\n            // case 'has_assets':\n            //   respData[adjustedIndex] = prdItem.has_assets;\n            //   break;\n            // case 'prospects_closed':\n            //   respData[adjustedIndex] = prdItem.prospects_closed;\n            //   break;\n            // case 'closing_ratio':\n            //   respData[adjustedIndex] = prdItem.closing_ratio;\n            //   break;\n            // case 'totalPending':\n            //   respData[adjustedIndex] = prdItem.totalPending;\n            //   break;\n            // case 'acatproduction':\n            //   respData[adjustedIndex] = prdItem.acatproduction;\n            //   break;\n            // case 'annuityproduction':\n            //   respData[adjustedIndex] = prdItem.annuityproduction;\n            //   break;\n            // case 'aumproduction':\n            //   respData[adjustedIndex] = prdItem.aumproduction;\n            //   break;\n            // case 'totalAssets':\n            //   respData[adjustedIndex] = prdItem.totalAssets;\n            //   break;\n            // case 'eventCost':\n            //   respData[adjustedIndex] = prdItem.eventCost;\n            //   break;\n            // case 'grossProfit':\n            //   respData[adjustedIndex] = prdItem.grossProfit;\n            //   break;\n            // case 'status':\n            //   respData[adjustedIndex] = prdItem.status;\n            //   break;\n          }\n        });\n\n        respResult.push(respData);\n      });\n\n      // Define column sizes for the Excel file\n      const colSize = headerArray.map((header, index) => ({\n        id: index + 1,\n        width: 20,\n      }));\n\n      // Generate the Excel file using the exceljsService\n      this.ExceljsService.generateExcel(\n        tableTitle,\n        headerArray,\n        respResult,\n        colSize\n        // currencyColumns,\n        // percentageColumns\n      );\n    } else {\n      const message = 'There are no records available to export.';\n      // this.layoutUtilService.showError(message, '');\n    }\n  }\n\n  // public onExportClick(event: any): void {\n  //   const exportType = event.item.value;\n  //   let selectedIds: number[] = [];\n\n  //   switch (exportType) {\n  //     case 'selected':\n  //       selectedIds = this.selectedRows.map((row) => row.permitId);\n  //       if (selectedIds.length === 0) {\n  //         //alert('Please select permits to export');\n  //         return;\n  //       }\n  //       break;\n  //     case 'filtered':\n  //       // Export filtered data\n  //       break;\n  //     case 'all':\n  //     default:\n  //       // Export all data\n  //       break;\n  //   }\n\n  //   this.exportPermits(exportType, selectedIds);\n  // }\n\n  // private exportPermits(exportType: string, selectedIds: number[]): void {\n  //   this.permitsService.exportPermits(exportType, selectedIds).subscribe({\n  //     next: (response: any) => {\n  //       if (response.data) {\n  //         const blob = new Blob([response.data], {\n  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n  //         });\n  //         saveAs(\n  //           blob,\n  //           `permits_${exportType}_${\n  //             new Date().toISOString().split('T')[0]\n  //           }.xlsx`\n  //         );\n  //       }\n  //     },\n  //     error: (error: any) => {\n  //       console.error('Export error:', error);\n  //       //alert('Error exporting permits data');\n  //     },\n  //   });\n  // }\n\n  // Column settings management\n  public saveHead(): void {\n    const settings = {\n      kendoHide: this.hiddenFields,\n      kendoColOrder: this.kendoColOrder,\n      kendoInitColOrder: this.kendoInitColOrder,\n    };\n\n    // Save to local storage only\n    this.kendoColumnService.saveToLocalStorage({\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    });\n\n    console.log('Column settings saved locally:', settings);\n                        this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n\n    //alert('Column settings saved locally');\n  }\n\n  private saveColumnSettingsToServer(settings: any): void {\n    const config = {\n      pageName: 'permits',\n      userID: this.loginUser.userId,\n      hiddenData: settings.kendoHide,\n      kendoColOrder: settings.kendoColOrder,\n      LoggedId: this.loginUser.userId\n    };\n\n    this.kendoColumnService.createHideFields(config).subscribe({\n      next: (response) => {\n        if (response.isFault === false) {\n          console.log('Column settings saved successfully:', response);\n                        this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n\n          //alert('Column settings saved successfully');\n        } else {\n          console.error('Failed to save column settings:', response.message);\n                        this.customLayoutUtilsService.showError(response.message, '');\n\n          //alert('Failed to save column settings: ' + response.message);\n        }\n      },\n      error: (error) => {\n        console.error('Error saving column settings:', error);\n                        this.customLayoutUtilsService.showError('Error saving column setting', '');\n\n        //alert('Error saving column settings. Please try again.');\n      }\n    });\n  }\n\n  private saveResetToServer(): void {\n    // First delete existing settings\n    const deleteConfig = {\n      pageName: 'permits',\n      userID: this.loginUser.userId\n    };\n\n    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n      next: (response) => {\n        console.log('Existing settings deleted:', response);\n        // Then save the reset state (all columns visible)\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      },\n      error: (error) => {\n        console.error('Error deleting existing settings:', error);\n        // Still try to save the reset state\n        this.saveColumnSettingsToServer({\n          kendoHide: [],\n          kendoColOrder: this.defaultColumns,\n          kendoInitColOrder: this.defaultColumns\n        });\n      }\n    });\n  }\n\n  public resetTable(): void {\n    console.log('Resetting Kendo settings for permits');\n    \n    // Clear all saved settings first\n    this.kendoHide = [];\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.kendoInitColOrder = [];\n    \n    // Clear local storage\n    this.kendoColumnService.clearFromLocalStorage('permits');\n    \n    // Reset to default settings\n    this.resetToDefaultSettings();\n    \n    // Trigger change detection to update the template\n    this.cdr.detectChanges();\n    \n    // Force grid refresh to show all columns\n    if (this.grid) {\n      this.grid.refresh();\n    }\n\n    // Show success message\n    console.log('Table reset to default settings');\n    //alert('Table reset to default settings - all columns restored');\n  }\n\n  // Navigation\n  public add(): void {\n    // this.router.navigate(['/permits/add']);\n    this.edit(0);\n  }\n\n  public view(permitId: number): void {\n    this.router.navigate(['/permits/view', permitId], { \n      queryParams: { from: 'permit-list' } \n    });\n  }\n\n  public edit(permitId: number): void {\n    if (permitId == 0) {\n      const permit = this.serverSideRowData.find(\n        (p) => p.permitId === permitId\n      );\n      const NgbModalOptions: {\n        size: string;\n        backdrop: boolean | 'static';\n        keyboard: boolean;\n        scrollable: boolean;\n      } = {\n        size: 'lg', // Large modal size\n        backdrop: 'static', // Prevents closing when clicking outside\n        keyboard: false, // Disables closing with the Escape key\n        scrollable: true, // Allows scrolling inside the modal\n      };\n\n      // Trigger global loader BEFORE opening modal to ensure full-page overlay\n      this.httpUtilService.loadingSubject.next(true);\n\n      // Open the modal and load the ProjectPopup\n      const modalRef = this.modalService.open(\n        PermitPopupComponent,\n        NgbModalOptions\n      );\n      // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n      modalRef.componentInstance.id = permitId;\n      modalRef.componentInstance.permit = permit;\n      // Subscribe to the modal event when data is updated\n      modalRef.componentInstance.passEntry.subscribe(\n        (receivedEntry: boolean) => {\n          if (receivedEntry === true) {\n            // Reload the table data after a successful update\n            this.loadTable();\n          }\n        }\n      );\n    } else {\n      this.router.navigate(['/permits/view', permitId], { \n        queryParams: { from: 'permit-list' } \n      });\n    }\n  }\n  deletePop(content: any) {\n    this.modalService.open(content, { centered: true });\n  }\n\n  confirmDelete() {\n    console.log('Item deleted ✅');\n    // your delete logic here\n  }\n\n  public delete(permitId: number): void {\n    if (confirm('Are you sure you want to delete this permit?')) {\n      this.permitsService.deletePermit({ permitId }).subscribe({\n        next: (response: any) => {\n          if (response.message) {\n            //alert('Permit deleted successfully');\n                        this.customLayoutUtilsService.showSuccess('Permit deleted successfully', '');\n\n            this.loadTable();\n          }\n        },\n        error: (error: any) => {\n          console.error('Delete error:', error);\n                        this.customLayoutUtilsService.showError('Error deleting permit', '');\n\n          //alert('Error deleting permit');\n        },\n      });\n    }\n  }\n\n  // Utility methods\n  public formatDate(dateString: string): string {\n    if (!dateString) return '';\n    return new Date(dateString).toLocaleDateString();\n  }\n\n  public getStatusClass(status: string): string {\n    switch (status) {\n      case 'Approved':\n        return 'badge-light-success';\n      case 'Requires Resubmit':\n        return 'badge-light-warning';\n      case 'On Hold':\n        return 'badge-light-danger';\n      case 'Pending':\n        return 'badge-light-info';\n      default:\n        return 'badge-light-secondary';\n    }\n  }\n\n  public getCategoryClass(category: string): string {\n    return category === 'Primary'\n      ? 'badge-light-primary'\n      : 'badge-light-secondary';\n  }\n\n  syncPermits(i: any) {\n    this.isLoading = true;\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({ municipalityId: 1, singlePermit: this.singlePermit, autoLogin: true }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Sync response:', res);\n        \n        // Handle wrapped response structure from interceptor\n        const responseData = res?.responseData || res;\n        \n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n                        this.customLayoutUtilsService.showError(responseData.faultMessage|| 'Failed to sync permit', '');\n\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n          } else if (responseData.message === 'No permits found for any keywords') {\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n         \n                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n           } else {\n                                    this.customLayoutUtilsService.showError(responseData.message ||'Failed to sync permit', '');\n\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else {\n                        this.customLayoutUtilsService.showSuccess('✅ Permit synced successfully', '');\n\n          //alert('✅ Permit synced successfully');\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        \n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {                        this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert(`❌ ${err.error.message}`)\n            // ;\n          }\n        } else if (err?.status === 404) {\n                                  this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n                                  this.customLayoutUtilsService.showError('❌ Error syncing permit', '');\n\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  onTabActivated() {\n    // This method is called when the tab is activated\n    // You can add any specific logic here if needed\n    console.log('Permits tab activated');\n  }\n\n  // Custom dropdown methods\n  toggleExcelDropdown(event?: Event): void {\n    this.isExcelDropdownOpen = !this.isExcelDropdownOpen;\n    console.log('Excel dropdown toggled:', this.isExcelDropdownOpen);\n\n    if (this.isExcelDropdownOpen && event) {\n      const button = event.target as HTMLElement;\n      const rect = button.getBoundingClientRect();\n      this.dropdownTop = rect.bottom + window.scrollY;\n      this.dropdownLeft = rect.left + window.scrollX;\n      console.log('Dropdown position:', this.dropdownTop, this.dropdownLeft);\n    }\n  }\n\n  closeExcelDropdown(): void {\n    this.isExcelDropdownOpen = false;\n    console.log('Excel dropdown closed');\n  }\n\n  @HostListener('document:click', ['$event'])\n  onDocumentClick(event: Event): void {\n    const target = event.target as HTMLElement;\n    const dropdown = target.closest('.custom-dropdown');\n    if (!dropdown && this.isExcelDropdownOpen) {\n      this.closeExcelDropdown();\n    }\n  }\n\n  public getHiddenField(fieldName: string): boolean {\n    return this.hiddenFields.includes(fieldName);\n  }\n\n\n\n\n\n\n}\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"grid-container\">\r\n  <kendo-grid\r\n    #normalGrid\r\n    [data]=\"gridData\"\r\n    [pageSize]=\"page.size\"\r\n    [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [total]=\"page.totalElements\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto; overflow-x: auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"skip\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (pageSizeChange)=\"onPageSizeChange($event)\"\r\n    (dataStateChange)=\"onDataStateChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\r\n    [loading]=\"false\"\r\n  >\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox\r\n          [style.width.px]=\"500\"\r\n          placeholder=\"Search...\"\r\n          [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\"\r\n          (keydown)=\"onSearchKeyDown($event)\"\r\n          (ngModelChange)=\"onSearchChange()\"\r\n        ></kendo-textbox>\r\n        <!-- <button kendoButton [disabled]=\"!searchData || searchData.trim() === ''\" (click)=\"loadTable()\" class=\"ms-2\">\r\n          <i class=\"fas fa-search\"></i> Search\r\n        </button> -->\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted\">Total: </span>\r\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-sm me-2\" (click)=\"add()\">\r\n        <span\r\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\r\n          class=\"svg-icon svg-icon-3\"\r\n        ></span>\r\n        Add\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-secondary btn-sm me-2\"\r\n        (click)=\"toggleExpand()\"\r\n        title=\"Toggle Grid Expansion\"\r\n      >\r\n        <i\r\n          class=\"fas\"\r\n          [class.fa-expand]=\"!isExpanded\"\r\n          [class.fa-compress]=\"isExpanded\"\r\n        ></i>\r\n      </button>\r\n\r\n      <!-- Excel Export Dropdown -->\r\n      <div class=\"custom-dropdown me-2\" [class.show]=\"isExcelDropdownOpen\" #excelDropdown>\r\n        <button class=\"btn btn-secondary btn-sm\" type=\"button\" (click)=\"toggleExcelDropdown($event)\" title=\"Export Excel\" #excelButton>\r\n          <i class=\"fas fa-file-excel text-success\"></i>\r\n        </button>\r\n        <div class=\"custom-dropdown-menu\" *ngIf=\"isExcelDropdownOpen\" [style.top.px]=\"dropdownTop\" [style.left.px]=\"dropdownLeft\">\r\n          <a class=\"custom-dropdown-item\" href=\"#\" (click)=\"onExportClick({value: 'all'}); closeExcelDropdown(); $event.preventDefault()\">All</a>\r\n          <a class=\"custom-dropdown-item\" href=\"#\" (click)=\"onExportClick({value: 'selected'}); closeExcelDropdown(); $event.preventDefault()\">Page Results</a>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Save Column Settings Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-success btn-sm me-2\"\r\n        (click)=\"saveHead()\"\r\n        title=\"Save Column Settings\"\r\n      >\r\n        <i class=\"fas fa-save\"></i>\r\n      </button> -->\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-warning btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo\"></i>\r\n      </button>\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-info btn-sm me-2\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh Grid Data\"\r\n      >\r\n        <i class=\"fas fa-sync-alt\"></i>\r\n      </button>\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <div\r\n        *ngIf=\"showAdvancedFilters\"\r\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\r\n      >\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Status</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.status\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              [(ngModel)]=\"appliedFilters.status\"\r\n              placeholder=\"Select Status\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Category</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.categories\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              [(ngModel)]=\"appliedFilters.category\"\r\n              placeholder=\"Select Category\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-6 d-flex align-items-end\">\r\n            <button\r\n              kendoButton\r\n              themeColor=\"primary\"\r\n              class=\"me-2\"\r\n              (click)=\"applyAdvancedFilters()\"\r\n            >\r\n              Apply Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              themeColor=\"secondary\"\r\n              class=\"me-2\"\r\n              (click)=\"clearAdvancedFilters()\"\r\n            >\r\n              Clear Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              themeColor=\"light\"\r\n              (click)=\"showAdvancedFilters = false\"\r\n            >\r\n              Hide Filters\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n\r\n    <!-- Toggle Advanced Filters Button -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-outline-secondary btn-sm me-2\"\r\n        (click)=\"showAdvancedFilters = !showAdvancedFilters\"\r\n        title=\"Toggle Advanced Filters\"\r\n      >\r\n        <i class=\"fas fa-filter\"></i>\r\n        {{ showAdvancedFilters ? \"Hide\" : \"Show\" }} Advanced Filters\r\n      </button>\r\n    </ng-template>\r\n\r\n    <ng-container *ngFor=\"let column of gridColumns\">\r\n      <!-- Action Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'action'\"\r\n        field=\"action\"\r\n        title=\"Actions\"\r\n        [width]=\"80\"\r\n        [sortable]=\"false\"\r\n        [filterable]=\"false\"\r\n        [includeInChooser]=\"false\"\r\n        [columnMenu]=\"false\"\r\n        [hidden]=\"getHiddenField('action')\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <div class=\"d-flex gap-1\">\r\n            <!-- <a\r\n              title=\"View\"\r\n              class=\"btn btn-icon btn-sm\"\r\n              (click)=\"view(dataItem.permitId)\"\r\n            >\r\n              <span\r\n                [inlineSVG]=\"'./assets/media/icons/duotune/general/gen019.svg'\"\r\n                class=\"svg-icon svg-icon-3 svg-icon-primary\"\r\n              >\r\n              </span>\r\n            </a> -->\r\n            <a\r\n              title=\"View\"\r\n              class=\"btn btn-icon btn-sm\"\r\n              (click)=\"edit(dataItem.permitId)\"\r\n            >\r\n              <i class=\"fas fa-pencil\" style=\"color: var(--bs-primary, #0d6efd); font-size: 1rem;\"></i>\r\n            </a>\r\n          </div>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Number Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitNumber'\"\r\n        field=\"permitNumber\"\r\n        title=\"Permit #\"\r\n        [width]=\"180\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('permitNumber')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span class=\"fw-bold\">{{ dataItem.permitNumber }}</span>\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitName'\"\r\n        field=\"permitName\"\r\n        title=\"Permit/Sub Project Name\"\r\n        [width]=\"180\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('permitName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span class=\"fw-bold\">{{ dataItem.permitName }}</span>\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n  <kendo-grid-column\r\n        *ngIf=\"column === 'projectName'\"\r\n        field=\"projectName\"\r\n        title=\"Project Name\"\r\n        [width]=\"180\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('projectName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.projectName }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <!-- Permit Type Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitType'\"\r\n        field=\"permitType\"\r\n        title=\"Permit Type\"\r\n        [width]=\"200\"\r\n\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('permitType')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.permitType }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Category Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitCategory'\"\r\n        field=\"permitCategory\"\r\n        title=\"Category\"\r\n        [width]=\"120\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span\r\n            class=\"badge\"\r\n            [ngClass]=\"getCategoryClass(dataItem.permitCategory)\"\r\n          >\r\n            {{ dataItem.permitCategory }}\r\n          </span>\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter  let-filterService=\"filterService\" let-column=\"column\">\r\n           <kendo-dropdownlist\r\n       [column]=\"column\"\r\n      [filter]=\"filter\"\r\n      [data]=\"advancedFilterOptions.categories\"\r\n      [(ngModel)]=\"appliedFilters.status\"\r\n      textField=\"text\"\r\n      valueField=\"value\"\r\n      [valuePrimitive]=\"true\"\r\n      [defaultItem]=\"{ text: 'All', value: null }\"\r\n      (valueChange)=\"onMenuDropdownChange($event, column.field, filterService)\">\r\n    </kendo-dropdownlist>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Status Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'internalReviewStatus'\"\r\n        field=\"internalReviewStatus\"\r\n        title=\"Internal Review Status\"\r\n        [width]=\"150\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span class=\"badge\" [ngClass]=\"getStatusClass(dataItem.internalReviewStatus)\">\r\n            {{ dataItem.internalReviewStatus }}\r\n          </span>\r\n        </ng-template>\r\n\r\n       <ng-template kendoGridFilterMenuTemplate let-filter let-filterService=\"filterService\" let-column=\"column\">\r\n            <kendo-dropdownlist\r\n       [column]=\"column\"\r\n      [filter]=\"filter\"\r\n      [data]=\"advancedFilterOptions.categoriess\"\r\n      [(ngModel)]=\"appliedFilters.internalReviewStatus\"\r\n      textField=\"text\"\r\n      valueField=\"value\"\r\n      [valuePrimitive]=\"true\"\r\n      [defaultItem]=\"{ text: 'All', value: null }\"\r\n      (valueChange)=\"onMenuDropdownChange($event, column.field, filterService)\">\r\n    </kendo-dropdownlist>\r\n          <!-- <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu> -->\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitReviewType'\"\r\n        field=\"permitReviewType\"\r\n        title=\"Type\"\r\n        [width]=\"150\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span class=\"badge\" [ngClass]=\"getStatusClass(dataItem.permitReviewType)\">\r\n            {{ dataItem.permitReviewType }}\r\n          </span>\r\n        </ng-template>\r\n\r\n       <ng-template kendoGridFilterMenuTemplate let-filter let-filterService=\"filterService\" let-column=\"column\">\r\n            <kendo-dropdownlist\r\n       [column]=\"column\"\r\n      [filter]=\"filter\"\r\n      [data]=\"advancedFilterOptions.categoriess\"\r\n      [(ngModel)]=\"appliedFilters.internalReviewStatus\"\r\n      textField=\"text\"\r\n      valueField=\"value\"\r\n      [valuePrimitive]=\"true\"\r\n      [defaultItem]=\"{ text: 'All', value: null }\"\r\n      (valueChange)=\"onMenuDropdownChange($event, column.field, filterService)\">\r\n    </kendo-dropdownlist>\r\n          <!-- <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu> -->\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitStatus'\"\r\n        field=\"permitStatus\"\r\n        title=\"Permit Status\"\r\n        [width]=\"150\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          <span class=\"badge\" [ngClass]=\"getStatusClass(dataItem.permitStatus)\">\r\n            {{ dataItem.permitStatus }}\r\n          </span>\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-filterService=\"filterService\" let-column=\"column\">\r\n            <kendo-dropdownlist\r\n       [column]=\"column\"\r\n      [filter]=\"filter\"\r\n      [data]=\"advancedFilterOptions.status\"\r\n      [(ngModel)]=\"appliedFilters.status\"\r\n      textField=\"text\"\r\n      valueField=\"value\"\r\n      [valuePrimitive]=\"true\"\r\n      [defaultItem]=\"{ text: 'All', value: null }\"\r\n      (valueChange)=\"onMenuDropdownChange($event, column.field, filterService)\">\r\n    </kendo-dropdownlist>\r\n          <!-- <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu> -->\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Location Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'location'\"\r\n        field=\"location\"\r\n        title=\"Location\"\r\n        [width]=\"200\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.location }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Project Name Column -->\r\n    \r\n\r\n      <!-- Permit Applied Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitAppliedDate'\"\r\n        field=\"permitAppliedDate\"\r\n        title=\"Applied Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitAppliedDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Permit Expiration Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitExpirationDate'\"\r\n        field=\"permitExpirationDate\"\r\n        title=\"Expiration Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitExpirationDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitFinalDate'\"\r\n        field=\"permitFinalDate\"\r\n        title=\"Final Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitFinalDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'permitCompleteDate'\"\r\n        field=\"permitCompleteDate\"\r\n        title=\"Complete Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.permitCompleteDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Attention Reason Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'attentionReason'\"\r\n        field=\"attentionReason\"\r\n        title=\"Attention Reason\"\r\n        [width]=\"180\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ dataItem.attentionReason || \"\" }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Last Updated Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'lastUpdatedDate'\"\r\n        field=\"lastUpdatedDate\"\r\n        title=\"Updated Date\"\r\n        [width]=\"130\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n          {{ formatDate(dataItem.lastUpdatedDate) }}\r\n        </ng-template>\r\n\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"false\"\r\n            operator=\"contains\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n    </ng-container>\r\n  </kendo-grid>\r\n</div>\r\n\r\n<!-- No Data Message -->\r\n<div *ngIf=\"!IsListHasValue\" class=\"text-center py-5\">\r\n  <div class=\"text-muted\">\r\n    <i class=\"fas fa-inbox fa-3x mb-3\"></i>\r\n    <h4>No Permits Found</h4>\r\n    <p>No permits match your current search criteria.</p>\r\n    <button class=\"btn btn-primary\" (click)=\"clearSearch(); loadTable()\">\r\n      Clear Search\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n<!-- Example trigger button -->\r\n<!-- <button class=\"btn btn-danger\" (click)=\"open(deleteModal)\">Delete</button> -->\r\n"], "mappings": "AAeA,SAAcA,IAAI,QAAQ,QAAQ;AAClC,SACEC,OAAO,EAEPC,YAAY,EACZC,oBAAoB,QAEf,MAAM;AAUb,SAASC,oBAAoB,QAAQ,wCAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC5BvEC,EAHN,CAAAC,cAAA,aAAqE,cACtC,cACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAuFIH,EADF,CAAAC,cAAA,cAA0H,YACQ;IAAvFD,EAAA,CAAAI,UAAA,mBAAAC,qEAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAE,aAAA,CAAc;QAAAC,KAAA,EAAQ;MAAK,CAAC,CAAC;MAAEH,MAAA,CAAAI,kBAAA,EAAoB;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAER,MAAA,CAAAS,cAAA,EAAuB;IAAA,EAAC;IAACf,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvIH,EAAA,CAAAC,cAAA,YAAqI;IAA5FD,EAAA,CAAAI,UAAA,mBAAAY,qEAAAV,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAE,aAAA,CAAc;QAAAC,KAAA,EAAQ;MAAU,CAAC,CAAC;MAAEH,MAAA,CAAAI,kBAAA,EAAoB;MAAA,OAAAb,EAAA,CAAAc,WAAA,CAAER,MAAA,CAAAS,cAAA,EAAuB;IAAA,EAAC;IAACf,EAAA,CAAAE,MAAA,mBAAY;IACnJF,EADmJ,CAAAG,YAAA,EAAI,EACjJ;;;;IAHqFH,EAA7B,CAAAiB,WAAA,QAAAR,MAAA,CAAAS,WAAA,OAA4B,SAAAT,MAAA,CAAAU,YAAA,OAA+B;;;;;;IAhDzHnB,EADF,CAAAC,cAAA,cAA2D,wBAQxD;IAJCD,EAAA,CAAAoB,gBAAA,2BAAAC,kFAAAf,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAAe,UAAA,EAAAlB,MAAA,MAAAG,MAAA,CAAAe,UAAA,GAAAlB,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAwB;IAGxBN,EADA,CAAAI,UAAA,qBAAAqB,4EAAAnB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAWL,MAAA,CAAAiB,eAAA,CAAApB,MAAA,CAAuB;IAAA,EAAC,2BAAAe,kFAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAClBL,MAAA,CAAAkB,cAAA,EAAgB;IAAA,EAAC;IAKtC3B,EAJG,CAAAG,YAAA,EAAgB,EAIb;IAENH,EAAA,CAAA4B,SAAA,wBAAuC;IAIrC5B,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAA0E;IAAhBD,EAAA,CAAAI,UAAA,mBAAAyB,mEAAA;MAAA7B,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAqB,GAAA,EAAK;IAAA,EAAC;IACvE9B,EAAA,CAAA4B,SAAA,eAGQ;IACR5B,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAI,UAAA,mBAAA2B,oEAAA;MAAA/B,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAuB,YAAA,EAAc;IAAA,EAAC;IAGxBhC,EAAA,CAAA4B,SAAA,aAIK;IACP5B,EAAA,CAAAG,YAAA,EAAS;IAIPH,EADF,CAAAC,cAAA,kBAAoF,qBAC6C;IAAxED,EAAA,CAAAI,UAAA,mBAAA6B,oEAAA3B,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAyB,mBAAA,CAAA5B,MAAA,CAA2B;IAAA,EAAC;IAC1FN,EAAA,CAAA4B,SAAA,aAA8C;IAChD5B,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAmC,UAAA,KAAAC,iDAAA,kBAA0H;IAI5HpC,EAAA,CAAAG,YAAA,EAAM;IAaNH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAI,UAAA,mBAAAiC,oEAAA;MAAArC,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAA6B,UAAA,EAAY;IAAA,EAAC;IAGtBtC,EAAA,CAAA4B,SAAA,aAA2B;IAC7B5B,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAI,UAAA,mBAAAmC,oEAAA;MAAAvC,EAAA,CAAAO,aAAA,CAAAe,GAAA;MAAA,MAAAb,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAA+B,WAAA,EAAa;IAAA,EAAC;IAGvBxC,EAAA,CAAA4B,SAAA,aAA+B;IACjC5B,EAAA,CAAAG,YAAA,EAAS;;;;IAjFLH,EAAA,CAAAyC,SAAA,EAAsB;IAAtBzC,EAAA,CAAAiB,WAAA,oBAAsB;IAEtBjB,EAAA,CAAA0C,gBAAA,YAAAjC,MAAA,CAAAe,UAAA,CAAwB;IACxBxB,EAAA,CAAA2C,UAAA,qBAAoB;IAcK3C,EAAA,CAAAyC,SAAA,GAA6B;IAA7BzC,EAAA,CAAA4C,iBAAA,CAAAnC,MAAA,CAAAoC,IAAA,CAAAC,aAAA,MAA6B;IAMtD9C,EAAA,CAAAyC,SAAA,GAA8D;IAA9DzC,EAAA,CAAA2C,UAAA,+DAA8D;IAc9D3C,EAAA,CAAAyC,SAAA,GAA+B;IAC/BzC,EADA,CAAA+C,WAAA,eAAAtC,MAAA,CAAAuC,UAAA,CAA+B,gBAAAvC,MAAA,CAAAuC,UAAA,CACC;IAKFhD,EAAA,CAAAyC,SAAA,EAAkC;IAAlCzC,EAAA,CAAA+C,WAAA,SAAAtC,MAAA,CAAAwC,mBAAA,CAAkC;IAI/BjD,EAAA,CAAAyC,SAAA,GAAyB;IAAzBzC,EAAA,CAAA2C,UAAA,SAAAlC,MAAA,CAAAwC,mBAAA,CAAyB;;;;;;IA6CxDjD,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAMC;IAFCD,EAAA,CAAAoB,gBAAA,2BAAA8B,6FAAA5C,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA2C,cAAA,CAAAC,MAAA,EAAA/C,MAAA,MAAAG,MAAA,CAAA2C,cAAA,CAAAC,MAAA,GAAA/C,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAmC;IAIvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAAsB,gBACM;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1CH,EAAA,CAAAC,cAAA,6BAMC;IAFCD,EAAA,CAAAoB,gBAAA,2BAAAkC,6FAAAhD,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA2C,cAAA,CAAAG,QAAA,EAAAjD,MAAA,MAAAG,MAAA,CAAA2C,cAAA,CAAAG,QAAA,GAAAjD,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAqC;IAIzCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,eAA6C,kBAM1C;IADCD,EAAA,CAAAI,UAAA,mBAAAoD,0EAAA;MAAAxD,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAgD,oBAAA,EAAsB;IAAA,EAAC;IAEhCzD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAKC;IADCD,EAAA,CAAAI,UAAA,mBAAAsD,0EAAA;MAAA1D,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAAkD,oBAAA,EAAsB;IAAA,EAAC;IAEhC3D,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IADCD,EAAA,CAAAI,UAAA,mBAAAwD,0EAAA;MAAA5D,EAAA,CAAAO,aAAA,CAAA4C,GAAA;MAAA,MAAA1C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAAL,MAAA,CAAAoD,mBAAA,GAA+B,KAAK;IAAA,EAAC;IAErC7D,EAAA,CAAAE,MAAA,sBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IA7CEH,EAAA,CAAAyC,SAAA,GAAqC;IAArCzC,EAAA,CAAA2C,UAAA,SAAAlC,MAAA,CAAAqD,qBAAA,CAAAT,MAAA,CAAqC;IAGrCrD,EAAA,CAAA0C,gBAAA,YAAAjC,MAAA,CAAA2C,cAAA,CAAAC,MAAA,CAAmC;IAQnCrD,EAAA,CAAAyC,SAAA,GAAyC;IAAzCzC,EAAA,CAAA2C,UAAA,SAAAlC,MAAA,CAAAqD,qBAAA,CAAAC,UAAA,CAAyC;IAGzC/D,EAAA,CAAA0C,gBAAA,YAAAjC,MAAA,CAAA2C,cAAA,CAAAG,QAAA,CAAqC;;;;;IAtB7CvD,EAAA,CAAAmC,UAAA,IAAA6B,gDAAA,mBAGC;;;;IAFEhE,EAAA,CAAA2C,UAAA,SAAAlC,MAAA,CAAAoD,mBAAA,CAAyB;;;;;;IAyD5B7D,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAI,UAAA,mBAAA6D,mEAAA;MAAAjE,EAAA,CAAAO,aAAA,CAAA2D,GAAA;MAAA,MAAAzD,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAAL,MAAA,CAAAoD,mBAAA,IAAApD,MAAA,CAAAoD,mBAAA;IAAA,EAAoD;IAGpD7D,EAAA,CAAA4B,SAAA,YAA6B;IAC7B5B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IADPH,EAAA,CAAAyC,SAAA,GACF;IADEzC,EAAA,CAAAmE,kBAAA,MAAA1D,MAAA,CAAAoD,mBAAA,yCACF;;;;;;IA6BM7D,EAZF,CAAAC,cAAA,cAA0B,YAgBvB;IADCD,EAAA,CAAAI,UAAA,mBAAAgE,iGAAA;MAAA,MAAAC,WAAA,GAAArE,EAAA,CAAAO,aAAA,CAAA+D,GAAA,EAAAC,QAAA;MAAA,MAAA9D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAASL,MAAA,CAAA+D,IAAA,CAAAH,WAAA,CAAAI,QAAA,CAAuB;IAAA,EAAC;IAEjCzE,EAAA,CAAA4B,SAAA,YAAyF;IAE7F5B,EADE,CAAAG,YAAA,EAAI,EACA;;;;;IA/BVH,EAAA,CAAAC,cAAA,4BAUC;IACCD,EAAA,CAAAmC,UAAA,IAAAuC,6EAAA,0BAA2D;IAsB7D1E,EAAA,CAAAG,YAAA,EAAoB;;;;IAxBlBH,EALA,CAAA2C,UAAA,aAAY,mBACM,qBACE,2BACM,qBACN,WAAAlC,MAAA,CAAAkE,cAAA,WACe;;;;;IAsCjC3E,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlCH,EAAA,CAAAyC,SAAA,EAA2B;IAA3BzC,EAAA,CAAA4C,iBAAA,CAAAgC,WAAA,CAAAC,YAAA,CAA2B;;;;;IAIjD7E,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAA4B,SAAA,qCAAiE;IACnE5B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAA2C,UAAA,WAAAmC,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAlBrB/E,EAAA,CAAAC,cAAA,4BASC;IAKCD,EAJA,CAAAmC,UAAA,IAAA6C,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1EjF,EAAA,CAAAG,YAAA,EAAoB;;;;IAhBlBH,EAJA,CAAA2C,UAAA,cAAa,gBAAA3C,EAAA,CAAAkF,eAAA,IAAAC,GAAA,EAE0D,WAAA1E,MAAA,CAAAkE,cAAA,iBAC9B,oBACtB;;;;;IA4BjB3E,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAhCH,EAAA,CAAAyC,SAAA,EAAyB;IAAzBzC,EAAA,CAAA4C,iBAAA,CAAAwC,YAAA,CAAAC,UAAA,CAAyB;;;;;IAI/CrF,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAA4B,SAAA,qCAAiE;IACnE5B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAA2C,UAAA,WAAA2C,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAlBrBvF,EAAA,CAAAC,cAAA,4BASC;IAKCD,EAJA,CAAAmC,UAAA,IAAAqD,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1EzF,EAAA,CAAAG,YAAA,EAAoB;;;;IAhBlBH,EAJA,CAAA2C,UAAA,cAAa,gBAAA3C,EAAA,CAAAkF,eAAA,IAAAC,GAAA,EAE0D,WAAA1E,MAAA,CAAAkE,cAAA,eAChC,oBACpB;;;;;IA2BjB3E,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAAuB,YAAA,CAAAC,WAAA,MACF;;;;;IAGE3F,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAA4B,SAAA,qCAAiE;IACnE5B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAA2C,UAAA,WAAAiD,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAjBzB7F,EAAA,CAAAC,cAAA,4BAQK;IAKCD,EAJA,CAAAmC,UAAA,IAAA2D,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1E/F,EAAA,CAAAG,YAAA,EAAoB;;;;IAhBlBH,EAHA,CAAA2C,UAAA,cAAa,2BACa,WAAAlC,MAAA,CAAAkE,cAAA,gBACc,oBACrB;;;;;IA6BjB3E,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAA6B,YAAA,CAAAC,UAAA,MACF;;;;;IAGEjG,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAA4B,SAAA,qCAAiE;IACnE5B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAA2C,UAAA,WAAAuD,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAlBrBnG,EAAA,CAAAC,cAAA,4BASC;IAKCD,EAJA,CAAAmC,UAAA,IAAAiE,6EAAA,0BAA2D,IAAAC,6EAAA,0BAIa;IAU1ErG,EAAA,CAAAG,YAAA,EAAoB;;;;IAhBlBH,EAJA,CAAA2C,UAAA,cAAa,gBAAA3C,EAAA,CAAAkF,eAAA,IAAAC,GAAA,EAE0D,WAAA1E,MAAA,CAAAkE,cAAA,eAChC,oBACpB;;;;;IA0BjB3E,EAAA,CAAAC,cAAA,eAGC;IACCD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAHLH,EAAA,CAAA2C,UAAA,YAAAlC,MAAA,CAAA6F,gBAAA,CAAAC,YAAA,CAAAC,cAAA,EAAqD;IAErDxG,EAAA,CAAAyC,SAAA,EACF;IADEzC,EAAA,CAAAmE,kBAAA,MAAAoC,YAAA,CAAAC,cAAA,MACF;;;;;;IAICxG,EAAA,CAAAC,cAAA,6BASqE;IAL1ED,EAAA,CAAAoB,gBAAA,2BAAAqF,0HAAAnG,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAmG,IAAA;MAAA,MAAAjG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA2C,cAAA,CAAAC,MAAA,EAAA/C,MAAA,MAAAG,MAAA,CAAA2C,cAAA,CAAAC,MAAA,GAAA/C,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAmC;IAKnCN,EAAA,CAAAI,UAAA,yBAAAuG,wHAAArG,MAAA;MAAA,MAAAsG,OAAA,GAAA5G,EAAA,CAAAO,aAAA,CAAAmG,IAAA;MAAA,MAAAG,iBAAA,GAAAD,OAAA,CAAAE,aAAA;MAAA,MAAAC,UAAA,GAAAH,OAAA,CAAAI,MAAA;MAAA,MAAAvG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAeL,MAAA,CAAAwG,oBAAA,CAAA3G,MAAA,EAAAyG,UAAA,CAAAG,KAAA,EAAAL,iBAAA,CAAyD;IAAA,EAAC;IAC3E7G,EAAA,CAAAG,YAAA,EAAqB;;;;;;IAPnBH,EAFC,CAAA2C,UAAA,WAAAoE,UAAA,CAAiB,WAAAI,UAAA,CACD,SAAA1G,MAAA,CAAAqD,qBAAA,CAAAC,UAAA,CACwB;IACzC/D,EAAA,CAAA0C,gBAAA,YAAAjC,MAAA,CAAA2C,cAAA,CAAAC,MAAA,CAAmC;IAInCrD,EADA,CAAA2C,UAAA,wBAAuB,gBAAA3C,EAAA,CAAAkF,eAAA,IAAAkC,GAAA,EACqB;;;;;IAxB5CpH,EAAA,CAAAC,cAAA,4BAKC;IAUCD,EATA,CAAAmC,UAAA,IAAAkF,6EAAA,0BAA2D,IAAAC,6EAAA,0BASgD;IAa7GtH,EAAA,CAAAG,YAAA,EAAoB;;;IAxBlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAkCX3C,EAAA,CAAAC,cAAA,eAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFaH,EAAA,CAAA2C,UAAA,YAAAlC,MAAA,CAAA8G,cAAA,CAAAC,YAAA,CAAAC,oBAAA,EAAyD;IAC3EzH,EAAA,CAAAyC,SAAA,EACF;IADEzC,EAAA,CAAAmE,kBAAA,MAAAqD,YAAA,CAAAC,oBAAA,MACF;;;;;;IAIEzH,EAAA,CAAAC,cAAA,6BASoE;IAL1ED,EAAA,CAAAoB,gBAAA,2BAAAsG,0HAAApH,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAoH,IAAA;MAAA,MAAAlH,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA2C,cAAA,CAAAqE,oBAAA,EAAAnH,MAAA,MAAAG,MAAA,CAAA2C,cAAA,CAAAqE,oBAAA,GAAAnH,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAiD;IAKjDN,EAAA,CAAAI,UAAA,yBAAAwH,wHAAAtH,MAAA;MAAA,MAAAuH,OAAA,GAAA7H,EAAA,CAAAO,aAAA,CAAAoH,IAAA;MAAA,MAAAG,iBAAA,GAAAD,OAAA,CAAAf,aAAA;MAAA,MAAAiB,UAAA,GAAAF,OAAA,CAAAb,MAAA;MAAA,MAAAvG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAeL,MAAA,CAAAwG,oBAAA,CAAA3G,MAAA,EAAAyH,UAAA,CAAAb,KAAA,EAAAY,iBAAA,CAAyD;IAAA,EAAC;IAC3E9H,EAAA,CAAAG,YAAA,EAAqB;;;;;;IAPnBH,EAFC,CAAA2C,UAAA,WAAAoF,UAAA,CAAiB,WAAAC,UAAA,CACD,SAAAvH,MAAA,CAAAqD,qBAAA,CAAAmE,WAAA,CACyB;IAC1CjI,EAAA,CAAA0C,gBAAA,YAAAjC,MAAA,CAAA2C,cAAA,CAAAqE,oBAAA,CAAiD;IAIjDzH,EADA,CAAA2C,UAAA,wBAAuB,gBAAA3C,EAAA,CAAAkF,eAAA,IAAAkC,GAAA,EACqB;;;;;IArB5CpH,EAAA,CAAAC,cAAA,4BAKC;IAOAD,EANC,CAAAmC,UAAA,IAAA+F,6EAAA,0BAA2D,IAAAC,6EAAA,0BAM8C;IAqB3GnI,EAAA,CAAAG,YAAA,EAAoB;;;IA7BlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAqCX3C,EAAA,CAAAC,cAAA,eAA0E;IACxED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFaH,EAAA,CAAA2C,UAAA,YAAAlC,MAAA,CAAA8G,cAAA,CAAAa,YAAA,CAAAC,gBAAA,EAAqD;IACvErI,EAAA,CAAAyC,SAAA,EACF;IADEzC,EAAA,CAAAmE,kBAAA,MAAAiE,YAAA,CAAAC,gBAAA,MACF;;;;;;IAIErI,EAAA,CAAAC,cAAA,6BASoE;IAL1ED,EAAA,CAAAoB,gBAAA,2BAAAkH,0HAAAhI,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgI,IAAA;MAAA,MAAA9H,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA2C,cAAA,CAAAqE,oBAAA,EAAAnH,MAAA,MAAAG,MAAA,CAAA2C,cAAA,CAAAqE,oBAAA,GAAAnH,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAiD;IAKjDN,EAAA,CAAAI,UAAA,yBAAAoI,wHAAAlI,MAAA;MAAA,MAAAmI,OAAA,GAAAzI,EAAA,CAAAO,aAAA,CAAAgI,IAAA;MAAA,MAAAG,iBAAA,GAAAD,OAAA,CAAA3B,aAAA;MAAA,MAAA6B,UAAA,GAAAF,OAAA,CAAAzB,MAAA;MAAA,MAAAvG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAeL,MAAA,CAAAwG,oBAAA,CAAA3G,MAAA,EAAAqI,UAAA,CAAAzB,KAAA,EAAAwB,iBAAA,CAAyD;IAAA,EAAC;IAC3E1I,EAAA,CAAAG,YAAA,EAAqB;;;;;;IAPnBH,EAFC,CAAA2C,UAAA,WAAAgG,UAAA,CAAiB,WAAAC,UAAA,CACD,SAAAnI,MAAA,CAAAqD,qBAAA,CAAAmE,WAAA,CACyB;IAC1CjI,EAAA,CAAA0C,gBAAA,YAAAjC,MAAA,CAAA2C,cAAA,CAAAqE,oBAAA,CAAiD;IAIjDzH,EADA,CAAA2C,UAAA,wBAAuB,gBAAA3C,EAAA,CAAAkF,eAAA,IAAAkC,GAAA,EACqB;;;;;IArB5CpH,EAAA,CAAAC,cAAA,4BAKC;IAOAD,EANC,CAAAmC,UAAA,IAAA0G,6EAAA,0BAA2D,IAAAC,6EAAA,0BAM8C;IAqB3G9I,EAAA,CAAAG,YAAA,EAAoB;;;IA7BlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAqCX3C,EAAA,CAAAC,cAAA,eAAsE;IACpED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFaH,EAAA,CAAA2C,UAAA,YAAAlC,MAAA,CAAA8G,cAAA,CAAAwB,YAAA,CAAAC,YAAA,EAAiD;IACnEhJ,EAAA,CAAAyC,SAAA,EACF;IADEzC,EAAA,CAAAmE,kBAAA,MAAA4E,YAAA,CAAAC,YAAA,MACF;;;;;;IAIEhJ,EAAA,CAAAC,cAAA,6BASoE;IAL1ED,EAAA,CAAAoB,gBAAA,2BAAA6H,0HAAA3I,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA2I,IAAA;MAAA,MAAAzI,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAuB,kBAAA,CAAAd,MAAA,CAAA2C,cAAA,CAAAC,MAAA,EAAA/C,MAAA,MAAAG,MAAA,CAAA2C,cAAA,CAAAC,MAAA,GAAA/C,MAAA;MAAA,OAAAN,EAAA,CAAAc,WAAA,CAAAR,MAAA;IAAA,EAAmC;IAKnCN,EAAA,CAAAI,UAAA,yBAAA+I,wHAAA7I,MAAA;MAAA,MAAA8I,OAAA,GAAApJ,EAAA,CAAAO,aAAA,CAAA2I,IAAA;MAAA,MAAAG,iBAAA,GAAAD,OAAA,CAAAtC,aAAA;MAAA,MAAAwC,UAAA,GAAAF,OAAA,CAAApC,MAAA;MAAA,MAAAvG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAc,WAAA,CAAeL,MAAA,CAAAwG,oBAAA,CAAA3G,MAAA,EAAAgJ,UAAA,CAAApC,KAAA,EAAAmC,iBAAA,CAAyD;IAAA,EAAC;IAC3ErJ,EAAA,CAAAG,YAAA,EAAqB;;;;;;IAPnBH,EAFC,CAAA2C,UAAA,WAAA2G,UAAA,CAAiB,WAAAC,UAAA,CACD,SAAA9I,MAAA,CAAAqD,qBAAA,CAAAT,MAAA,CACoB;IACrCrD,EAAA,CAAA0C,gBAAA,YAAAjC,MAAA,CAAA2C,cAAA,CAAAC,MAAA,CAAmC;IAInCrD,EADA,CAAA2C,UAAA,wBAAuB,gBAAA3C,EAAA,CAAAkF,eAAA,IAAAkC,GAAA,EACqB;;;;;IArB5CpH,EAAA,CAAAC,cAAA,4BAKC;IAOCD,EANA,CAAAmC,UAAA,IAAAqH,6EAAA,0BAA2D,IAAAC,6EAAA,0BAM+C;IAqB5GzJ,EAAA,CAAAG,YAAA,EAAoB;;;IA7BlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAuCX3C,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAAuF,YAAA,CAAAC,QAAA,MACF;;;;;IAGE3J,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAA4B,SAAA,qCAAiE;IACnE5B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAA2C,UAAA,WAAAiH,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrB7J,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAmC,UAAA,IAAA2H,8EAAA,0BAA2D,IAAAC,8EAAA,0BAIa;IAU1E/J,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IA6BX3C,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAA1D,MAAA,CAAAuJ,UAAA,CAAAC,YAAA,CAAAC,iBAAA,OACF;;;;;IAQElK,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAA4B,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnE5B,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAA2C,UAAA,WAAAwH,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrCrK,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAmC,UAAA,IAAAmI,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeHvK,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAoCX3C,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAA1D,MAAA,CAAAuJ,UAAA,CAAAQ,YAAA,CAAAC,oBAAA,OACF;;;;;IAQEzK,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAA4B,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnE5B,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAA2C,UAAA,WAAA+H,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrC5K,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAmC,UAAA,IAAA0I,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeH9K,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAkCX3C,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAA1D,MAAA,CAAAuJ,UAAA,CAAAe,YAAA,CAAAC,eAAA,OACF;;;;;IAQEhL,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAA4B,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnE5B,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAA2C,UAAA,WAAAsI,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrCnL,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAmC,UAAA,IAAAiJ,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeHrL,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAkCX3C,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAA1D,MAAA,CAAAuJ,UAAA,CAAAsB,YAAA,CAAAC,kBAAA,OACF;;;;;IAQEvL,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAA4B,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnE5B,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAA2C,UAAA,WAAA6I,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IApBrC1L,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAmC,UAAA,IAAAwJ,8EAAA,0BAA2D,IAAAC,8EAAA,0BAS1D;IAeH5L,EAAA,CAAAG,YAAA,EAAoB;;;IA1BlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAoCX3C,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAA0H,YAAA,CAAAC,eAAA,YACF;;;;;IAGE9L,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAA4B,SAAA,qCAAiE;IACnE5B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAA2C,UAAA,WAAAoJ,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrBhM,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAmC,UAAA,IAAA8J,8EAAA,0BAA2D,IAAAC,8EAAA,0BAIa;IAU1ElM,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IA0BX3C,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmE,kBAAA,MAAA1D,MAAA,CAAAuJ,UAAA,CAAAmC,YAAA,CAAAC,eAAA,OACF;;;;;IAGEpM,EAAA,CAAAC,cAAA,wCAKC;IACCD,EAAA,CAAA4B,SAAA,qCAAiE;IACnE5B,EAAA,CAAAG,YAAA,EAAgC;;;;;IAJ9BH,EAFA,CAAA2C,UAAA,WAAA0J,UAAA,CAAiB,WAAAC,UAAA,CACA,gBACF;;;;;IAdrBtM,EAAA,CAAAC,cAAA,4BAKC;IAKCD,EAJA,CAAAmC,UAAA,IAAAoK,8EAAA,0BAA2D,IAAAC,8EAAA,0BAIa;IAU1ExM,EAAA,CAAAG,YAAA,EAAoB;;;IAhBlBH,EAAA,CAAA2C,UAAA,cAAa;;;;;IAxcjB3C,EAAA,CAAAyM,uBAAA,GAAiD;IAoc/CzM,EAlcA,CAAAmC,UAAA,IAAAuK,+DAAA,gCAUC,IAAAC,+DAAA,gCAmCA,IAAAC,+DAAA,gCAyBA,IAAAC,+DAAA,gCAwBA,IAAAC,+DAAA,gCA0BA,IAAAC,+DAAA,gCAuBA,IAAAC,+DAAA,gCA+BA,IAAAC,+DAAA,gCAkCA,IAAAC,+DAAA,gCAkCA,KAAAC,gEAAA,gCAoCA,KAAAC,gEAAA,gCA0BA,KAAAC,gEAAA,gCAiCA,KAAAC,gEAAA,gCA+BA,KAAAC,gEAAA,gCA+BA,KAAAC,gEAAA,gCAiCA,KAAAC,gEAAA,gCAuBA;;;;;IAtcEzN,EAAA,CAAAyC,SAAA,EAAyB;IAAzBzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,cAAyB;IAoCzB1N,EAAA,CAAAyC,SAAA,EAA+B;IAA/BzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,oBAA+B;IAyB/B1N,EAAA,CAAAyC,SAAA,EAA6B;IAA7BzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,kBAA6B;IAyB7B1N,EAAA,CAAAyC,SAAA,EAA8B;IAA9BzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,mBAA8B;IAyB9B1N,EAAA,CAAAyC,SAAA,EAA6B;IAA7BzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,kBAA6B;IA2B7B1N,EAAA,CAAAyC,SAAA,EAAiC;IAAjCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,sBAAiC;IA+BjC1N,EAAA,CAAAyC,SAAA,EAAuC;IAAvCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,4BAAuC;IAkCvC1N,EAAA,CAAAyC,SAAA,EAAmC;IAAnCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,wBAAmC;IAkCnC1N,EAAA,CAAAyC,SAAA,EAA+B;IAA/BzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,oBAA+B;IAoC/B1N,EAAA,CAAAyC,SAAA,EAA2B;IAA3BzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,gBAA2B;IA0B3B1N,EAAA,CAAAyC,SAAA,EAAoC;IAApCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,yBAAoC;IAiCpC1N,EAAA,CAAAyC,SAAA,EAAuC;IAAvCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,4BAAuC;IA+BvC1N,EAAA,CAAAyC,SAAA,EAAkC;IAAlCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,uBAAkC;IA+BlC1N,EAAA,CAAAyC,SAAA,EAAqC;IAArCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,0BAAqC;IAiCrC1N,EAAA,CAAAyC,SAAA,EAAkC;IAAlCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,uBAAkC;IAuBlC1N,EAAA,CAAAyC,SAAA,EAAkC;IAAlCzC,EAAA,CAAA2C,UAAA,SAAA+K,UAAA,uBAAkC;;;;;;IA0BzC1N,EADF,CAAAC,cAAA,cAAsD,cAC5B;IACtBD,EAAA,CAAA4B,SAAA,YAAuC;IACvC5B,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,uBAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,qDAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACrDH,EAAA,CAAAC,cAAA,iBAAqE;IAArCD,EAAA,CAAAI,UAAA,mBAAAuN,2DAAA;MAAA3N,EAAA,CAAAO,aAAA,CAAAqN,IAAA;MAAA,MAAAnN,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAoN,WAAA,EAAa;MAAA,OAAA7N,EAAA,CAAAc,WAAA,CAAEL,MAAA,CAAAqN,SAAA,EAAW;IAAA,EAAC;IAClE9N,EAAA,CAAAE,MAAA,qBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;ADxoBN,OAAM,MAAO4N,mBAAmB;EAsYpBC,MAAA;EACAC,KAAA;EACAC,cAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,kBAAA;EACAC,YAAA;EACAC,GAAA;EACDC,UAAA;EACAC,cAAA;EACCC,QAAA;EA/YeC,IAAI;EAE7B;EACOC,iBAAiB,GAAU,EAAE;EAC7BC,QAAQ,GAAQ,EAAE;EAClBC,cAAc,GAAY,KAAK;EAE/BC,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjCC,SAAS,GAAQ,EAAE;EAEnB;EACOzN,UAAU,GAAW,EAAE;EACtB0N,WAAW,GAAG,IAAItP,OAAO,EAAU;EACnCuP,kBAAkB;EAC5BlI,oBAAoBA,CAACrG,KAAU,EAAEsG,KAAa,EAAEJ,aAA4B;IAC1E,MAAMsI,IAAI,GACRxO,KAAK,IAAI,IAAI,GACT;MAAEyO,OAAO,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAE,GAC7B;MAAED,OAAO,EAAE,CAAC;QAAEnI,KAAK;QAAEqI,QAAQ,EAAE,IAAI;QAAE3O;MAAK,CAAE,CAAC;MAAE0O,KAAK,EAAE;IAAK,CAAE;IACnExI,aAAa,CAAC0I,MAAM,CAACJ,IAAI,CAAC;EAC5B;EACE;EACOI,MAAM,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAED,OAAO,EAAE;EAAE,CAAE;EACjEI,UAAU,GAA8B;IAAEH,KAAK,EAAE,KAAK;IAAED,OAAO,EAAE;EAAE,CAAE;EACrEK,aAAa,GAIf,EAAE;EAEAC,aAAa,GAAkD,CACpE;IAAEC,IAAI,EAAE,KAAK;IAAEhP,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAEgP,IAAI,EAAE,QAAQ;IAAEhP,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAEgP,IAAI,EAAE,UAAU;IAAEhP,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACOkD,qBAAqB,GAAG;IAC7BT,MAAM,EAAE,CACL;MAAEuM,IAAI,EAAE,KAAK;MAAEhP,KAAK,EAAE;IAAI,CAAE,EACjC;MAAEgP,IAAI,EAAE,mBAAmB;MAAEhP,KAAK,EAAE;IAAmB,CAAE,EACzD;MAAEgP,IAAI,EAAE,SAAS;MAAEhP,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEgP,IAAI,EAAE,UAAU;MAAEhP,KAAK,EAAE;IAAU,CAAE,EACvC;MAAEgP,IAAI,EAAE,SAAS;MAAEhP,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEgP,IAAI,EAAE,UAAU;MAAEhP,KAAK,EAAE;IAAU,CAAE,EACvC;MAAEgP,IAAI,EAAE,UAAU;MAAEhP,KAAK,EAAE;IAAU,CAAE,EACvC;MAAEgP,IAAI,EAAE,SAAS;MAAEhP,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEgP,IAAI,EAAE,UAAU;MAAEhP,KAAK,EAAE;IAAU,CAAE,EACvC;MAAEgP,IAAI,EAAE,WAAW;MAAEhP,KAAK,EAAE;IAAW,CAAE,EACzC;MAAEgP,IAAI,EAAE,QAAQ;MAAEhP,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAEgP,IAAI,EAAE,iCAAiC;MAAEhP,KAAK,EAAE;IAAiC,CAAE,EACrF;MAAEgP,IAAI,EAAE,oBAAoB;MAAEhP,KAAK,EAAE;IAAoB,CAAE,EAC3D;MAAEgP,IAAI,EAAE,MAAM;MAAEhP,KAAK,EAAE;IAAM,CAAE,CACqB;IAClDmD,UAAU,EAAE,CACV;MAAE6L,IAAI,EAAE,KAAK;MAAEhP,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAEgP,IAAI,EAAE,SAAS;MAAEhP,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEgP,IAAI,EAAE,YAAY;MAAEhP,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAEgP,IAAI,EAAE,YAAY;MAAEhP,KAAK,EAAE;IAAY,CAAE,EAC3C;MAAEgP,IAAI,EAAE,WAAW;MAAEhP,KAAK,EAAE;IAAW,CAAE,EACzC;MAAEgP,IAAI,EAAE,eAAe;MAAEhP,KAAK,EAAE;IAAe,CAAE,CAED;IAClDqH,WAAW,EAAE,CACX;MAAE2H,IAAI,EAAE,KAAK;MAAEhP,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAEgP,IAAI,EAAE,UAAU;MAAEhP,KAAK,EAAE;IAAU,CAAE,EACvC;MAAEgP,IAAI,EAAE,uBAAuB;MAAEhP,KAAK,EAAE;IAAuB,CAAE,EACjE;MAAEgP,IAAI,EAAE,cAAc;MAAEhP,KAAK,EAAE;IAAc,CAAE,EAC/C;MAAEgP,IAAI,EAAE,SAAS;MAAEhP,KAAK,EAAE;IAAS,CAAE,EACrC;MAAEgP,IAAI,EAAE,cAAc;MAAEhP,KAAK,EAAE;IAAc,CAAE,EAC/C;MAAEgP,IAAI,EAAE,WAAW;MAAEhP,KAAK,EAAE;IAAW,CAAE,EACzC;MAAEgP,IAAI,EAAE,mBAAmB;MAAEhP,KAAK,EAAE;IAAmB;IAEvD;IAAA;GAGH;EAED;EACOiD,mBAAmB,GAAG,KAAK;EAC3BT,cAAc,GAIjB,EAAE;EAEN;EACOyM,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BC,UAAU;EACVC,YAAY;EACZrN,UAAU,GAAG,KAAK;EAEzB;EACOsN,uBAAuB,GAAG,KAAK;EAEtC;EACOC,gBAAgB,GAQlB,CACH;IACErJ,KAAK,EAAE,QAAQ;IACfsJ,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACA;IACC1J,KAAK,EAAE,YAAY;IACnBsJ,KAAK,EAAE,yBAAyB;IAChCC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,cAAc;IACrBsJ,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EAEC;IACA1J,KAAK,EAAE,aAAa;IACpBsJ,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACE;IACD1J,KAAK,EAAE,gBAAgB;IACvBsJ,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,YAAY;IACnBsJ,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EAED;IACE1J,KAAK,EAAE,cAAc;IACrBsJ,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,QAAQ;IACdE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,sBAAsB;IAC7BsJ,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,QAAQ;IACdE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,kBAAkB;IACzBsJ,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,QAAQ;IACdE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,UAAU;IACjBsJ,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EAED;IACE1J,KAAK,EAAE,mBAAmB;IAC1BsJ,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,sBAAsB;IAC7BsJ,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,iBAAiB;IACxBsJ,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,oBAAoB;IAC3BsJ,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,iBAAiB;IACxBsJ,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACE1J,KAAK,EAAE,iBAAiB;IACxBsJ,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EACIE,UAAU,GAAS,CACxB;IAAElB,IAAI,EAAE,UAAU;IAAEhP,KAAK,EAAE;EAAU,CAAE,EACvC;IAAEgP,IAAI,EAAE,SAAS;IAAEhP,KAAK,EAAE;EAAS,CAAE,EACrC;IAAEgP,IAAI,EAAE,UAAU;IAAEhP,KAAK,EAAE;EAAU,CAAE,CACxC;EACC;EACOmQ,IAAI,GAAqB,CAAC;IAAE7J,KAAK,EAAE,iBAAiB;IAAE8J,GAAG,EAAE;EAAM,CAAE,CAAC;EAEpEnO,IAAI,GAAQ;IACjBoO,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbpO,aAAa,EAAE,CAAC;IAChBqO,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EAEMC,IAAI,GAAW,CAAC;EAEvB;EACOC,YAAY,GAAU,EAAE;EACxBC,aAAa,GAAY,KAAK;EAErC;EACOC,aAAa,GAAG,CACrB;IAAE7B,IAAI,EAAE,KAAK;IAAEhP,KAAK,EAAE;EAAK,CAAE,EAC7B;IAAEgP,IAAI,EAAE,cAAc;IAAEhP,KAAK,EAAE;EAAU,CAAE,CAC5C;EAED;EACOqC,mBAAmB,GAAY,KAAK;EACpC/B,WAAW,GAAW,CAAC;EACvBC,YAAY,GAAW,CAAC;EAE/BuQ,gBAAgB;EAChBjN,QAAQ;EACRkN,YAAY;EACJC,sBAAsBA,CAAA;IAC5BC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;IAE/C;IACA,IAAI,CAAC7B,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC3C,IAAI,CAACJ,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;IAE7C;IACA,IAAI,CAACY,IAAI,GAAG,CAAC;MAAE7J,KAAK,EAAE,iBAAiB;MAAE8J,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAACnO,IAAI,CAACuO,OAAO,GAAG,iBAAiB;IACrC,IAAI,CAACvO,IAAI,CAACwO,QAAQ,GAAG,MAAM;IAE3B;IACA,IAAI,CAACxO,IAAI,CAACqO,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IAEb;IACA,IAAI,CAAC9B,MAAM,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAED,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACK,aAAa,GAAG,EAAE;IAEvB;IACA,IAAI,CAACtM,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC5B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACqC,mBAAmB,GAAG,KAAK;IAEhCgO,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE;MAC9B7B,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BC,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7BC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCY,IAAI,EAAE,IAAI,CAACA,IAAI;MACfvB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBhO,UAAU,EAAE,IAAI,CAACA;KAClB,CAAC;IAEF;IACA,IAAI,IAAI,CAACmN,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACa,MAAM,GAAG;QAAEF,KAAK,EAAE,KAAK;QAAED,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACV,IAAI,CAACoC,IAAI,GAAG,CAAC;QAAE7J,KAAK,EAAE,iBAAiB;QAAE8J,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAACrC,IAAI,CAACoD,OAAO,CAACC,OAAO,CAAEhL,MAAW,IAAI;QACxC,IAAIA,MAAM,CAACE,KAAK,IAAIF,MAAM,CAACE,KAAK,KAAK,QAAQ,EAAE;UAC7CF,MAAM,CAACiL,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;MAEF;MACA,IAAI,CAACtD,IAAI,CAAC2C,IAAI,GAAG,CAAC;MAClB,IAAI,CAAC3C,IAAI,CAACuD,QAAQ,GAAG,IAAI,CAACrP,IAAI,CAACoO,IAAI;IACrC;IAEA;IACA,IAAI,CAAC1C,GAAG,CAAC4D,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACxD,IAAI,EAAE;MACbyD,UAAU,CAAC,MAAK;QACd,IAAI,CAACzD,IAAI,CAAC0D,OAAO,EAAE;QACnB;QACA,IAAI,CAAC1D,IAAI,CAAC2D,KAAK,EAAE;MACnB,CAAC,EAAE,GAAG,CAAC;IACT;IAEA;IACA,IAAI,CAACxE,SAAS,EAAE;EAClB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAyE,YACUvE,MAAc,EACdC,KAAqB,EACrBC,cAA8B,EAC9BC,eAAiC,EACjCC,wBAAkD,EAClDC,kBAAsC,EACtCC,YAAsB,EACtBC,GAAsB,EACvBC,UAAsB,EACtBC,cAA8B,EAC7BC,QAAyB;IAVzB,KAAAV,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACJ,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,QAAQ,GAARA,QAAQ;IAEhB;IACA,IAAI,CAACS,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvCsD,IAAI,CAAC3S,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/C2S,SAAS,CAAC,MAAK;MACd;MACA,IAAI,CAAC1D,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC;MAC9C,IAAI,CAACtB,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEA6E,QAAQA,CAAA;IACN,IAAI,CAACjE,QAAQ,CAACkE,WAAW,CAAC,SAAS,CAAC;IACpC,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAAC/E,SAAS,EAAE;EAClB;EAEAgF,eAAeA,CAAA;IACb,IAAI,CAACC,cAAc,EAAE;EACvB;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAC7D,kBAAkB,EAAE;MAC3B,IAAI,CAACD,WAAW,CAAC+D,QAAQ,EAAE;IAC7B;EACF;EAEQJ,mBAAmBA,CAAA;IACzB;IACA,IAAI,CAAC5D,SAAS,GAAG,IAAI,CAACT,UAAU,CAAC0E,eAAe,EAAE;IAClD;IACA,IAAI,CAACC,0BAA0B,EAAE;EACnC;EAEQA,0BAA0BA,CAAA;IAChC;IACA,IAAI,CAACC,iBAAiB,EAAE;IAExB;IACA,MAAMC,WAAW,GAAG,IAAI,CAAChF,kBAAkB,CAACiF,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAACrE,SAAS,CAACsE,MAAM,CAAC;IAEjG,IAAIF,WAAW,EAAE;MACf;MACA,IAAI,CAACxD,SAAS,GAAGwD,WAAW,CAACvD,UAAU,IAAI,EAAE;MAC7C,IAAI,CAACC,aAAa,GAAGsD,WAAW,CAACtD,aAAa,IAAI,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC1E,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;IAClD,CAAC,MAAM;MACL;MACA,IAAI,CAACF,SAAS,GAAG,EAAE;MACnB,IAAI,CAACC,UAAU,GAAG,EAAE;MACpB,IAAI,CAACC,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;MAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;IACnD;IAEA;IACA,IAAI,CAACqD,wBAAwB,EAAE;EACjC;EAEQC,4BAA4BA,CAAA;IAClC,MAAMC,MAAM,GAAG;MACbC,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC3E,SAAS,CAACsE;KACxB;IAED,IAAI,CAAClF,kBAAkB,CAACwF,aAAa,CAACH,MAAM,CAAC,CAACjB,SAAS,CAAC;MACtDrD,IAAI,EAAG0E,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,IAAID,QAAQ,CAACE,IAAI,EAAE;UAC/C;UACA,IAAI,CAACnE,SAAS,GAAGiE,QAAQ,CAACE,IAAI,CAACC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACC,QAAQ,CAAC,GAAG,EAAE;UACjF,IAAI,CAAClE,aAAa,GAAG+D,QAAQ,CAACE,IAAI,CAACjE,aAAa,GAAGmE,IAAI,CAACC,KAAK,CAACL,QAAQ,CAACE,IAAI,CAACjE,aAAa,CAAC,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UACrH,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACD,aAAa,CAAC;UAEhD;UACA,IAAI,CAACyD,wBAAwB,EAAE;UAE/B3B,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAE;YACjDjC,SAAS,EAAE,IAAI,CAACA,SAAS;YACzBE,aAAa,EAAE,IAAI,CAACA;WACrB,CAAC;QACJ,CAAC,MAAM;UACL;UACA,IAAI,CAACF,SAAS,GAAG,EAAE;UACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;UAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;UACjD,IAAI,CAACqD,wBAAwB,EAAE;QACjC;MACF,CAAC;MACDY,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD;QACA,IAAI,CAACvE,SAAS,GAAG,EAAE;QACnB,IAAI,CAACE,aAAa,GAAG,CAAC,GAAG,IAAI,CAACI,cAAc,CAAC;QAC7C,IAAI,CAACH,iBAAiB,GAAG,CAAC,GAAG,IAAI,CAACG,cAAc,CAAC;QACjD,IAAI,CAACqD,wBAAwB,EAAE;MACjC;KACD,CAAC;EACJ;EAEQJ,iBAAiBA,CAAA;IACvB,IAAI,CAAClD,WAAW,GAAG,IAAI,CAACK,gBAAgB,CAAC8D,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAACpN,KAAK,CAAC;IAChE,IAAI,CAACiJ,cAAc,GAAG,CAAC,GAAG,IAAI,CAACD,WAAW,CAAC;EAC7C;EAEQ6C,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACpE,IAAI,EAAE;MACb;MACA,IAAI,CAAC6E,wBAAwB,EAAE;IACjC;EACF;EAEQA,wBAAwBA,CAAA;IAC9B,IAAI,IAAI,CAAC3D,SAAS,IAAI,IAAI,CAACA,SAAS,CAAC0E,MAAM,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACtE,YAAY,GAAG,IAAI,CAACJ,SAAS;IACpC;IAEA,IAAI,IAAI,CAACE,aAAa,IAAI,IAAI,CAACA,aAAa,CAACwE,MAAM,GAAG,CAAC,EAAE;MACvD;MACA,IAAI,CAAChE,gBAAgB,CAACQ,IAAI,CAAC,CAACyD,CAAC,EAAEC,CAAC,KAAI;QAClC,MAAMC,MAAM,GAAG,IAAI,CAAC3E,aAAa,CAAC4E,OAAO,CAACH,CAAC,CAACtN,KAAK,CAAC;QAClD,MAAM0N,MAAM,GAAG,IAAI,CAAC7E,aAAa,CAAC4E,OAAO,CAACF,CAAC,CAACvN,KAAK,CAAC;QAClD,OAAOwN,MAAM,GAAGE,MAAM;MACxB,CAAC,CAAC;IACJ;EACF;EAEA;EACO9G,SAASA,CAAA;IACd,IAAI,CAAC+G,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,CAAC9F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,MAAM0F,cAAc,GAAG1C,UAAU,CAAC,MAAK;MACrCP,OAAO,CAACkD,IAAI,CAAC,mDAAmD,CAAC;MACjE,IAAI,CAACC,kBAAkB,EAAE;IAC3B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAAClE,IAAI,CAACwD,MAAM,GAAG,CAAC,GACvC,IAAI,CAACxD,IAAI,GACT,CAAC;MAAE7J,KAAK,EAAE,iBAAiB;MAAE8J,GAAG,EAAE;IAAM,CAAE,CAAC;IAC/C,MAAMkE,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAACtS,IAAI,CAACoO,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfP,IAAI,EAAEkE,cAAc;MACpBzF,MAAM,EAAE,IAAI,CAACA,MAAM,CAACH,OAAO;MAC3B+F,MAAM,EAAE,IAAI,CAAC5T,UAAU;MACvB6T,cAAc,EAAE,IAAI,CAACpG,SAAS,CAACsE;KAChC;IAED1B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEoD,KAAK,CAAC;IAE/C,IAAI,CAAChH,cAAc,CAACoH,sBAAsB,CAACJ,KAAK,CAAC,CAACzC,SAAS,CAAC;MAC1DrD,IAAI,EAAGmG,IAYN,IAAI;QACH;QACAC,YAAY,CAACV,cAAc,CAAC;QAE5BjD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEyD,IAAI,CAAC;QAElC;QACA,IACEA,IAAI,CAACxB,OAAO,IACXwB,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAACnB,MAAM,GAAG,CAAE,EACtC;UACA,MAAMmB,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7D7D,OAAO,CAACuC,KAAK,CAAC,uBAAuB,EAAEsB,MAAM,CAAC;UAE9C;UACA,IAAIH,IAAI,CAACE,YAAY,EAAEpS,MAAM,KAAK,GAAG,IAAIkS,IAAI,CAAClS,MAAM,KAAK,GAAG,EAAE;YAC5DwO,OAAO,CAACkD,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;UAEA,IAAI,CAACY,mBAAmB,EAAE;UAC1B;UACA,IAAI,CAAC5G,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;QACjD,CAAC,MAAM;UACL;UACA,MAAMqG,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMK,UAAU,GAAGH,YAAY,CAACF,IAAI,IAAI,EAAE;UAC1C,MAAMM,KAAK,GAAGJ,YAAY,CAACI,KAAK,IAAI,CAAC;UAErC,IAAI,CAAC/G,cAAc,GAAG8G,UAAU,CAACrB,MAAM,KAAK,CAAC;UAC7C,IAAI,CAAC3F,iBAAiB,GAAGgH,UAAU;UACnC,IAAI,CAAC/G,QAAQ,GAAG,IAAI,CAACD,iBAAiB;UACtC,IAAI,CAAC/L,IAAI,CAACC,aAAa,GAAG+S,KAAK;UAC/B,IAAI,CAAChT,IAAI,CAACsO,UAAU,GAAG2E,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAAChT,IAAI,CAACoO,IAAI,CAAC;UAExD;UACA,IAAI,CAACpC,QAAQ,GAAG;YACd0G,IAAI,EAAEK,UAAU;YAChBC,KAAK,EAAEA;WACR;UACDhE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAClD,iBAAiB,CAAC;UAC9DiD,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACjD,QAAQ,CAAC;UAC5CgD,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE,IAAI,CAAChD,cAAc,CAAC;UACxD+C,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACjP,IAAI,CAAC;UACpCgP,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACjP,IAAI,CAACC,aAAa,CAAC;UAC9D+O,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACjP,IAAI,CAACsO,UAAU,CAAC;UAC5DU,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACR,IAAI,EAAE,YAAY,EAAE,IAAI,CAACzO,IAAI,CAACoO,IAAI,CAAC;UACrEY,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAG,IAAI,CAACR,IAAI,GAAG,CAAC,EAAG,GAAG,EAAEwE,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC1E,IAAI,GAAG,IAAI,CAACzO,IAAI,CAACoO,IAAI,EAAE,IAAI,CAACpO,IAAI,CAACC,aAAa,CAAC,CAAC;UAEzH;UACAsP,UAAU,CAAC,MAAK;YACd,IAAI,IAAI,CAACzD,IAAI,EAAE;cACbkD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACnD,IAAI,CAACkH,KAAK,CAAC;cAC3ChE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,IAAI,CAACnD,IAAI,CAACuD,QAAQ,CAAC;cACjDL,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE,IAAI,CAACnD,IAAI,CAAC2C,IAAI,CAAC;cACzCO,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACnD,IAAI,CAACsH,SAAS,CAAC;cACnDpE,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,IAAI,CAACnD,IAAI,CAAC4G,IAAI,GAAG,IAAI,CAAC5G,IAAI,CAAC4G,IAAI,CAAChB,MAAM,GAAG,SAAS,CAAC;cAEpF;cACA,IAAI,CAAC5F,IAAI,CAACkH,KAAK,GAAG,IAAI,CAAChT,IAAI,CAACC,aAAa;cACzC,IAAI,CAAC6L,IAAI,CAAC2C,IAAI,GAAG,IAAI,CAACA,IAAI;cAC1B,IAAI,CAAC3C,IAAI,CAACsH,SAAS,GAAG,IAAI,CAACpT,IAAI,CAACqO,UAAU;cAC1CW,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAACnD,IAAI,CAACkH,KAAK,EAAE,UAAU,EAAE,IAAI,CAACvE,IAAI,EAAE,eAAe,EAAE,IAAI,CAACzO,IAAI,CAACqO,UAAU,CAAC;YACrH;UACF,CAAC,EAAE,GAAG,CAAC;UAEP,IAAI,CAAC3C,GAAG,CAAC2H,YAAY,EAAE;UACvB;UACA,IAAI,CAACnH,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;UACtB,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;QACjD;MACF,CAAC;MACDgF,KAAK,EAAGA,KAAc,IAAI;QACxB;QACAoB,YAAY,CAACV,cAAc,CAAC;QAE5BjD,OAAO,CAACuC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAElE;QACA,IAAIA,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,QAAQ,IAAIA,KAAK,EAAE;UAC3D,MAAM+B,SAAS,GAAG/B,KAAY;UAC9B,IAAI+B,SAAS,CAAC9S,MAAM,KAAK,GAAG,EAAE;YAC5BwO,OAAO,CAACkD,IAAI,CAAC,6CAA6C,CAAC;YAC3D;YACA;UACF;QACF;QAEA,IAAI,CAACY,mBAAmB,EAAE;QAC1B,IAAI,CAAC5G,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACD6D,QAAQ,EAAEA,CAAA,KAAK;QACb;QACAuC,YAAY,CAACV,cAAc,CAAC;QAE5B;QACA,IAAI,CAAC/F,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;MACjD;KACD,CAAC;EACJ;EAEQuG,mBAAmBA,CAAA;IACzB,IAAI,CAAC7G,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAAChM,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAACsO,UAAU,GAAG,CAAC;IAExB;IACA,IAAI,CAACpC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACQ4F,kBAAkBA,CAAA;IACxB,IAAI,CAACjG,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACb,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;EACjD;EAEA;EACO5M,WAAWA,CAAA;IAChBqP,OAAO,CAACC,GAAG,CAAC,6BAA6B,CAAC;IAC1C,IAAI,CAACkD,kBAAkB,EAAE;IACzB,IAAI,CAAClH,SAAS,EAAE;EAClB;EAEA;EACOpM,eAAeA,CAAC0U,KAAoB;IACzC,IAAIA,KAAK,CAACC,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACvI,SAAS,EAAE;IAClB;EACF;EAEOnM,cAAcA,CAAA;IACnBkQ,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAACtQ,UAAU,CAAC;IAC/C;IACA,IAAI,CAAC0N,WAAW,CAACE,IAAI,CAAC,IAAI,CAAC5N,UAAU,IAAI,EAAE,CAAC;EAC9C;EAEQ8U,WAAWA,CAAA;IACjB,IAAI,CAACxI,SAAS,EAAE;EAClB;EAEOD,WAAWA,CAAA;IAChB,IAAI,CAACqB,WAAW,CAACE,IAAI,CAAC,IAAI,CAAC5N,UAAU,CAAC;EACxC;EAEA;EACO+U,YAAYA,CAAC/G,MAAiC;IACnDqC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEtC,MAAM,CAAC;IAC7B,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC1B,SAAS,EAAE;EAClB;EAEOrK,oBAAoBA,CAAA;IACzBoO,OAAO,CAACC,GAAG,CAAC,kBAAkB,CAAC;IAC/B;IACA,IAAI,IAAI,CAAC1O,cAAc,CAACC,MAAM,EAAE;MAC9B,IAAI,CAACmM,MAAM,CAACH,OAAO,GAAG,IAAI,CAACG,MAAM,CAACH,OAAO,CAACG,MAAM,CAAEgH,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAACtP,KAAK,KAAK,cAAc;QACnC;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAACsI,MAAM,CAACH,OAAO,CAACoH,IAAI,CAAC;QACvBvP,KAAK,EAAE,cAAc;QACrBqI,QAAQ,EAAE,IAAI;QACd3O,KAAK,EAAE,IAAI,CAACwC,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA;IACA,IAAI,IAAI,CAACD,cAAc,CAACG,QAAQ,EAAE;MAChC,IAAI,CAACiM,MAAM,CAACH,OAAO,GAAG,IAAI,CAACG,MAAM,CAACH,OAAO,CAACG,MAAM,CAAEgH,CAAC,IAAI;QACrD,IAAI,OAAO,IAAIA,CAAC,EAAE;UAChB,OAAOA,CAAC,CAACtP,KAAK,KAAK,gBAAgB;QACrC;QACA,OAAO,IAAI;MACb,CAAC,CAAC;MACF,IAAI,CAACsI,MAAM,CAACH,OAAO,CAACoH,IAAI,CAAC;QACvBvP,KAAK,EAAE,gBAAgB;QACvBqI,QAAQ,EAAE,IAAI;QACd3O,KAAK,EAAE,IAAI,CAACwC,cAAc,CAACG;OAC5B,CAAC;IACJ;IAEA,IAAI,CAACuK,SAAS,EAAE;EAClB;EAEOnK,oBAAoBA,CAAA;IACzB,IAAI,CAACP,cAAc,GAAG,EAAE;IACxB,IAAI,CAACoM,MAAM,CAACH,OAAO,GAAG,EAAE;IACxB,IAAI,CAACvB,SAAS,EAAE;EAClB;EAEA;EACO4I,YAAYA,CAAC3F,IAAsB;IACxC;IACA,MAAM4F,YAAY,GAAG5F,IAAI,CAACwD,MAAM,GAAG,CAAC,IAAIxD,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,KAAK4F,SAAS;IAE5E,IAAID,YAAY,EAAE;MAChB;MACA,IAAI,CAAC5F,IAAI,GAAG,EAAE;MACd,IAAI,CAAClO,IAAI,CAACuO,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAACvO,IAAI,CAACwO,QAAQ,GAAG,MAAM;IAC7B,CAAC,MAAM,IAAIN,IAAI,CAACwD,MAAM,GAAG,CAAC,IAAIxD,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,EAAE;MACpD;MACA,IAAI,CAACD,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAClO,IAAI,CAACuO,OAAO,GAAGL,IAAI,CAAC,CAAC,CAAC,CAAC7J,KAAK,IAAI,iBAAiB;MACtD,IAAI,CAACrE,IAAI,CAACwO,QAAQ,GAAGN,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG;IAClC,CAAC,MAAM;MACL;MACA,IAAI,CAACD,IAAI,GAAG,EAAE;MACd,IAAI,CAAClO,IAAI,CAACuO,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAACvO,IAAI,CAACwO,QAAQ,GAAG,MAAM;IAC7B;IAEA,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAACzO,IAAI,CAACqO,UAAU,GAAG,CAAC;IACxB,IAAI,CAACpD,SAAS,EAAE;EAClB;EAEA;EACO+I,UAAUA,CAACT,KAAU;IAC1BvE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEsE,KAAK,CAAC;IACxCvE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACjP,IAAI,CAACoO,IAAI,CAAC;IACjDY,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEsE,KAAK,CAAClE,QAAQ,CAAC;IAC/CL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEsE,KAAK,CAACH,SAAS,CAAC;IACjDpE,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEsE,KAAK,CAAC9E,IAAI,CAAC;IAEtC;IACA,IAAI,CAACA,IAAI,GAAG8E,KAAK,CAAC9E,IAAI;IACtB,IAAI,CAACzO,IAAI,CAACoO,IAAI,GAAGmF,KAAK,CAACjB,IAAI,IAAI,IAAI,CAACtS,IAAI,CAACoO,IAAI;IAC7C,IAAI,CAACpO,IAAI,CAACqO,UAAU,GAAG4E,IAAI,CAACgB,KAAK,CAAC,IAAI,CAACxF,IAAI,GAAG,IAAI,CAACzO,IAAI,CAACoO,IAAI,CAAC;IAE7DY,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACR,IAAI,EAAE,YAAY,EAAE,IAAI,CAACzO,IAAI,CAACoO,IAAI,EAAE,cAAc,EAAE,IAAI,CAACpO,IAAI,CAACqO,UAAU,CAAC;IAC3GW,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAG,IAAI,CAACR,IAAI,GAAG,CAAC,EAAG,GAAG,EAAEwE,IAAI,CAACE,GAAG,CAAC,IAAI,CAAC1E,IAAI,GAAG,IAAI,CAACzO,IAAI,CAACoO,IAAI,EAAE,IAAI,CAACpO,IAAI,CAACC,aAAa,CAAC,CAAC;IAEzH,IAAI,CAACgL,SAAS,EAAE;EAClB;EAEA;EACOiJ,gBAAgBA,CAACX,KAAU;IAChCvE,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEsE,KAAK,CAAC;IAC7CvE,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEsE,KAAK,CAAClE,QAAQ,CAAC;IAE7C,IAAIkE,KAAK,CAAClE,QAAQ,IAAIkE,KAAK,CAAClE,QAAQ,KAAK,IAAI,CAACrP,IAAI,CAACoO,IAAI,EAAE;MACvDY,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACjP,IAAI,CAACoO,IAAI,EAAE,IAAI,EAAEmF,KAAK,CAAClE,QAAQ,CAAC;MAC5E,IAAI,CAACrP,IAAI,CAACoO,IAAI,GAAGmF,KAAK,CAAClE,QAAQ;MAC/B,IAAI,CAACrP,IAAI,CAACqO,UAAU,GAAG,CAAC,CAAC,CAAC;MAC1B,IAAI,CAACI,IAAI,GAAG,CAAC;MAEb;MACA,IAAI,IAAI,CAACzO,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE;QAC/B,IAAI,CAACD,IAAI,CAACsO,UAAU,GAAG2E,IAAI,CAACC,IAAI,CAAC,IAAI,CAAClT,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,IAAI,CAACoO,IAAI,CAAC;QAC1EY,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE,IAAI,CAACjP,IAAI,CAACsO,UAAU,EAAE,KAAK,EAAE,IAAI,CAACtO,IAAI,CAACC,aAAa,EAAE,sBAAsB,EAAE,IAAI,CAACD,IAAI,CAACoO,IAAI,CAAC;MACxI;MAEA;MACA,IAAI,IAAI,CAACtC,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACuD,QAAQ,GAAG,IAAI,CAACrP,IAAI,CAACoO,IAAI;QACnC,IAAI,CAACtC,IAAI,CAAC2C,IAAI,GAAG,CAAC;QAClB,IAAI,CAAC3C,IAAI,CAACsH,SAAS,GAAG,CAAC;QACvB,IAAI,CAACtH,IAAI,CAACkH,KAAK,GAAG,IAAI,CAAChT,IAAI,CAACC,aAAa;QACzC+O,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACnD,IAAI,CAACkH,KAAK,CAAC;MACxD;MAEAhE,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAACjP,IAAI,CAACoO,IAAI,EAAE,OAAO,EAAE,IAAI,CAACK,IAAI,CAAC;MACrE,IAAI,CAACxD,SAAS,EAAE;IAClB;EACF;EAEA;EACOkJ,iBAAiBA,CAACZ,KAAU;IACjCvE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEsE,KAAK,CAAC;IAE9C;IACA,IAAIA,KAAK,CAACjB,IAAI,IAAIiB,KAAK,CAACjB,IAAI,KAAK,IAAI,CAACtS,IAAI,CAACoO,IAAI,EAAE;MAC/CY,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAACjP,IAAI,CAACoO,IAAI,EAAE,IAAI,EAAEmF,KAAK,CAACjB,IAAI,CAAC;MACvF,IAAI,CAACtS,IAAI,CAACoO,IAAI,GAAGmF,KAAK,CAACjB,IAAI;MAC3B,IAAI,CAACtS,IAAI,CAACqO,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MAEb;MACA,IAAI,IAAI,CAACzO,IAAI,CAACC,aAAa,GAAG,CAAC,EAAE;QAC/B,IAAI,CAACD,IAAI,CAACsO,UAAU,GAAG2E,IAAI,CAACC,IAAI,CAAC,IAAI,CAAClT,IAAI,CAACC,aAAa,GAAG,IAAI,CAACD,IAAI,CAACoO,IAAI,CAAC;QAC1EY,OAAO,CAACC,GAAG,CAAC,0CAA0C,EAAE,IAAI,CAACjP,IAAI,CAACsO,UAAU,EAAE,KAAK,EAAE,IAAI,CAACtO,IAAI,CAACC,aAAa,EAAE,sBAAsB,EAAE,IAAI,CAACD,IAAI,CAACoO,IAAI,CAAC;MACvJ;MAEA;MACA,IAAI,IAAI,CAACtC,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACuD,QAAQ,GAAG,IAAI,CAACrP,IAAI,CAACoO,IAAI;QACnC,IAAI,CAACtC,IAAI,CAAC2C,IAAI,GAAG,CAAC;QAClB,IAAI,CAAC3C,IAAI,CAACsH,SAAS,GAAG,CAAC;QACvB,IAAI,CAACtH,IAAI,CAACkH,KAAK,GAAG,IAAI,CAAChT,IAAI,CAACC,aAAa;QACzC+O,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE,IAAI,CAACnD,IAAI,CAACkH,KAAK,CAAC;MACvE;MAEAhE,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE,IAAI,CAACjP,IAAI,CAACoO,IAAI,EAAE,OAAO,EAAE,IAAI,CAACK,IAAI,CAAC;MACpF,IAAI,CAACxD,SAAS,EAAE;IAClB;EACF;EAIOmJ,sBAAsBA,CAACb,KAAU;IACtC;IACA,MAAMc,aAAa,GAAGd,KAAK,CAACc,aAAa,IAAI,EAAE;IAC/C,IAAI,CAACjH,YAAY,GAAGiH,aAAa;EACnC;EAEA;EACOC,iBAAiBA,CAACf,KAAU;IACjC,IAAI,CAAC7E,YAAY,GAAG6E,KAAK,CAAC7E,YAAY,IAAI,EAAE;IAC5C,IAAI,CAACC,aAAa,GAChB,IAAI,CAACD,YAAY,CAACgD,MAAM,KAAK,IAAI,CAAC3F,iBAAiB,CAAC2F,MAAM;EAC9D;EAEO6C,SAASA,CAAA;IACd,IAAI,IAAI,CAAC5F,aAAa,EAAE;MACtB,IAAI,CAACD,YAAY,GAAG,EAAE;MACtB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,YAAY,GAAG,CAAC,GAAG,IAAI,CAAC3C,iBAAiB,CAAC;MAC/C,IAAI,CAAC4C,aAAa,GAAG,IAAI;IAC3B;EACF;EAEA;EACOxP,YAAYA,CAAA;IACjB;IACA,MAAMqV,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAACzU,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAAC2L,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAAC0D,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACA1R,aAAaA,CAACyV,KAAU;IACtB,MAAMsB,cAAc,GAAGtB,KAAK,CAACxV,KAAK,CAAC,CAAC;IAEpC,IAAI+W,QAAQ,GAAQ,EAAE;IACtB,IAAID,cAAc,KAAK,UAAU,EAAE;MACjCC,QAAQ,GAAG,IAAI,CAAC/I,iBAAiB;MAEjC;MACA;MACA,IAAI,CAACgJ,WAAW,CAACD,QAAQ,CAAC;IAC5B,CAAC,MAAM,IAAID,cAAc,KAAK,KAAK,EAAE;MACnC,MAAMG,gBAAgB,GAAG;QACvB3F,QAAQ,EAAE,IAAI,CAACrP,IAAI,CAACC,aAAa;QACjCgV,SAAS,EAAE,IAAI,CAACjV,IAAI,CAACwO,QAAQ;QAC7B0G,SAAS,EAAE,IAAI,CAAClV,IAAI,CAACuO,OAAO;QAC5BF,UAAU,EAAE,IAAI,CAACrO,IAAI,CAACqO;QACtB;OACD;MAED;MACA,IAAI,CAAC/C,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC;MAC9C;MACA,IAAI,CAAClB,cAAc,CAChB8J,aAAa,CAACH,gBAAgB;MAC/B;MAAA,CACCpF,SAAS,CAAE8C,IAAI,IAAI;QAClB;QACA,IAAI,CAACpH,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAImG,IAAI,CAACxB,OAAO,EAAE;UAChB,IAAI,CAACjF,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACP,GAAG,CAAC2H,YAAY,EAAE;UACvB,OAAO,CAAC;QACV;QAEA,IAAI,CAACpH,cAAc,GAAG,IAAI;QAC1B6I,QAAQ,GAAGpC,IAAI,CAACE,YAAY,CAACF,IAAI,IAAI,EAAE;QAEvC,IAAI,CAAChH,GAAG,CAAC4D,aAAa,EAAE,CAAC,CAAC;QAC1B,IAAI,CAACyF,WAAW,CAACD,QAAQ,CAAC;MAC5B,CAAC,CAAC;IACN;EACF;EAEAC,WAAWA,CAACK,WAAgB;IAC1B;IACA,IAAIN,QAAQ,GAAQM,WAAW;IAC/B,IAAIC,WAAW,GAAS,IAAI,CAAC1J,UAAU,CAAC2J,eAAe,CAAC,IAAIC,IAAI,EAAE,CAAC;IAEnEvG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE6F,QAAQ,CAAC;IAEjC;IACA,IAAIA,QAAQ,KAAKf,SAAS,IAAIe,QAAQ,CAACpD,MAAM,GAAG,CAAC,EAAE;MACjD;MACA,MAAM8D,UAAU,GAAG,QAAQ;MAE3B;MACA;MACA;MACA;MAEA;MAEA,MAAMC,WAAW,GAAG,CAClB,eAAe,EACf,aAAa,EACb,UAAU,EACV,QAAQ,EACR,UAAU,EACV,cAAc,EACd,cAAc,EAEd,iBAAiB,EACjB,kBAAkB,CACnB;MACD;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MAEA,MAAMC,iBAAiB,GAAQ,EAAE;MAEjC;MACA,MAAMC,UAAU,GAAQ,EAAE;MAE1B;MACA7Y,IAAI,CAACgY,QAAQ,EAAGc,OAAY,IAAI;QAC9B;QACA,MAAMC,QAAQ,GAAGC,KAAK,CAACL,WAAW,CAAC/D,MAAM,CAAC,CAACqE,IAAI,CAAC,IAAI,CAAC;QACrDF,QAAQ,CAAC,CAAC,CAAC,GAAGD,OAAO,CAACI,gBAAgB;QACtCH,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAAClK,UAAU,CAAC2J,eAAe,CAACM,OAAO,CAACK,UAAU,CAAC;QACjE;QACAR,WAAW,CAACtG,OAAO,CAAC,CAACsC,GAAQ,EAAEyE,CAAS,KAAI;UAC1C,MAAMC,aAAa,GAAGD,CAAC,CAAC,CAAC;UACzB,QAAQzE,GAAG;YACT,KAAK,eAAe;cAClBoE,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAAC5T,YAAY;cAC9C;YACF,KAAK,aAAa;cAChB6T,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAACxS,UAAU;cAC5C;YACF,KAAK,UAAU;cACbyS,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAACjS,cAAc;cAChD;YACF,KAAK,QAAQ;cACXkS,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAACzP,YAAY;cAC9C;YACF,KAAK,UAAU;cACb0P,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAAC9O,QAAQ;cAC1C;YACF,KAAK,cAAc;cACjB+O,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAAC9S,WAAW;cAC7C;YACF,KAAK,cAAc;cACjB+S,QAAQ,CAACM,aAAa,CAAC,GAAG,IAAI,CAACxK,UAAU,CAACxE,UAAU,CAACyO,OAAO,CAACvO,iBAAiB,CAAC;cAC/E;YACF,KAAK,iBAAiB;cACpBwO,QAAQ,CAACM,aAAa,CAAC,GAAG,IAAI,CAACxK,UAAU,CAACxE,UAAU,CAACyO,OAAO,CAAChO,oBAAoB,CAAC;cAClF;YACF,KAAK,kBAAkB;cACrBiO,QAAQ,CAACM,aAAa,CAAC,GAAGP,OAAO,CAAC3M,eAAe;cACjD;YACF;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;UACF;QACF,CAAC,CAAC;QAEF0M,UAAU,CAAC/B,IAAI,CAACiC,QAAQ,CAAC;MAC3B,CAAC,CAAC;MAEF;MACA,MAAMO,OAAO,GAAGX,WAAW,CAACjE,GAAG,CAAC,CAAC6E,MAAM,EAAEC,KAAK,MAAM;QAClDC,EAAE,EAAED,KAAK,GAAG,CAAC;QACb1I,KAAK,EAAE;OACR,CAAC,CAAC;MAEH;MACA,IAAI,CAAChC,cAAc,CAAC4K,aAAa,CAC/BhB,UAAU,EACVC,WAAW,EACXE,UAAU,EACVS;MACA;MACA;OACD;IACH,CAAC,MAAM;MACL,MAAMK,OAAO,GAAG,2CAA2C;MAC3D;IACF;EACF;EAEA;EACA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACA;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EAEA;EACOC,QAAQA,CAAA;IACb,MAAMC,QAAQ,GAAG;MACf3J,SAAS,EAAE,IAAI,CAACI,YAAY;MAC5BF,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCC,iBAAiB,EAAE,IAAI,CAACA;KACzB;IAED;IACA,IAAI,CAAC3B,kBAAkB,CAACoL,kBAAkB,CAAC;MACzC9F,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC3E,SAAS,CAACsE,MAAM;MAC7BzD,UAAU,EAAE0J,QAAQ,CAAC3J,SAAS;MAC9BE,aAAa,EAAEyJ,QAAQ,CAACzJ,aAAa;MACrC2J,QAAQ,EAAE,IAAI,CAACzK,SAAS,CAACsE;KAC1B,CAAC;IAEF1B,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE0H,QAAQ,CAAC;IACnC,IAAI,CAACpL,wBAAwB,CAACuL,WAAW,CAAC,+BAA+B,EAAE,EAAE,CAAC;IAElG;EACF;EAEQC,0BAA0BA,CAACJ,QAAa;IAC9C,MAAM9F,MAAM,GAAG;MACbC,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC3E,SAAS,CAACsE,MAAM;MAC7BzD,UAAU,EAAE0J,QAAQ,CAAC3J,SAAS;MAC9BE,aAAa,EAAEyJ,QAAQ,CAACzJ,aAAa;MACrC2J,QAAQ,EAAE,IAAI,CAACzK,SAAS,CAACsE;KAC1B;IAED,IAAI,CAAClF,kBAAkB,CAACwL,gBAAgB,CAACnG,MAAM,CAAC,CAACjB,SAAS,CAAC;MACzDrD,IAAI,EAAG0E,QAAQ,IAAI;QACjB,IAAIA,QAAQ,CAACC,OAAO,KAAK,KAAK,EAAE;UAC9BlC,OAAO,CAACC,GAAG,CAAC,qCAAqC,EAAEgC,QAAQ,CAAC;UAC9C,IAAI,CAAC1F,wBAAwB,CAACuL,WAAW,CAAC,oCAAoC,EAAE,EAAE,CAAC;UAEjG;QACF,CAAC,MAAM;UACL9H,OAAO,CAACuC,KAAK,CAAC,iCAAiC,EAAEN,QAAQ,CAACwF,OAAO,CAAC;UACpD,IAAI,CAAClL,wBAAwB,CAAC0L,SAAS,CAAChG,QAAQ,CAACwF,OAAO,EAAE,EAAE,CAAC;UAE3E;QACF;MACF,CAAC;MACDlF,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrC,IAAI,CAAChG,wBAAwB,CAAC0L,SAAS,CAAC,6BAA6B,EAAE,EAAE,CAAC;QAE1F;MACF;KACD,CAAC;EACJ;EAEQC,iBAAiBA,CAAA;IACvB;IACA,MAAMC,YAAY,GAAG;MACnBrG,QAAQ,EAAE,SAAS;MACnBC,MAAM,EAAE,IAAI,CAAC3E,SAAS,CAACsE;KACxB;IAED,IAAI,CAAClF,kBAAkB,CAAC4L,gBAAgB,CAACD,YAAY,CAAC,CAACvH,SAAS,CAAC;MAC/DrD,IAAI,EAAG0E,QAAQ,IAAI;QACjBjC,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEgC,QAAQ,CAAC;QACnD;QACA,IAAI,CAAC8F,0BAA0B,CAAC;UAC9B/J,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ,CAAC;MACDiE,KAAK,EAAGA,KAAK,IAAI;QACfvC,OAAO,CAACuC,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzD;QACA,IAAI,CAACwF,0BAA0B,CAAC;UAC9B/J,SAAS,EAAE,EAAE;UACbE,aAAa,EAAE,IAAI,CAACI,cAAc;UAClCH,iBAAiB,EAAE,IAAI,CAACG;SACzB,CAAC;MACJ;KACD,CAAC;EACJ;EAEO7N,UAAUA,CAAA;IACfuP,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;IAEnD;IACA,IAAI,CAACjC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACC,iBAAiB,GAAG,EAAE;IAE3B;IACA,IAAI,CAAC3B,kBAAkB,CAAC6L,qBAAqB,CAAC,SAAS,CAAC;IAExD;IACA,IAAI,CAACtI,sBAAsB,EAAE;IAE7B;IACA,IAAI,CAACrD,GAAG,CAAC4D,aAAa,EAAE;IAExB;IACA,IAAI,IAAI,CAACxD,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC0D,OAAO,EAAE;IACrB;IAEA;IACAR,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;IAC9C;EACF;EAEA;EACOhQ,GAAGA,CAAA;IACR;IACA,IAAI,CAAC0C,IAAI,CAAC,CAAC,CAAC;EACd;EAEO2V,IAAIA,CAAC1V,QAAgB;IAC1B,IAAI,CAACuJ,MAAM,CAACoM,QAAQ,CAAC,CAAC,eAAe,EAAE3V,QAAQ,CAAC,EAAE;MAChD4V,WAAW,EAAE;QAAEC,IAAI,EAAE;MAAa;KACnC,CAAC;EACJ;EAEO9V,IAAIA,CAACC,QAAgB;IAC1B,IAAIA,QAAQ,IAAI,CAAC,EAAE;MACjB,MAAM8V,MAAM,GAAG,IAAI,CAAC3L,iBAAiB,CAAC4L,IAAI,CACvCC,CAAC,IAAKA,CAAC,CAAChW,QAAQ,KAAKA,QAAQ,CAC/B;MACD,MAAMiW,eAAe,GAKjB;QACFzJ,IAAI,EAAE,IAAI;QAAE;QACZ0J,QAAQ,EAAE,QAAQ;QAAE;QACpBC,QAAQ,EAAE,KAAK;QAAE;QACjBC,UAAU,EAAE,IAAI,CAAE;OACnB;MAED;MACA,IAAI,CAAC1M,eAAe,CAACuE,cAAc,CAACtD,IAAI,CAAC,IAAI,CAAC;MAE9C;MACA,MAAM0L,QAAQ,GAAG,IAAI,CAACxM,YAAY,CAACyM,IAAI,CACrChb,oBAAoB,EACpB2a,eAAe,CAChB;MACD;MACAI,QAAQ,CAACE,iBAAiB,CAAC5B,EAAE,GAAG3U,QAAQ;MACxCqW,QAAQ,CAACE,iBAAiB,CAACT,MAAM,GAAGA,MAAM;MAC1C;MACAO,QAAQ,CAACE,iBAAiB,CAACC,SAAS,CAACxI,SAAS,CAC3CyI,aAAsB,IAAI;QACzB,IAAIA,aAAa,KAAK,IAAI,EAAE;UAC1B;UACA,IAAI,CAACpN,SAAS,EAAE;QAClB;MACF,CAAC,CACF;IACH,CAAC,MAAM;MACL,IAAI,CAACE,MAAM,CAACoM,QAAQ,CAAC,CAAC,eAAe,EAAE3V,QAAQ,CAAC,EAAE;QAChD4V,WAAW,EAAE;UAAEC,IAAI,EAAE;QAAa;OACnC,CAAC;IACJ;EACF;EACAa,SAASA,CAACC,OAAY;IACpB,IAAI,CAAC9M,YAAY,CAACyM,IAAI,CAACK,OAAO,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACrD;EAEAC,aAAaA,CAAA;IACXzJ,OAAO,CAACC,GAAG,CAAC,gBAAgB,CAAC;IAC7B;EACF;EAEOyJ,MAAMA,CAAC9W,QAAgB;IAC5B,IAAI+W,OAAO,CAAC,8CAA8C,CAAC,EAAE;MAC3D,IAAI,CAACtN,cAAc,CAACuN,YAAY,CAAC;QAAEhX;MAAQ,CAAE,CAAC,CAACgO,SAAS,CAAC;QACvDrD,IAAI,EAAG0E,QAAa,IAAI;UACtB,IAAIA,QAAQ,CAACwF,OAAO,EAAE;YACpB;YACY,IAAI,CAAClL,wBAAwB,CAACuL,WAAW,CAAC,6BAA6B,EAAE,EAAE,CAAC;YAExF,IAAI,CAAC7L,SAAS,EAAE;UAClB;QACF,CAAC;QACDsG,KAAK,EAAGA,KAAU,IAAI;UACpBvC,OAAO,CAACuC,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;UACvB,IAAI,CAAChG,wBAAwB,CAAC0L,SAAS,CAAC,uBAAuB,EAAE,EAAE,CAAC;UAElF;QACF;OACD,CAAC;IACJ;EACF;EAEA;EACO9P,UAAUA,CAAC0R,UAAkB;IAClC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,OAAO,IAAItD,IAAI,CAACsD,UAAU,CAAC,CAACC,kBAAkB,EAAE;EAClD;EAEOpU,cAAcA,CAAClE,MAAc;IAClC,QAAQA,MAAM;MACZ,KAAK,UAAU;QACb,OAAO,qBAAqB;MAC9B,KAAK,mBAAmB;QACtB,OAAO,qBAAqB;MAC9B,KAAK,SAAS;QACZ,OAAO,oBAAoB;MAC7B,KAAK,SAAS;QACZ,OAAO,kBAAkB;MAC3B;QACE,OAAO,uBAAuB;IAClC;EACF;EAEOiD,gBAAgBA,CAAC/C,QAAgB;IACtC,OAAOA,QAAQ,KAAK,SAAS,GACzB,qBAAqB,GACrB,uBAAuB;EAC7B;EAEAqY,WAAWA,CAAC7C,CAAM;IAChB,IAAI,CAAC/J,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC2C,YAAY,GAAGoH,CAAC,IAAI,KAAK;IAC9B,IAAI,CAAC7K,cAAc,CAAC0N,WAAW,CAAC;MAAEC,cAAc,EAAE,CAAC;MAAElK,YAAY,EAAE,IAAI,CAACA,YAAY;MAAEmK,SAAS,EAAE;IAAI,CAAE,CAAC,CAACrJ,SAAS,CAAC;MACjHrD,IAAI,EAAG2M,GAAQ,IAAI;QACjB,IAAI,CAAC/M,SAAS,GAAG,KAAK;QACtB6C,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiK,GAAG,CAAC;QAElC;QACA,MAAMtG,YAAY,GAAGsG,GAAG,EAAEtG,YAAY,IAAIsG,GAAG;QAE7C,IAAItG,YAAY,EAAE1B,OAAO,EAAE;UACzB;UACc,IAAI,CAAC3F,wBAAwB,CAAC0L,SAAS,CAACrE,YAAY,CAACuG,YAAY,IAAG,uBAAuB,EAAE,EAAE,CAAC;QAEhH,CAAC,MAAM,IAAIvG,YAAY,EAAEwG,OAAO,KAAK,KAAK,EAAE;UAC1C;UACA,IAAIxG,YAAY,CAAC6D,OAAO,KAAK,oCAAoC,EAAE;YACjE;YACY,IAAI,CAAClL,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAEjJ,CAAC,MAAM,IAAIrE,YAAY,CAAC6D,OAAO,KAAK,mCAAmC,EAAE;YACvE;YAEY,IAAI,CAAClL,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAChJ,CAAC,MAAM;YACkB,IAAI,CAAC1L,wBAAwB,CAAC0L,SAAS,CAACrE,YAAY,CAAC6D,OAAO,IAAG,uBAAuB,EAAE,EAAE,CAAC;YAEnH;UACF;QACF,CAAC,MAAM;UACS,IAAI,CAAClL,wBAAwB,CAACuL,WAAW,CAAC,8BAA8B,EAAE,EAAE,CAAC;UAE3F;QACF;QACA,IAAI,CAACpL,GAAG,CAAC2H,YAAY,EAAE;MACzB,CAAC;MACD9B,KAAK,EAAG8H,GAAQ,IAAI;QAClB,IAAI,CAAClN,SAAS,GAAG,KAAK;QACtB;QACA6C,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEoK,GAAG,CAAC;QACnCrK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,OAAOoK,GAAG,CAAC;QACtCrK,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEqK,MAAM,CAACC,IAAI,CAACF,GAAG,IAAI,EAAE,CAAC,CAAC;QAClDrK,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEoK,GAAG,EAAE7Y,MAAM,CAAC;QACzCwO,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEoK,GAAG,EAAE5C,OAAO,CAAC;QAC3CzH,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEoK,GAAG,EAAE9H,KAAK,CAAC;QAEvC;QACA;QACA,IAAI8H,GAAG,EAAED,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAIC,GAAG,CAAC5C,OAAO,KAAK,oCAAoC,EAAE;YAChC,IAAI,CAAClL,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAIoC,GAAG,CAAC5C,OAAO,KAAK,mCAAmC,EAAE;YACtC,IAAI,CAAClL,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YAAyB,IAAI,CAAC1L,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAGjK;UACF;QACF,CAAC,MAAM,IAAIoC,GAAG,EAAE9H,KAAK,EAAEkF,OAAO,EAAE;UAC9B,IAAI4C,GAAG,CAAC9H,KAAK,CAACkF,OAAO,KAAK,oCAAoC,EAAE;YACtC,IAAI,CAAClL,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAIoC,GAAG,CAAC9H,KAAK,CAACkF,OAAO,KAAK,mCAAmC,EAAE;YAC5C,IAAI,CAAClL,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAAC1L,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;YACA;UACF;QACF,CAAC,MAAM,IAAIoC,GAAG,EAAE7Y,MAAM,KAAK,GAAG,EAAE;UACN,IAAI,CAAC+K,wBAAwB,CAAC0L,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAE3J;UACA;QACF,CAAC,MAAM;UACmB,IAAI,CAAC1L,wBAAwB,CAAC0L,SAAS,CAAC,wBAAwB,EAAE,EAAE,CAAC;UAE7F;QACF;QACAjI,OAAO,CAACuC,KAAK,CAAC8H,GAAG,CAAC;QAClB,IAAI,CAAC3N,GAAG,CAAC2H,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEAmG,cAAcA,CAAA;IACZ;IACA;IACAxK,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAEA;EACA5P,mBAAmBA,CAACkU,KAAa;IAC/B,IAAI,CAACnT,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;IACpD4O,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAAC7O,mBAAmB,CAAC;IAEhE,IAAI,IAAI,CAACA,mBAAmB,IAAImT,KAAK,EAAE;MACrC,MAAMkG,MAAM,GAAGlG,KAAK,CAACmG,MAAqB;MAC1C,MAAMC,IAAI,GAAGF,MAAM,CAACG,qBAAqB,EAAE;MAC3C,IAAI,CAACvb,WAAW,GAAGsb,IAAI,CAACE,MAAM,GAAGC,MAAM,CAACC,OAAO;MAC/C,IAAI,CAACzb,YAAY,GAAGqb,IAAI,CAACK,IAAI,GAAGF,MAAM,CAACG,OAAO;MAC9CjL,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC5Q,WAAW,EAAE,IAAI,CAACC,YAAY,CAAC;IACxE;EACF;EAEAN,kBAAkBA,CAAA;IAChB,IAAI,CAACoC,mBAAmB,GAAG,KAAK;IAChC4O,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAGAiL,eAAeA,CAAC3G,KAAY;IAC1B,MAAMmG,MAAM,GAAGnG,KAAK,CAACmG,MAAqB;IAC1C,MAAMS,QAAQ,GAAGT,MAAM,CAACU,OAAO,CAAC,kBAAkB,CAAC;IACnD,IAAI,CAACD,QAAQ,IAAI,IAAI,CAAC/Z,mBAAmB,EAAE;MACzC,IAAI,CAACpC,kBAAkB,EAAE;IAC3B;EACF;EAEO8D,cAAcA,CAACuY,SAAiB;IACrC,OAAO,IAAI,CAACjN,YAAY,CAACkN,QAAQ,CAACD,SAAS,CAAC;EAC9C;;qCA5/CWnP,mBAAmB,EAAA/N,EAAA,CAAAod,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAtd,EAAA,CAAAod,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAAvd,EAAA,CAAAod,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAAzd,EAAA,CAAAod,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAA3d,EAAA,CAAAod,iBAAA,CAAAQ,EAAA,CAAAC,wBAAA,GAAA7d,EAAA,CAAAod,iBAAA,CAAAU,EAAA,CAAAC,kBAAA,GAAA/d,EAAA,CAAAod,iBAAA,CAAAY,EAAA,CAAAC,QAAA,GAAAje,EAAA,CAAAod,iBAAA,CAAApd,EAAA,CAAAke,iBAAA,GAAAle,EAAA,CAAAod,iBAAA,CAAAe,EAAA,CAAAC,UAAA,GAAApe,EAAA,CAAAod,iBAAA,CAAAiB,EAAA,CAAA5P,cAAA,GAAAzO,EAAA,CAAAod,iBAAA,CAAAkB,EAAA,CAAAC,eAAA;EAAA;;UAAnBxQ,mBAAmB;IAAAyQ,SAAA;IAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;QAAnB3e,EAAA,CAAAI,UAAA,mBAAAye,6CAAAve,MAAA;UAAA,OAAAse,GAAA,CAAA7B,eAAA,CAAAzc,MAAA,CAAuB;QAAA,UAAAN,EAAA,CAAA8e,iBAAA,CAAJ;;;;;;;;;QCxChC9e,EAAA,CAAAmC,UAAA,IAAA4c,kCAAA,iBAAqE;QAUnE/e,EADF,CAAAC,cAAA,aAA4B,uBAgCzB;QAFCD,EAbA,CAAAI,UAAA,6BAAA4e,mEAAA1e,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0e,GAAA;UAAA,OAAAjf,EAAA,CAAAc,WAAA,CAAmB8d,GAAA,CAAAzH,iBAAA,CAAA7W,MAAA,CAAyB;QAAA,EAAC,0BAAA4e,gEAAA5e,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0e,GAAA;UAAA,OAAAjf,EAAA,CAAAc,WAAA,CAQ7B8d,GAAA,CAAArI,YAAA,CAAAjW,MAAA,CAAoB;QAAA,EAAC,wBAAA6e,8DAAA7e,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0e,GAAA;UAAA,OAAAjf,EAAA,CAAAc,WAAA,CACvB8d,GAAA,CAAA/H,UAAA,CAAAvW,MAAA,CAAkB;QAAA,EAAC,4BAAA8e,kEAAA9e,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0e,GAAA;UAAA,OAAAjf,EAAA,CAAAc,WAAA,CACf8d,GAAA,CAAA7H,gBAAA,CAAAzW,MAAA,CAAwB;QAAA,EAAC,6BAAA+e,mEAAA/e,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0e,GAAA;UAAA,OAAAjf,EAAA,CAAAc,WAAA,CACxB8d,GAAA,CAAA5H,iBAAA,CAAA1W,MAAA,CAAyB;QAAA,EAAC,wBAAAgf,8DAAAhf,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0e,GAAA;UAAA,OAAAjf,EAAA,CAAAc,WAAA,CAC/B8d,GAAA,CAAAlI,YAAA,CAAApW,MAAA,CAAoB;QAAA,EAAC,oCAAAif,0EAAAjf,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAA0e,GAAA;UAAA,OAAAjf,EAAA,CAAAc,WAAA,CACT8d,GAAA,CAAA3H,sBAAA,CAAA3W,MAAA,CAA8B;QAAA,EAAC;QAkKzDN,EA/JA,CAAAmC,UAAA,IAAAqd,0CAAA,2BAAsC,IAAAC,0CAAA,yBAyFA,IAAAC,0CAAA,yBA0DA,IAAAC,2CAAA,4BAYW;QA2drD3f,EADE,CAAAG,YAAA,EAAa,EACT;QAGNH,EAAA,CAAAmC,UAAA,IAAAyd,kCAAA,iBAAsD;;;QAvqBhD5f,EAAA,CAAA2C,UAAA,SAAAic,GAAA,CAAA7P,OAAA,IAAA6P,GAAA,CAAA5P,SAAA,CAA0B;QAY5BhP,EAAA,CAAAyC,SAAA,GAAiB;QA4BjBzC,EA5BA,CAAA2C,UAAA,SAAAic,GAAA,CAAA/P,QAAA,CAAiB,aAAA+P,GAAA,CAAA/b,IAAA,CAAAoO,IAAA,CACK,SAAA2N,GAAA,CAAA7N,IAAA,CACT,aAAA/Q,EAAA,CAAA6f,eAAA,KAAAC,GAAA,EAAA9f,EAAA,CAAAkF,eAAA,KAAA6a,GAAA,GAOX,UAAAnB,GAAA,CAAA/b,IAAA,CAAAC,aAAA,CAC0B,aAAA9C,EAAA,CAAAkF,eAAA,KAAA8a,GAAA,EACsB,oBAC/B,eAAAhgB,EAAA,CAAAkF,eAAA,KAAA+a,GAAA,EACoC,qBAEnC,oBAED,eACL,SAAArB,GAAA,CAAAtN,IAAA,CACD,WAAAsN,GAAA,CAAApP,MAAA,CACI,eAAAxP,EAAA,CAAAkF,eAAA,KAAAgb,GAAA,EACc,kBAOd;QAiKgBlgB,EAAA,CAAAyC,SAAA,GAAc;QAAdzC,EAAA,CAAA2C,UAAA,YAAAic,GAAA,CAAA1O,WAAA,CAAc;QA8d7ClQ,EAAA,CAAAyC,SAAA,EAAqB;QAArBzC,EAAA,CAAA2C,UAAA,UAAAic,GAAA,CAAA9P,cAAA,CAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
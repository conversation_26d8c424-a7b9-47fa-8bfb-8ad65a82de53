import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subscription, combineLatest } from 'rxjs';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { ProjectsService } from '../../services/projects.service';
import { PermitsService } from '../../services/permits.service';
import { AppService } from '../../services/app.service';
import { ProjectPopupComponent } from '../project-popup/project-popup.component';

@Component({
  selector: 'app-project-view',
  templateUrl: './project-view.component.html',
  styleUrls: ['./project-view.component.scss']
})
export class ProjectViewComponent implements OnInit, OnDestroy {
  public projectId: number | null = null;
  public project: any = null;
  public isLoading: boolean = false;
  public selectedTab: string = 'details';
  public projectPermits: any[] = [];
  public loginUser: any = {};
  private routeSubscription: Subscription = new Subscription();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private cdr: ChangeDetectorRef,
    private appService: AppService,
    private projectsService: ProjectsService,
    private permitsService: PermitsService,
    private modalService: NgbModal
  ) {}

  ngOnInit(): void {
    this.loginUser = this.appService.getLoggedInUser();

    // Combine route params and query params to handle both together
    this.routeSubscription = combineLatest([
      this.route.paramMap,
      this.route.queryParams
    ]).subscribe(([paramMap, queryParams]) => {
      const idParam = paramMap.get('id');
      this.projectId = idParam ? Number(idParam) : null;

      console.log('Project view - received params:', { projectId: this.projectId, queryParams });
      
      // Handle active tab from query params
      const activeTab = queryParams['activeTab'];
      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {
        this.selectedTab = activeTab;
        console.log('Setting selectedTab from query params:', activeTab);
      } else {
        console.log('No valid activeTab found, keeping default:', this.selectedTab);
      }

      if (this.projectId) {
        this.fetchProjectDetails();
        this.fetchProjectPermits();
      }
      
      this.cdr.markForCheck();
    });
  }

  ngOnDestroy(): void {
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }

  public fetchProjectDetails(): void {
    if (!this.projectId) { return; }
    this.isLoading = true;
    this.projectsService.getProject({ projectId: this.projectId }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        console.log('Project API Response:', res);
        if (!res?.isFault) {
          // Try different response structures
          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;
          console.log('Project data assigned:', this.project);
          console.log('Project fields available:', Object.keys(this.project || {}));
          // Don't override selectedTab here - let query params handle it
        } else {
          console.error('API returned fault:', res.faultMessage);
          this.project = null;
        }
        this.cdr.markForCheck();
      },
      error: (err) => {
        this.isLoading = false;
        console.error('Error fetching project details:', err);
        this.cdr.markForCheck();
      }
    });
  }

  public fetchProjectPermits(): void {
    if (!this.projectId) { return; }
    this.isLoading = true;
    
    // Get permits for this specific project
    this.permitsService.getPermitsForKendoGrid({
      take: 100,
      skip: 0,
      sort: [],
      filter: {
        logic: 'and',
        filters: [
          {
            field: 'projectId',
            operator: 'eq',
            value: this.projectId
          }
        ]
      },
      search: '',
      loggedInUserId: this.loginUser.userId
    }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        console.log('Project permits API response:', res);
        if (res?.isFault) {
          console.error('Failed to load project permits:', res.faultMessage);
          this.projectPermits = [];
        } else {
          const rawPermits = res.responseData?.data || res.data || [];
          // Client-side guard: ensure only permits for this project are shown
          this.projectPermits = (rawPermits || []).filter((p: any) => {
            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;
            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;
          });
          console.log('Project permits assigned (filtered):', this.projectPermits);
        }
        this.cdr.markForCheck();
      },
      error: (err: any) => {
        this.isLoading = false;
        console.error('Error loading project permits:', err);
        this.projectPermits = [];
        this.cdr.markForCheck();
      }
    });
  }

  public goBack(): void {
    this.router.navigate(['/projects/list']);
  }

  public editProject(): void {
    if (!this.projectId) { return; }
    
    const NgbModalOptions: {
      size: string;
      backdrop: boolean | 'static';
      keyboard: boolean;
      scrollable: boolean;
    } = {
      size: 'lg', // Large modal size
      backdrop: 'static', // Prevents closing when clicking outside
      keyboard: false, // Disables closing with the Escape key
      scrollable: true, // Allows scrolling inside the modal
    };

    // Open the modal and load the ProjectPopup
    const modalRef = this.modalService.open(
      ProjectPopupComponent,
      NgbModalOptions
    );
    
    // Pass the selected ID to the modal component (0 for new, existing ID for edit)
    modalRef.componentInstance.id = this.projectId;
    modalRef.componentInstance.project = this.project;
    
    // Subscribe to the modal event when it closes
    modalRef.result.then(
      (result) => {
        // Handle successful edit
        if (result) {
          console.log('Project edited successfully:', result);
          // Refresh project details
          this.fetchProjectDetails();
        }
      },
      (reason) => {
        // Handle modal dismissal
        console.log('Modal dismissed:', reason);
      }
    );
  }

  public viewPermit(permitId: number): void {
    this.router.navigate(['/permits/view', permitId], { 
      queryParams: { from: 'project', projectId: this.projectId } 
    });
  }

  public onStatusChange(permit: any, newStatus: string): void {
    if (!permit?.permitId || !newStatus) { return; }
    const allowed = [
      'Approved',
      'Pacifica Verification',
      'Dis-Approved',
      'Pending',
      'Not Required',
      'In Review',
      '1 Cycle Completed'
    ];
    if (!allowed.includes(newStatus)) { return; }

    const previous = permit.internalReviewStatus;
    permit.internalReviewStatus = newStatus;
    this.isLoading = true;
    this.cdr.markForCheck();

    this.permitsService
      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })
      .subscribe({
        next: (res: any) => {
          const isFault = res?.isFault || res?.responseData?.isFault;
          if (isFault) {
            permit.internalReviewStatus = previous;
            this.isLoading = false;
            this.cdr.markForCheck();
          }
          this.isLoading = false;
          this.cdr.markForCheck();
        },
        error: () => {
          permit.internalReviewStatus = previous;
          this.isLoading = false;
          this.cdr.markForCheck();
        }
      });
  }

  public getStatusClass(status: string): string {
    if (!status) return 'status-n-a';
    const key = status.toLowerCase();
    if (key === 'current') return 'status-active';
    if (key === 'completed') return 'status-completed';
    if (key === 'cancelled & archived') return 'status-cancelled';
    if (key === 'closed & archived') return 'status-cancelled';
    return 'status-' + status.toLowerCase().replace(/\s+/g, '-').replace(/\//g, '-');
  }

  showTab(tab: string, $event: any) {
    this.selectedTab = tab;
    this.cdr.markForCheck();
  }
}

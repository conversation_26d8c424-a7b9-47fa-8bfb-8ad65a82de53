import { Component, OnInit } from '@angular/core';
import { LayoutService } from '../../core/layout.service';
import { AppService } from 'src/app/modules/services/app.service';

@Component({
  selector: 'app-topbar',
  templateUrl: './topbar.component.html',
  styleUrls: ['./topbar.component.scss'],
})
export class TopbarComponent implements OnInit {
  toolbarButtonMarginClass = 'ms-1 ms-lg-3';
  toolbarButtonHeightClass = 'w-30px h-30px w-md-40px h-md-40px';
  toolbarUserAvatarHeightClass = 'symbol-30px symbol-md-40px';
  toolbarButtonIconSizeClass = 'svg-icon-1';
  headerLeft: string = 'menu';
  initials: string = '';

  constructor(private layout: LayoutService, private appService: AppService) {}

  ngOnInit(): void {
    this.headerLeft = this.layout.getProp('header.left') as string;
    const user = this.appService.getLoggedInUser();
    this.initials = this.appService.getUserInitials(user);
  }
}

{"ast": null, "code": "import { each } from 'lodash';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/permits.service\";\nimport * as i3 from \"../../services/http-utils.service\";\nimport * as i4 from \"../../services/custom-layout.utils.service\";\nimport * as i5 from \"../../services/kendo-column.service\";\nimport * as i6 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i7 from \"../../services/app.service\";\nimport * as i8 from \"../../services/exceljs.service\";\nimport * as i9 from \"src/app/_metronic/layout/core/page-info.service\";\nimport * as i10 from \"@angular/common\";\nimport * as i11 from \"@angular/forms\";\nimport * as i12 from \"@progress/kendo-angular-grid\";\nimport * as i13 from \"@progress/kendo-angular-inputs\";\nimport * as i14 from \"@progress/kendo-angular-dropdowns\";\nimport * as i15 from \"@progress/kendo-angular-buttons\";\nimport * as i16 from \"ng-inline-svg-2\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nconst _c7 = () => ({\n  text: \"All\",\n  value: null\n});\nfunction PermitListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"span\", 12);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 13);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitListComponent_ng_template_4_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_div_18_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.onExportClick({\n        value: \"all\"\n      });\n      ctx_r2.closeExcelDropdown();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(2, \"All\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_div_18_Template_a_click_3_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      ctx_r2.onExportClick({\n        value: \"selected\"\n      });\n      ctx_r2.closeExcelDropdown();\n      return i0.ɵɵresetView($event.preventDefault());\n    });\n    i0.ɵɵtext(4, \"Page Results\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"top\", ctx_r2.dropdownTop, \"px\")(\"left\", ctx_r2.dropdownLeft, \"px\");\n  }\n}\nfunction PermitListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 14)(1, \"kendo-textbox\", 15);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function PermitListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function PermitListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 16)(4, \"span\", 17);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 18);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 19);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 20);\n    i0.ɵɵtext(10, \" Add \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"button\", 21);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(12, \"i\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 23, 1)(15, \"button\", 24, 2);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_15_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExcelDropdown($event));\n    });\n    i0.ɵɵelement(17, \"i\", 25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, PermitListComponent_ng_template_4_div_18_Template, 5, 4, \"div\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_19_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(20, \"i\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"button\", 29);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_4_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(22, \"i\", 30);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"show\", ctx_r2.isExcelDropdownOpen);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isExcelDropdownOpen);\n  }\n}\nfunction PermitListComponent_ng_template_5_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35)(2, \"div\", 36)(3, \"label\", 37);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 38);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 36)(7, \"label\", 37);\n    i0.ɵɵtext(8, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_template_5_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.category, $event) || (ctx_r2.appliedFilters.category = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 40)(11, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵtext(12, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAdvancedFilters());\n    });\n    i0.ɵɵtext(14, \" Clear Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_5_div_0_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.showAdvancedFilters = false);\n    });\n    i0.ɵɵtext(16, \" Hide Filters \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.categories);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.category);\n  }\n}\nfunction PermitListComponent_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, PermitListComponent_ng_template_5_div_0_Template, 17, 4, \"div\", 33);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction PermitListComponent_ng_template_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_template_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.showAdvancedFilters = !ctx_r2.showAdvancedFilters);\n    });\n    i0.ɵɵelement(1, \"i\", 45);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.showAdvancedFilters ? \"Hide\" : \"Show\", \" Advanced Filters \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 64)(1, \"a\", 65);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template_a_click_1_listener() {\n      const dataItem_r8 = i0.ɵɵrestoreView(_r7).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r8.permitId));\n    });\n    i0.ɵɵelement(2, \"i\", 66);\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 62);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_1_ng_template_1_Template, 3, 0, \"ng-template\", 63);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 80)(\"sortable\", false)(\"filterable\", false)(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r9 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r9.permitNumber);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r10 = ctx.$implicit;\n    const column_r11 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r11)(\"filter\", filter_r10)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 67);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_1_Template, 2, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_2_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"permitNumber\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 69);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r12 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r12.permitName);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r13 = ctx.$implicit;\n    const column_r14 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r14)(\"filter\", filter_r13)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 71);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_1_Template, 2, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_3_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"permitName\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r15 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r15.projectName, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r16 = ctx.$implicit;\n    const column_r17 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r17)(\"filter\", filter_r16)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 72);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_4_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 180)(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"projectName\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r18 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r18.permitType, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r19 = ctx.$implicit;\n    const column_r20 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r20)(\"filter\", filter_r19)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 73);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_5_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 200)(\"headerStyle\", i0.ɵɵpureFunction0(4, _c6))(\"hidden\", ctx_r2.getHiddenField(\"permitType\"))(\"filterable\", true);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r21 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getCategoryClass(dataItem_r21.permitCategory));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r21.permitCategory, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r22 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r22);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r22 = i0.ɵɵrestoreView(_r22);\n      const filterService_r24 = ctx_r22.filterService;\n      const column_r25 = ctx_r22.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r25.field, filterService_r24));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r25 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r25)(\"filter\", filter_r26)(\"data\", ctx_r2.advancedFilterOptions.categories);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 74);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_6_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 120);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r27 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r27.internalReviewStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r27.internalReviewStatus, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r28 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r28);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.internalReviewStatus, $event) || (ctx_r2.appliedFilters.internalReviewStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r28 = i0.ɵɵrestoreView(_r28);\n      const filterService_r30 = ctx_r28.filterService;\n      const column_r31 = ctx_r28.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r31.field, filterService_r30));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r32 = ctx.$implicit;\n    const column_r31 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r31)(\"filter\", filter_r32)(\"data\", ctx_r2.advancedFilterOptions.categoriess);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.internalReviewStatus);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 77);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_7_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r33 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r33.permitReviewType));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r33.permitReviewType, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r34 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r34);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.internalReviewStatus, $event) || (ctx_r2.appliedFilters.internalReviewStatus = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r34 = i0.ɵɵrestoreView(_r34);\n      const filterService_r36 = ctx_r34.filterService;\n      const column_r37 = ctx_r34.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r37.field, filterService_r36));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r38 = ctx.$implicit;\n    const column_r37 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r37)(\"filter\", filter_r38)(\"data\", ctx_r2.advancedFilterOptions.categoriess);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.internalReviewStatus);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 78);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_8_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 75);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r39 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r39.permitStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r39.permitStatus, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 76);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template_kendo_dropdownlist_ngModelChange_0_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"valueChange\", function PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r40 = i0.ɵɵrestoreView(_r40);\n      const filterService_r42 = ctx_r40.filterService;\n      const column_r43 = ctx_r40.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onMenuDropdownChange($event, column_r43.field, filterService_r42));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r44 = ctx.$implicit;\n    const column_r43 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"column\", column_r43)(\"filter\", filter_r44)(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵproperty(\"valuePrimitive\", true)(\"defaultItem\", i0.ɵɵpureFunction0(6, _c7));\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 79);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_1_Template, 2, 2, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_9_ng_template_2_Template, 1, 7, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 150);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r45 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r45.location, \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r46 = ctx.$implicit;\n    const column_r47 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r47)(\"filter\", filter_r46)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 80);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_10_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 200);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r48 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r48.permitAppliedDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r49 = ctx.$implicit;\n    const column_r50 = ctx.column;\n    const filterService_r51 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r50)(\"filter\", filter_r49)(\"filterService\", filterService_r51);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 81);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_11_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r52 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r52.permitExpirationDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r53 = ctx.$implicit;\n    const column_r54 = ctx.column;\n    const filterService_r55 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r54)(\"filter\", filter_r53)(\"filterService\", filterService_r55);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 83);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_12_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r56 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r56.permitFinalDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r57 = ctx.$implicit;\n    const column_r58 = ctx.column;\n    const filterService_r59 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r58)(\"filter\", filter_r57)(\"filterService\", filterService_r59);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 84);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_13_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r60 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r60.permitCompleteDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 82);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r61 = ctx.$implicit;\n    const column_r62 = ctx.column;\n    const filterService_r63 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r62)(\"filter\", filter_r61)(\"filterService\", filterService_r63);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 85);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_14_ng_template_2_Template, 7, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r64 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r64.attentionReason || \"\", \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r65 = ctx.$implicit;\n    const column_r66 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r66)(\"filter\", filter_r65)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 86);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_15_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 180);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r67 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r67.lastUpdatedDate), \" \");\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 70);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r68 = ctx.$implicit;\n    const column_r69 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r69)(\"filter\", filter_r68)(\"extra\", false);\n  }\n}\nfunction PermitListComponent_ng_container_7_kendo_grid_column_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 87);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_1_Template, 1, 1, \"ng-template\", 63)(2, PermitListComponent_ng_container_7_kendo_grid_column_16_ng_template_2_Template, 2, 3, \"ng-template\", 68);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"width\", 130);\n  }\n}\nfunction PermitListComponent_ng_container_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitListComponent_ng_container_7_kendo_grid_column_1_Template, 2, 6, \"kendo-grid-column\", 46)(2, PermitListComponent_ng_container_7_kendo_grid_column_2_Template, 3, 5, \"kendo-grid-column\", 47)(3, PermitListComponent_ng_container_7_kendo_grid_column_3_Template, 3, 5, \"kendo-grid-column\", 48)(4, PermitListComponent_ng_container_7_kendo_grid_column_4_Template, 3, 4, \"kendo-grid-column\", 49)(5, PermitListComponent_ng_container_7_kendo_grid_column_5_Template, 3, 5, \"kendo-grid-column\", 50)(6, PermitListComponent_ng_container_7_kendo_grid_column_6_Template, 3, 1, \"kendo-grid-column\", 51)(7, PermitListComponent_ng_container_7_kendo_grid_column_7_Template, 3, 1, \"kendo-grid-column\", 52)(8, PermitListComponent_ng_container_7_kendo_grid_column_8_Template, 3, 1, \"kendo-grid-column\", 53)(9, PermitListComponent_ng_container_7_kendo_grid_column_9_Template, 3, 1, \"kendo-grid-column\", 54)(10, PermitListComponent_ng_container_7_kendo_grid_column_10_Template, 3, 1, \"kendo-grid-column\", 55)(11, PermitListComponent_ng_container_7_kendo_grid_column_11_Template, 3, 1, \"kendo-grid-column\", 56)(12, PermitListComponent_ng_container_7_kendo_grid_column_12_Template, 3, 1, \"kendo-grid-column\", 57)(13, PermitListComponent_ng_container_7_kendo_grid_column_13_Template, 3, 1, \"kendo-grid-column\", 58)(14, PermitListComponent_ng_container_7_kendo_grid_column_14_Template, 3, 1, \"kendo-grid-column\", 59)(15, PermitListComponent_ng_container_7_kendo_grid_column_15_Template, 3, 1, \"kendo-grid-column\", 60)(16, PermitListComponent_ng_container_7_kendo_grid_column_16_Template, 3, 1, \"kendo-grid-column\", 61);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r70 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitNumber\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"projectName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitType\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitCategory\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"internalReviewStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitReviewType\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"location\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitAppliedDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitExpirationDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitFinalDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"permitCompleteDate\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"attentionReason\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r70 === \"lastUpdatedDate\");\n  }\n}\nfunction PermitListComponent_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r71 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 88)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"i\", 89);\n    i0.ɵɵelementStart(3, \"h4\");\n    i0.ɵɵtext(4, \"No Permits Found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"No permits match your current search criteria.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 90);\n    i0.ɵɵlistener(\"click\", function PermitListComponent_div_8_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r71);\n      const ctx_r2 = i0.ɵɵnextContext();\n      ctx_r2.clearSearch();\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵtext(8, \" Clear Search \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nexport let PermitListComponent = /*#__PURE__*/(() => {\n  class PermitListComponent {\n    router;\n    route;\n    permitsService;\n    httpUtilService;\n    customLayoutUtilsService;\n    kendoColumnService;\n    modalService;\n    cdr;\n    appService;\n    ExceljsService;\n    pageInfo;\n    grid;\n    // Data\n    serverSideRowData = [];\n    gridData = [];\n    IsListHasValue = false;\n    loading = false;\n    isLoading = false;\n    loginUser = {};\n    // Search\n    searchData = '';\n    searchTerms = new Subject();\n    searchSubscription;\n    onMenuDropdownChange(value, field, filterService) {\n      const next = value == null ? {\n        filters: [],\n        logic: 'and'\n      } : {\n        filters: [{\n          field,\n          operator: 'eq',\n          value\n        }],\n        logic: 'and'\n      };\n      filterService.filter(next);\n    }\n    // Enhanced Filters for Kendo UI\n    filter = {\n      logic: 'and',\n      filters: []\n    };\n    gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    activeFilters = [];\n    filterOptions = [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }];\n    // Advanced filter options\n    advancedFilterOptions = {\n      status: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Requires Resubmit',\n        value: 'Requires Resubmit'\n      }, {\n        text: 'On Hold',\n        value: 'On Hold'\n      }, {\n        text: 'Approved',\n        value: 'Approved'\n      }, {\n        text: 'Pending',\n        value: 'Pending'\n      }, {\n        text: 'Canceled',\n        value: 'Canceled'\n      }, {\n        text: 'Complete',\n        value: 'Complete'\n      }, {\n        text: 'Expired',\n        value: 'Expired'\n      }, {\n        text: 'Fees Due',\n        value: 'Fees Due'\n      }, {\n        text: 'In Review',\n        value: 'In Review'\n      }, {\n        text: 'Issued',\n        value: 'Issued'\n      }, {\n        text: 'Requires Resubmit for Prescreen',\n        value: 'Requires Resubmit for Prescreen'\n      }, {\n        text: 'Submitted - Online',\n        value: 'Submitted - Online'\n      }, {\n        text: 'Void',\n        value: 'Void'\n      }],\n      categories: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Primary',\n        value: 'Primary'\n      }, {\n        text: 'Sub Permit',\n        value: 'Sub Permit'\n      }, {\n        text: 'Industrial',\n        value: 'Industrial'\n      }, {\n        text: 'Municipal',\n        value: 'Municipal'\n      }, {\n        text: 'Environmental',\n        value: 'Environmental'\n      }],\n      categoriess: [{\n        text: 'All',\n        value: null\n      }, {\n        text: 'Approved',\n        value: 'Approved'\n      }, {\n        text: 'Pacifica Verification',\n        value: 'Pacifica Verification'\n      }, {\n        text: 'Dis-Approved',\n        value: 'Dis-Approved'\n      }, {\n        text: 'Pending',\n        value: 'Pending'\n      }, {\n        text: 'Not Required',\n        value: 'Not Required'\n      }, {\n        text: 'In Review',\n        value: 'In Review'\n      }, {\n        text: '1 Cycle Completed',\n        value: '1 Cycle Completed'\n      }\n      // 'Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed'\n      ]\n    };\n    // Filter state\n    showAdvancedFilters = false;\n    appliedFilters = {};\n    // Column visibility system\n    kendoHide;\n    hiddenData = [];\n    kendoColOrder = [];\n    kendoInitColOrder = [];\n    hiddenFields = [];\n    // Column configuration\n    gridColumns = [];\n    defaultColumns = [];\n    normalGrid;\n    expandedGrid;\n    isExpanded = false;\n    // Custom reorderable configuration that prevents fixed column reordering\n    customReorderableConfig = false;\n    // Enhanced Columns with Kendo UI features - adapted for permits\n    gridColumnConfig = [{\n      field: 'action',\n      title: 'Action',\n      width: 100,\n      isFixed: true,\n      type: 'action',\n      order: 1\n    }, {\n      field: 'permitName',\n      title: 'Permit/Sub Project Name',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 2\n    }, {\n      field: 'permitNumber',\n      title: 'Permit #',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 3\n    }, {\n      field: 'projectName',\n      title: 'Project Name',\n      width: 180,\n      isFixed: true,\n      type: 'text',\n      filterable: true,\n      order: 4\n    }, {\n      field: 'permitCategory',\n      title: 'Category',\n      width: 120,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 5\n    }, {\n      field: 'permitType',\n      title: 'Permit Type',\n      width: 200,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 6\n    }, {\n      field: 'permitStatus',\n      title: 'Permit Status',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 7\n    }, {\n      field: 'internalReviewStatus',\n      title: 'Internal Review Status',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 8\n    }, {\n      field: 'permitReviewType',\n      title: 'Type',\n      width: 150,\n      isFixed: false,\n      type: 'status',\n      filterable: true,\n      order: 9\n    }, {\n      field: 'location',\n      title: 'Location',\n      width: 200,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 10\n    }, {\n      field: 'permitAppliedDate',\n      title: 'Applied Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 11\n    }, {\n      field: 'permitExpirationDate',\n      title: 'Expiration Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 12\n    }, {\n      field: 'permitFinalDate',\n      title: 'Final Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 13\n    }, {\n      field: 'permitCompleteDate',\n      title: 'Complete Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 14\n    }, {\n      field: 'attentionReason',\n      title: 'Attention Reason',\n      width: 180,\n      isFixed: false,\n      type: 'text',\n      filterable: true,\n      order: 15\n    }, {\n      field: 'lastUpdatedDate',\n      title: 'Updated Date',\n      width: 130,\n      isFixed: false,\n      type: 'date',\n      filterable: true,\n      order: 16\n    }];\n    statusList = [{\n      text: 'Approved',\n      value: 'Approved'\n    }, {\n      text: 'Pending',\n      value: 'Pending'\n    }, {\n      text: 'Rejected',\n      value: 'Rejected'\n    }];\n    // State\n    sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    page = {\n      size: 15,\n      pageNumber: 0,\n      totalElements: 0,\n      totalPages: 0,\n      orderBy: 'lastUpdatedDate',\n      orderDir: 'desc'\n    };\n    skip = 0;\n    // Selection\n    selectedRows = [];\n    isAllSelected = false;\n    // Export options\n    exportOptions = [{\n      text: 'All',\n      value: 'all'\n    }, {\n      text: 'Page Results',\n      value: 'selected'\n    }];\n    // Custom dropdown state\n    isExcelDropdownOpen = false;\n    dropdownTop = 0;\n    dropdownLeft = 0;\n    columnJSONFormat;\n    permitId;\n    singlePermit;\n    resetToDefaultSettings() {\n      console.log('Resetting to default settings...');\n      // Reset column visibility - show all columns\n      this.hiddenFields = [];\n      this.gridColumns = [...this.defaultColumns];\n      this.kendoColOrder = [...this.defaultColumns];\n      // Reset sort state to default\n      this.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n      // Reset page state\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      // Reset all filters - clear everything\n      this.filter = {\n        logic: 'and',\n        filters: []\n      };\n      this.activeFilters = [];\n      // Reset advanced filters\n      this.appliedFilters = {};\n      // Reset search\n      this.searchData = '';\n      // Reset advanced filters visibility\n      this.showAdvancedFilters = false;\n      console.log('Reset completed:', {\n        hiddenFields: this.hiddenFields,\n        gridColumns: this.gridColumns,\n        defaultColumns: this.defaultColumns,\n        sort: this.sort,\n        filter: this.filter,\n        searchData: this.searchData\n      });\n      // Reset the Kendo Grid's internal state\n      if (this.grid) {\n        // Clear all filters\n        this.grid.filter = {\n          logic: 'and',\n          filters: []\n        };\n        // Reset sorting\n        this.grid.sort = [{\n          field: 'lastUpdatedDate',\n          dir: 'desc'\n        }];\n        // Reset column visibility - show all columns\n        this.grid.columns.forEach(column => {\n          if (column.field && column.field !== 'action') {\n            column.hidden = false;\n          }\n        });\n        // Reset to first page\n        this.grid.skip = 0;\n        this.grid.pageSize = this.page.size;\n      }\n      // Trigger change detection\n      this.cdr.detectChanges();\n      // Force grid refresh to apply all changes\n      if (this.grid) {\n        setTimeout(() => {\n          this.grid.refresh();\n          // Also try to reset the grid state completely\n          this.grid.reset();\n        }, 100);\n      }\n      // Reload data with clean state\n      this.loadTable();\n    }\n    // private saveColumnState(): void {\n    //   try {\n    //     const columnState = {\n    //       columns: this.gridColumns,\n    //       hidden: this.hiddenFields,\n    //       order: this.kendoColOrder,\n    //     };\n    //     localStorage.setItem(\n    //       `${this.GRID_STATE_KEY}-columns`,\n    //       JSON.stringify(columnState)\n    //     );\n    //   } catch (error) {\n    //     console.warn('Error saving column state:', error);\n    //   }\n    // }\n    constructor(router, route, permitsService, httpUtilService, customLayoutUtilsService, kendoColumnService, modalService, cdr, appService, ExceljsService, pageInfo) {\n      this.router = router;\n      this.route = route;\n      this.permitsService = permitsService;\n      this.httpUtilService = httpUtilService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.kendoColumnService = kendoColumnService;\n      this.modalService = modalService;\n      this.cdr = cdr;\n      this.appService = appService;\n      this.ExceljsService = ExceljsService;\n      this.pageInfo = pageInfo;\n      // Initialize search subscription\n      this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n        // Set loading state for search\n        this.loading = true;\n        this.isLoading = true;\n        this.httpUtilService.loadingSubject.next(true);\n        this.loadTable();\n      });\n    }\n    ngOnInit() {\n      this.pageInfo.updateTitle('Permits');\n      this.initializeComponent();\n      this.loadTable();\n    }\n    ngAfterViewInit() {\n      this.initializeGrid();\n    }\n    ngOnDestroy() {\n      if (this.searchSubscription) {\n        this.searchTerms.complete();\n      }\n    }\n    initializeComponent() {\n      // Get login user info\n      this.loginUser = this.appService.getLoggedInUser();\n      // Initialize column visibility system\n      this.initializeColumnVisibility();\n    }\n    initializeColumnVisibility() {\n      // Set up column arrays first\n      this.setupColumnArrays();\n      // Try to load from local storage first\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('permits', this.loginUser.userId);\n      if (savedConfig) {\n        // Load saved settings from local storage\n        this.kendoHide = savedConfig.hiddenData || [];\n        this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.kendoColOrder];\n      } else {\n        // Initialize with default values\n        this.kendoHide = [];\n        this.hiddenData = [];\n        this.kendoColOrder = [...this.defaultColumns];\n        this.kendoInitColOrder = [...this.defaultColumns];\n      }\n      // Apply settings\n      this.applySavedColumnSettings();\n    }\n    loadColumnSettingsFromServer() {\n      const config = {\n        pageName: 'permits',\n        userID: this.loginUser.userId\n      };\n      this.kendoColumnService.getHideFields(config).subscribe({\n        next: response => {\n          if (response.isFault === false && response.Data) {\n            // Parse the saved settings\n            this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];\n            this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];\n            this.kendoInitColOrder = [...this.kendoColOrder];\n            // Apply the settings\n            this.applySavedColumnSettings();\n            console.log('Column settings loaded from server:', {\n              kendoHide: this.kendoHide,\n              kendoColOrder: this.kendoColOrder\n            });\n          } else {\n            // No saved settings, use defaults\n            this.kendoHide = [];\n            this.kendoColOrder = [...this.defaultColumns];\n            this.kendoInitColOrder = [...this.defaultColumns];\n            this.applySavedColumnSettings();\n          }\n        },\n        error: error => {\n          console.error('Error loading column settings:', error);\n          // Use defaults on error\n          this.kendoHide = [];\n          this.kendoColOrder = [...this.defaultColumns];\n          this.kendoInitColOrder = [...this.defaultColumns];\n          this.applySavedColumnSettings();\n        }\n      });\n    }\n    setupColumnArrays() {\n      this.gridColumns = this.gridColumnConfig.map(col => col.field);\n      this.defaultColumns = [...this.gridColumns];\n    }\n    initializeGrid() {\n      if (this.grid) {\n        // Apply saved column settings\n        this.applySavedColumnSettings();\n      }\n    }\n    applySavedColumnSettings() {\n      if (this.kendoHide && this.kendoHide.length > 0) {\n        this.hiddenFields = this.kendoHide;\n      }\n      if (this.kendoColOrder && this.kendoColOrder.length > 0) {\n        // Apply column order\n        this.gridColumnConfig.sort((a, b) => {\n          const aOrder = this.kendoColOrder.indexOf(a.field);\n          const bOrder = this.kendoColOrder.indexOf(b.field);\n          return aOrder - bOrder;\n        });\n      }\n    }\n    // Load table data\n    loadTable() {\n      this.loadTableWithKendoEndpoint();\n    }\n    // New method to load data using Kendo UI specific endpoint\n    loadTableWithKendoEndpoint() {\n      this.loading = true;\n      this.isLoading = true;\n      // Enable loader\n      this.httpUtilService.loadingSubject.next(true);\n      // Safety timeout to prevent loader from getting stuck\n      const loadingTimeout = setTimeout(() => {\n        console.warn('Loading timeout reached, resetting loading states');\n        this.resetLoadingStates();\n      }, 30000); // 30 seconds timeout\n      // Prepare state object for Kendo UI endpoint\n      // When sort is empty (3rd click), send default sort to backend\n      const sortForBackend = this.sort.length > 0 ? this.sort : [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      const state = {\n        take: this.page.size,\n        skip: this.skip,\n        sort: sortForBackend,\n        filter: this.filter.filters,\n        search: this.searchData,\n        loggedInUserId: this.loginUser.userId\n      };\n      console.log('Loading table with state:', state);\n      this.permitsService.getPermitsForKendoGrid(state).subscribe({\n        next: data => {\n          // Clear the safety timeout since we got a response\n          clearTimeout(loadingTimeout);\n          console.log('API Response:', data);\n          // Handle the new API response structure\n          if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n            const errors = data.responseData?.errors || data.errors || [];\n            console.error('Kendo UI Grid errors:', errors);\n            // Check if this is an authentication error\n            if (data.responseData?.status === 401 || data.status === 401) {\n              console.warn('Authentication error - token may be expired');\n              // Don't handle empty response here, let the interceptor handle auth\n              return;\n            }\n            this.handleEmptyResponse();\n            // Always reset loading states regardless of data content\n            this.loading = false;\n            this.isLoading = false;\n            this.httpUtilService.loadingSubject.next(false);\n          } else {\n            // Handle both old and new response structures\n            const responseData = data.responseData || data;\n            const permitData = responseData.data || [];\n            const total = responseData.total || 0;\n            this.IsListHasValue = permitData.length !== 0;\n            this.serverSideRowData = permitData;\n            this.gridData = this.serverSideRowData;\n            this.page.totalElements = total;\n            this.page.totalPages = Math.ceil(total / this.page.size);\n            // Create a data source with total count for Kendo Grid\n            this.gridData = {\n              data: permitData,\n              total: total\n            };\n            console.log('this.serverSideRowData ', this.serverSideRowData);\n            console.log('this.gridData ', this.gridData);\n            console.log('this.IsListHasValue ', this.IsListHasValue);\n            console.log('this.page ', this.page);\n            console.log('Total elements set to:', this.page.totalElements);\n            console.log('Total pages calculated:', this.page.totalPages);\n            console.log('Current skip:', this.skip, 'Page size:', this.page.size);\n            console.log('Expected items range:', this.skip + 1, '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n            // Debug grid state after data load\n            setTimeout(() => {\n              if (this.grid) {\n                console.log('Grid total:', this.grid.total);\n                console.log('Grid pageSize:', this.grid.pageSize);\n                console.log('Grid skip:', this.grid.skip);\n                console.log('Grid pageIndex:', this.grid.pageIndex);\n                console.log('Grid data length:', this.grid.data ? this.grid.data.length : 'no data');\n                // Force grid to update its total and maintain pagination state\n                this.grid.total = this.page.totalElements;\n                this.grid.skip = this.skip;\n                this.grid.pageIndex = this.page.pageNumber;\n                console.log('Forced grid total to:', this.grid.total, 'skip to:', this.skip, 'pageIndex to:', this.page.pageNumber);\n              }\n            }, 100);\n            this.cdr.markForCheck();\n            // Always reset loading states regardless of data content\n            this.loading = false;\n            this.isLoading = false;\n            this.httpUtilService.loadingSubject.next(false);\n          }\n        },\n        error: error => {\n          // Clear the safety timeout since we got an error\n          clearTimeout(loadingTimeout);\n          console.error('Error loading data with Kendo UI endpoint:', error);\n          // Check if this is an authentication error\n          if (error && typeof error === 'object' && 'status' in error) {\n            const httpError = error;\n            if (httpError.status === 401) {\n              console.warn('Authentication error - token may be expired');\n              // Don't handle empty response here, let the interceptor handle auth\n              return;\n            }\n          }\n          this.handleEmptyResponse();\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        },\n        complete: () => {\n          // Clear the safety timeout\n          clearTimeout(loadingTimeout);\n          // Ensure loading states are reset in complete block as well\n          this.loading = false;\n          this.isLoading = false;\n          this.httpUtilService.loadingSubject.next(false);\n        }\n      });\n    }\n    handleEmptyResponse() {\n      this.IsListHasValue = false;\n      this.serverSideRowData = [];\n      this.gridData = [];\n      this.page.totalElements = 0;\n      this.page.totalPages = 0;\n      // Ensure loading states are reset when handling empty response\n      this.loading = false;\n      this.isLoading = false;\n      this.httpUtilService.loadingSubject.next(false);\n    }\n    // Method to manually reset loading states if they get stuck\n    resetLoadingStates() {\n      this.loading = false;\n      this.isLoading = false;\n      this.httpUtilService.loadingSubject.next(false);\n    }\n    // Public method to manually refresh the grid and reset any stuck loading states\n    refreshGrid() {\n      console.log('Manually refreshing grid...');\n      this.resetLoadingStates();\n      this.loadTable();\n    }\n    // Search functionality\n    onSearchKeyDown(event) {\n      if (event.key === 'Enter') {\n        this.loadTable();\n      }\n    }\n    onSearchChange() {\n      console.log('Search changed:', this.searchData);\n      // Trigger search with debounce\n      this.searchTerms.next(this.searchData || '');\n    }\n    applySearch() {\n      this.loadTable();\n    }\n    clearSearch() {\n      this.searchTerms.next(this.searchData);\n    }\n    // Filter functionality\n    filterChange(filter) {\n      console.log('filter', filter);\n      this.filter = filter;\n      this.loadTable();\n    }\n    applyAdvancedFilters() {\n      console.log('yes it came here');\n      // Apply status filter\n      if (this.appliedFilters.status) {\n        this.filter.filters = this.filter.filters.filter(f => {\n          if ('field' in f) {\n            return f.field !== 'permitStatus';\n          }\n          return true;\n        });\n        this.filter.filters.push({\n          field: 'permitStatus',\n          operator: 'eq',\n          value: this.appliedFilters.status\n        });\n      }\n      // Apply category filter\n      if (this.appliedFilters.category) {\n        this.filter.filters = this.filter.filters.filter(f => {\n          if ('field' in f) {\n            return f.field !== 'permitCategory';\n          }\n          return true;\n        });\n        this.filter.filters.push({\n          field: 'permitCategory',\n          operator: 'eq',\n          value: this.appliedFilters.category\n        });\n      }\n      this.loadTable();\n    }\n    clearAdvancedFilters() {\n      this.appliedFilters = {};\n      this.filter.filters = [];\n      this.loadTable();\n    }\n    // Sorting functionality\n    onSortChange(sort) {\n      // Check if this is the 3rd click (dir is undefined)\n      const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n      if (isThirdClick) {\n        // 3rd click - clear sort and use default\n        this.sort = [];\n        this.page.orderBy = 'lastUpdatedDate';\n        this.page.orderDir = 'desc';\n      } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n        // Valid sort with direction\n        this.sort = sort;\n        this.page.orderBy = sort[0].field || 'lastUpdatedDate';\n        this.page.orderDir = sort[0].dir;\n      } else {\n        // Empty sort array or invalid sort\n        this.sort = [];\n        this.page.orderBy = 'lastUpdatedDate';\n        this.page.orderDir = 'desc';\n      }\n      this.skip = 0;\n      this.page.pageNumber = 0;\n      this.loadTable();\n    }\n    // Pagination functionality\n    pageChange(event) {\n      console.log('Page change event:', event);\n      console.log('Current page size:', this.page.size);\n      console.log('Event page size:', event.pageSize);\n      console.log('Event page index:', event.pageIndex);\n      console.log('Event skip:', event.skip);\n      // Use Kendo's provided values as source of truth\n      this.skip = event.skip;\n      this.page.size = event.take || this.page.size;\n      this.page.pageNumber = Math.floor(this.skip / this.page.size);\n      console.log('Updated skip:', this.skip, 'page size:', this.page.size, 'page number:', this.page.pageNumber);\n      console.log('Expected items range:', this.skip + 1, '-', Math.min(this.skip + this.page.size, this.page.totalElements));\n      this.loadTable();\n    }\n    // Handle page size change specifically\n    onPageSizeChange(event) {\n      console.log('Page size change event:', event);\n      console.log('New page size:', event.pageSize);\n      if (event.pageSize && event.pageSize !== this.page.size) {\n        console.log('Page size changing from', this.page.size, 'to', event.pageSize);\n        this.page.size = event.pageSize;\n        this.page.pageNumber = 0; // Reset to first page when changing page size\n        this.skip = 0;\n        // Recalculate total pages based on new page size\n        if (this.page.totalElements > 0) {\n          this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n          console.log('Recalculated total pages:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n        }\n        // Force grid to update its page size and reset to first page\n        if (this.grid) {\n          this.grid.pageSize = this.page.size;\n          this.grid.skip = 0;\n          this.grid.pageIndex = 0;\n          this.grid.total = this.page.totalElements;\n          console.log('Updated grid total to:', this.grid.total);\n        }\n        console.log('Updated page size:', this.page.size, 'skip:', this.skip);\n        this.loadTable();\n      }\n    }\n    // Handle data state changes (includes page size changes)\n    onDataStateChange(event) {\n      console.log('Data state change event:', event);\n      // Check if page size changed\n      if (event.take && event.take !== this.page.size) {\n        console.log('Page size changing via data state from', this.page.size, 'to', event.take);\n        this.page.size = event.take;\n        this.page.pageNumber = 0;\n        this.skip = 0;\n        // Recalculate total pages based on new page size\n        if (this.page.totalElements > 0) {\n          this.page.totalPages = Math.ceil(this.page.totalElements / this.page.size);\n          console.log('Recalculated total pages via data state:', this.page.totalPages, 'for', this.page.totalElements, 'items with page size', this.page.size);\n        }\n        // Force grid to update its page size and reset to first page\n        if (this.grid) {\n          this.grid.pageSize = this.page.size;\n          this.grid.skip = 0;\n          this.grid.pageIndex = 0;\n          this.grid.total = this.page.totalElements;\n          console.log('Updated grid total via data state to:', this.grid.total);\n        }\n        console.log('Updated page size via data state:', this.page.size, 'skip:', this.skip);\n        this.loadTable();\n      }\n    }\n    updateColumnVisibility(event) {\n      // Handle column visibility changes\n      const hiddenColumns = event.hiddenColumns || [];\n      this.hiddenFields = hiddenColumns;\n    }\n    // Selection functionality\n    onSelectionChange(event) {\n      this.selectedRows = event.selectedRows || [];\n      this.isAllSelected = this.selectedRows.length === this.serverSideRowData.length;\n    }\n    selectAll() {\n      if (this.isAllSelected) {\n        this.selectedRows = [];\n        this.isAllSelected = false;\n      } else {\n        this.selectedRows = [...this.serverSideRowData];\n        this.isAllSelected = true;\n      }\n    }\n    // Grid expansion\n    toggleExpand() {\n      // Find grid container element and toggle fullscreen class\n      const gridContainer = document.querySelector('.grid-container');\n      if (gridContainer) {\n        gridContainer.classList.toggle('fullscreen-grid');\n        this.isExpanded = !this.isExpanded;\n        // Refresh grid after resize to ensure proper rendering\n        if (this.grid) {\n          this.grid.refresh();\n        }\n      }\n    }\n    // Export functionality\n    onExportClick(event) {\n      const selectedOption = event.value; // Get selected option\n      let prdItems = [];\n      if (selectedOption === 'selected') {\n        prdItems = this.serverSideRowData;\n        // declare the title and header data for excel\n        // get the data for excel in a array format\n        this.exportExcel(prdItems);\n      } else if (selectedOption === 'all') {\n        const queryparamsExcel = {\n          pageSize: this.page.totalElements,\n          sortOrder: this.page.orderDir,\n          sortField: this.page.orderBy,\n          pageNumber: this.page.pageNumber\n          // filter: this.filterConfiguration()\n        };\n        // Enable loading indicator\n        this.httpUtilService.loadingSubject.next(true);\n        // API call\n        this.permitsService.getAllPermits(queryparamsExcel)\n        // .pipe(map((data: any) => data as any))\n        .subscribe(data => {\n          // Disable loading indicator\n          this.httpUtilService.loadingSubject.next(false);\n          if (data.isFault) {\n            this.IsListHasValue = false;\n            this.cdr.markForCheck();\n            return; // Exit early if the response has a fault\n          }\n          this.IsListHasValue = true;\n          prdItems = data.responseData.data || [];\n          this.cdr.detectChanges(); // Manually trigger UI update\n          this.exportExcel(prdItems);\n        });\n      }\n    }\n    exportExcel(listOfItems) {\n      // Define local variables for the items and current date\n      let prdItems = listOfItems;\n      let currentDate = this.appService.formatMonthDate(new Date());\n      console.log('prdItems', prdItems);\n      // Check if the data exists and is not empty\n      if (prdItems !== undefined && prdItems.length > 0) {\n        // Define the title for the Excel file\n        const tableTitle = 'Events';\n        // Filter out hidden columns and sort by order\n        // const visibleColumns = this.columnJSONFormat\n        //   .filter((col: any) => !col.hidden)\n        //   .sort((a: any, b: any) => a.order - b.order);\n        // Create header from visible columns\n        const headerArray = ['Permit Number', 'Permit Type', 'Category', 'Status', 'Location', 'Project Name', 'Applied Date', 'Expiration Date', 'Attention Reason'];\n        // ...visibleColumns.map((col: any) => col.title),\n        // Define which columns should have currency and percentage formatting\n        // const currencyColumns: any = [\n        //   'Pending',\n        //   'ACAT',\n        //   'Annuity',\n        //   'AUM',\n        //   'Total Assets',\n        //   'Event Cost',\n        //   'Gross Profit',\n        // ].filter((col) => headerArray.includes(col));\n        const percentageColumns = [];\n        // Get the data for excel in an array format\n        const respResult = [];\n        // Prepare the data for export based on visible columns\n        each(prdItems, prdItem => {\n          // Create an array with the same length as headerArray\n          const respData = Array(headerArray.length).fill(null);\n          respData[0] = prdItem.eventDescription;\n          respData[1] = this.appService.formatMonthDate(prdItem.event_date);\n          // Fill in data for each visible column\n          headerArray.forEach((col, i) => {\n            const adjustedIndex = i; // +2 for 'Name' and 'Hot'\n            switch (col) {\n              case 'Permit Number':\n                respData[adjustedIndex] = prdItem.permitNumber;\n                break;\n              case 'Permit Type':\n                respData[adjustedIndex] = prdItem.permitType;\n                break;\n              case 'Category':\n                respData[adjustedIndex] = prdItem.permitCategory;\n                break;\n              case 'Status':\n                respData[adjustedIndex] = prdItem.permitStatus;\n                break;\n              case 'Location':\n                respData[adjustedIndex] = prdItem.location;\n                break;\n              case 'Project Name':\n                respData[adjustedIndex] = prdItem.projectName;\n                break;\n              case 'Applied Date':\n                respData[adjustedIndex] = this.appService.formatDate(prdItem.permitAppliedDate);\n                break;\n              case 'Expiration Date':\n                respData[adjustedIndex] = this.appService.formatDate(prdItem.permitExpirationDate);\n                break;\n              case 'Attention Reason':\n                respData[adjustedIndex] = prdItem.attentionReason;\n                break;\n              // case 'kept_appointments':\n              //   respData[adjustedIndex] = prdItem.kept_appointments;\n              //   break;\n              // case 'kept_appt_ratio':\n              //   respData[adjustedIndex] = prdItem.kept_appt_ratio;\n              //   break;\n              // case 'apptKeptNo':\n              //   respData[adjustedIndex] = prdItem.apptKeptNo;\n              //   break;\n              // case 'has_assets':\n              //   respData[adjustedIndex] = prdItem.has_assets;\n              //   break;\n              // case 'prospects_closed':\n              //   respData[adjustedIndex] = prdItem.prospects_closed;\n              //   break;\n              // case 'closing_ratio':\n              //   respData[adjustedIndex] = prdItem.closing_ratio;\n              //   break;\n              // case 'totalPending':\n              //   respData[adjustedIndex] = prdItem.totalPending;\n              //   break;\n              // case 'acatproduction':\n              //   respData[adjustedIndex] = prdItem.acatproduction;\n              //   break;\n              // case 'annuityproduction':\n              //   respData[adjustedIndex] = prdItem.annuityproduction;\n              //   break;\n              // case 'aumproduction':\n              //   respData[adjustedIndex] = prdItem.aumproduction;\n              //   break;\n              // case 'totalAssets':\n              //   respData[adjustedIndex] = prdItem.totalAssets;\n              //   break;\n              // case 'eventCost':\n              //   respData[adjustedIndex] = prdItem.eventCost;\n              //   break;\n              // case 'grossProfit':\n              //   respData[adjustedIndex] = prdItem.grossProfit;\n              //   break;\n              // case 'status':\n              //   respData[adjustedIndex] = prdItem.status;\n              //   break;\n            }\n          });\n          respResult.push(respData);\n        });\n        // Define column sizes for the Excel file\n        const colSize = headerArray.map((header, index) => ({\n          id: index + 1,\n          width: 20\n        }));\n        // Generate the Excel file using the exceljsService\n        this.ExceljsService.generateExcel(tableTitle, headerArray, respResult, colSize\n        // currencyColumns,\n        // percentageColumns\n        );\n      } else {\n        const message = 'There are no records available to export.';\n        // this.layoutUtilService.showError(message, '');\n      }\n    }\n    // public onExportClick(event: any): void {\n    //   const exportType = event.item.value;\n    //   let selectedIds: number[] = [];\n    //   switch (exportType) {\n    //     case 'selected':\n    //       selectedIds = this.selectedRows.map((row) => row.permitId);\n    //       if (selectedIds.length === 0) {\n    //         //alert('Please select permits to export');\n    //         return;\n    //       }\n    //       break;\n    //     case 'filtered':\n    //       // Export filtered data\n    //       break;\n    //     case 'all':\n    //     default:\n    //       // Export all data\n    //       break;\n    //   }\n    //   this.exportPermits(exportType, selectedIds);\n    // }\n    // private exportPermits(exportType: string, selectedIds: number[]): void {\n    //   this.permitsService.exportPermits(exportType, selectedIds).subscribe({\n    //     next: (response: any) => {\n    //       if (response.data) {\n    //         const blob = new Blob([response.data], {\n    //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',\n    //         });\n    //         saveAs(\n    //           blob,\n    //           `permits_${exportType}_${\n    //             new Date().toISOString().split('T')[0]\n    //           }.xlsx`\n    //         );\n    //       }\n    //     },\n    //     error: (error: any) => {\n    //       console.error('Export error:', error);\n    //       //alert('Error exporting permits data');\n    //     },\n    //   });\n    // }\n    // Column settings management\n    saveHead() {\n      const settings = {\n        kendoHide: this.hiddenFields,\n        kendoColOrder: this.kendoColOrder,\n        kendoInitColOrder: this.kendoInitColOrder\n      };\n      // Save to local storage only\n      this.kendoColumnService.saveToLocalStorage({\n        pageName: 'permits',\n        userID: this.loginUser.userId,\n        hiddenData: settings.kendoHide,\n        kendoColOrder: settings.kendoColOrder,\n        LoggedId: this.loginUser.userId\n      });\n      console.log('Column settings saved locally:', settings);\n      this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');\n      //alert('Column settings saved locally');\n    }\n    saveColumnSettingsToServer(settings) {\n      const config = {\n        pageName: 'permits',\n        userID: this.loginUser.userId,\n        hiddenData: settings.kendoHide,\n        kendoColOrder: settings.kendoColOrder,\n        LoggedId: this.loginUser.userId\n      };\n      this.kendoColumnService.createHideFields(config).subscribe({\n        next: response => {\n          if (response.isFault === false) {\n            console.log('Column settings saved successfully:', response);\n            this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');\n            //alert('Column settings saved successfully');\n          } else {\n            console.error('Failed to save column settings:', response.message);\n            this.customLayoutUtilsService.showError(response.message, '');\n            //alert('Failed to save column settings: ' + response.message);\n          }\n        },\n        error: error => {\n          console.error('Error saving column settings:', error);\n          this.customLayoutUtilsService.showError('Error saving column setting', '');\n          //alert('Error saving column settings. Please try again.');\n        }\n      });\n    }\n    saveResetToServer() {\n      // First delete existing settings\n      const deleteConfig = {\n        pageName: 'permits',\n        userID: this.loginUser.userId\n      };\n      this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({\n        next: response => {\n          console.log('Existing settings deleted:', response);\n          // Then save the reset state (all columns visible)\n          this.saveColumnSettingsToServer({\n            kendoHide: [],\n            kendoColOrder: this.defaultColumns,\n            kendoInitColOrder: this.defaultColumns\n          });\n        },\n        error: error => {\n          console.error('Error deleting existing settings:', error);\n          // Still try to save the reset state\n          this.saveColumnSettingsToServer({\n            kendoHide: [],\n            kendoColOrder: this.defaultColumns,\n            kendoInitColOrder: this.defaultColumns\n          });\n        }\n      });\n    }\n    resetTable() {\n      console.log('Resetting Kendo settings for permits');\n      // Clear all saved settings first\n      this.kendoHide = [];\n      this.hiddenData = [];\n      this.kendoColOrder = [];\n      this.kendoInitColOrder = [];\n      // Clear local storage\n      this.kendoColumnService.clearFromLocalStorage('permits');\n      // Reset to default settings\n      this.resetToDefaultSettings();\n      // Trigger change detection to update the template\n      this.cdr.detectChanges();\n      // Force grid refresh to show all columns\n      if (this.grid) {\n        this.grid.refresh();\n      }\n      // Show success message\n      console.log('Table reset to default settings');\n      //alert('Table reset to default settings - all columns restored');\n    }\n    // Navigation\n    add() {\n      // this.router.navigate(['/permits/add']);\n      this.edit(0);\n    }\n    view(permitId) {\n      this.router.navigate(['/permits/view', permitId], {\n        queryParams: {\n          from: 'permit-list'\n        }\n      });\n    }\n    edit(permitId) {\n      if (permitId == 0) {\n        const permit = this.serverSideRowData.find(p => p.permitId === permitId);\n        const NgbModalOptions = {\n          size: 'lg',\n          // Large modal size\n          backdrop: 'static',\n          // Prevents closing when clicking outside\n          keyboard: false,\n          // Disables closing with the Escape key\n          scrollable: true // Allows scrolling inside the modal\n        };\n        // Trigger global loader BEFORE opening modal to ensure full-page overlay\n        this.httpUtilService.loadingSubject.next(true);\n        // Open the modal and load the ProjectPopup\n        const modalRef = this.modalService.open(PermitPopupComponent, NgbModalOptions);\n        // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n        modalRef.componentInstance.id = permitId;\n        modalRef.componentInstance.permit = permit;\n        // Subscribe to the modal event when data is updated\n        modalRef.componentInstance.passEntry.subscribe(receivedEntry => {\n          if (receivedEntry === true) {\n            // Reload the table data after a successful update\n            this.loadTable();\n          }\n        });\n      } else {\n        this.router.navigate(['/permits/view', permitId], {\n          queryParams: {\n            from: 'permit-list'\n          }\n        });\n      }\n    }\n    deletePop(content) {\n      this.modalService.open(content, {\n        centered: true\n      });\n    }\n    confirmDelete() {\n      console.log('Item deleted ✅');\n      // your delete logic here\n    }\n    delete(permitId) {\n      if (confirm('Are you sure you want to delete this permit?')) {\n        this.permitsService.deletePermit({\n          permitId\n        }).subscribe({\n          next: response => {\n            if (response.message) {\n              //alert('Permit deleted successfully');\n              this.customLayoutUtilsService.showSuccess('Permit deleted successfully', '');\n              this.loadTable();\n            }\n          },\n          error: error => {\n            console.error('Delete error:', error);\n            this.customLayoutUtilsService.showError('Error deleting permit', '');\n            //alert('Error deleting permit');\n          }\n        });\n      }\n    }\n    // Utility methods\n    formatDate(dateString) {\n      if (!dateString) return '';\n      return new Date(dateString).toLocaleDateString();\n    }\n    getStatusClass(status) {\n      switch (status) {\n        case 'Approved':\n          return 'badge-light-success';\n        case 'Requires Resubmit':\n          return 'badge-light-warning';\n        case 'On Hold':\n          return 'badge-light-danger';\n        case 'Pending':\n          return 'badge-light-info';\n        default:\n          return 'badge-light-secondary';\n      }\n    }\n    getCategoryClass(category) {\n      return category === 'Primary' ? 'badge-light-primary' : 'badge-light-secondary';\n    }\n    syncPermits(i) {\n      this.isLoading = true;\n      this.singlePermit = i || false;\n      this.permitsService.syncPermits({\n        municipalityId: 1,\n        singlePermit: this.singlePermit,\n        autoLogin: true\n      }).subscribe({\n        next: res => {\n          this.isLoading = false;\n          console.log('Sync response:', res);\n          // Handle wrapped response structure from interceptor\n          const responseData = res?.responseData || res;\n          if (responseData?.isFault) {\n            //alert(responseData.faultMessage || 'Failed to sync permit');\n            this.customLayoutUtilsService.showError(responseData.faultMessage || 'Failed to sync permit', '');\n          } else if (responseData?.success === false) {\n            // Handle specific error messages from the API\n            if (responseData.message === 'Permit not found in Energov system') {\n              //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            } else if (responseData.message === 'No permits found for any keywords') {\n              //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            } else {\n              this.customLayoutUtilsService.showError(responseData.message || 'Failed to sync permit', '');\n              //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n            }\n          } else {\n            this.customLayoutUtilsService.showSuccess('✅ Permit synced successfully', '');\n            //alert('✅ Permit synced successfully');\n          }\n          this.cdr.markForCheck();\n        },\n        error: err => {\n          this.isLoading = false;\n          // Handle HTTP error responses\n          console.log('Error response:', err);\n          console.log('Error type:', typeof err);\n          console.log('Error keys:', Object.keys(err || {}));\n          console.log('Error status:', err?.status);\n          console.log('Error message:', err?.message);\n          console.log('Error error:', err?.error);\n          // The interceptor passes err.error to the error handler\n          // So err might actually be the response data\n          if (err?.success === false) {\n            // Handle specific error messages from the API\n            if (err.message === 'Permit not found in Energov system') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n            } else if (err.message === 'No permits found for any keywords') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n            } else {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n            }\n          } else if (err?.error?.message) {\n            if (err.error.message === 'Permit not found in Energov system') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n            } else if (err.error.message === 'No permits found for any keywords') {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n            } else {\n              this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n              //alert(`❌ ${err.error.message}`)\n              // ;\n            }\n          } else if (err?.status === 404) {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            // Handle 404 specifically for permit not found\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Error syncing permit', '');\n            //alert('❌ Error syncing permit');\n          }\n          console.error(err);\n          this.cdr.markForCheck();\n        }\n      });\n    }\n    onTabActivated() {\n      // This method is called when the tab is activated\n      // You can add any specific logic here if needed\n      console.log('Permits tab activated');\n    }\n    // Custom dropdown methods\n    toggleExcelDropdown(event) {\n      this.isExcelDropdownOpen = !this.isExcelDropdownOpen;\n      console.log('Excel dropdown toggled:', this.isExcelDropdownOpen);\n      if (this.isExcelDropdownOpen && event) {\n        const button = event.target;\n        const rect = button.getBoundingClientRect();\n        this.dropdownTop = rect.bottom + window.scrollY;\n        this.dropdownLeft = rect.left + window.scrollX;\n        console.log('Dropdown position:', this.dropdownTop, this.dropdownLeft);\n      }\n    }\n    closeExcelDropdown() {\n      this.isExcelDropdownOpen = false;\n      console.log('Excel dropdown closed');\n    }\n    onDocumentClick(event) {\n      const target = event.target;\n      const dropdown = target.closest('.custom-dropdown');\n      if (!dropdown && this.isExcelDropdownOpen) {\n        this.closeExcelDropdown();\n      }\n    }\n    getHiddenField(fieldName) {\n      return this.hiddenFields.includes(fieldName);\n    }\n    static ɵfac = function PermitListComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PermitListComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.PermitsService), i0.ɵɵdirectiveInject(i3.HttpUtilsService), i0.ɵɵdirectiveInject(i4.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i5.KendoColumnService), i0.ɵɵdirectiveInject(i6.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i7.AppService), i0.ɵɵdirectiveInject(i8.ExceljsService), i0.ɵɵdirectiveInject(i9.PageInfoService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PermitListComponent,\n      selectors: [[\"app-permit-list\"]],\n      viewQuery: function PermitListComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n        }\n      },\n      hostBindings: function PermitListComponent_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵlistener(\"click\", function PermitListComponent_click_HostBindingHandler($event) {\n            return ctx.onDocumentClick($event);\n          }, false, i0.ɵɵresolveDocument);\n        }\n      },\n      decls: 9,\n      vars: 24,\n      consts: [[\"normalGrid\", \"\"], [\"excelDropdown\", \"\"], [\"excelButton\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"selectionChange\", \"filterChange\", \"pageChange\", \"pageSizeChange\", \"dataStateChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\", \"loading\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"class\", \"text-center py-5\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [1, \"custom-dropdown\", \"me-2\"], [\"type\", \"button\", \"title\", \"Export Excel\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-file-excel\", \"text-success\"], [\"class\", \"custom-dropdown-menu\", 3, \"top\", \"left\", 4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-warning\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"custom-dropdown-menu\"], [\"href\", \"#\", 1, \"custom-dropdown-item\", 3, \"click\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Category\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-6\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", \"themeColor\", \"primary\", 1, \"me-2\", 3, \"click\"], [\"kendoButton\", \"\", \"themeColor\", \"secondary\", 1, \"me-2\", 3, \"click\"], [\"kendoButton\", \"\", \"themeColor\", \"light\", 3, \"click\"], [\"type\", \"button\", \"title\", \"Toggle Advanced Filters\", 1, \"btn\", \"btn-outline-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-filter\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\", \"includeInChooser\", \"columnMenu\", \"hidden\", 4, \"ngIf\"], [\"field\", \"permitNumber\", \"title\", \"Permit #\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"permitName\", \"title\", \"Permit/Sub Project Name\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"permitType\", \"title\", \"Permit Type\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"permitCategory\", \"title\", \"Category\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"internalReviewStatus\", \"title\", \"Internal Review Status\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitReviewType\", \"title\", \"Type\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitStatus\", \"title\", \"Permit Status\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"location\", \"title\", \"Location\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitAppliedDate\", \"title\", \"Applied Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitExpirationDate\", \"title\", \"Expiration Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitFinalDate\", \"title\", \"Final Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"permitCompleteDate\", \"title\", \"Complete Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"attentionReason\", \"title\", \"Attention Reason\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\", 4, \"ngIf\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [1, \"d-flex\", \"gap-1\"], [\"title\", \"View\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\"], [\"field\", \"permitNumber\", \"title\", \"Permit #\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bold\"], [\"operator\", \"contains\", 3, \"column\", \"filter\", \"extra\"], [\"field\", \"permitName\", \"title\", \"Permit/Sub Project Name\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"projectName\", \"title\", \"Project Name\", 3, \"width\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"field\", \"permitType\", \"title\", \"Permit Type\", 3, \"width\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"permitCategory\", \"title\", \"Category\", 3, \"width\"], [1, \"badge\", 3, \"ngClass\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"ngModelChange\", \"valueChange\", \"column\", \"filter\", \"data\", \"ngModel\", \"valuePrimitive\", \"defaultItem\"], [\"field\", \"internalReviewStatus\", \"title\", \"Internal Review Status\", 3, \"width\"], [\"field\", \"permitReviewType\", \"title\", \"Type\", 3, \"width\"], [\"field\", \"permitStatus\", \"title\", \"Permit Status\", 3, \"width\"], [\"field\", \"location\", \"title\", \"Location\", 3, \"width\"], [\"field\", \"permitAppliedDate\", \"title\", \"Applied Date\", 3, \"width\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"field\", \"permitExpirationDate\", \"title\", \"Expiration Date\", 3, \"width\"], [\"field\", \"permitFinalDate\", \"title\", \"Final Date\", 3, \"width\"], [\"field\", \"permitCompleteDate\", \"title\", \"Complete Date\", 3, \"width\"], [\"field\", \"attentionReason\", \"title\", \"Attention Reason\", 3, \"width\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", 3, \"width\"], [1, \"text-center\", \"py-5\"], [1, \"fas\", \"fa-inbox\", \"fa-3x\", \"mb-3\"], [1, \"btn\", \"btn-primary\", 3, \"click\"]],\n      template: function PermitListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵtemplate(0, PermitListComponent_div_0_Template, 7, 0, \"div\", 3);\n          i0.ɵɵelementStart(1, \"div\", 4)(2, \"kendo-grid\", 5, 0);\n          i0.ɵɵlistener(\"selectionChange\", function PermitListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSelectionChange($event));\n          })(\"filterChange\", function PermitListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.filterChange($event));\n          })(\"pageChange\", function PermitListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.pageChange($event));\n          })(\"pageSizeChange\", function PermitListComponent_Template_kendo_grid_pageSizeChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onPageSizeChange($event));\n          })(\"dataStateChange\", function PermitListComponent_Template_kendo_grid_dataStateChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onDataStateChange($event));\n          })(\"sortChange\", function PermitListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.onSortChange($event));\n          })(\"columnVisibilityChange\", function PermitListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n          });\n          i0.ɵɵtemplate(4, PermitListComponent_ng_template_4_Template, 23, 13, \"ng-template\", 6)(5, PermitListComponent_ng_template_5_Template, 1, 1, \"ng-template\", 6)(6, PermitListComponent_ng_template_6_Template, 3, 1, \"ng-template\", 6)(7, PermitListComponent_ng_container_7_Template, 17, 16, \"ng-container\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(8, PermitListComponent_div_8_Template, 9, 0, \"div\", 8);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(19, _c2, i0.ɵɵpureFunction0(18, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", i0.ɵɵpureFunction0(21, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(22, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(23, _c5))(\"loading\", false);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.IsListHasValue);\n        }\n      },\n      dependencies: [i10.NgClass, i10.NgForOf, i10.NgIf, i11.NgControlStatus, i11.NgModel, i12.GridComponent, i12.ToolbarTemplateDirective, i12.GridSpacerComponent, i12.ColumnComponent, i12.CellTemplateDirective, i12.ContainsFilterOperatorComponent, i12.EqualFilterOperatorComponent, i12.NotEqualFilterOperatorComponent, i12.AfterFilterOperatorComponent, i12.AfterEqFilterOperatorComponent, i12.BeforeEqFilterOperatorComponent, i12.BeforeFilterOperatorComponent, i12.StringFilterMenuComponent, i12.FilterMenuTemplateDirective, i12.DateFilterMenuComponent, i13.TextBoxComponent, i14.DropDownListComponent, i15.ButtonComponent, i16.InlineSVGDirective],\n      styles: [\".grid-container[_ngcontent-%COMP%]{padding:20px;display:flex;flex-direction:column;height:100%;position:relative}.grid-container.fullscreen-grid[_ngcontent-%COMP%]{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:9999;background:#fff;padding:20px;overflow:auto}.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{border-radius:.375rem;padding:.5rem .75rem;width:80%;border:2px solid #afc7dd;box-shadow:0 0 6px #3b538780}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{border-radius:.375rem;padding:.75rem 1.25rem;min-width:120px;background-color:#4c4e4f;color:#fff;font-weight:500;transition:background .3s,transform .2s}.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#4c4e4f;transform:scale(1.05)}.advanced-filters-panel[_ngcontent-%COMP%]{border-radius:8px;margin-bottom:1rem}.advanced-filters-panel[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%]{font-weight:600;color:#495057;margin-bottom:.5rem}.advanced-filters-panel[_ngcontent-%COMP%]   .k-dropdownlist[_ngcontent-%COMP%]{width:100%;border-radius:6px}.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]{border-radius:.375rem!important;font-weight:500!important;font-size:.75rem!important;padding:.25rem .5rem!important;background-color:#6c757d!important;border-color:#6c757d!important;color:#fff!important;margin-right:.5rem!important;transition:all .2s ease!important;height:auto!important;min-height:31px!important}.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover{background-color:#5a6268!important;border-color:#545b62!important;transform:translateY(-1px)!important;box-shadow:0 2px 8px #00000026!important}.k-dropdownbutton.btn[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:focus{box-shadow:0 0 0 .2rem #6c757d80!important}[_nghost-%COMP%]     .k-grid{border-radius:8px;box-shadow:0 2px 10px #0000001a}[_nghost-%COMP%]     .k-grid .k-grid-header{background:#f8f9fa;border-bottom:2px solid #dee2e6}[_nghost-%COMP%]     .k-grid .k-grid-header .k-header{background:#f8f9fa;border-color:#dee2e6;font-weight:600;color:#495057}[_nghost-%COMP%]     .k-grid .k-grid-header .k-header:hover{background:#e9ecef}[_nghost-%COMP%]     .k-grid .k-grid-toolbar{background:#f8f9fa;border-bottom:1px solid #dee2e6;padding:1rem}[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button{border-radius:6px;font-weight:500}[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary{background:#007bff;border-color:#007bff}[_nghost-%COMP%]     .k-grid .k-grid-toolbar .k-button.k-primary:hover{background:#0056b3;border-color:#0056b3}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row:hover{background:#f8f9fa}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt{background:#f8f9fa}[_nghost-%COMP%]     .k-grid .k-grid-content .k-grid-row.k-alt:hover{background:#e9ecef}[_nghost-%COMP%]     .k-grid .k-pager{background:#f8f9fa;border-top:1px solid #dee2e6}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-info{color:#6c757d}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link{border-radius:4px}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link:hover{background:#e9ecef}[_nghost-%COMP%]     .k-grid .k-pager .k-pager-numbers .k-link.k-state-selected{background:#007bff;color:#fff}[_nghost-%COMP%]     .custom-dropdown .k-button{border-radius:6px;font-weight:500}[_nghost-%COMP%]     .k-textbox{border-radius:6px}[_nghost-%COMP%]     .k-textbox:focus{box-shadow:0 0 0 .2rem #007bff40}[_nghost-%COMP%]     .k-dropdownlist{border-radius:6px}[_nghost-%COMP%]     .k-dropdownlist:focus{box-shadow:0 0 0 .2rem #007bff40}.badge[_ngcontent-%COMP%]{padding:.5em .75em;font-size:.75em;font-weight:600;border-radius:6px}.badge.badge-light-success[_ngcontent-%COMP%]{background:#d4edda;color:#155724}.badge.badge-light-warning[_ngcontent-%COMP%]{background:#fff3cd;color:#856404}.badge.badge-light-danger[_ngcontent-%COMP%]{background:#f8d7da;color:#721c24}.badge.badge-light-info[_ngcontent-%COMP%]{background:#d1ecf1;color:#0c5460}.badge.badge-light-secondary[_ngcontent-%COMP%]{background:#e2e3e5;color:#383d41}.badge.badge-light-primary[_ngcontent-%COMP%]{background:#cce7ff;color:#004085}.btn[_ngcontent-%COMP%]{border-radius:6px;font-weight:500;transition:all .2s ease}.btn[_ngcontent-%COMP%]:hover{transform:translateY(-1px);box-shadow:0 2px 8px #00000026}.btn.btn-success[_ngcontent-%COMP%]{background:#28a745;border-color:#28a745}.btn.btn-success[_ngcontent-%COMP%]:hover{background:#1e7e34;border-color:#1e7e34}.btn.btn-warning[_ngcontent-%COMP%]{background:#ffc107;border-color:#ffc107;color:#212529}.btn.btn-warning[_ngcontent-%COMP%]:hover{background:#e0a800;border-color:#d39e00}.btn.btn-info[_ngcontent-%COMP%]{background:#17a2b8;border-color:#17a2b8}.btn.btn-info[_ngcontent-%COMP%]:hover{background:#138496;border-color:#138496}.btn.btn-outline-secondary[_ngcontent-%COMP%]{color:#6c757d;border-color:#6c757d}.btn.btn-outline-secondary[_ngcontent-%COMP%]:hover{background:#6c757d;color:#fff}.btn-icon[_ngcontent-%COMP%]{width:32px;height:32px;padding:0;display:inline-flex;align-items:center;justify-content:center;border-radius:6px;transition:all .2s ease}.btn-icon[_ngcontent-%COMP%]:hover{transform:scale(1.1)}.text-muted[_ngcontent-%COMP%]   .fas[_ngcontent-%COMP%]{color:#6c757d}.text-muted[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{color:#495057;margin-top:1rem}.text-muted[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{color:#6c757d;margin-bottom:1.5rem}@media (max-width: 768px){.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]{flex-direction:column;gap:1rem}.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]{width:100%}.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%]{width:100%!important}.grid-container[_ngcontent-%COMP%]   .k-grid-toolbar[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%]{justify-content:center}.advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-3[_ngcontent-%COMP%], .advanced-filters-panel[_ngcontent-%COMP%]   .row[_ngcontent-%COMP%]   .col-md-6[_ngcontent-%COMP%]{margin-bottom:1rem}}.grid-container[_ngcontent-%COMP%]{transition:all .3s cubic-bezier(.4,0,.2,1)}.grid-container.fullscreen-grid[_ngcontent-%COMP%]{animation:_ngcontent-%COMP%_expandGrid .3s ease-out}@keyframes _ngcontent-%COMP%_expandGrid{0%{opacity:.8;transform:scale(.95)}to{opacity:1;transform:scale(1)}}[_nghost-%COMP%]     .dropdown{z-index:999999!important;position:relative!important}[_nghost-%COMP%]     .excel-dropdown-menu{z-index:999999!important;position:absolute!important}[_nghost-%COMP%]     .k-grid, [_nghost-%COMP%]     .k-grid-header, [_nghost-%COMP%]     .k-grid-header-wrap{z-index:1!important}\"]\n    });\n  }\n  return PermitListComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
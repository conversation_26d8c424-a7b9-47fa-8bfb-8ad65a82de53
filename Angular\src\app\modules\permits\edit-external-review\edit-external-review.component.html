<div class="modal-content h-auto">
  <!-- Header -->
  <div class="modal-header bg-light-primary">
    <div class="modal-title h5 fs-3">
      <ng-container>
        <div>Edit External Review - {{ reviewData?.name }}</div>
      </ng-container>
    </div>
    <div class="float-right">
      <i
        class="fa-solid fs-2 fa-xmark text-white"
        (click)="onCancel()"
      ></i>
    </div>
  </div>

  <!-- Body -->
  <div
    class="modal-body "
   
  >
   <!-- style="max-height: calc(100vh - 250px); overflow-y: auto; position: relative;" -->
    <!-- Loading Overlay -->
    <div *ngIf="isLoading" class="fullscreen-loading-overlay">
      <div class="loading-content">
        <div class="custom-colored-spinner" role="status">
          <span class="visually-hidden">Loading...</span>
        </div>
        <div class="mt-4 fs-5">Loading review data...</div>
      </div>
    </div>
    <div class="dates-section">
      <div class="date-row">
        <div class="date-item">
          <label class="fw-bold form-label mb-2">Reviwer</label>
          <span class="date-value">{{ reviewData?.reviewer }}</span>
        </div>
        <div class="date-item">
          <label class="fw-bold form-label mb-2">Status</label>
          <span class="review-status" [ngClass]="getStatusClass(reviewData.status)">
            {{ reviewData?.status }}
          </span>
          <!-- <span class="date-value" [ngClass]="getStatusClass(reviewData.status)">{{ reviewData?.status }}</span> -->
        </div>
        <div class="date-item">
          <label class="fw-bold form-label mb-2">Due Date</label>
          <span class="date-value">{{ reviewData?.dueDate | date:'MM/dd/yyyy' }}</span>
        </div>
        <div class="date-item">
          <label class="fw-bold form-label mb-2">Completed Date</label>
          <span class="date-value">{{ reviewData?.completedDate | date:'MM/dd/yyyy' }}</span>
        </div>
      </div>
      <div class="date-row">
        <div class="date-item">
          <label class="fw-bold form-label mb-2">Comments</label>
          <span class="date-value">{{ reviewData?.comments }}</span>
        </div>
        </div>
    </div>

    <!-- Form -->
    <form [formGroup]="reviewForm" (ngSubmit)="onSubmit()" novalidate>



      <!-- City Comments -->
      <!-- <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">City Comments</label>
          <textarea
            formControlName="cityComments"
            rows="3"
            class="form-control form-control-sm"
            placeholder="Type here"
            readonly
            [disabled] =true
          ></textarea>
        </div>
      </div> -->

      <!-- EOR / AOR / Owner Response -->
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">EOR / AOR / Owner Response</label>
          <textarea
            formControlName="EORAOROwner_Response"
            rows="3"
            class="form-control form-control-sm"
            placeholder="Type here"
            [disabled]="isLoading"
          ></textarea>
        </div>
      </div>

      <!-- Comment Responded By -->
      <div class="row mt-4">
        <div class="col-xl-12">
          <label class="fw-bold form-label mb-2">Comment Responded By</label>
          <input
            type="text"
            formControlName="commentResponsedBy"
            class="form-control form-control-sm"
            placeholder="Type here"
            [disabled]="isLoading"
          />
        </div>
      </div>
    </form>
  </div>

  <!-- Footer -->
  <div class="modal-footer justify-content-end">
    <div>
      <button
        type="button"
        class="btn btn-danger btn-sm btn-elevate me-2"
        (click)="onCancel()"
        [disabled]="isLoading"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-success btn-sm"
        [disabled]="isLoading"
        (click)="onSubmit()"
      >
        <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
        Update
      </button>
    </div>
  </div>
</div>


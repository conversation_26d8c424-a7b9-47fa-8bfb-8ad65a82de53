// Response modal specific styles
.modal-header {
  background-color: #549c54;
  color: white;
  padding: 1rem 1.75rem;
}

.modal-title {
  color: #ffffff;
  font-weight: 600;
}
:host ::ng-deep .modal {
  --bs-modal-padding: 1rem !important;
}
.btn-close {
  filter: invert(1);
}

.modal-body {
  padding: 1rem;
}

.modal-footer {
  padding: 1rem 1.5rem;
  // border-top: 1px solid #dee2e6;
}

.form-label {
  font-weight: 600;
  color: #495057;
}

.form-control {
  border: 1px solid #ced4da;
  border-radius: 0.375rem;
  
  &:focus {
    border-color: #3699ff;
    box-shadow: 0 0 0 0.2rem rgba(54, 153, 255, 0.25);
  }
}

.btn-success {
  background-color: #198754;
  border-color: #198754;
  
  &:hover {
    background-color: #157347;
    border-color: #146c43;
  }
}

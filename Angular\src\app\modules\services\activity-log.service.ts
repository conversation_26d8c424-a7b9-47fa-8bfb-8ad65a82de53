import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, mergeMap } from 'rxjs/operators';
import { AppSettings } from 'src/app/app.settings';

@Injectable({
  providedIn: 'root'
})
export class ActivityLogService {

  constructor(private http: HttpClient) { }

  // Get activity logs for Kendo UI Grid
  public getActivityLogsForKendoGrid(state: any): Observable<any> {
    const requestBody = {
      take: state.take || 10,
      skip: state.skip || 0,
      sort: state.sort || [],
      filter: state.filter || { logic: 'and', filters: [] },
      search: state.search || ''
    };

    return this.http.post(`${AppSettings.REST_ENDPOINT}/getActivityLogsForKendoGrid`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getActivityLogsForKendoGrid:', err);
          return of({
            data: [],
            total: 0,
            errors: ['Error retrieving activity logs']
          });
        })
      );
  }

  // Get activity log by ID
  public getActivityLog(id: number): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getActivityLogById`, { activityId: id });
  }
}

import { ChangeDetectorRef, Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Observable } from 'rxjs';
import * as _ from 'lodash';
import { each } from 'lodash';
import { AppService } from '../../services/app.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-role-edit',
  templateUrl: './role-edit.component.html',
  styleUrls: ['./role-edit.component.scss']
})
export class RoleEditComponent {
 @Input() id: number; // RolePID -input from the role component
  @Input() permissions: any;// Permissions - input from the role component
  @Output() passEntry: EventEmitter<any> = new EventEmitter(); // output tp the role component
  editroles: FormGroup; //Initialize the form
  role: any = {}; // field to save the role details
  permissionArray: any = [];// field to save the initial values of default permissions
  perNameArray: any = [];// field to save the Name's  of the Permissions in a array
  rolePermissions: any = [];// field to save the final values of the Permissions for the role
  Permissions: any = []; //store permission array
  loginUser:any={}; //store local storage data of logged in user
  selectedpermission:any=[];//store permission array based on role name changes
  roleName:any ='';
  isLoading$: Observable<boolean>; // Loading state observable
  constructor(public modal: NgbActiveModal,
    private cdr:ChangeDetectorRef,
    private httpUtilService:HttpUtilsService,
    private layoutUtilService:CustomLayoutUtilsService,
    private fb: FormBuilder,
    public appService:AppService,
    private UserService:UserService) { 
      this.isLoading$ = this.httpUtilService.loadingSubject.asObservable();
    }

  ngOnInit(): void {
    this.loginUser = this.appService.getLoggedInUser()
    this.permissionArray = this.permissions; // get the initial values of the default permissions
    this.loadForm(); // load the form variables
    if (this.id !== 0) {
      //get the Role details and patch the values to the form fields
      this.patchForm();
    }
  }

    /**
   * Form initialization
   * Default params, validators
   */

   loadForm() {
    const formGroup: any = {};
    //assign the form fields
    formGroup['roleName'] = new FormControl('', Validators.compose([Validators.required]));
    // formGroup['systemRole'] = new FormControl(null,Validators.compose([Validators.required]));
    formGroup['description'] = new FormControl('');
    // formGroup['status'] = new FormControl('');

    let pArray: any = [];
    // assign the form fields for Permissions
    this.permissionArray.forEach((perm: any) => {
      pArray.push(perm.Name);
      formGroup[perm.Name] = new FormControl('');
    });
    //Group the form field and assign it to the form group
    this.editroles = this.fb.group(formGroup);
    // field to display the Permission in UI
    this.perNameArray = pArray;
  }
  //function to patch field from API
  patchForm() {
    this.httpUtilService.loadingSubject.next(true);
    this.UserService.getRole({ roleId: this.id }).subscribe((role: any) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!role.isFault) {
        let rPerms = JSON.parse(role.responseData.rolePermissions);
        this.roleName = role.responseData.roleName
        this.editroles.patchValue({
          roleName: role.responseData.roleName,
          description: role.responseData.description,
          // systemRole:role.responseData.DefaultRoleId,
          // status:role.responseData.status
        });

        // patch the permission values
       let self: any = this;
        each(rPerms, (r) => {
           _.forEach(r, function (value, key) {
             self.editroles.patchValue({
              [key]: value,
            });

          });
        });
      }
    });

  }
  //function to change permission array based on role name changes
  changeSystemAccess(event:any){
    this.selectedpermission  = JSON.parse(event.Permissions);

    let perArray: any = [];
    let self: any = this;
    each(this.selectedpermission, (r) => {
      _.forEach(r, function (value, key) {
        self.editroles.patchValue({
          [key]: value,
        });
      });
    });

  }

  //Form submit
  save(){
    let roleData: any = this.prepareRole();
    console.log('roleData ', roleData)
    const controls = this.editroles.controls;
    if (this.editroles.invalid) {
      Object.keys(controls).forEach(controlName =>
        controls[controlName].markAsTouched()
      );
      this.layoutUtilService.showError('Please fill all required fields', '');
      return;
    }
    if (this.id === 0) {
      this.create(roleData);
    } else {
      this.edit(roleData);
    }
  }
   // API to update the role details based on the RolePID
   edit(roleData: any) {
    this.httpUtilService.loadingSubject.next(true);
    this.UserService.editRole(roleData).subscribe((res:any) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.layoutUtilService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true);
        this.modal.close()
      } else {
        this.layoutUtilService.showError(res.responseData.message, '');
      }
    });

  }
 // API to save new role details
  create(roleData: any) {
    this.httpUtilService.loadingSubject.next(true);
    this.UserService.addRole(roleData).subscribe((res:any) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.layoutUtilService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true);
        this.modal.close()
      } else {
        this.layoutUtilService.showError(res.responseData.message, '');
      }
    });

  }
  //function to map input form fields to API fields
  prepareRole() {
    const formData = this.editroles.value;
    this.role.roleId = this.id;
    // this.role.DefaultRoleId = formData.systemRole;
    // this.role.DefaultRoleId = '';
    this.role.roleName = formData.roleName;
    this.role.description = formData.description;
    this.role.loggedInUserId= this.loginUser.userId;
    this.role.status= 'Active';
    let controls = this.editroles.controls;

    let perArray: any = [];
    let self: any = this;
    console.log('permissionArray ', self.permissionArray)
    _.forEach(controls, function (value, key) {
      console.log('valeu ', value, 'Key ',key);
      let rjson = _.find(self.permissionArray, function (o) {
        return o.Name === key;
      });
      if (rjson !== undefined) {
        let permissionJson = self.getPermissionJson(rjson, value);
        perArray.push(permissionJson);
      }
    });
    this.role.rolePermissions = perArray;
    return this.role;
  }

   // format the permission data for each permission
   getPermissionJson(permission: any, controls: any) {
    let newPermission = {
      [permission.Name]:controls.value,
    };
    return newPermission;
  }
}

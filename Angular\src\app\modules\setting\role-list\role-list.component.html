<!-- Full Screen Loading Overlay -->
<div *ngIf="loading || isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 fs-5">Loading...</div>
  </div>
</div>

<div class="grid-container">
  <kendo-grid
    #normalGrid
    [data]="serverSideRowData"
    [pageSize]="page.size"
    [sort]="sort"
    [pageable]="{
      pageSizes: [10, 15, 20, 50, 100],
      previousNext: true,
      info: true,
      type: 'numeric',
      buttonCount: 5
    }"
    [sortable]="{ allowUnsort: true, mode: 'single' }"
    [groupable]="false"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    (columnReorder)="onColumnReorder($event)"
    (selectionChange)="onSelectionChange($event)"
    [reorderable]="true"
    style="width: auto; overflow-x: auto"
    [resizable]="false"
    [height]="720"
    [skip]="skip"
    [filter]="filter"
    [columnMenu]="{ filter: true }"
    (filterChange)="filterChange($event)"
    (pageChange)="pageChange($event)"
    (sortChange)="onSortChange($event)"
    (columnVisibilityChange)="updateColumnVisibility($event)"
  >
    <ng-template kendoGridToolbarTemplate>
      <!-- Search Section -->
      <div class="d-flex align-items-center me-3 search-section">
        <kendo-textbox
          [style.width.px]="500"
          placeholder="Search..."
          [(ngModel)]="searchData"
          [clearButton]="true"
          (keydown)="onSearchKeyDown($event)"
          (valueChange)="onSearchChange()"
        ></kendo-textbox>
      </div>

      <kendo-grid-spacer></kendo-grid-spacer>

      <!-- Total Count - Repositioned to the right -->
      <div class="d-flex align-items-center me-3">
        <span class="text-muted">Total: </span>
        <span class="fw-bold ms-1">{{ page.totalElements || 0 }}</span>
      </div>

      <!-- Action Buttons -->
      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="create()"
        title="Add Role"
      >
        <span
          [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'"
          class="svg-icon svg-icon-3 text-primary"
        ></span>
      </button>

      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="toggleExpand()"
        title="Toggle Grid Expansion"
      >
        <i
          class="fas text-secondary"
          [class.fa-expand]="!isExpanded"
          [class.fa-compress]="isExpanded"
        ></i>
      </button>

      <!-- <kendo-dropdownbutton
        text="Export Excel"
        iconClass="fas fa-file-excel"
        [data]="exportOptions"
        class="custom-dropdown"
        (itemClick)="onExportClick($event)"
        title="Export"
      >
      </kendo-dropdownbutton> -->

      <!-- Save Column Settings Button -->
      <!-- <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="saveHead()"
        title="Save Column Settings"
      >
        <i class="fas fa-save text-success"></i>
      </button> -->

      <!-- Reset Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="resetTable()"
        title="Reset to Default"
      >
        <i class="fas fa-undo text-warning"></i>
      </button>

      <!-- Refresh Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="refreshGrid()"
        title="Refresh Grid Data"
      >
        <i class="fas fa-sync-alt text-info"></i>
      </button>
    </ng-template>

    <!-- Advanced Filters Panel -->
    <ng-template kendoGridToolbarTemplate>
      <div
        *ngIf="showAdvancedFilters"
        class="advanced-filters-panel p-3 bg-light border-bottom"
      >
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Status</label>
            <kendo-dropdownlist
              [data]="advancedFilterOptions.status"
              [(ngModel)]="appliedFilters.status"
              textField="text"
              valueField="value"
              placeholder="Select Status"
            >
            </kendo-dropdownlist>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button
              kendoButton
              (click)="applyAdvancedFilters()"
              class="btn-primary me-2"
            >
              <i class="fas fa-check"></i> Apply Filters
            </button>
            <button
              kendoButton
              (click)="clearAllFilters()"
              class="btn-secondary"
            >
              <i class="fas fa-times"></i> Clear
            </button>
          </div>
        </div>
      </div>
    </ng-template>

    <ng-container *ngFor="let column of gridColumns">
      <!-- Action Column -->
      <kendo-grid-column
        *ngIf="column === 'action'"
        title="Actions"
        [width]="90"
        [sticky]="true"
        [reorderable]="!fixedColumns.includes('action')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [includeInChooser]="false"
        [columnMenu]="false"
        [sortable]="false"
        [style]="{ 'background-color': '#efefef !important' }"
        [hidden]="getHiddenField('action')"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <a
            title="Edit"
            class="btn btn-icon btn-sm"
            (click)="edit(dataItem.roleId)"
          >
            <span
              [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'"
              class="svg-icon svg-icon-3 svg-icon-primary"
            >
            </span>
          </a>
          <!-- Delete button hidden -->
          <!-- <a
            title="Delete"
            class="btn btn-icon btn-sm mx-1"
            *ngIf="dataItem.isDeletable === true"
            (click)="deleteRoles(dataItem)"
          >
            <span
              [inlineSVG]="'./assets/media/icons/duotune/general/gen027.svg'"
              class="svg-icon svg-icon-3 svg-icon-danger"
            >
            </span>
          </a> -->
        </ng-template>
      </kendo-grid-column>

      <!-- Name Column -->
      <kendo-grid-column
        *ngIf="column === 'roleName'"
        field="roleName"
        title="Name"
        [width]="200"
        [sticky]="true"
        [reorderable]="!fixedColumns.includes('roleName')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [includeInChooser]="false"
        [hidden]="getHiddenField('roleName')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span class="fw-bolder cursor-pointer">
              {{ dataItem.roleName }}
            </span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Permissions Column -->
      <kendo-grid-column
        *ngIf="column === 'rolePermissions'"
        field="rolePermissions"
        title="Permissions"
        [width]="400"
        [reorderable]="!fixedColumns.includes('rolePermissions')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('rolePermissions')"
        [filterable]="true"
        [sortable]="false"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div [innerHTML]="formatPermission(dataItem.rolePermissions)"></div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Status Column -->
      <kendo-grid-column
        *ngIf="column === 'status'"
        field="status"
        title="Status"
        [width]="100"
        [reorderable]="!fixedColumns.includes('status')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('status')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <span
            *ngIf="dataItem.status === 'Active'"
            ngbTooltip="Active"
            [inlineSVG]="'./assets/media/icons/duotune/general/gen037.svg'"
            class="svg-icon svg-icon-3 svg-icon-success"
          >
            {{ dataItem.status }}
          </span>
          <span
            *ngIf="dataItem.status === 'Inactive'"
            ngbTooltip="Inactive"
            [inlineSVG]="'./assets/media/icons/duotune/general/gen040.svg'"
            class="svg-icon svg-icon-3 svg-icon-danger text-danger"
          >
            {{ dataItem.status }}
          </span>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Last Updated Date Column -->
      <kendo-grid-column
        *ngIf="column === 'lastUpdatedDate'"
        field="lastUpdatedDate"
        title="Updated Date"
        [width]="180"
        [reorderable]="!fixedColumns.includes('lastUpdatedDate')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('lastUpdatedDate')"
        [filterable]="true"
        format="MM/dd/yyyy"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span class="fw-bolder"
              >{{ dataItem.lastUpdatedDate | date : "MM/dd/yyyy" }}
              {{ dataItem.lastUpdatedDate | date : "hh:mm a" }}</span
            ><br />
            <span>{{ dataItem.lastUpdatedByFullName }}</span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-gte-operator></kendo-filter-gte-operator>
            <kendo-filter-lte-operator></kendo-filter-lte-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
    </ng-container>

    <!-- No Data Template -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="custom-no-records" *ngIf="!loading && !isLoading">
        <div class="text-center">
          <i
            class="fas fa-shield-alt text-muted mb-2"
            style="font-size: 2rem"
          ></i>
          <p class="text-muted">No roles found</p>
          <button kendoButton (click)="loadTable()" class="btn-primary">
            <i class="fas fa-refresh me-2"></i>Refresh
          </button>
        </div>
      </div>
    </ng-template>
  </kendo-grid>
</div>

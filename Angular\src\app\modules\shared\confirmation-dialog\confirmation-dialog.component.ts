import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-confirmation-dialog',
  templateUrl: './confirmation-dialog.component.html',
  styleUrls: ['./confirmation-dialog.component.scss']
})
export class ConfirmationDialogComponent implements OnInit {

  @Input() showClose:boolean = true;
  @Input() description:string ='';
  @Input() actionButtonText:string ='';
  @Input() cancelButtonText:string='';
  @Input() title:string = '';
  @Input() selectedTab: string;

  @Output() passEntry: EventEmitter<any> = new EventEmitter();
  constructor(public modal: NgbActiveModal,
  ) { }

  ngOnInit(): void {
  }

  onYesClick(): void {
    this.passEntry.emit({ success: true, selectedTab: this.selectedTab });
    // this.passEntry.emit(true)
    this.modal.close();
	}

  onCancelClick(): void {
    this.passEntry.emit({ success: false, selectedTab: this.selectedTab });
    // this.passEntry.emit(false)
    this.modal.close();
	}


}

{"ast": null, "code": "import { catchError, from, mergeMap, of } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport let PermitsService = /*#__PURE__*/(() => {\n  class PermitsService {\n    http;\n    constructor(http) {\n      this.http = http;\n    }\n    handleError(operation = 'operation', result) {\n      return error => {\n        // TODO: send the error to remote logging infrastructure\n        console.error(error); // log to console instead\n        // Let the app keep running by returning an empty result.\n        return from(result);\n      };\n    }\n    getAllPermits(queryParams) {\n      const requestBody = {\n        pageSize: queryParams.pageSize,\n        pageNumber: queryParams.pageNumber,\n        sortField: queryParams.sortField,\n        sortOrder: queryParams.sortOrder,\n        paginate: true,\n        search: queryParams.filter?.search || '',\n        columnFilter: queryParams.filter?.columnFilter || []\n      };\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllPermits`, requestBody).pipe(mergeMap(res => {\n        return of(res);\n      }), catchError(err => {\n        console.error('Error in getAllPermits:', err);\n        return of({\n          error: {\n            isFault: true\n          },\n          message: 'Error retrieving Permits'\n        });\n      }));\n    }\n    getPermitsForKendoGrid(state) {\n      const requestBody = {\n        take: state.take || 10,\n        skip: state.skip || 0,\n        sort: state.sort || [],\n        filter: state.filter || {\n          logic: 'and',\n          filters: []\n        },\n        search: state.search || '',\n        loggedInUserId: state.loggedInUserId\n      };\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsForKendoGrid`, requestBody).pipe(mergeMap(res => {\n        return of(res);\n      }), catchError(err => {\n        console.error('Error in getPermitsForKendoGrid:', err);\n        return of({\n          data: [],\n          total: 0,\n          errors: ['Error retrieving Permits']\n        });\n      }));\n    }\n    createPermit(data) {\n      return this.http.post(AppSettings.REST_ENDPOINT + '/createPermit', data);\n    }\n    updatePermit(data) {\n      return this.http.post(AppSettings.REST_ENDPOINT + '/editPermit', data);\n    }\n    updatePermitInternalReviewStatus(data) {\n      return this.http.post(AppSettings.REST_ENDPOINT + '/updatePermitInternalReviewStatus', data);\n    }\n    getPermit(data) {\n      return this.http.post(AppSettings.REST_ENDPOINT + '/getPermitById', data);\n    }\n    deletePermit(data) {\n      return this.http.post(AppSettings.REST_ENDPOINT + '/deletePermit', data);\n    }\n    searchPermits(searchTerm) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/searchPermits`, {\n        search: searchTerm\n      });\n    }\n    exportPermits(exportType, selectedIds) {\n      const requestBody = {\n        exportType: exportType,\n        selectedIds: selectedIds || []\n      };\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/exportPermits`, requestBody);\n    }\n    getAllMunicipalities(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllMunicipalities`, params);\n    }\n    getAllReviews(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllReviews`, params);\n    }\n    syncPermits(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/syncPermits`, params);\n    }\n    // Internal Reviews Methods\n    addInternalReview(data) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/addInternalReview`, data);\n    }\n    updateInternalReview(data) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/updateInternalReview`, data);\n    }\n    getInternalReviews(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/getInternalReviews`, params);\n    }\n    updateExternalReview(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/updateExternalReview`, params);\n    }\n    getPermitDetails(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitDetails`, params);\n    }\n    editNotesAndActions(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/editNotesAndActions`, params);\n    }\n    validatePermitNumber(params) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/validatePermitNumber`, params);\n    }\n    getPermitsByProjectId(projectId) {\n      return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsByProjectId`, {\n        projectId\n      });\n    }\n    static ɵfac = function PermitsService_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || PermitsService)(i0.ɵɵinject(i1.HttpClient));\n    };\n    static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: PermitsService,\n      factory: PermitsService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n  return PermitsService;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
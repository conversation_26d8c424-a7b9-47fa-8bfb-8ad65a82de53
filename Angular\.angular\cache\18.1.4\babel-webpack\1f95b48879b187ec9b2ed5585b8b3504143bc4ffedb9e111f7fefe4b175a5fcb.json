{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./splash-screen.service\";\nconst _c0 = [\"splashScreen\"];\nexport class SplashScreenComponent {\n  splashScreenService;\n  splashScreen;\n  constructor(splashScreenService) {\n    this.splashScreenService = splashScreenService;\n  }\n  ngOnInit() {\n    this.splashScreenService.init(this.splashScreen);\n  }\n  static ɵfac = function SplashScreenComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || SplashScreenComponent)(i0.ɵɵdirectiveInject(i1.SplashScreenService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: SplashScreenComponent,\n    selectors: [[\"app-splash-screen\"]],\n    viewQuery: function SplashScreenComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.splashScreen = _t.first);\n      }\n    },\n    decls: 4,\n    vars: 0,\n    consts: [[\"splashScreen\", \"\"], [\"id\", \"splash-screen\"], [\"viewBox\", \"0 0 50 50\", 1, \"splash-spinner\"], [\"cx\", \"25\", \"cy\", \"25\", \"r\", \"20\", \"fill\", \"none\", \"stroke-width\", \"5\", 1, \"path\"]],\n    template: function SplashScreenComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 1, 0);\n        i0.ɵɵnamespaceSVG();\n        i0.ɵɵelementStart(2, \"svg\", 2);\n        i0.ɵɵelement(3, \"circle\", 3);\n        i0.ɵɵelementEnd()();\n      }\n    },\n    styles: [\"#splash-screen[_ngcontent-%COMP%] {\\n  position: absolute;\\n  z-index: 1000;\\n  width: 100%;\\n  height: 100%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-direction: column;\\n  background-color: #f2f3f8;\\n}\\n\\n#splash-screen[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  margin-left: calc(100vw - 100%);\\n  margin-bottom: 30px;\\n}\\n\\n#splash-screen.hidden[_ngcontent-%COMP%] {\\n  opacity: 0;\\n  visibility: hidden;\\n}\\n\\n.splash-spinner[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_rotate 2s linear infinite;\\n  margin-left: calc(100vw - 100%);\\n  width: 50px;\\n  height: 50px;\\n}\\n\\n.splash-spinner[_ngcontent-%COMP%]   .path[_ngcontent-%COMP%] {\\n  stroke: #5d78ff;\\n  stroke-linecap: round;\\n  animation: _ngcontent-%COMP%_dash 1.5s ease-in-out infinite;\\n}\\n\\n@keyframes _ngcontent-%COMP%_rotate {\\n  100% {\\n    transform: rotate(360deg);\\n  }\\n}\\n@keyframes _ngcontent-%COMP%_dash {\\n  0% {\\n    stroke-dasharray: 1, 150;\\n    stroke-dashoffset: 0;\\n  }\\n  50% {\\n    stroke-dasharray: 90, 150;\\n    stroke-dashoffset: -35;\\n  }\\n  100% {\\n    stroke-dasharray: 90, 150;\\n    stroke-dashoffset: -124;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["SplashScreenComponent", "splashScreenService", "splashScreen", "constructor", "ngOnInit", "init", "i0", "ɵɵdirectiveInject", "i1", "SplashScreenService", "selectors", "viewQuery", "SplashScreenComponent_Query", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\partials\\layout\\splash-screen\\splash-screen.component.ts", "D:\\permittracker\\Angular\\src\\app\\_metronic\\partials\\layout\\splash-screen\\splash-screen.component.html"], "sourcesContent": ["import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';\nimport { SplashScreenService } from './splash-screen.service';\n\n@Component({\n  selector: 'app-splash-screen',\n  templateUrl: './splash-screen.component.html',\n  styleUrls: ['./splash-screen.component.scss'],\n})\nexport class SplashScreenComponent implements OnInit {\n  @ViewChild('splashScreen', { static: true }) splashScreen: ElementRef;\n\n  constructor(private splashScreenService: SplashScreenService) {}\n\n  ngOnInit(): void {\n    this.splashScreenService.init(this.splashScreen);\n  }\n}\n", "<!-- splash screen -->\r\n<div #splashScreen id=\"splash-screen\">\r\n  <!-- Logo removed to prevent showing behind loader spinner -->\r\n  <!-- <img src=\"./assets/media/logos/default.svg\" alt=\"Logo\" /> -->\r\n  <svg class=\"splash-spinner\" viewBox=\"0 0 50 50\">\r\n    <circle\r\n      class=\"path\"\r\n      cx=\"25\"\r\n      cy=\"25\"\r\n      r=\"20\"\r\n      fill=\"none\"\r\n      stroke-width=\"5\"\r\n    ></circle>\r\n  </svg>\r\n</div>\r\n"], "mappings": ";;;AAQA,OAAM,MAAOA,qBAAqB;EAGZC,mBAAA;EAFyBC,YAAY;EAEzDC,YAAoBF,mBAAwC;IAAxC,KAAAA,mBAAmB,GAAnBA,mBAAmB;EAAwB;EAE/DG,QAAQA,CAAA;IACN,IAAI,CAACH,mBAAmB,CAACI,IAAI,CAAC,IAAI,CAACH,YAAY,CAAC;EAClD;;qCAPWF,qBAAqB,EAAAM,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,mBAAA;EAAA;;UAArBT,qBAAqB;IAAAU,SAAA;IAAAC,SAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;QCPlCP,EAAA,CAAAS,cAAA,gBAAsC;;QAGpCT,EAAA,CAAAS,cAAA,aAAgD;QAC9CT,EAAA,CAAAU,SAAA,gBAOU;QAEdV,EADE,CAAAW,YAAA,EAAM,EACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
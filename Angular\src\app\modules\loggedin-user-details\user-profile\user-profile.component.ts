import { Component } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ChangeDetectorRef, EventEmitter, Input, OnInit, Output } from '@angular/core';

import { AppService } from '../../services/app.service';
import { FormGroup, FormBuilder, FormControl, Validators } from '@angular/forms';

import { HttpUtilsService } from '../../services/http-utils.service';
import { AppSettings } from 'src/app/app.settings';
import { Router } from '@angular/router';


import * as _ from 'lodash';
import { map } from 'rxjs';
import { UserService } from '../../services/user.service';
import { each } from 'lodash';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { PageInfoService } from '../../../_metronic/layout/core/page-info.service';
@Component({
  selector: 'app-user-profile',
  templateUrl: './user-profile.component.html',
  styleUrl: './user-profile.component.scss'
})
export class UserProfileComponent {
  @Input() id: number;
    @Input() defaultPermissions:any;
    @Output() passEntry: EventEmitter<any> = new EventEmitter();
    userForm: FormGroup;
    users:any={};
    loginUser:any={};
    roleArray: any = []
    medicalCentersArray:any=[];
    pharmacyArray:any =[]
    image = '';
    imageUrl = '';
    perNameArray: any = [];//store permission in UI
    permissionArray: any = [];// field to save the initial values of default permissions
    selectedTab: string = 'basic'; //store navigation tab
    IsAutoPassword: boolean = true;
    newPasswordShown = true; //boolean for password shown
    showExternalPassword = false;
    isLoading: boolean = false;

  constructor(
    private cdr: ChangeDetectorRef,
    private httpUtilService: HttpUtilsService,
    private layoutUtilService: CustomLayoutUtilsService,
    private fb: FormBuilder,
    public appService: AppService,
    private userService: UserService,
    private router: Router,
    private pageInfo: PageInfoService,
    ) {
    this.httpUtilService.loadingSubject.subscribe((loading) => {
      this.isLoading = !!loading;
      this.cdr.markForCheck();
    });
     }
    //policyForm: FormGroup;

  ngOnInit(): void {
    this.pageInfo.updateTitle('Profile');
    this.loginUser = this.appService.getLoggedInUser();
    console.log("Call add client component", this.loginUser )
    // this.permissionArray = this.defaultPermissions;
    this.loadForm();
    // if (this.id !== 0) {
    setTimeout(() => {
      
      this.patchForm();
    }, 250);
    // }
 

    this.cdr.markForCheck();
  }


  loadForm() {
    const formGroup: any = {};
    formGroup['firstname'] = new FormControl('', Validators.compose([Validators.required]));
    formGroup['lastname'] = new FormControl('', Validators.compose([Validators.required]));
    formGroup['email'] = new FormControl('',Validators.compose([Validators.email]));
    formGroup['phone'] = new FormControl('');
    // formGroup['password'] = new FormControl('');
    // formGroup['IsAuto'] = new FormControl('');
    // formGroup['role'] = new FormControl('');
    formGroup['title'] = new FormControl('');
    //  formGroup['role'] = new FormControl('');
    // formGroup['status'] = new FormControl('');
    // let pArray: any = [];
    // // assign the form fields for Permissions
    // this.permissionArray.forEach((perm: any) => {
    //   pArray.push(perm.Name);
    //   formGroup[perm.Name] = new FormControl('');
    // });
    // this.perNameArray = pArray;
    this.userForm = this.fb.group(formGroup);
    // field to display the Permission in UI
    // this.perNameArray = pArray;
  }


  //function to modify boolean depending on whether the  password eye symbol is on or off
  newshowpassword(event: any) {
    this.newPasswordShown = event;
  }
  togglePasswordVisibility(): void {
    this.showExternalPassword = !this.showExternalPassword;
  }

  onAutoPasswordChange(event: any) {
    const controls = this.userForm.controls
    this.IsAutoPassword = controls.IsAuto.value;
    if (this.IsAutoPassword == false) {
      this.userForm.get('password')?.setValidators(Validators.compose([Validators.required]));
      this.userForm.get('password')?.updateValueAndValidity();
      this.userForm.controls['password'].enable()
    }
    else {
      this.userForm.controls['password'].disable()
    }

  }

  patchForm(){
    // this.httpUtilService.loadingSubject.next(true);
    // console.log("Call patch form component line: 151" , this.id)
     this.userForm.patchValue({
      
            firstname: this.loginUser.firstName,
            lastname: this.loginUser.lastName,
            phone: this.loginUser.phoneNo?.replace(/[()\-\sExt.]/g, '') || '',
            // status: userData.userStatus === 'Active' ? 'true' : 'false',
            email: this.loginUser.email,
            title: this.loginUser.title,
            // role: userData.roleId,
            // pharmacyId:userData.pharmacyId,
            // medicalCenterId:userData.medicalCenterId
          });
    // this.userService.getUser({ userId: this.id,loggedInUserId :this.loginUser.userId  }).subscribe({
    //   next: (user: any) => {
    //     console.log("Line: 153", user.responseData.data); // <-- This should now print

    //     this.httpUtilService.loadingSubject.next(false);

    //     if (!user.isFault) {
    //       const userData = user.responseData;
    //       let rPerms = JSON.parse(user.responseData.rolePermissions );
    //       this.image = user.responseData.imageName
    //       this.imageUrl = this.appService.ImageUrl(this.image)
         

    //           // patch the permission values
    //       let self: any = this;
    //       each(rPerms, (r:any) => {
    //         _.forEach(r, function (value, key) {
    //           self.userForm.patchValue({
    //             [key]: value,
    //           });

    //         });
    //       });
    //     } else {
    //       console.warn("User response has isFault = true", user.responseData);
    //     }
    //   },
    //   error: (err) => {
    //     this.httpUtilService.loadingSubject.next(false);
    //     console.error("API call failed", err);
    //   }
    // });

  }


  //function to browse imgae file
  browseFile(event: any) {
    const fSize = Math.round(event.target.files[0].size / 1024);
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.readAsDataURL(file);
    let selectedImage = file.name;
    const fileData = new FormData();
    fileData.append('files', file)
    if (fSize > 500) {
      this.layoutUtilService.showError(selectedImage + ' - File size upto 500KB.Couldnt upload the files', '');
    } else {
      this.userService.uploadImage(fileData).subscribe((res: any) => {
        if (!res.isFault) {
          this.image = res.responseData.fileName;
          this.imageUrl = AppSettings.IMAGEPATH + this.image;
          this.cdr.markForCheck();
        }
      });
    }
  }

 



updateProfile(){

  // console.log("this.userForm",this.userForm.value)
  let userData = {
    firstName:this.userForm.value.firstname,
    lastName:this.userForm.value.lastname,
    phoneNo:this.userForm.value.phone,
    title:this.userForm.value.title,
    userId:this.loginUser.userId
  }

  this.loginUser.firstName = this.userForm.value.firstname
  this.loginUser.lastName = this.userForm.value.lastname
  this.loginUser.phoneNo = this.userForm.value.phone
  this.loginUser.title = this.userForm.value.title

 
 this.httpUtilService.loadingSubject.next(true);
 this.userService.editUserProfile(userData).subscribe({
      next: (res: any) => {
        this.httpUtilService.loadingSubject.next(false);
        if (!res.isFault) {
          this.layoutUtilService.showSuccess(res.responseData.message, '');
          this.appService.setLocalStorageItem("permitUser",this.loginUser, true)
        } else {
          this.layoutUtilService.showError(res.responseData.message, '');
        }
      },
      error: () => {
        this.httpUtilService.loadingSubject.next(false);
      }
    });

}

cancelToDashboard(){
  this.router.navigate(['/dashboard']);
}
}

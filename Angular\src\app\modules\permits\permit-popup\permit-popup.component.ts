import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  OnInit,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ProjectsService } from '../../services/projects.service';
import { PermitsService } from '../../services/permits.service';
import { AppService } from '../../services/app.service';
import { HttpUtilsService } from '../../services/http-utils.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { even } from '@rxweb/reactive-form-validators';
import { debounceTime, distinctUntilChanged, switchMap, catchError, map } from 'rxjs/operators';
import { of } from 'rxjs';

@Component({
  selector: 'app-permit-popup',
  templateUrl: './permit-popup.component.html',
})
export class PermitPopupComponent {
  @Input() id: number = 0; // 0 = Add, otherwise Edit
  @Input() isHideInternalReviewStatus: Boolean = true; // 0 = Add, otherwise Edit
  @Input() permit: any; // incoming permit data (for edit)
  @Output() passEntry: EventEmitter<any> = new EventEmitter();

  permitForm: FormGroup;
  projects: any[] = [];
  reviewTypeArray: any[] = ['Internal', 'External', 'Both'];
  loginUser: any = {};
  isLoading: boolean = false;
  muncipalities: any = [];
  selectedTab: any = 'basic';
  // dropdown options
  permitTypes = [
    'Access Control System - Commercial',
    'Addition - Commercial',
    'Addition - Residential',
    'Backflow - Commercial',
    'Building Miscellaneous - Commercial',
    'Building Move Permit - Residential',
    'Building Revisions - Commercial Revision',
    'Certificate of Completion',
    'Certificate of Occupancy - Commercial',
    'Commercial - LV Data Voice Cable Sub-Permit',
    'Demolition - Commercial',
    'Document Submittal - Commercial Building',
    'Electrical Sub-Permit - Commercial',
    'Engineering Construction Traffic & Parking Management Plan',
    'Fence - Commercial',
    'Fire Alarm - Fire',
    'Fire Sprinkler/Fire Suppression - Fire',
    'Foundation Only - Commercial',
    'Gas Sub-Permit - Commercial',
    'General Electrical - Commercial',
    'General Paving - Paving',
    'General Sign Permit',
    'Generator - Commercial',
    'Interceptor - Commercial',
    'Interior (<5000 sq ft) - Commercial',
    'Irrigation - Commercial',
    'Landscape Non-Residential and Multi-Family',
    'Low Voltage - Commercial',
    'Mechanical Sub-Permit - Commercial',
    'Monument - Sign',
    'Mural - Sign',
    'New Building - Commercial',
    'Plumbing Sub-Permit - Commercial',
    'Pool Plumbing Commercial (Sub-Permit)',
    'Public Art Permit Application',
    'Remodel - Commercial',
    'Right-of-Way | ENG A - General',
    'Sewer Cap for Demo - Commercial',
    'Windows and Doors - Commercial',
  ];
  categories = [
    'Primary',
    'Sub Permit',
    'Industrial',
    'Municipal',
    'Environmental',
  ];
  statuses = [
    'Canceled',
    'Complete',
    'Expired',
    'Fees Due',
    'In Review',
    'Issued',
    'On Hold',
    'Requires Resubmit',
    'Requires Resubmit for Prescreen',
    'Submitted - Online',
    'Void',
  ];
  internalStatusArray =['Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed']
  permitNumber: any;
  isPermitMunicipalRequired: boolean = false;
  iscityreviewLinkMunicipalRequired: boolean=false;
  cityReviewLink:any ='';
  syncedPermitData:any ={};
  permitNumberValidationError: string = '';
  isPermitNumberValidating: boolean = false;
  constructor(
    public modal: NgbActiveModal,
    private fb: FormBuilder,
    private projectsService: ProjectsService,
    private permitsService: PermitsService,
    private appService: AppService,
    private httpUtilService: HttpUtilsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private cdr: ChangeDetectorRef
  ) {
    // Subscribe to loading state
    this.httpUtilService.loadingSubject.subscribe((loading) => {
      this.isLoading = loading;
    });
  }

  ngOnInit() {
    this.loginUser = this.appService.getLoggedInUser();
    this.loadMunicipalities();
    this.loadForm();
    this.loadProjects();
    this.setupPermitNumberValidation();
    if (this.id !== 0) {
      this.patchForm();
    }
  }

  loadForm() {
    this.permitForm = this.fb.group({
      projectId: ['', Validators.required],
      permitName: ['', Validators.required],
      permitNumber: ['', Validators.required],
      permitCategory: ['', Validators.required],
      permitType: ['', Validators.required],
      description: [''],
      permitReviewType: ['', Validators.required],
      location: ['',Validators.required],
      primaryContact: [''],
      permitAppliedDate: ['', Validators.required],
      permitExpirationDate: [''],
      permitIssueDate: [''],
      permitFinalDate: [''],
      permitCompleteDate: [''],
      permitStatus: ['', Validators.required],
      internalReviewStatus: ['', Validators.required],
      attentionReason: [''],
      internalNotes: [''],
      actionTaken: [''],
      reviewResponsibleParty: ['', Validators.required],
      permitMunicipality: [''],
      // cityReviewLink: [''],
    });
  }

  setupPermitNumberValidation() {
    const permitNumberControl = this.permitForm.get('permitNumber');
    if (permitNumberControl) {
      // Only clear server-side validation while typing; don't call API here
      permitNumberControl.valueChanges
        .pipe(distinctUntilChanged())
        .subscribe(() => {
          this.permitNumberValidationError = '';
          if (permitNumberControl.hasError('permitNumberExists')) {
            const errors: any = { ...permitNumberControl.errors };
            delete errors['permitNumberExists'];
            permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);
          }
        });
    }
  }

  triggerPermitNumberValidation() {
    const permitNumberControl = this.permitForm.get('permitNumber');
    const projectIdControl = this.permitForm.get('projectId');
    const permitNumber = permitNumberControl?.value;
    const projectId = projectIdControl?.value;

    const enteredNormalized = (permitNumber || '').toString().trim().toLowerCase();
    const originalNormalized = (this.permitNumber || '').toString().trim().toLowerCase();

    // If empty, or in edit mode and value hasn't changed, just clear any prior error and skip API
    if (!enteredNormalized || (this.id !== 0 && enteredNormalized === originalNormalized)) {
      this.isPermitNumberValidating = false;
      this.permitNumberValidationError = '';
      if (permitNumberControl?.hasError('permitNumberExists')) {
        const errors: any = { ...permitNumberControl.errors };
        delete errors['permitNumberExists'];
        permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);
      }
      return;
    }

    if (enteredNormalized && projectId) {
      this.isPermitNumberValidating = true;
      this.validatePermitNumber(permitNumber, projectId)
        .pipe(map((res: any) => res?.responseData ?? res))
        .subscribe((result: any) => {
          this.isPermitNumberValidating = false;
          if (result && result.exists) {
            this.permitNumberValidationError = result.message;
            permitNumberControl?.setErrors({ 'permitNumberExists': true });
          } else {
            this.permitNumberValidationError = '';
            if (permitNumberControl?.hasError('permitNumberExists')) {
              const errors: any = { ...permitNumberControl.errors };
              delete errors['permitNumberExists'];
              permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);
            }
          }
        });
    }
  }

  validatePermitNumber(permitNumber: string, projectId: number) {
    this.isPermitNumberValidating = true;
    this.permitNumberValidationError = '';

    const params = {
      permitNumber: permitNumber,
      projectId: projectId,
      permitId: this.id !== 0 ? this.id : null // Include permitId for edit mode
    };

    return this.permitsService.validatePermitNumber(params).pipe(
      catchError(() => {
        this.isPermitNumberValidating = false;
        return of({ exists: false, message: '' });
      })
    );
  }

  loadProjects() {
    this.httpUtilService.loadingSubject.next(true);
    const params = { paginate: false };
    this.projectsService.getAllProjectsData(params).subscribe({
      next: (response: any) => {
        this.httpUtilService.loadingSubject.next(false);
        if (response && response.responseData) {
          this.projects = response.responseData.data;
        }
      },
      error: (error: any) => {
        this.httpUtilService.loadingSubject.next(false);
        console.error('Error loading projects:', error);
        this.projects = [];
      },
    });
  }

  loadMunicipalities() {
    this.httpUtilService.loadingSubject.next(true);
    const params = { loggedinUser: this.loginUser.userId };
    this.permitsService.getAllMunicipalities(params).subscribe({
      next: (response: any) => {
        this.httpUtilService.loadingSubject.next(false);
        if (response && response.responseData) {
          this.muncipalities = response.responseData.data;
        }
      },
      error: (error: any) => {
        this.httpUtilService.loadingSubject.next(false);
        console.error('Error loading projects:', error);
        this.muncipalities = [];
      },
    });
  }

  patchForm() {
    this.httpUtilService.loadingSubject.next(true);
    this.permitsService
      .getPermit({ permitId: this.id, loggedInUserId: this.loginUser.userId })
      .subscribe({
        next: (permitResponse: any) => {
          this.httpUtilService.loadingSubject.next(false);
          if (!permitResponse.isFault) {
            let permitData = permitResponse.responseData.data;
            this.permitNumber = permitData.permitNumber;
            this.permitForm.patchValue({
              projectId: permitData.projectId,
              permitNumber: permitData.permitNumber,
              permitReviewType: permitData.permitReviewType,
              permitCategory: permitData.permitCategory,
              permitType: permitData.permitType,
              description: permitData.description,
              permitName: permitData.permitName,
              location: permitData.location,
              internalReviewStatus:permitData.internalReviewStatus,
              primaryContact: permitData.primaryContact,
              permitAppliedDate: permitData.permitAppliedDate
                ? this.formatDateForInput(permitData.permitAppliedDate)
                : '',
              permitExpirationDate: permitData.permitExpirationDate
                ? this.formatDateForInput(permitData.permitExpirationDate)
                : '',
              permitIssueDate: permitData.permitIssueDate
                ? this.formatDateForInput(permitData.permitIssueDate)
                : '',
                  permitFinalDate: permitData.permitFinalDate
                ? this.formatDateForInput(permitData.permitFinalDate)
                : '',
              permitCompleteDate: permitData.permitCompleteDate
                ? this.formatDateForInput(permitData.permitCompleteDate)
                : '',
              permitStatus: permitData.permitStatus,
              attentionReason: permitData.attentionReason,
              internalNotes: permitData.internalNotes,
              actionTaken: permitData.actionTaken,
              reviewResponsibleParty: permitData.reviewResponsibleParty,
              permitMunicipality: permitData.permitMunicipality,
              // cityReviewLink: permitData.cityReviewLink,
            });
            this.onPermitReviewTypeChange(permitData.permitReviewType)
          } else {
            console.warn(
              'Permit response has isFault = true',
              permitResponse.responseData
            );
          }
        },
        error: (err) => {
          this.httpUtilService.loadingSubject.next(false);
          console.error('API call failed', err);
        },
      });
  }

  formatDateForInput(dateString: string): string {
    if (!dateString) return '';

    const date = new Date(dateString);
    // Use local timezone to avoid date shifting
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  }

  preparePermitData() {
    const formData = this.permitForm.value;

    let permitRequestData: any = {};
    permitRequestData.projectId = formData.projectId;
    permitRequestData.permitNumber = formData.permitNumber;
    permitRequestData.permitReviewType = formData.permitReviewType;
    permitRequestData.permitCategory = formData.permitCategory;
    permitRequestData.permitType = formData.permitType;
    permitRequestData.description = formData.description;
    permitRequestData.location = formData.location;
    permitRequestData.primaryContact = formData.primaryContact;
    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;
    permitRequestData.permitExpirationDate =
      formData.permitExpirationDate || null;
    permitRequestData.permitIssueDate = formData.permitIssueDate || null;
    permitRequestData.permitFinalDate =
      formData.permitFinalDate || null;
    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;
    permitRequestData.permitStatus = formData.permitStatus;
    permitRequestData.internalReviewStatus = formData.internalReviewStatus;
    permitRequestData.permitName = formData.permitName;
    permitRequestData.attentionReason = formData.attentionReason;
    permitRequestData.internalNotes = formData.internalNotes;
    permitRequestData.actionTaken = formData.actionTaken;
    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;
    permitRequestData.permitMunicipality = formData.permitMunicipality;
    permitRequestData.cityReviewLink = this.cityReviewLink;
    permitRequestData.loggedInUserId = this.loginUser.userId;
    permitRequestData.syncedPermitData = this.syncedPermitData;
    const caseId =  this.syncedPermitData.EntityId ||  this.syncedPermitData.permitId ||  this.syncedPermitData.Id || null;
    permitRequestData.permitEntityID = caseId
    if (this.id !== 0) {
      permitRequestData.permitId = this.id;
    }

    return permitRequestData;
  }

  save() {
    let controls = this.permitForm.controls;
    console.log('Permit Data:', this.permitForm.value);
    if (this.permitForm.invalid) {
      Object.keys(controls).forEach((controlName) =>
        controls[controlName].markAsTouched()
      );
      this.customLayoutUtilsService.showError(
        'Please fill all required fields',
        ''
      );
      return;
    }
    let permitData: any = this.preparePermitData();
    console.log('Permit Data:', permitData);
    if (this.id === 0) {
      this.create(permitData);
    } else {
      this.edit(permitData);
    }
  }

  create(permitData: any) {
    this.httpUtilService.loadingSubject.next(true);
    this.permitsService.createPermit(permitData).subscribe((res: any) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true);
        this.modal.close();
      } else {
        this.customLayoutUtilsService.showError(res.responseData.message, '');
        this.passEntry.emit(false);
      }
    });
  }

  edit(permitData: any) {
    this.httpUtilService.loadingSubject.next(true);
    this.permitsService.updatePermit(permitData).subscribe((res) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true);
        this.modal.close();
      } else {
        this.customLayoutUtilsService.showError(res.responseData.message, '');
        this.passEntry.emit(false);
      }
    });
  }
  showTab(tab: any, $event: any) {
    this.selectedTab = tab;
    this.cdr.markForCheck();
  }

  goToNextTab() {
    if (this.selectedTab === 'basic') {
      this.selectedTab = 'details';
    } else if (this.selectedTab === 'details') {
      this.selectedTab = 'notes';
    }
    this.cdr.markForCheck();
  }

  goToPreviousTab() {
    if (this.selectedTab === 'notes') {
      this.selectedTab = 'details';
    } else if (this.selectedTab === 'details') {
      this.selectedTab = 'basic';
    }
    this.cdr.markForCheck();
  }

  onProjectChange(event:any){
     this.permitForm.patchValue({
              location: event.projectLocation })
    // console.log("project loacation",event)
    // Auto-fill Permit / Sub Project Name for Add mode when selecting a project
    if (this.id === 0) {
      const permitNameControl = this.permitForm.get('permitName');
      const currentValue = (permitNameControl?.value || '').toString().trim();
      const projectName = event?.projectName || event?.project?.projectName || '';
      if (permitNameControl && projectName && (currentValue === '' || permitNameControl.pristine)) {
        permitNameControl.setValue(projectName);
        permitNameControl.markAsDirty();
      }
    }

  }

onPermitReviewTypeChange(event: any) {
  const permitControl = this.permitForm.get('permitMunicipality');
  // const cityReviewControl = this.permitForm.get('cityReviewLink');

  if (event === 'External') {
    this.isPermitMunicipalRequired = true;
    // this.iscityreviewLinkMunicipalRequired = true;

    permitControl?.setValidators([Validators.required]);
    // cityReviewControl?.setValidators([Validators.required]);
  } else {
    this.isPermitMunicipalRequired = false;
    // this.iscityreviewLinkMunicipalRequired = false;

    permitControl?.clearValidators();
    // cityReviewControl?.clearValidators();
  }

  permitControl?.updateValueAndValidity();
  // cityReviewControl?.updateValueAndValidity();
}

onPermitReviewTypeClear() {
  this.permitForm.patchValue({ permitReviewType: null });
  // Also clear the municipality when review type is cleared
  this.permitForm.patchValue({ permitMunicipality: null });
  this.cityReviewLink = '';
  this.isPermitMunicipalRequired = false;
  
  // Clear validators for municipality
  const permitControl = this.permitForm.get('permitMunicipality');
  permitControl?.clearValidators();
  permitControl?.updateValueAndValidity();
}

onPermitMunicipalityChange(event:any){
  console.log('event 0 ',event);
  this.cityReviewLink= event.cityWebsiteLink+'#/permit/';
  this.getSyncButtonDisableStatus()
}

onPermitMunicipalityClear(){
  this.permitForm.patchValue({ permitMunicipality: null });
  this.cityReviewLink = '';
  this.getSyncButtonDisableStatus();
}

syncPermitDetails(){
   const formData = this.permitForm.value;
    this.httpUtilService.loadingSubject.next(true);
    this.permitsService
      .getPermitDetails({ permitNumber: formData.permitNumber, municipalityId: formData.permitMunicipality })
      .subscribe({
        next: (permitResponse: any) => {
          this.httpUtilService.loadingSubject.next(false);
          if (!permitResponse.isFault) {
            let permitData = permitResponse.responseData.permit;
            this.syncedPermitData = permitData
            this.permitForm.patchValue({
              permitType: permitData.permitType,
              description: permitData.description,
              location: permitData.address,
              permitAppliedDate: permitData.applyDate
                ? this.formatDateForInput(permitData.applyDate)
                : '',
              permitExpirationDate: permitData.expireDate
                ? this.formatDateForInput(permitData.expireDate)
                : '',
              permitIssueDate: permitData.issueDate
                ? this.formatDateForInput(permitData.issueDate)
                : '',
              permitFinalDate: permitData.finalDate
                ? this.formatDateForInput(permitData.finalDate)
                : '',
              permitCompleteDate: permitData.completeDate
                ? this.formatDateForInput(permitData.completeDate)
                : '',
              permitStatus: permitData.permitStatus,

            });
            // Navigate to details tab only after successful sync and form patch
            this.selectedTab = 'details';
            this.cdr.markForCheck();
          } else {
            console.warn(
              'Permit response has isFault = true',
              permitResponse.responseData.message
            );
          }
        },
        error: (err) => {
          this.httpUtilService.loadingSubject.next(false);
          console.error('API call failed', err);
        },
      });
}

getSyncButtonDisableStatus(): boolean {
  const reviewType = this.permitForm.get('permitReviewType')?.value;
  const permitNumber = this.permitForm.get('permitNumber')?.value;
  const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;

  const isExternal = reviewType === 'External';
  const hasPermitNumber = !!permitNumber;
  const hasPermitMunicipality = !!permitMunicipality;

  console.log('isExternal ', isExternal)
  console.log('hasPermitNumber ', hasPermitNumber)
  console.log('hasPermitMunicipality ', hasPermitMunicipality)
  // Disable if any of the conditions are not satisfied
  return !(isExternal && hasPermitNumber && hasPermitMunicipality);
}
}

import { Component, OnInit, ViewChild, AfterViewInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { AppService } from '../services/app.service';
import { PageInfoService } from '../../_metronic/layout/core/page-info.service';
import { ProjectListComponent } from './project-list/project-list.component';

@Component({
  selector: 'app-projects',
  templateUrl: './projects.component.html',
  styleUrls: ['./projects.component.scss']
})
export class ProjectsComponent implements OnInit, AfterViewInit, OnDestroy {
  selectedTab = 'Projects'; //store default selected tab
  loginUser: any;
  showNavBar = true; // Control visibility of navigation bar
  private routeSubscription: Subscription = new Subscription();
  
  @ViewChild(ProjectListComponent) projectListComponent: ProjectListComponent;
  
  constructor(
    public AppService: AppService,
    private router: Router,
    private pageInfo: PageInfoService
  ) {
    // set the default paging options
  }
  
  ngOnInit() {
    this.pageInfo.updateTitle('Projects');
    // Ensure Projects tab is selected by default
    this.selectedTab = 'Projects';
    
    // Check if we're on the view route and hide nav bar accordingly
    this.checkRouteAndToggleNavBar();
    
    // Subscribe to route changes to update nav bar visibility
    this.routeSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.checkRouteAndToggleNavBar();
      });
  }

  ngAfterViewInit() {
    // Any initialization after view is ready
  }
  
  ngOnDestroy() {
    // Clean up subscription
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  
  checkRouteAndToggleNavBar() {
    // Hide nav bar when on projects/view route
    this.showNavBar = !this.router.url.includes('/projects/view/');
  }
  
  onNavChange(tabName: string) {
    console.log(`Switching to tab: ${tabName}`);
    this.selectedTab = tabName;
    
    if (tabName === 'Projects') {
      // Navigate to projects list
      this.router.navigate(['/projects/list']);
    }
  }
}

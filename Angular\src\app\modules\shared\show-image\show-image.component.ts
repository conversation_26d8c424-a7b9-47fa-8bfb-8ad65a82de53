import { ChangeDetectorRef, Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { AppSettings } from 'src/app/app.settings';
import { AppService } from 'src/app/modules/services/app.service';

@Component({
  selector: 'app-show-image',
  templateUrl: './show-image.component.html',
  styleUrls: ['./show-image.component.scss']
})
export class ShowImageComponent implements OnInit {
  @Input() image: any;
  imageUrl:any
  constructor(public modal: NgbActiveModal,public appService:AppService,
    private cdr: ChangeDetectorRef,) { }

  ngOnInit(): void {
    this.imageUrl = AppSettings.IMAGEPATH+this.image;
    this.cdr.markForCheck();
  }
}

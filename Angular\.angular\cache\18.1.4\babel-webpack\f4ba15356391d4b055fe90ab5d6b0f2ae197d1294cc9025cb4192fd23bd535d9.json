{"ast": null, "code": "import _asyncToGenerator from \"D:/permittracker/Angular/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { NavigationStart } from '@angular/router';\nimport { EmailTemplatesEditComponent } from '../email-templates-edit/email-templates-edit.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/user.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i4 from \"src/app/modules/services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/email-template.service\";\nimport * as i8 from \"../../services/kendo-column.service\";\nimport * as i9 from \"@angular/common\";\nimport * as i10 from \"@angular/forms\";\nimport * as i11 from \"@progress/kendo-angular-grid\";\nimport * as i12 from \"@progress/kendo-angular-inputs\";\nimport * as i13 from \"@progress/kendo-angular-buttons\";\nimport * as i14 from \"ng-inline-svg-2\";\nimport * as i15 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nconst _c6 = () => ({\n  \"background-color\": \"#efefef !important\"\n});\nconst _c7 = () => ({\n  \"background-color\": \"#edf0f3\",\n  \"font-weight\": \"600\"\n});\nfunction EmailTemplatesListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16)(1, \"div\", 17)(2, \"div\", 18)(3, \"span\", 19);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 20);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 21)(1, \"kendo-textbox\", 22);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    })(\"clear\", function EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_clear_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearSearch());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 23)(4, \"span\", 24);\n    i0.ɵɵtext(5, \"Total: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 25);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.add());\n    });\n    i0.ɵɵelement(9, \"span\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 28);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(11, \"i\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleExpand());\n    });\n    i0.ɵɵelement(13, \"i\", 31);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_15_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(15, \"i\", 33);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements || 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/arrows/arr075.svg\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"fa-expand\", !ctx_r2.isExpanded)(\"fa-compress\", ctx_r2.isExpanded);\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_16_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36)(2, \"div\", 37)(3, \"label\", 38);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 39);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 37)(7, \"label\", 38);\n    i0.ɵɵtext(8, \"Role\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 40);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.role, $event) || (ctx_r2.appliedFilters.role = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 41)(11, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵelement(12, \"i\", 43);\n    i0.ɵɵtext(13, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_14_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.clearAllFilters());\n    });\n    i0.ɵɵelement(15, \"i\", 45);\n    i0.ɵɵtext(16, \" Clear \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.roles);\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.role);\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_template_16_div_0_Template, 17, 4, \"div\", 34);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.showAdvancedFilters);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 58);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const dataItem_r6 = i0.ɵɵnextContext().$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.unlockUser(dataItem_r6));\n    });\n    i0.ɵɵelement(1, \"span\", 59);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 55);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.edit(dataItem_r6.templatePID));\n    });\n    i0.ɵɵelement(1, \"span\", 56);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template, 2, 1, \"a\", 57);\n  }\n  if (rf & 2) {\n    const dataItem_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen055.svg\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r6.IsLocked);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 53);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template, 3, 2, \"ng-template\", 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleMap(i0.ɵɵpureFunction0(9, _c6));\n    i0.ɵɵproperty(\"width\", 125)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"action\"))(\"headerStyle\", i0.ɵɵpureFunction0(10, _c7))(\"includeInChooser\", false)(\"columnMenu\", false)(\"hidden\", ctx_r2.getHiddenField(\"action\"));\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r8.templateName, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r9 = ctx.$implicit;\n    const column_r10 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r10)(\"filter\", filter_r9)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 60);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 150)(\"sticky\", true)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"templateName\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"includeInChooser\", false)(\"hidden\", ctx_r2.getHiddenField(\"templateName\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r11.emailTo, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r12 = ctx.$implicit;\n    const column_r13 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r13)(\"filter\", filter_r12)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 64);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 250)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"email\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"emailTo\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r14.emailSubject, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r15 = ctx.$implicit;\n    const column_r16 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r16)(\"filter\", filter_r15)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 65);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"title\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"title\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\")(1, \"span\", 62);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r17 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r17.category, \" \");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-string-filter-menu\", 63);\n    i0.ɵɵelement(1, \"kendo-filter-contains-operator\")(2, \"kendo-filter-eq-operator\")(3, \"kendo-filter-neq-operator\")(4, \"kendo-filter-startswith-operator\")(5, \"kendo-filter-endswith-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r18 = ctx.$implicit;\n    const column_r19 = ctx.column;\n    i0.ɵɵproperty(\"column\", column_r19)(\"filter\", filter_r18)(\"extra\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 66);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template, 3, 1, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template, 6, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 120)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"roleName\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"roleName\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 70);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen037.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 71);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"inlineSVG\", \"./assets/media/icons/duotune/general/gen040.svg\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template, 1, 1, \"span\", 68)(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template, 1, 1, \"span\", 69);\n  }\n  if (rf & 2) {\n    const dataItem_r20 = ctx.$implicit;\n    i0.ɵɵproperty(\"ngIf\", dataItem_r20.userStatus === \"Active\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", dataItem_r20.userStatus === \"Inactive\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"kendo-dropdownlist\", 72);\n    i0.ɵɵlistener(\"valueChange\", function EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener($event) {\n      const ctx_r21 = i0.ɵɵrestoreView(_r21);\n      const filter_r23 = ctx_r21.$implicit;\n      const column_r24 = ctx_r21.column;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.onStatusFilterChange($event, filter_r23, column_r24));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r23 = ctx.$implicit;\n    const column_r24 = ctx.column;\n    const ctx_r2 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"data\", ctx_r2.filterOptions)(\"value\", ctx_r2.getFilterValue(filter_r23, column_r24));\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 67);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template, 2, 2, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template, 1, 2, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 100)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"userStatus\"))(\"headerStyle\", i0.ɵɵpureFunction0(6, _c7))(\"hidden\", ctx_r2.getHiddenField(\"userStatus\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 74);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"br\");\n    i0.ɵɵelementStart(4, \"span\", 74);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r25 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(2, 2, dataItem_r25.lastUpdatedDate, \"MM/dd/yyyy hh:mm a\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(dataItem_r25.lastUpdatedByFullName);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-date-filter-menu\", 75);\n    i0.ɵɵelement(1, \"kendo-filter-eq-operator\")(2, \"kendo-filter-neq-operator\")(3, \"kendo-filter-before-operator\")(4, \"kendo-filter-before-eq-operator\")(5, \"kendo-filter-after-operator\")(6, \"kendo-filter-after-eq-operator\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const filter_r26 = ctx.$implicit;\n    const column_r27 = ctx.column;\n    const filterService_r28 = ctx.filterService;\n    i0.ɵɵproperty(\"column\", column_r27)(\"filter\", filter_r26)(\"filterService\", filterService_r28);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"kendo-grid-column\", 73);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template, 6, 5, \"ng-template\", 54)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template, 7, 3, \"ng-template\", 61);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"width\", 160)(\"sticky\", false)(\"reorderable\", !ctx_r2.fixedColumns.includes(\"lastUpdatedDate\"))(\"headerStyle\", i0.ɵɵpureFunction0(7, _c7))(\"maxResizableWidth\", 240)(\"hidden\", ctx_r2.getHiddenField(\"lastUpdatedDate\"))(\"filterable\", true);\n  }\n}\nfunction EmailTemplatesListComponent_ng_container_17_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template, 2, 11, \"kendo-grid-column\", 46)(2, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template, 3, 8, \"kendo-grid-column\", 47)(3, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template, 3, 7, \"kendo-grid-column\", 48)(4, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template, 3, 7, \"kendo-grid-column\", 49)(5, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template, 3, 7, \"kendo-grid-column\", 50)(6, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template, 3, 7, \"kendo-grid-column\", 51)(7, EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template, 3, 8, \"kendo-grid-column\", 52);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const column_r29 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"action\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"templateName\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"emailTo\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"emailSubject\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"category\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"userStatus\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", column_r29 === \"lastUpdatedDate\");\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_18_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 77)(1, \"div\", 78);\n    i0.ɵɵelement(2, \"i\", 79);\n    i0.ɵɵelementStart(3, \"p\", 24);\n    i0.ɵɵtext(4, \"No email templates found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 80);\n    i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_ng_template_18_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 81);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EmailTemplatesListComponent_ng_template_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, EmailTemplatesListComponent_ng_template_18_div_0_Template, 8, 0, \"div\", 76);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && !ctx_r2.isLoading);\n  }\n}\nexport class EmailTemplatesListComponent {\n  usersService;\n  cdr;\n  router;\n  route;\n  modalService;\n  AppService;\n  customLayoutUtilsService;\n  httpUtilService;\n  emailTemplateService;\n  kendoColumnService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Active',\n    value: 'Active'\n  }, {\n    text: 'Inactive',\n    value: 'Inactive'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Active',\n      value: 'Active'\n    }, {\n      text: 'Inactive',\n      value: 'Inactive'\n    }],\n    roles: [],\n    // Will be populated from backend\n    categories: [] // Will be populated from backend\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration for the new system\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  normalGrid;\n  expandedGrid;\n  isExpanded = false;\n  // Enhanced Columns with Kendo UI features\n  gridColumnConfig = [{\n    field: 'action',\n    title: 'Actions',\n    width: 80,\n    isFixed: true,\n    type: 'action',\n    order: 1\n  }, {\n    field: 'templateName',\n    title: 'Template Name',\n    width: 150,\n    isFixed: true,\n    type: 'text',\n    filterable: true,\n    order: 2\n  },\n  // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\n  {\n    field: 'emailTo',\n    title: 'Email',\n    width: 250,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 4\n  }, {\n    field: 'emailSubject',\n    title: 'Email Subject',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 5\n  }, {\n    field: 'emailBody',\n    title: 'Email Body',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 6\n  }, {\n    field: 'category',\n    title: 'Category',\n    width: 120,\n    isFixed: false,\n    type: 'text',\n    filterable: true,\n    order: 7\n  }, {\n    field: 'templateStatus',\n    title: 'Template Status',\n    width: 100,\n    type: 'status',\n    isFixed: false,\n    filterable: true,\n    order: 8\n  }, {\n    field: 'lastUpdatedDate',\n    title: 'Updated Date',\n    width: 160,\n    isFixed: false,\n    type: 'date',\n    filterable: true,\n    order: 9\n  }];\n  // OLD SYSTEM - to be removed\n  columnsVisibility = {};\n  // Old column configuration management removed - replaced with new system\n  // State\n  sort = [{\n    field: 'lastUpdatedDate',\n    dir: 'desc'\n  }];\n  // Custom sort state tracking for three-state cycle\n  columnSortStates = {};\n  // Router subscription for saving state on navigation\n  routerSubscription;\n  // Storage key for state persistence\n  GRID_STATE_KEY = 'form-templates-grid-state';\n  // Pagination\n  page = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'lastUpdatedDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  // Export options\n  exportOptions = [{\n    text: 'Export All',\n    value: 'all'\n  }, {\n    text: 'Export Selected',\n    value: 'selected'\n  }, {\n    text: 'Export Filtered',\n    value: 'filtered'\n  }];\n  // Selection state\n  selectedUsers = [];\n  isAllSelected = false;\n  // Statistics\n  userStatistics = {\n    activeUsers: 0,\n    inactiveUsers: 0,\n    suspendedUsers: 0,\n    lockedUsers: 0,\n    totalUsers: 0\n  };\n  // Bulk operations\n  showBulkActions = false;\n  bulkActionStatus = 'Active';\n  //add or edit default paramters\n  permissionArray = [];\n  constructor(usersService, cdr, router, route, modalService,\n  // Provides modal functionality to display modals\n  AppService, customLayoutUtilsService, httpUtilService, emailTemplateService, kendoColumnService) {\n    this.usersService = usersService;\n    this.cdr = cdr;\n    this.router = router;\n    this.route = route;\n    this.modalService = modalService;\n    this.AppService = AppService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.httpUtilService = httpUtilService;\n    this.emailTemplateService = emailTemplateService;\n    this.kendoColumnService = kendoColumnService;\n  }\n  goBack() {\n    this.router.navigate(['/setting/view']);\n  }\n  ngOnInit() {\n    this.loginUser = this.AppService.getLoggedInUser();\n    console.log('Login user loaded:', this.loginUser);\n    // Setup search with debounce\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTable();\n    });\n    // Subscribe to router events to save state before navigation\n    this.routerSubscription = this.router.events.subscribe(event => {\n      if (event instanceof NavigationStart) {\n        this.saveGridState();\n      }\n    });\n    // Load saved state if available\n    this.loadGridState();\n    // Load roles for advanced filters\n    this.loadRoles();\n    // Load user statistics\n    // this.loadUserStatistics();\n    // Initialize with default page load\n    this.onPageLoad();\n    // Initialize new column visibility system\n    this.initializeColumnVisibilitySystem();\n    // Load column configuration after a short delay to ensure loginUser is available\n    setTimeout(() => {\n      this.loadColumnConfigFromDatabase();\n    }, 100);\n  }\n  /**\n   * Initialize the new column visibility system\n   */\n  initializeColumnVisibilitySystem() {\n    // Initialize default columns\n    this.defaultColumns = this.gridColumnConfig.map(col => col.field);\n    this.gridColumns = [...this.defaultColumns];\n    // Set fixed columns (first 3 columns)\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\n    // Set draggable columns (all except fixed)\n    this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));\n    // Initialize normal and expanded grid references\n    this.normalGrid = this.grid;\n    this.expandedGrid = this.grid;\n    // Load additional data after main data is loaded\n    setTimeout(() => {\n      this.loadCategories();\n      this.loadTemplateStatistics();\n    }, 100);\n  }\n  ngAfterViewInit() {\n    // Load the table after the view is initialized\n    // Small delay to ensure the grid is properly rendered\n    setTimeout(() => {\n      this.loadTable();\n    }, 200);\n  }\n  // Method to handle when the component becomes visible\n  onTabActivated() {\n    // Set loading state for tab activation\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data when the tab is activated\n    this.loadTable();\n    this.loadTemplateStatistics();\n  }\n  // Method to handle initial page load\n  onPageLoad() {\n    // Initialize the component with default data\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    // Load the data\n    this.loadTable();\n  }\n  // Refresh grid data - only refresh the grid with latest API call\n  refreshGrid() {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n    // Reset to first page and clear any applied filters\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    // Clear search data\n    this.searchData = '';\n    // Load fresh data from API\n    this.loadTable();\n  }\n  ngOnDestroy() {\n    // Clean up subscriptions\n    if (this.routerSubscription) {\n      this.routerSubscription.unsubscribe();\n    }\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // New method to load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Prepare state object for Kendo UI endpoint\n    // When sort is empty (3rd click), send default sort to backend\n    const sortForBackend = this.sort.length > 0 ? this.sort : [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: sortForBackend,\n      filter: this.filter,\n      search: this.searchData,\n      loggedInUserId: this.loginUser.userId\n    };\n    this.emailTemplateService.getEmailTemplatesForKendoGrid(state).subscribe({\n      next: data => {\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const userData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = userData.length !== 0;\n          this.serverSideRowData = userData;\n          this.gridData = this.serverSideRowData;\n          console.log('this.gridData', this.gridData);\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n        }\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: error => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n        this.cdr.detectChanges();\n      }\n    });\n  }\n  // Enhanced loadTable method that can use either endpoint\n  loadTable() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Use the new Kendo UI specific endpoint for better performance\n      _this.loadTableWithKendoEndpoint();\n    })();\n  }\n  handleEmptyResponse() {\n    this.loading = false;\n    this.isLoading = false;\n    this.httpUtilService.loadingSubject.next(false);\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n    this.cdr.detectChanges();\n  }\n  // Enhanced search handling\n  clearSearch() {\n    if (this.searchData === '') {\n      this.searchTerms.next('');\n    }\n  }\n  onSearchChange() {\n    this.searchTerms.next(this.searchData || '');\n  }\n  // Clear all filters and search\n  clearAllFilters() {\n    this.searchData = '';\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.gridFilter = {\n      logic: 'and',\n      filters: []\n    };\n    this.activeFilters = [];\n    this.appliedFilters = {};\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTable();\n  }\n  // Apply advanced filters\n  applyAdvancedFilters() {\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.loadTable();\n  }\n  // Toggle advanced filters panel\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  // Load roles for advanced filters\n  loadRoles() {\n    const queryParams = {\n      pageSize: 1000,\n      sortOrder: 'ASC',\n      sortField: 'roleName',\n      pageNumber: 0\n    };\n    this.usersService.getAllRoles(queryParams).subscribe({\n      next: data => {\n        if (data && data.responseData && data.responseData.content) {\n          this.advancedFilterOptions.roles = [{\n            text: 'All Roles',\n            value: null\n          }, ...data.responseData.content.map(role => ({\n            text: role.roleName,\n            value: role.roleName\n          }))];\n        } else {\n          // Set default if no data\n          this.advancedFilterOptions.categories = [{\n            text: 'All Categories',\n            value: null\n          }];\n        }\n      },\n      error: error => {\n        console.error('Error loading roles:', error);\n        // Set default roles if loading fails\n        this.advancedFilterOptions.roles = [{\n          text: 'All Roles',\n          value: null\n        }];\n      }\n    });\n    this.usersService.getDefaultPermissions({}).subscribe(permissions => {\n      this.permissionArray = permissions.responseData;\n    });\n  }\n  // Load user statistics\n  loadUserStatistics() {\n    this.usersService.getUserStatistics().subscribe({\n      next: data => {\n        if (data && data.statistics) {\n          this.userStatistics = data.statistics;\n        }\n      },\n      error: error => {\n        console.error('Error loading user statistics:', error);\n      }\n    });\n  }\n  // Selection handling\n  onSelectionChange(selection) {\n    this.selectedUsers = selection.selectedRows || [];\n    this.isAllSelected = this.selectedUsers.length === this.serverSideRowData.length;\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Select all users\n  selectAllUsers() {\n    if (this.isAllSelected) {\n      this.selectedUsers = [];\n      this.isAllSelected = false;\n    } else {\n      this.selectedUsers = [...this.serverSideRowData];\n      this.isAllSelected = true;\n    }\n    this.showBulkActions = this.selectedUsers.length > 0;\n  }\n  // Delete user\n  deleteUser(user) {\n    if (confirm(`Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const deleteData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.deleteUser(deleteData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error deleting user:', error);\n          this.customLayoutUtilsService.showError('Error deleting user', '');\n          //alert('Error deleting user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Bulk update user status\n  bulkUpdateUserStatus() {\n    if (this.selectedUsers.length === 0) {\n      this.customLayoutUtilsService.showError('Please select users to update', '');\n      //alert('Please select users to update.');\n      return;\n    }\n    if (confirm(`Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const bulkUpdateData = {\n        userIds: this.selectedUsers.map(user => user.userId),\n        status: this.bulkActionStatus,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n            this.selectedUsers = []; // Clear selection\n            this.showBulkActions = false;\n          }\n        },\n        error: error => {\n          console.error('Error updating users:', error);\n          //alert('Error updating users. Please try again.');\n          this.customLayoutUtilsService.showError('Error updating users', '');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  // Unlock user\n  unlockUser(user) {\n    if (confirm(`Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`)) {\n      // Show loading state\n      this.loading = true;\n      this.isLoading = true;\n      const unlockData = {\n        userId: user.userId,\n        loggedInUserId: this.loginUser.userId || 0\n      };\n      this.usersService.unlockUser(unlockData).subscribe({\n        next: response => {\n          if (response && response.message) {\n            //alert(response.message);\n            this.customLayoutUtilsService.showSuccess(response.message, '');\n            this.loadTable(); // Reload the table\n            // this.loadUserStatistics(); // Reload statistics\n          }\n        },\n        error: error => {\n          console.error('Error unlocking user:', error);\n          this.customLayoutUtilsService.showSuccess('Error unlocking user. Please try again', '');\n          //alert('Error unlocking user. Please try again.');\n          // Reset loading state on error\n          this.loading = false;\n          this.isLoading = false;\n        }\n      });\n    }\n  }\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      this.searchTerms.next(this.searchData);\n    }\n  }\n  // Enhanced function to filter data from search and advanced filters\n  filterConfiguration() {\n    let filter = {\n      paginate: true,\n      search: '',\n      columnFilter: []\n    };\n    // Handle search text\n    let searchText;\n    if (this.searchData === null || this.searchData === undefined) {\n      searchText = '';\n    } else {\n      searchText = this.searchData;\n    }\n    filter.search = searchText.trim();\n    // Handle Kendo UI grid filters\n    if (this.activeFilters && this.activeFilters.length > 0) {\n      filter.columnFilter = [...this.activeFilters];\n    }\n    // Add advanced filters\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\n      filter.columnFilter.push({\n        field: 'userStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\n      filter.columnFilter.push({\n        field: 'roleName',\n        operator: 'eq',\n        value: this.appliedFilters.role\n      });\n    }\n    return filter;\n  }\n  // Grid event handlers\n  pageChange(event) {\n    this.skip = event.skip;\n    this.page.pageNumber = event.skip / event.take;\n    this.page.size = event.take;\n    this.loadTable();\n  }\n  // Use native Kendo sort/unsort via sortChange\n  onSortChange(sort) {\n    // Check if this is the 3rd click (dir is undefined)\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n    if (isThirdClick) {\n      // 3rd click - clear sort and use default\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n      // Valid sort with direction\n      this.sort = sort;\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\n      this.page.orderDir = sort[0].dir;\n    } else {\n      // Empty sort array or invalid sort\n      this.sort = [];\n      this.page.orderBy = 'lastUpdatedDate';\n      this.page.orderDir = 'desc';\n    }\n    this.loadTable();\n  }\n  filterChange(filter) {\n    this.filter = filter;\n    this.gridFilter = filter;\n    this.activeFilters = this.flattenFilters(filter);\n    this.page.pageNumber = 0;\n    this.saveGridState();\n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    this.skip = 0;\n    this.loadTable();\n  }\n  // Old column visibility methods removed - replaced with new system\n  // Fix 2: More robust getFilterValue method\n  getFilterValue(filter, column) {\n    if (!filter || !filter.filters || !column) {\n      return null;\n    }\n    const predicate = filter.filters.find(f => f && 'field' in f && f.field === column.field);\n    return predicate && 'value' in predicate ? predicate.value : null;\n  }\n  // Fix 3: More robust onStatusFilterChange method\n  onStatusFilterChange(value, filter, column) {\n    if (!filter || !filter.filters || !column) {\n      console.error('Invalid filter or column:', {\n        filter,\n        column\n      });\n      return;\n    }\n    const exists = filter.filters.findIndex(f => f && 'field' in f && f.field === column.field);\n    if (exists > -1) {\n      filter.filters.splice(exists, 1);\n    }\n    if (value !== null) {\n      filter.filters.push({\n        field: column.field,\n        operator: 'eq',\n        value: value\n      });\n    }\n    this.filterChange(filter);\n  }\n  // Fix 4: More robust flattenFilters method\n  flattenFilters(filter) {\n    const filters = [];\n    if (!filter || !filter.filters) {\n      return filters;\n    }\n    filter.filters.forEach(f => {\n      if (f && 'field' in f) {\n        // It's a FilterDescriptor\n        filters.push({\n          field: f.field,\n          operator: f.operator,\n          value: f.value\n        });\n      } else if (f && 'filters' in f) {\n        // It's a CompositeFilterDescriptor\n        filters.push(...this.flattenFilters(f));\n      }\n    });\n    return filters;\n  }\n  // Fix 5: More robust loadGridState method\n  loadGridState() {\n    try {\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n      if (!savedState) {\n        return;\n      }\n      const state = JSON.parse(savedState);\n      // Restore sort state\n      if (state && state.sort) {\n        this.sort = state.sort;\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\n          this.page.orderDir = this.sort[0].dir || 'desc';\n        }\n      }\n      // Restore filter state\n      if (state && state.filter) {\n        this.filter = state.filter;\n        this.gridFilter = state.filter;\n        this.activeFilters = state.activeFilters || [];\n      }\n      // Restore pagination state\n      if (state && state.page) {\n        this.page = state.page;\n      }\n      if (state && state.skip !== undefined) {\n        this.skip = state.skip;\n      }\n      // Restore column visibility\n      if (state && state.columnsVisibility) {\n        this.columnsVisibility = state.columnsVisibility;\n      }\n      // Restore search state\n      if (state && state.searchData) {\n        this.searchData = state.searchData;\n      }\n      // Restore advanced filter states\n      if (state && state.appliedFilters) {\n        this.appliedFilters = state.appliedFilters;\n      }\n      if (state && state.showAdvancedFilters !== undefined) {\n        this.showAdvancedFilters = state.showAdvancedFilters;\n      }\n    } catch (error) {\n      console.error('Error loading grid state:', error);\n      // If there's an error, use default state\n    }\n  }\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   */\n  resetTable() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\n      return;\n    }\n    // Reset all grid state to default\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'lastUpdatedDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    this.appliedFilters = {};\n    this.showAdvancedFilters = false;\n    // Reset column visibility and order\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'lastUpdatedDate',\n        dir: 'desc'\n      }];\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Prepare reset data\n    const userData = {\n      pageName: 'EmailTemplates',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save reset state to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Also clear from localStorage\n          this.kendoColumnService.clearFromLocalStorage('EmailTemplates');\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings reset successfully.', '');\n        } else {\n          this.customLayoutUtilsService.showError(res.message || 'Failed to reset column settings.', '');\n        }\n        // Trigger change detection and refresh grid\n        this.cdr.detectChanges();\n        // Small delay to ensure the grid is updated\n        setTimeout(() => {\n          if (this.grid) {\n            this.grid.refresh();\n          }\n        }, 100);\n        this.loadTable();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error resetting column settings:', error);\n        // Check if it's an authentication error\n        if (error.status === 401 || error.error && error.error.status === 401) {\n          this.customLayoutUtilsService.showError('Authentication failed. Please login again.', '');\n          // Optionally redirect to login page\n        } else {\n          this.customLayoutUtilsService.showError('Error resetting column settings. Please try again.', '');\n        }\n      }\n    });\n  }\n  // Grid state persistence methods\n  saveGridState() {\n    const state = {\n      sort: this.sort,\n      filter: this.filter,\n      page: this.page,\n      skip: this.skip,\n      columnsVisibility: this.columnsVisibility,\n      searchData: this.searchData,\n      activeFilters: this.activeFilters,\n      appliedFilters: this.appliedFilters,\n      showAdvancedFilters: this.showAdvancedFilters\n    };\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n  }\n  // Function to add a new company (calls edit function with ID 0)\n  add() {\n    this.edit(0);\n  }\n  // Function to open the edit modal for adding/editing a company\n  edit(id) {\n    console.log('Line: 413', 'call edit function: ', id);\n    // Configuration options for the modal dialog\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddCompaniesComponent\n    const modalRef = this.modalService.open(EmailTemplatesEditComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = id;\n    // modalRef.componentInstance.defaultPermissions = this.permissionArray;\n    // // Subscribe to the modal event when data is updated\n    // modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {\n    //   if (receivedEntry === true) {\n    //     // Reload the table data after a successful update\n    //     this.loadTable();\n    //   }\n    // });\n  }\n  // Delete functionality removed\n  // Load categories for advanced filters\n  loadCategories() {\n    // Implementation for loading categories\n    console.log('Loading categories...');\n  }\n  // Load template statistics\n  loadTemplateStatistics() {\n    // Implementation for loading template statistics\n    console.log('Loading template statistics...');\n  }\n  // Setup search subscription\n  setupSearchSubscription() {\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      this.page.pageNumber = 0;\n      this.skip = 0;\n      this.loadTable();\n    });\n  }\n  toggleExpand() {\n    // Find grid container element and toggle fullscreen class\n    const gridContainer = document.querySelector('.grid-container');\n    if (gridContainer) {\n      gridContainer.classList.toggle('fullscreen-grid');\n      this.isExpanded = !this.isExpanded;\n      // Refresh grid after resize to ensure proper rendering\n      if (this.grid) {\n        this.grid.refresh();\n      }\n    }\n  }\n  // Enhanced export functionality\n  onExportClick(event) {\n    switch (event.item.value) {\n      case 'all':\n        this.exportAllUsers();\n        break;\n      case 'selected':\n        this.exportSelectedUsers();\n        break;\n      case 'filtered':\n        this.exportFilteredUsers();\n        break;\n      default:\n        console.warn('Unknown export option:', event.item.value);\n    }\n  }\n  exportAllUsers() {\n    const exportParams = {\n      filters: {},\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'All_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting users:', error);\n        //alert('Error exporting users. Please try again.');\n      }\n    });\n  }\n  exportSelectedUsers() {\n    if (this.selectedUsers.length === 0) {\n      //alert('Please select users to export.');\n      return;\n    }\n    const exportParams = {\n      filters: {\n        userIds: this.selectedUsers.map(user => user.UserId)\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Selected_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting selected users:', error);\n        //alert('Error exporting selected users. Please try again.');\n      }\n    });\n  }\n  exportFilteredUsers() {\n    const exportParams = {\n      filters: {\n        status: this.appliedFilters.status,\n        role: this.appliedFilters.role,\n        searchTerm: this.searchData\n      },\n      format: 'excel'\n    };\n    this.usersService.exportUsers(exportParams).subscribe({\n      next: response => {\n        if (response && response.exportData) {\n          this.downloadExcel(response.exportData, 'Filtered_Users');\n        }\n      },\n      error: error => {\n        console.error('Error exporting filtered users:', error);\n        //alert('Error exporting filtered users. Please try again.');\n      }\n    });\n  }\n  downloadExcel(data, filename) {\n    // This would typically use a library like xlsx or similar\n    // For now, we'll create a simple CSV download\n    const csvContent = this.convertToCSV(data);\n    const blob = new Blob([csvContent], {\n      type: 'text/csv;charset=utf-8;'\n    });\n    const link = document.createElement('a');\n    const url = URL.createObjectURL(blob);\n    link.setAttribute('href', url);\n    link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);\n    link.style.visibility = 'hidden';\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n  }\n  convertToCSV(data) {\n    if (data.length === 0) return '';\n    const headers = Object.keys(data[0]);\n    const csvRows = [headers.join(',')];\n    for (const row of data) {\n      const values = headers.map(header => {\n        const value = row[header];\n        return typeof value === 'string' && value.includes(',') ? `\"${value}\"` : value;\n      });\n      csvRows.push(values.join(','));\n    }\n    return csvRows.join('\\n');\n  }\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\n  /**\n   * Saves the current state of column visibility and order in the grid.\n   * This function categorizes columns into visible and hidden columns, records their titles,\n   * fields, and visibility status, and also captures the order of draggable columns.\n   * After gathering the necessary data, it sends this information to the backend for saving.\n   */\n  saveHead() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page.', '');\n      return;\n    }\n    const nonHiddenColumns = [];\n    const hiddenColumns = [];\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        if (!column.hidden) {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          nonHiddenColumns.push(columnData);\n        } else {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          hiddenColumns.push(columnData);\n        }\n      });\n    }\n    const draggableColumnsOrder = this.gridColumns.filter(col => !this.fixedColumns.includes(col)).map((field, index) => ({\n      field,\n      orderIndex: index\n    }));\n    // Prepare data for backend\n    const userData = {\n      pageName: 'Users',\n      userID: this.loginUser.userId,\n      hiddenData: hiddenColumns,\n      kendoColOrder: draggableColumnsOrder,\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Update local state\n          this.hiddenData = hiddenColumns;\n          this.kendoColOrder = draggableColumnsOrder;\n          this.hiddenFields = this.hiddenData.map(col => col.field);\n          // Also save to localStorage as backup\n          this.kendoColumnService.saveToLocalStorage(userData);\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings saved successfully.', '');\n        } else {\n          this.customLayoutUtilsService.showError(res.message || 'Failed to save column settings.', '');\n        }\n        this.cdr.markForCheck();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error saving column settings:', error);\n        // Fallback to localStorage on error\n        this.kendoColumnService.saveToLocalStorage(userData);\n        // Update local state\n        this.hiddenData = hiddenColumns;\n        this.kendoColOrder = draggableColumnsOrder;\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.customLayoutUtilsService.showError('Failed to save to server. Settings saved locally.', '');\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  /**\n   * Loads and applies the saved column order from the user preferences or configuration.\n   * This function updates the grid column order, ensuring the fixed columns remain in place\n   * and the draggable columns are ordered according to the saved preferences.\n   */\n  loadSavedColumnOrder(kendoColOrder) {\n    try {\n      const savedOrder = kendoColOrder;\n      if (savedOrder) {\n        const parsedOrder = savedOrder;\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\n          // Get only the draggable columns from saved order\n          const savedDraggableColumns = parsedOrder.sort((a, b) => a.orderIndex - b.orderIndex).map(col => col.field).filter(field => !this.fixedColumns.includes(field));\n          // Add any missing draggable columns at the end\n          const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));\n          // Combine fixed columns with saved draggable columns\n          this.gridColumns = [...this.fixedColumns, ...savedDraggableColumns, ...missingColumns];\n        } else {\n          this.gridColumns = [...this.defaultColumns];\n        }\n      } else {\n        this.gridColumns = [...this.defaultColumns];\n      }\n    } catch (error) {\n      this.gridColumns = [...this.defaultColumns];\n    }\n  }\n  /**\n   * Checks if a given column is marked as hidden.\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\n   */\n  getHiddenField(columnName) {\n    return this.hiddenFields.indexOf(columnName) > -1;\n  }\n  /**\n   * Handles the column reordering event triggered when a column is moved by the user.\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\n   * of fixed columns.\n   */\n  onColumnReorder(event) {\n    const {\n      columns,\n      newIndex,\n      oldIndex\n    } = event;\n    // Prevent reordering of fixed columns\n    if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {\n      return;\n    }\n    // Update the gridColumns array\n    const reorderedColumns = [...this.gridColumns];\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\n    reorderedColumns.splice(newIndex, 0, movedColumn);\n    this.gridColumns = reorderedColumns;\n    this.cdr.markForCheck();\n  }\n  /**\n   * Handles column visibility changes from the Kendo Grid.\n   * Updates the local state when columns are shown or hidden.\n   */\n  updateColumnVisibility(event) {\n    if (this.isExpanded === false) {\n      if (this.grid && this.grid.columns) {\n        this.grid.columns.forEach(column => {\n          const columnData = {\n            title: column.title,\n            field: column.field,\n            hidden: column.hidden\n          };\n          if (column.hidden) {\n            const exists = this.hiddenData.some(item => item.field === columnData.field && item.hidden === true);\n            if (!exists) {\n              this.hiddenData.push(columnData);\n            }\n          } else {\n            let indexExists = this.hiddenData.findIndex(item => item.field === columnData.field && item.hidden === true);\n            if (indexExists !== -1) {\n              this.hiddenData.splice(indexExists, 1);\n            }\n          }\n        });\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        this.cdr.markForCheck();\n      }\n    }\n  }\n  /**\n   * Loads the saved column configuration from the backend or localStorage as fallback.\n   * This method is called during component initialization to restore user preferences.\n   */\n  loadColumnConfigFromDatabase() {\n    try {\n      // First try to load from backend\n      if (this.loginUser && this.loginUser.userId) {\n        this.kendoColumnService.getHideFields({\n          pageName: 'Users',\n          userID: this.loginUser.userId\n        }).subscribe({\n          next: res => {\n            if (!res.isFault && res.Data) {\n              this.kendoHide = res.Data;\n              this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];\n              this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];\n              this.hiddenFields = this.hiddenData.map(col => col.field);\n              // Update grid columns based on the hidden fields\n              if (this.grid && this.grid.columns) {\n                this.grid.columns.forEach(column => {\n                  if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n                    column.includeInChooser = true;\n                    column.hidden = true;\n                  } else {\n                    column.hidden = false;\n                  }\n                });\n              }\n              // Load saved column order and update grid\n              this.loadSavedColumnOrder(this.kendoInitColOrder);\n              // Also save to localStorage as backup\n              this.kendoColumnService.saveToLocalStorage({\n                pageName: 'Users',\n                userID: this.loginUser.userId,\n                hiddenData: this.hiddenData,\n                kendoColOrder: this.kendoInitColOrder\n              });\n            }\n          },\n          error: error => {\n            console.error('Error loading from backend, falling back to localStorage:', error);\n            this.loadFromLocalStorageFallback();\n          }\n        });\n      } else {\n        // Fallback to localStorage if no user ID\n        this.loadFromLocalStorageFallback();\n      }\n    } catch (error) {\n      console.error('Error loading column configuration:', error);\n      this.loadFromLocalStorageFallback();\n    }\n  }\n  /**\n   * Fallback method to load column configuration from localStorage\n   */\n  loadFromLocalStorageFallback() {\n    try {\n      const savedConfig = this.kendoColumnService.getFromLocalStorage('Users', this.loginUser?.UserId || 0);\n      if (savedConfig) {\n        this.kendoHide = savedConfig;\n        this.hiddenData = savedConfig.hiddenData || [];\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\n        this.hiddenFields = this.hiddenData.map(col => col.field);\n        // Update grid columns based on the hidden fields\n        if (this.grid && this.grid.columns) {\n          this.grid.columns.forEach(column => {\n            if (this.hiddenData.some(item => item.title === column.title && item.hidden)) {\n              column.includeInChooser = true;\n              column.hidden = true;\n            } else {\n              column.hidden = false;\n            }\n          });\n        }\n        // Load saved column order and update grid\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\n      }\n    } catch (error) {\n      console.error('Error loading from localStorage fallback:', error);\n    }\n  }\n  static ɵfac = function EmailTemplatesListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || EmailTemplatesListComponent)(i0.ɵɵdirectiveInject(i1.UserService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i2.ActivatedRoute), i0.ɵɵdirectiveInject(i3.NgbModal), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.EmailTemplateService), i0.ɵɵdirectiveInject(i8.KendoColumnService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: EmailTemplatesListComponent,\n    selectors: [[\"app-email-templates-list\"]],\n    viewQuery: function EmailTemplatesListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 19,\n    vars: 21,\n    consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"card\", \"mb-5\", \"mb-xl-5\"], [1, \"card-body\", \"pb-0\", \"pt-0\"], [1, \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-3x\", \"border-transparent\", \"fs-5\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", \"active\"], [1, \"d-flex\", \"align-items-center\"], [\"type\", \"button\", \"title\", \"Go back to Settings Dashboard\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"back-button\", 3, \"click\"], [1, \"fa\", \"fa-arrow-left\", \"me-2\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [4, \"ngFor\", \"ngForOf\"], [\"kendoGridNoRecordsTemplate\", \"\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"spinner-border\", \"text-primary\", \"spinner-md\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"clear\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\"], [1, \"fw-bold\", \"ms-1\"], [\"type\", \"button\", \"title\", \"Add Email Template\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"text-primary\", 3, \"inlineSVG\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"text-warning\"], [\"type\", \"button\", \"title\", \"Toggle Grid Expansion\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\"], [\"type\", \"button\", \"title\", \"Refresh\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Status\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [\"textField\", \"text\", \"valueField\", \"value\", \"placeholder\", \"Select Role\", 3, \"ngModelChange\", \"data\", \"ngModel\"], [1, \"col-md-3\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"btn-primary\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-check\"], [\"kendoButton\", \"\", 1, \"btn-secondary\", 3, \"click\"], [1, \"fas\", \"fa-times\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"style\", \"hidden\", 4, \"ngIf\"], [\"field\", \"templateName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"emailTo\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"emailSubject\", \"title\", \"Email Subject\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"category\", \"title\", \"category\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\", 4, \"ngIf\"], [\"title\", \"Actions\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"columnMenu\", \"hidden\"], [\"kendoGridCellTemplate\", \"\"], [\"title\", \"Edit\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-primary\", 3, \"inlineSVG\"], [\"title\", \"Unlock\", \"class\", \"btn btn-icon btn-sm\", 3, \"click\", 4, \"ngIf\"], [\"title\", \"Unlock\", 1, \"btn\", \"btn-icon\", \"btn-sm\", 3, \"click\"], [1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-warning\", 3, \"inlineSVG\"], [\"field\", \"templateName\", \"title\", \"Name\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"includeInChooser\", \"hidden\", \"filterable\"], [\"kendoGridFilterMenuTemplate\", \"\"], [1, \"fw-bolder\", \"cursor-pointer\"], [3, \"column\", \"filter\", \"extra\"], [\"field\", \"emailTo\", \"title\", \"Email\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"emailSubject\", \"title\", \"Email Subject\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"category\", \"title\", \"category\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"field\", \"userStatus\", \"title\", \"Status\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"hidden\", \"filterable\"], [\"ngbTooltip\", \"Active\", \"class\", \"svg-icon svg-icon-3 svg-icon-success\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Inactive\", \"class\", \"svg-icon svg-icon-3 svg-icon-danger text-danger\", \"style\", \"margin-left: 1.5rem\", 3, \"inlineSVG\", 4, \"ngIf\"], [\"ngbTooltip\", \"Active\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-success\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"ngbTooltip\", \"Inactive\", 1, \"svg-icon\", \"svg-icon-3\", \"svg-icon-danger\", \"text-danger\", 2, \"margin-left\", \"1.5rem\", 3, \"inlineSVG\"], [\"textField\", \"text\", \"valueField\", \"value\", 3, \"valueChange\", \"data\", \"value\"], [\"field\", \"lastUpdatedDate\", \"title\", \"Updated Date\", \"filter\", \"date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"sticky\", \"reorderable\", \"headerStyle\", \"maxResizableWidth\", \"hidden\", \"filterable\"], [1, \"text-gray-600\", \"fs-1r\"], [\"operator\", \"eq\", 3, \"column\", \"filter\", \"filterService\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-envelope\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"]],\n    template: function EmailTemplatesListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, EmailTemplatesListComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"ul\", 5)(5, \"li\", 6)(6, \"a\", 7);\n        i0.ɵɵtext(7, \" Email Templates \");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 8)(9, \"button\", 9);\n        i0.ɵɵlistener(\"click\", function EmailTemplatesListComponent_Template_button_click_9_listener() {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.goBack());\n        });\n        i0.ɵɵelement(10, \"i\", 10);\n        i0.ɵɵtext(11, \" Back \");\n        i0.ɵɵelementEnd()()()();\n        i0.ɵɵelementStart(12, \"div\", 11)(13, \"kendo-grid\", 12, 0);\n        i0.ɵɵlistener(\"columnReorder\", function EmailTemplatesListComponent_Template_kendo_grid_columnReorder_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function EmailTemplatesListComponent_Template_kendo_grid_selectionChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function EmailTemplatesListComponent_Template_kendo_grid_filterChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function EmailTemplatesListComponent_Template_kendo_grid_pageChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"sortChange\", function EmailTemplatesListComponent_Template_kendo_grid_sortChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function EmailTemplatesListComponent_Template_kendo_grid_columnVisibilityChange_13_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(15, EmailTemplatesListComponent_ng_template_15_Template, 16, 10, \"ng-template\", 13)(16, EmailTemplatesListComponent_ng_template_16_Template, 1, 1, \"ng-template\", 13)(17, EmailTemplatesListComponent_ng_container_17_Template, 8, 7, \"ng-container\", 14)(18, EmailTemplatesListComponent_ng_template_18_Template, 1, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(13);\n        i0.ɵɵproperty(\"data\", ctx.serverSideRowData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(16, _c2, i0.ɵɵpureFunction0(15, _c1)))(\"sortable\", i0.ɵɵpureFunction0(18, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(19, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.page.pageNumber * ctx.page.size)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(20, _c5));\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngForOf\", ctx.gridColumns);\n      }\n    },\n    dependencies: [i9.NgForOf, i9.NgIf, i10.NgControlStatus, i10.NgModel, i3.NgbTooltip, i11.GridComponent, i11.ToolbarTemplateDirective, i11.GridSpacerComponent, i11.ColumnComponent, i11.CellTemplateDirective, i11.NoRecordsTemplateDirective, i11.ContainsFilterOperatorComponent, i11.EndsWithFilterOperatorComponent, i11.EqualFilterOperatorComponent, i11.NotEqualFilterOperatorComponent, i11.StartsWithFilterOperatorComponent, i11.AfterFilterOperatorComponent, i11.AfterEqFilterOperatorComponent, i11.BeforeEqFilterOperatorComponent, i11.BeforeFilterOperatorComponent, i11.StringFilterMenuComponent, i11.FilterMenuTemplateDirective, i11.DateFilterMenuComponent, i12.TextBoxComponent, i13.ButtonComponent, i14.InlineSVGDirective, i15.DropDownListComponent, i9.DatePipe],\n    styles: [\".nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n}\\n.nav-line-tabs[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%] {\\n  color: #009ef7 !important;\\n  border-bottom: 2px solid #009ef7;\\n  font-weight: 600;\\n}\\n.nav-line-tabs[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover {\\n  color: #009ef7 !important;\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_fadeIn 0.3s ease-in;\\n}\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(10px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n.grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n\\n\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background-color: rgba(0, 0, 0, 0.7);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  z-index: 9999;\\n}\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  width: 80%;\\n  border: 2px solid #afc7dd;\\n  box-shadow: 0 0 6px rgba(59, 83, 135, 0.5);\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%] {\\n  border-radius: 0.375rem;\\n  padding: 0.75rem 1.25rem;\\n  min-width: 120px;\\n  background-color: #4c4e4f;\\n  color: white;\\n  font-weight: 500;\\n  transition: background 0.3s, transform 0.2s;\\n}\\n.search-section[_ngcontent-%COMP%]   .k-button[_ngcontent-%COMP%]:hover {\\n  background-color: #4c4e4f;\\n  transform: scale(1.05);\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background-color: white;\\n  padding: 40px;\\n  border-radius: 12px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\\n  min-width: 200px;\\n}\\n\\n.grid-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n\\n.grid-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n\\n\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 5px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n  border: 1px solid transparent;\\n  background-color: transparent;\\n  \\n\\n  \\n\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  min-width: 40px;\\n  width: 40px;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:not(.btn-icon) {\\n  padding: 0.375rem 0.75rem;\\n  gap: 0.5rem;\\n}\\n\\n\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.template-statistics[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n.template-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n\\n\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n}\\n\\n.card-body[_ngcontent-%COMP%]    > .d-flex.justify-content-between.align-items-center[_ngcontent-%COMP%] {\\n  align-items: center;\\n}\\n\\n.back-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  align-self: center;\\n  padding: 0.15rem 0.5rem;\\n  border-radius: 0.55rem;\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1;\\n  margin-top: 0.1rem;\\n}\\n.back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n}\\n\\n\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.badge-success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n\\n.badge-danger[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.badge-info[_ngcontent-%COMP%] {\\n  background-color: #d1ecf1;\\n  color: #0c5460;\\n}\\n\\n.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n\\n\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n}\\n\\n\\n\\n.custom-no-records[_ngcontent-%COMP%] {\\n  padding: 40px;\\n  text-align: center;\\n}\\n.custom-no-records[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n}\\n\\n\\n\\n.bulk-actions[_ngcontent-%COMP%] {\\n  background-color: #fff3cd;\\n  border: 1px solid #ffeaa7;\\n  border-radius: 6px;\\n  padding: 10px;\\n  margin-bottom: 15px;\\n}\\n\\n\\n\\n.fullscreen-grid[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  z-index: 9999;\\n  background: #ffffff;\\n  padding: 20px;\\n  overflow: auto;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subject", "debounceTime", "distinctUntilChanged", "NavigationStart", "EmailTemplatesEditComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "EmailTemplatesListComponent_ng_template_15_Template_kendo_textbox_clear_1_listener", "clearSearch", "ɵɵelement", "EmailTemplatesListComponent_ng_template_15_Template_button_click_8_listener", "add", "EmailTemplatesListComponent_ng_template_15_Template_button_click_10_listener", "resetTable", "EmailTemplatesListComponent_ng_template_15_Template_button_click_12_listener", "toggleExpand", "EmailTemplatesListComponent_ng_template_15_Template_button_click_14_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ɵɵclassProp", "isExpanded", "EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "EmailTemplatesListComponent_ng_template_16_div_0_Template_kendo_dropdownlist_ngModelChange_9_listener", "role", "EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_11_listener", "applyAdvancedFilters", "EmailTemplatesListComponent_ng_template_16_div_0_Template_button_click_14_listener", "clearAllFilters", "advancedFilterOptions", "roles", "ɵɵtemplate", "EmailTemplatesListComponent_ng_template_16_div_0_Template", "showAdvancedFilters", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template_a_click_0_listener", "_r7", "dataItem_r6", "$implicit", "unlockUser", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template_a_click_0_listener", "_r5", "edit", "templatePID", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_a_2_Template", "IsLocked", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_ng_template_1_Template", "ɵɵstyleMap", "ɵɵpureFunction0", "_c6", "fixedColumns", "includes", "_c7", "getHiddenField", "ɵɵtextInterpolate1", "dataItem_r8", "templateName", "column_r10", "filter_r9", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_ng_template_2_Template", "dataItem_r11", "emailTo", "column_r13", "filter_r12", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_ng_template_2_Template", "dataItem_r14", "emailSubject", "column_r16", "filter_r15", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_ng_template_2_Template", "dataItem_r17", "category", "column_r19", "filter_r18", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_ng_template_2_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_0_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_span_1_Template", "dataItem_r20", "userStatus", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template_kendo_dropdownlist_valueChange_0_listener", "ctx_r21", "_r21", "filter_r23", "column_r24", "column", "onStatusFilterChange", "filterOptions", "getFilterValue", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_ng_template_2_Template", "ɵɵpipeBind2", "dataItem_r25", "lastUpdatedDate", "lastUpdatedByFullName", "column_r27", "filter_r26", "filterService_r28", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_ng_template_2_Template", "ɵɵelementContainerStart", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_1_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_2_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_3_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_4_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_5_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_6_Template", "EmailTemplatesListComponent_ng_container_17_kendo_grid_column_7_Template", "column_r29", "EmailTemplatesListComponent_ng_template_18_div_0_Template_button_click_5_listener", "_r30", "loadTable", "EmailTemplatesListComponent_ng_template_18_div_0_Template", "loading", "isLoading", "EmailTemplatesListComponent", "usersService", "cdr", "router", "route", "modalService", "AppService", "customLayoutUtilsService", "httpUtilService", "emailTemplateService", "kendoColumnService", "grid", "serverSideRowData", "gridData", "IsListHasValue", "loginUser", "searchTerms", "searchSubscription", "filter", "logic", "filters", "gridFilter", "activeFilters", "text", "value", "categories", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "draggableColumns", "normalGrid", "expandedGrid", "gridColumnConfig", "field", "title", "width", "isFixed", "type", "order", "filterable", "columnsVisibility", "sort", "dir", "columnSortStates", "routerSubscription", "GRID_STATE_KEY", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "exportOptions", "selectedUsers", "isAllSelected", "userStatistics", "activeUsers", "inactiveUsers", "suspendedUsers", "lockedUsers", "totalUsers", "showBulkActions", "bulkActionStatus", "permissionArray", "constructor", "goBack", "navigate", "ngOnInit", "getLoggedInUser", "console", "log", "pipe", "subscribe", "events", "event", "saveGridState", "loadGridState", "loadRoles", "onPageLoad", "initializeColumnVisibilitySystem", "setTimeout", "loadColumnConfigFromDatabase", "map", "col", "loadCategories", "loadTemplateStatistics", "ngAfterViewInit", "onTabActivated", "ngOnDestroy", "unsubscribe", "complete", "loadTableWithKendoEndpoint", "loadingSubject", "next", "sortForBackend", "length", "state", "take", "search", "loggedInUserId", "userId", "getEmailTemplatesForKendoGrid", "data", "<PERSON><PERSON><PERSON>", "responseData", "errors", "error", "handleEmptyResponse", "userData", "total", "Math", "ceil", "detectChanges", "_this", "_asyncToGenerator", "toggleAdvancedFilters", "queryParams", "pageSize", "sortOrder", "sortField", "getAllRoles", "content", "<PERSON><PERSON><PERSON>", "getDefaultPermissions", "permissions", "loadUserStatistics", "getUserStatistics", "statistics", "onSelectionChange", "selection", "selectedRows", "selectAllUsers", "deleteUser", "user", "confirm", "FirstName", "LastName", "deleteData", "response", "message", "showSuccess", "showError", "bulkUpdateUserStatus", "bulkUpdateData", "userIds", "firstName", "lastName", "unlockData", "key", "filterConfiguration", "paginate", "columnFilter", "searchText", "undefined", "trim", "push", "operator", "pageChange", "onSortChange", "isThirdClick", "filterChange", "flattenFilters", "predicate", "find", "f", "exists", "findIndex", "splice", "for<PERSON>ach", "savedState", "localStorage", "getItem", "JSON", "parse", "columns", "index", "indexOf", "orderIndex", "hidden", "pageName", "userID", "LoggedId", "createHideFields", "res", "clearFromLocalStorage", "refresh", "setItem", "stringify", "id", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "setupSearchSubscription", "gridContainer", "document", "querySelector", "classList", "toggle", "onExportClick", "item", "exportAllUsers", "exportSelectedUsers", "exportFilteredUsers", "warn", "exportParams", "format", "exportUsers", "exportData", "downloadExcel", "UserId", "searchTerm", "filename", "csv<PERSON><PERSON>nt", "convertToCSV", "blob", "Blob", "link", "createElement", "url", "URL", "createObjectURL", "setAttribute", "Date", "toISOString", "split", "style", "visibility", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "headers", "Object", "keys", "csvRows", "join", "row", "values", "header", "saveHead", "nonHiddenColumns", "hiddenColumns", "columnData", "draggableColumnsOrder", "saveToLocalStorage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "loadSavedColumnOrder", "savedOrder", "parsedOrder", "Array", "isArray", "savedDraggableColumns", "a", "b", "missingColumns", "columnName", "onColumnReorder", "newIndex", "oldIndex", "reorderedColumns", "movedColumn", "updateColumnVisibility", "some", "indexExists", "getHideFields", "Data", "hideData", "includeInChooser", "loadFromLocalStorageFallback", "savedConfig", "getFromLocalStorage", "ɵɵdirectiveInject", "i1", "UserService", "ChangeDetectorRef", "i2", "Router", "ActivatedRoute", "i3", "NgbModal", "i4", "i5", "CustomLayoutUtilsService", "i6", "HttpUtilsService", "i7", "EmailTemplateService", "i8", "KendoColumnService", "selectors", "viewQuery", "EmailTemplatesListComponent_Query", "rf", "ctx", "EmailTemplatesListComponent_div_0_Template", "EmailTemplatesListComponent_Template_button_click_9_listener", "_r1", "EmailTemplatesListComponent_Template_kendo_grid_columnReorder_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_selectionChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_filterChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_pageChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_sortChange_13_listener", "EmailTemplatesListComponent_Template_kendo_grid_columnVisibilityChange_13_listener", "EmailTemplatesListComponent_ng_template_15_Template", "EmailTemplatesListComponent_ng_template_16_Template", "EmailTemplatesListComponent_ng_container_17_Template", "EmailTemplatesListComponent_ng_template_18_Template", "ɵɵpureFunction1", "_c2", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\email-templates-list\\email-templates-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\email-templates-list\\email-templates-list.component.html"], "sourcesContent": ["import {\r\n  Compo<PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON><PERSON>,\r\n  ChangeDetector<PERSON>ef,\r\n  ViewChild,\r\n  AfterViewInit,\r\n} from '@angular/core';\r\nimport { SortDescriptor } from '@progress/kendo-data-query';\r\nimport {\r\n  FilterDescriptor,\r\n  CompositeFilterDescriptor,\r\n  process,\r\n} from '@progress/kendo-data-query';\r\nimport { State } from '@progress/kendo-data-query';\r\nimport {\r\n  Subject,\r\n  debounceTime,\r\n  distinctUntilChanged,\r\n  Subscription,\r\n} from 'rxjs';\r\nimport { Router, NavigationStart, ActivatedRoute } from '@angular/router';\r\nimport { saveAs } from '@progress/kendo-file-saver';\r\nimport { AppService } from 'src/app/modules/services/app.service';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\n\r\nimport { AddUserComponent } from '../add-user/user-add.component';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { UserService } from '../../services/user.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { KendoColumnService } from '../../services/kendo-column.service';\r\nimport { EmailTemplateService } from '../../services/email-template.service';\r\nimport { EmailTemplatesEditComponent } from '../email-templates-edit/email-templates-edit.component';\r\n\r\n// Type definitions\r\ninterface EmailTemplateData {\r\n  templatePID: number;\r\n  templateName: string;\r\n  emailSubject: string;\r\n  emailBody: string;\r\n  category: string;\r\n  templateStatus: string;\r\n  lastUpdatedDate: string;\r\n  lastUpdatedUserFullName: string;\r\n  createdDate: string;\r\n  createdUserFullName: string;\r\n}\r\n\r\n// Type for page configuration\r\ninterface PageConfig {\r\n  size: number;\r\n  pageNumber: number;\r\n  totalElements: number;\r\n  totalPages: number;\r\n  orderBy: string;\r\n  orderDir: string;\r\n}\r\ninterface UserData {\r\n  UserId: number;\r\n  FirstName: string;\r\n  LastName: string;\r\n  Email: string;\r\n  Status: string;\r\n  Title: string;\r\n  PhoneNo: string;\r\n  RoleName: string;\r\n  LastUpdatedDate: string;\r\n  CreatedDate: string;\r\n  IsEmailNotificationEnabled: boolean;\r\n  IsPasswordChanged: boolean;\r\n  IsLocked: boolean;\r\n  PharmacyId?: number;\r\n  MedicalCenterId?: number;\r\n  CreatedBy?: string;\r\n  LastUpdatedBy?: string;\r\n}\r\n\r\n// Type for page configuration\r\ninterface PageConfig {\r\n  size: number;\r\n  pageNumber: number;\r\n  totalElements: number;\r\n  totalPages: number;\r\n  orderBy: string;\r\n  orderDir: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-email-templates-list',\r\n  templateUrl: './email-templates-list.component.html',\r\n  styleUrls: ['./email-templates-list.component.scss'],\r\n})\r\nexport class EmailTemplatesListComponent implements OnInit, OnDestroy, AfterViewInit{\r\n  @ViewChild('normalGrid') grid: any;\r\n\r\n  // Data\r\n  public serverSideRowData: any[] = [];\r\n  public gridData: any[] = [];\r\n  public IsListHasValue: boolean = false;\r\n\r\n  public loading: boolean = false;\r\n  public isLoading: boolean = false;\r\n\r\n  loginUser: any = {};\r\n\r\n  // Search\r\n  public searchData: string = '';\r\n  private searchTerms = new Subject<string>();\r\n  private searchSubscription: Subscription;\r\n\r\n  // Enhanced Filters for Kendo UI\r\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public activeFilters: Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n  }> = [];\r\n\r\n  public filterOptions: Array<{ text: string; value: string | null }> = [\r\n    { text: 'All', value: null },\r\n    { text: 'Active', value: 'Active' },\r\n    { text: 'Inactive', value: 'Inactive' },\r\n  ];\r\n\r\n  // Advanced filter options\r\n  public advancedFilterOptions = {\r\n    status: [\r\n      { text: 'All', value: null },\r\n      { text: 'Active', value: 'Active' },\r\n      { text: 'Inactive', value: 'Inactive' },\r\n    ] as Array<{ text: string; value: string | null }>,\r\n    roles: [] as Array<{ text: string; value: string | null }>, // Will be populated from backend\r\n    categories: [] as Array<{ text: string; value: string | null }>, // Will be populated from backend\r\n  };\r\n\r\n  // Filter state\r\n  public showAdvancedFilters = false;\r\n  public appliedFilters: {\r\n    status?: string | null;\r\n    role?: string | null;\r\n  } = {};\r\n\r\n  // NEW COLUMN VISIBILITY SYSTEM - replacing the old one\r\n  public kendoHide: any;\r\n  public hiddenData: any = [];\r\n  public kendoColOrder: any = [];\r\n  public kendoInitColOrder: any = [];\r\n  public hiddenFields: any = [];\r\n\r\n  // Column configuration for the new system\r\n  public gridColumns: string[] = [];\r\n  public defaultColumns: string[] = [];\r\n  public fixedColumns: string[] = [];\r\n  public draggableColumns: string[] = [];\r\n  public normalGrid: any;\r\n  public expandedGrid: any;\r\n  public isExpanded = false;\r\n\r\n  // Enhanced Columns with Kendo UI features\r\n  public gridColumnConfig: Array<{\r\n    field: string;\r\n    title: string;\r\n    width: number;\r\n    isFixed: boolean;\r\n    type: string;\r\n    filterable?: boolean;\r\n    order: number;\r\n  }> = [\r\n    {\r\n      field: 'action',\r\n      title: 'Actions',\r\n      width: 80,\r\n      isFixed: true,\r\n      type: 'action',\r\n      order: 1,\r\n    },\r\n    {\r\n      field: 'templateName',\r\n      title: 'Template Name',\r\n      width: 150,\r\n      isFixed: true,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 2,\r\n    },\r\n    // { field: 'lastName', title: 'Last Name', width: 150, isFixed: true, type: 'text', filterable: true, order: 3 },\r\n    {\r\n      field: 'emailTo',\r\n      title: 'Email',\r\n      width: 250,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 4,\r\n    },\r\n    {\r\n      field: 'emailSubject',\r\n      title: 'Email Subject',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 5,\r\n    },\r\n    {\r\n      field: 'emailBody',\r\n      title: 'Email Body',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 6,\r\n    },\r\n    {\r\n      field: 'category',\r\n      title: 'Category',\r\n      width: 120,\r\n      isFixed: false,\r\n      type: 'text',\r\n      filterable: true,\r\n      order: 7,\r\n    },\r\n    {\r\n      field: 'templateStatus',\r\n      title: 'Template Status',\r\n      width: 100,\r\n      type: 'status',\r\n      isFixed: false,\r\n      filterable: true,\r\n      order: 8,\r\n    },\r\n    {\r\n      field: 'lastUpdatedDate',\r\n      title: 'Updated Date',\r\n      width: 160,\r\n      isFixed: false,\r\n      type: 'date',\r\n      filterable: true,\r\n      order: 9,\r\n    },\r\n  ];\r\n\r\n  // OLD SYSTEM - to be removed\r\n  public columnsVisibility: Record<string, boolean> = {};\r\n\r\n  // Old column configuration management removed - replaced with new system\r\n\r\n  // State\r\n  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n  \r\n  // Custom sort state tracking for three-state cycle\r\n  public columnSortStates: { [key: string]: 'none' | 'asc' | 'desc' } = {};\r\n\r\n  // Router subscription for saving state on navigation\r\n  private routerSubscription: Subscription;\r\n\r\n  // Storage key for state persistence\r\n  private readonly GRID_STATE_KEY = 'form-templates-grid-state';\r\n\r\n  // Pagination\r\n  public page: PageConfig = {\r\n    size: 10,\r\n    pageNumber: 0,\r\n    totalElements: 0,\r\n    totalPages: 0,\r\n    orderBy: 'lastUpdatedDate',\r\n    orderDir: 'desc',\r\n  };\r\n  public skip: number = 0;\r\n\r\n  // Export options\r\n  public exportOptions: Array<{ text: string; value: string }> = [\r\n    { text: 'Export All', value: 'all' },\r\n    { text: 'Export Selected', value: 'selected' },\r\n    { text: 'Export Filtered', value: 'filtered' },\r\n  ];\r\n\r\n  // Selection state\r\n  public selectedUsers: any[] = [];\r\n  public isAllSelected: boolean = false;\r\n\r\n  // Statistics\r\n  public userStatistics: {\r\n    activeUsers: number;\r\n    inactiveUsers: number;\r\n    suspendedUsers: number;\r\n    lockedUsers: number;\r\n    totalUsers: number;\r\n  } = {\r\n    activeUsers: 0,\r\n    inactiveUsers: 0,\r\n    suspendedUsers: 0,\r\n    lockedUsers: 0,\r\n    totalUsers: 0,\r\n  };\r\n\r\n  // Bulk operations\r\n  public showBulkActions = false;\r\n  public bulkActionStatus: string = 'Active';\r\n\r\n  //add or edit default paramters\r\n  public permissionArray: any = [];\r\n\r\n  constructor(\r\n    private usersService: UserService,\r\n    private cdr: ChangeDetectorRef,\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private modalService: NgbModal, // Provides modal functionality to display modals\r\n    public AppService: AppService,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private httpUtilService: HttpUtilsService,\r\n    private emailTemplateService: EmailTemplateService,\r\n    private kendoColumnService: KendoColumnService\r\n  ) {}\r\n\r\n  goBack(): void {\r\n    this.router.navigate(['/setting/view']);\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.AppService.getLoggedInUser();\r\n    console.log('Login user loaded:', this.loginUser);\r\n\r\n    // Setup search with debounce\r\n    this.searchSubscription = this.searchTerms\r\n      .pipe(debounceTime(500), distinctUntilChanged())\r\n      .subscribe(() => {\r\n        // Set loading state for search\r\n        this.loading = true;\r\n        this.isLoading = true;\r\n        this.page.pageNumber = 0;\r\n        this.skip = 0;\r\n        this.loadTable();\r\n      });\r\n\r\n    // Subscribe to router events to save state before navigation\r\n    this.routerSubscription = this.router.events.subscribe((event) => {\r\n      if (event instanceof NavigationStart) {\r\n        this.saveGridState();\r\n      }\r\n    });\r\n\r\n    // Load saved state if available\r\n    this.loadGridState();\r\n\r\n    // Load roles for advanced filters\r\n    this.loadRoles();\r\n\r\n    // Load user statistics\r\n    // this.loadUserStatistics();\r\n\r\n    // Initialize with default page load\r\n    this.onPageLoad();\r\n\r\n    // Initialize new column visibility system\r\n    this.initializeColumnVisibilitySystem();\r\n\r\n    // Load column configuration after a short delay to ensure loginUser is available\r\n    setTimeout(() => {\r\n      this.loadColumnConfigFromDatabase();\r\n    }, 100);\r\n  }\r\n\r\n  /**\r\n   * Initialize the new column visibility system\r\n   */\r\n  private initializeColumnVisibilitySystem(): void {\r\n    // Initialize default columns\r\n    this.defaultColumns = this.gridColumnConfig.map((col) => col.field);\r\n    this.gridColumns = [...this.defaultColumns];\r\n\r\n    // Set fixed columns (first 3 columns)\r\n    this.fixedColumns = ['action', 'FirstName', 'LastName'];\r\n\r\n    // Set draggable columns (all except fixed)\r\n    this.draggableColumns = this.defaultColumns.filter(\r\n      (col) => !this.fixedColumns.includes(col)\r\n    );\r\n\r\n    // Initialize normal and expanded grid references\r\n    this.normalGrid = this.grid;\r\n    this.expandedGrid = this.grid;\r\n    \r\n    // Load additional data after main data is loaded\r\n    setTimeout(() => {\r\n      this.loadCategories();\r\n      this.loadTemplateStatistics();\r\n    }, 100);\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    // Load the table after the view is initialized\r\n    // Small delay to ensure the grid is properly rendered\r\n    setTimeout(() => {\r\n      this.loadTable();\r\n    }, 200);\r\n  }\r\n\r\n  // Method to handle when the component becomes visible\r\n  onTabActivated(): void {\r\n    // Set loading state for tab activation\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    // Refresh the data when the tab is activated\r\n    this.loadTable();\r\n    this.loadTemplateStatistics();\r\n  }\r\n\r\n  // Method to handle initial page load\r\n  onPageLoad(): void {\r\n    // Initialize the component with default data\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.searchData = '';\r\n\r\n    // Load the data\r\n    this.loadTable();\r\n  }\r\n\r\n  // Refresh grid data - only refresh the grid with latest API call\r\n  refreshGrid(): void {\r\n    // Set loading state to show full-screen loader\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Reset to first page and clear any applied filters\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.gridFilter = { logic: 'and', filters: [] };\r\n    this.activeFilters = [];\r\n    this.appliedFilters = {};\r\n\r\n    // Clear search data\r\n    this.searchData = '';\r\n\r\n    // Load fresh data from API\r\n    this.loadTable();\r\n  }\r\n  ngOnDestroy(): void {\r\n    // Clean up subscriptions\r\n    if (this.routerSubscription) {\r\n      this.routerSubscription.unsubscribe();\r\n    }\r\n    if (this.searchSubscription) {\r\n      this.searchSubscription.unsubscribe();\r\n    }\r\n    this.searchTerms.complete();\r\n  }\r\n  // New method to load data using Kendo UI specific endpoint\r\n  loadTableWithKendoEndpoint() {\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Enable loader\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Prepare state object for Kendo UI endpoint\r\n    // When sort is empty (3rd click), send default sort to backend\r\n    const sortForBackend = this.sort.length > 0\r\n      ? this.sort\r\n      : [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    const state = {\r\n      take: this.page.size,\r\n      skip: this.skip,\r\n      sort: sortForBackend,\r\n      filter: this.filter,\r\n      search: this.searchData,\r\n      loggedInUserId: this.loginUser.userId,\r\n    };\r\n\r\n    this.emailTemplateService.getEmailTemplatesForKendoGrid(state).subscribe({\r\n      next: (data: {\r\n        isFault?: boolean;\r\n        responseData?: {\r\n          data: any[];\r\n          total: number;\r\n          errors?: string[];\r\n          status?: number;\r\n        };\r\n        data?: any[];\r\n        total?: number;\r\n        errors?: string[];\r\n        status?: number;\r\n      }) => {\r\n        // Handle the new API response structure\r\n        if (\r\n          data.isFault ||\r\n          (data.responseData &&\r\n            data.responseData.errors &&\r\n            data.responseData.errors.length > 0)\r\n        ) {\r\n          const errors = data.responseData?.errors || data.errors || [];\r\n          console.error('Kendo UI Grid errors:', errors);\r\n          this.handleEmptyResponse();\r\n        } else {\r\n          // Handle both old and new response structures\r\n          const responseData = data.responseData || data;\r\n          const userData = responseData.data || [];\r\n          const total = responseData.total || 0;\r\n\r\n          this.IsListHasValue = userData.length !== 0;\r\n          this.serverSideRowData = userData;\r\n          this.gridData = this.serverSideRowData;\r\n          console.log('this.gridData', this.gridData);\r\n          this.page.totalElements = total;\r\n          this.page.totalPages = Math.ceil(total / this.page.size);\r\n        }\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading data with Kendo UI endpoint:', error);\r\n        this.handleEmptyResponse();\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n      complete: () => {\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        this.cdr.detectChanges();\r\n      },\r\n    });\r\n  }\r\n\r\n  // Enhanced loadTable method that can use either endpoint\r\n  async loadTable() {\r\n    // Use the new Kendo UI specific endpoint for better performance\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  private handleEmptyResponse(): void {\r\n    this.loading = false;\r\n    this.isLoading = false;\r\n    this.httpUtilService.loadingSubject.next(false);\r\n    this.IsListHasValue = false;\r\n    this.serverSideRowData = [];\r\n    this.gridData = [];\r\n    this.page.totalElements = 0;\r\n    this.page.totalPages = 0;\r\n    this.cdr.detectChanges();\r\n  }\r\n\r\n  // Enhanced search handling\r\n  clearSearch(): void {\r\n    if (this.searchData === '') {\r\n      this.searchTerms.next('');\r\n    }\r\n  }\r\n\r\n  onSearchChange(): void {\r\n    this.searchTerms.next(this.searchData || '');\r\n  }\r\n\r\n  // Clear all filters and search\r\n  clearAllFilters(): void {\r\n    this.searchData = '';\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.gridFilter = { logic: 'and', filters: [] };\r\n    this.activeFilters = [];\r\n    this.appliedFilters = {};\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Apply advanced filters\r\n  applyAdvancedFilters(): void {\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Toggle advanced filters panel\r\n  toggleAdvancedFilters(): void {\r\n    this.showAdvancedFilters = !this.showAdvancedFilters;\r\n  }\r\n\r\n  // Load roles for advanced filters\r\n  loadRoles(): void {\r\n    const queryParams: {\r\n      pageSize: number;\r\n      sortOrder: string;\r\n      sortField: string;\r\n      pageNumber: number;\r\n    } = {\r\n      pageSize: 1000,\r\n      sortOrder: 'ASC',\r\n      sortField: 'roleName',\r\n      pageNumber: 0,\r\n    };\r\n\r\n    this.usersService.getAllRoles(queryParams).subscribe({\r\n      next: (data: {\r\n        responseData?: {\r\n          content: Array<{ roleName: string }>;\r\n        };\r\n      }) => {\r\n        if (data && data.responseData && data.responseData.content) {\r\n          this.advancedFilterOptions.roles = [\r\n            { text: 'All Roles', value: null },\r\n            ...data.responseData.content.map((role: { roleName: string }) => ({\r\n              text: role.roleName,\r\n              value: role.roleName,\r\n            })),\r\n          ];\r\n        } else {\r\n          // Set default if no data\r\n          this.advancedFilterOptions.categories = [\r\n            { text: 'All Categories', value: null },\r\n          ];\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading roles:', error);\r\n        // Set default roles if loading fails\r\n        this.advancedFilterOptions.roles = [{ text: 'All Roles', value: null }];\r\n      },\r\n    });\r\n    this.usersService\r\n      .getDefaultPermissions({})\r\n      .subscribe((permissions: any) => {\r\n        this.permissionArray = permissions.responseData;\r\n      });\r\n  }\r\n\r\n  // Load user statistics\r\n  loadUserStatistics(): void {\r\n    this.usersService.getUserStatistics().subscribe({\r\n      next: (data: any) => {\r\n        if (data && data.statistics) {\r\n          this.userStatistics = data.statistics;\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading user statistics:', error);\r\n      },\r\n    });\r\n  }\r\n\r\n  // Selection handling\r\n  onSelectionChange(selection: any): void {\r\n    this.selectedUsers = selection.selectedRows || [];\r\n    this.isAllSelected =\r\n      this.selectedUsers.length === this.serverSideRowData.length;\r\n    this.showBulkActions = this.selectedUsers.length > 0;\r\n  }\r\n\r\n  // Select all users\r\n  selectAllUsers(): void {\r\n    if (this.isAllSelected) {\r\n      this.selectedUsers = [];\r\n      this.isAllSelected = false;\r\n    } else {\r\n      this.selectedUsers = [...this.serverSideRowData];\r\n      this.isAllSelected = true;\r\n    }\r\n    this.showBulkActions = this.selectedUsers.length > 0;\r\n  }\r\n\r\n  // Delete user\r\n  deleteUser(user: any): void {\r\n    if (\r\n      confirm(\r\n        `Are you sure you want to delete user ${user.FirstName} ${user.LastName}?`\r\n      )\r\n    ) {\r\n      // Show loading state\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n\r\n      const deleteData = {\r\n        userId: user.userId,\r\n        loggedInUserId: this.loginUser.userId || 0,\r\n      };\r\n\r\n      this.usersService.deleteUser(deleteData).subscribe({\r\n        next: (response: any) => {\r\n          if (response && response.message) {\r\n            //alert(response.message);\r\n                            this.customLayoutUtilsService.showSuccess(response.message, '');\r\n\r\n            this.loadTable(); // Reload the table\r\n            // this.loadUserStatistics(); // Reload statistics\r\n          }\r\n        },\r\n        error: (error: unknown) => {\r\n          console.error('Error deleting user:', error);\r\n                            this.customLayoutUtilsService.showError('Error deleting user', '');\r\n          \r\n          //alert('Error deleting user. Please try again.');\r\n          // Reset loading state on error\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Bulk update user status\r\n  bulkUpdateUserStatus(): void {\r\n    if (this.selectedUsers.length === 0) {\r\n                            this.customLayoutUtilsService.showError('Please select users to update', '');\r\n      //alert('Please select users to update.');\r\n\r\n\r\n      return;\r\n    }\r\n\r\n    if (\r\n      confirm(\r\n        `Are you sure you want to update ${this.selectedUsers.length} users to status: ${this.bulkActionStatus}?`\r\n      )\r\n    ) {\r\n      // Show loading state\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n\r\n      const bulkUpdateData = {\r\n        userIds: this.selectedUsers.map((user) => user.userId),\r\n        status: this.bulkActionStatus,\r\n        loggedInUserId: this.loginUser.userId || 0,\r\n      };\r\n\r\n      this.usersService.bulkUpdateUserStatus(bulkUpdateData).subscribe({\r\n        next: (response: any) => {\r\n          if (response && response.message) {\r\n            //alert(response.message);\r\n                            this.customLayoutUtilsService.showSuccess(response.message, '');\r\n\r\n            this.loadTable(); // Reload the table\r\n            // this.loadUserStatistics(); // Reload statistics\r\n            this.selectedUsers = []; // Clear selection\r\n            this.showBulkActions = false;\r\n          }\r\n        },\r\n        error: (error: unknown) => {\r\n          console.error('Error updating users:', error);\r\n          //alert('Error updating users. Please try again.');\r\n                            this.customLayoutUtilsService.showError('Error updating users', '');\r\n\r\n          // Reset loading state on error\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  // Unlock user\r\n  unlockUser(user: any): void {\r\n    if (\r\n      confirm(\r\n        `Are you sure you want to unlock user ${user.firstName} ${user.lastName}?`\r\n      )\r\n    ) {\r\n      // Show loading state\r\n      this.loading = true;\r\n      this.isLoading = true;\r\n\r\n      const unlockData = {\r\n        userId: user.userId,\r\n        loggedInUserId: this.loginUser.userId || 0,\r\n      };\r\n\r\n      this.usersService.unlockUser(unlockData).subscribe({\r\n        next: (response: any) => {\r\n          if (response && response.message) {\r\n            //alert(response.message);\r\n                            this.customLayoutUtilsService.showSuccess(response.message, '');\r\n\r\n            this.loadTable(); // Reload the table\r\n            // this.loadUserStatistics(); // Reload statistics\r\n          }\r\n        },\r\n        error: (error: unknown) => {\r\n          console.error('Error unlocking user:', error);\r\n                            this.customLayoutUtilsService.showSuccess('Error unlocking user. Please try again', '');\r\n\r\n          //alert('Error unlocking user. Please try again.');\r\n          // Reset loading state on error\r\n          this.loading = false;\r\n          this.isLoading = false;\r\n        },\r\n      });\r\n    }\r\n  }\r\n\r\n  onSearchKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter') {\r\n      this.searchTerms.next(this.searchData);\r\n    }\r\n  }\r\n\r\n  // Enhanced function to filter data from search and advanced filters\r\n  filterConfiguration(): {\r\n    paginate: boolean;\r\n    search: string;\r\n    columnFilter: Array<{\r\n      field: string;\r\n      operator: string;\r\n      value: any;\r\n    }>;\r\n  } {\r\n    let filter: {\r\n      paginate: boolean;\r\n      search: string;\r\n      columnFilter: Array<{\r\n        field: string;\r\n        operator: string;\r\n        value: any;\r\n      }>;\r\n    } = {\r\n      paginate: true,\r\n      search: '',\r\n      columnFilter: [],\r\n    };\r\n\r\n    // Handle search text\r\n    let searchText: string;\r\n    if (this.searchData === null || this.searchData === undefined) {\r\n      searchText = '';\r\n    } else {\r\n      searchText = this.searchData;\r\n    }\r\n    filter.search = searchText.trim();\r\n\r\n    // Handle Kendo UI grid filters\r\n    if (this.activeFilters && this.activeFilters.length > 0) {\r\n      filter.columnFilter = [...this.activeFilters];\r\n    }\r\n\r\n    // Add advanced filters\r\n    if (this.appliedFilters.status && this.appliedFilters.status !== null) {\r\n      filter.columnFilter.push({\r\n        field: 'userStatus',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.status,\r\n      });\r\n    }\r\n\r\n    if (this.appliedFilters.role && this.appliedFilters.role !== null) {\r\n      filter.columnFilter.push({\r\n        field: 'roleName',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.role,\r\n      });\r\n    }\r\n\r\n    return filter;\r\n  }\r\n\r\n  // Grid event handlers\r\n  public pageChange(event: { skip: number; take: number }): void {\r\n    this.skip = event.skip;\r\n    this.page.pageNumber = event.skip / event.take;\r\n    this.page.size = event.take;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Use native Kendo sort/unsort via sortChange\r\n\r\n  public onSortChange(sort: SortDescriptor[]): void {\r\n    // Check if this is the 3rd click (dir is undefined)\r\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\r\n\r\n    if (isThirdClick) {\r\n      // 3rd click - clear sort and use default\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\r\n      // Valid sort with direction\r\n      this.sort = sort;\r\n      this.page.orderBy = sort[0].field || 'lastUpdatedDate';\r\n      this.page.orderDir = sort[0].dir;\r\n    } else {\r\n      // Empty sort array or invalid sort\r\n      this.sort = [];\r\n      this.page.orderBy = 'lastUpdatedDate';\r\n      this.page.orderDir = 'desc';\r\n    }\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  public filterChange(filter: CompositeFilterDescriptor): void {\r\n    this.filter = filter;\r\n    this.gridFilter = filter;\r\n    this.activeFilters = this.flattenFilters(filter);\r\n    this.page.pageNumber = 0;\r\n    this.saveGridState();\r\n    // Set loading state for sorting\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.skip = 0;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Old column visibility methods removed - replaced with new system\r\n\r\n  // Fix 2: More robust getFilterValue method\r\n  public getFilterValue(\r\n    filter: CompositeFilterDescriptor,\r\n    column: { field: string }\r\n  ): any {\r\n    if (!filter || !filter.filters || !column) {\r\n      return null;\r\n    }\r\n    const predicate = filter.filters.find(\r\n      (f: any) => f && 'field' in f && f.field === column.field\r\n    );\r\n    return predicate && 'value' in predicate ? predicate.value : null;\r\n  }\r\n\r\n  // Fix 3: More robust onStatusFilterChange method\r\n  public onStatusFilterChange(\r\n    value: string | null,\r\n    filter: CompositeFilterDescriptor,\r\n    column: { field: string }\r\n  ): void {\r\n    if (!filter || !filter.filters || !column) {\r\n      console.error('Invalid filter or column:', { filter, column });\r\n      return;\r\n    }\r\n\r\n    const exists = filter.filters.findIndex(\r\n      (f: any) => f && 'field' in f && f.field === column.field\r\n    );\r\n    if (exists > -1) {\r\n      filter.filters.splice(exists, 1);\r\n    }\r\n\r\n    if (value !== null) {\r\n      filter.filters.push({\r\n        field: column.field,\r\n        operator: 'eq',\r\n        value: value,\r\n      });\r\n    }\r\n\r\n    this.filterChange(filter);\r\n  }\r\n\r\n  // Fix 4: More robust flattenFilters method\r\n  private flattenFilters(filter: CompositeFilterDescriptor): Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n  }> {\r\n    const filters: Array<{\r\n      field: string;\r\n      operator: string;\r\n      value: any;\r\n    }> = [];\r\n\r\n    if (!filter || !filter.filters) {\r\n      return filters;\r\n    }\r\n\r\n    filter.filters.forEach((f: any) => {\r\n      if (f && 'field' in f) {\r\n        // It's a FilterDescriptor\r\n        filters.push({\r\n          field: f.field,\r\n          operator: f.operator,\r\n          value: f.value,\r\n        });\r\n      } else if (f && 'filters' in f) {\r\n        // It's a CompositeFilterDescriptor\r\n        filters.push(...this.flattenFilters(f));\r\n      }\r\n    });\r\n\r\n    return filters;\r\n  }\r\n\r\n  // Fix 5: More robust loadGridState method\r\n  private loadGridState(): void {\r\n    try {\r\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\r\n\r\n      if (!savedState) {\r\n        return;\r\n      }\r\n\r\n      const state: {\r\n        sort?: SortDescriptor[];\r\n        filter?: CompositeFilterDescriptor;\r\n        page?: {\r\n          size: number;\r\n          pageNumber: number;\r\n          totalElements: number;\r\n          totalPages: number;\r\n          orderBy: string;\r\n          orderDir: string;\r\n        };\r\n        skip?: number;\r\n        columnsVisibility?: Record<string, boolean>;\r\n        searchData?: string;\r\n        activeFilters?: Array<{\r\n          field: string;\r\n          operator: string;\r\n          value: any;\r\n        }>;\r\n        appliedFilters?: {\r\n          status?: string | null;\r\n          role?: string | null;\r\n        };\r\n        showAdvancedFilters?: boolean;\r\n      } = JSON.parse(savedState);\r\n\r\n      // Restore sort state\r\n      if (state && state.sort) {\r\n        this.sort = state.sort;\r\n        if (this.sort && this.sort.length > 0 && this.sort[0]) {\r\n          this.page.orderBy = this.sort[0].field || 'lastUpdatedDate';\r\n          this.page.orderDir = this.sort[0].dir || 'desc';\r\n        }\r\n      }\r\n\r\n      // Restore filter state\r\n      if (state && state.filter) {\r\n        this.filter = state.filter;\r\n        this.gridFilter = state.filter;\r\n        this.activeFilters = state.activeFilters || [];\r\n      }\r\n\r\n      // Restore pagination state\r\n      if (state && state.page) {\r\n        this.page = state.page;\r\n      }\r\n\r\n      if (state && state.skip !== undefined) {\r\n        this.skip = state.skip;\r\n      }\r\n\r\n      // Restore column visibility\r\n      if (state && state.columnsVisibility) {\r\n        this.columnsVisibility = state.columnsVisibility;\r\n      }\r\n\r\n      // Restore search state\r\n      if (state && state.searchData) {\r\n        this.searchData = state.searchData;\r\n      }\r\n\r\n      // Restore advanced filter states\r\n      if (state && state.appliedFilters) {\r\n        this.appliedFilters = state.appliedFilters;\r\n      }\r\n\r\n      if (state && state.showAdvancedFilters !== undefined) {\r\n        this.showAdvancedFilters = state.showAdvancedFilters;\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading grid state:', error);\r\n      // If there's an error, use default state\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Reset the current state of column visibility and order in the grid to its original state.\r\n   */\r\n  resetTable(): void {\r\n    // Check if loginUser is available\r\n    if (!this.loginUser || !this.loginUser.userId) {\r\n      console.error('loginUser not available:', this.loginUser);\r\n      this.customLayoutUtilsService.showError('User not logged in. Please refresh the page and try again.', '');\r\n      return;\r\n    }\r\n\r\n    // Reset all grid state to default\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.searchData = '';\r\n    this.appliedFilters = {};\r\n    this.showAdvancedFilters = false;\r\n\r\n    // Reset column visibility and order\r\n    if (this.grid && this.grid.columns) {\r\n      this.grid.columns.forEach((column: any) => {\r\n        const index = this.gridColumns.indexOf(column.field);\r\n        if (index !== -1) {\r\n          column.orderIndex = index;\r\n        }\r\n        // Reset column visibility - show all columns\r\n        if (column.field && column.field !== 'action') {\r\n          column.hidden = false;\r\n        }\r\n      });\r\n    }\r\n\r\n    // Clear hidden columns\r\n    this.hiddenData = [];\r\n    this.kendoColOrder = [];\r\n    this.hiddenFields = [];\r\n\r\n    // Reset the Kendo Grid's internal state\r\n    if (this.grid) {\r\n      // Clear all filters\r\n      this.grid.filter = { logic: 'and', filters: [] };\r\n      \r\n      // Reset sorting\r\n      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];\r\n      \r\n      // Reset to first page\r\n      this.grid.skip = 0;\r\n      this.grid.pageSize = this.page.size;\r\n    }\r\n\r\n    // Prepare reset data\r\n    const userData = {\r\n      pageName: 'EmailTemplates',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: [],\r\n      kendoColOrder: [],\r\n      LoggedId: this.loginUser.userId\r\n    };\r\n\r\n    // Show loading state\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Save reset state to backend\r\n    this.kendoColumnService.createHideFields(userData).subscribe({\r\n      next: (res) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (!res.isFault) {\r\n          // Also clear from localStorage\r\n          this.kendoColumnService.clearFromLocalStorage('EmailTemplates');\r\n          this.customLayoutUtilsService.showSuccess(res.message || 'Column settings reset successfully.', '');\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.message || 'Failed to reset column settings.', '');\r\n        }\r\n        \r\n        // Trigger change detection and refresh grid\r\n        this.cdr.detectChanges();\r\n        \r\n        // Small delay to ensure the grid is updated\r\n        setTimeout(() => {\r\n          if (this.grid) {\r\n            this.grid.refresh();\r\n          }\r\n        }, 100);\r\n        \r\n        this.loadTable();\r\n      },\r\n      error: (error) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error resetting column settings:', error);\r\n\r\n        // Check if it's an authentication error\r\n        if (error.status === 401 || (error.error && error.error.status === 401)) {\r\n          this.customLayoutUtilsService.showError('Authentication failed. Please login again.', '');\r\n          // Optionally redirect to login page\r\n        } else {\r\n          this.customLayoutUtilsService.showError('Error resetting column settings. Please try again.', '');\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // Grid state persistence methods\r\n  private saveGridState(): void {\r\n    const state: {\r\n      sort: SortDescriptor[];\r\n      filter: CompositeFilterDescriptor;\r\n      page: {\r\n        size: number;\r\n        pageNumber: number;\r\n        totalElements: number;\r\n        totalPages: number;\r\n        orderBy: string;\r\n        orderDir: string;\r\n      };\r\n      skip: number;\r\n      columnsVisibility: Record<string, boolean>;\r\n      searchData: string;\r\n      activeFilters: Array<{\r\n        field: string;\r\n        operator: string;\r\n        value: any;\r\n      }>;\r\n      appliedFilters: {\r\n        status?: string | null;\r\n        role?: string | null;\r\n      };\r\n      showAdvancedFilters: boolean;\r\n    } = {\r\n      sort: this.sort,\r\n      filter: this.filter,\r\n      page: this.page,\r\n      skip: this.skip,\r\n      columnsVisibility: this.columnsVisibility,\r\n      searchData: this.searchData,\r\n      activeFilters: this.activeFilters,\r\n      appliedFilters: this.appliedFilters,\r\n      showAdvancedFilters: this.showAdvancedFilters,\r\n    };\r\n\r\n    localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\r\n  }\r\n\r\n  // Function to add a new company (calls edit function with ID 0)\r\n  add() {\r\n    this.edit(0);\r\n  }\r\n\r\n  // Function to open the edit modal for adding/editing a company\r\n  edit(id: number) {\r\n    console.log('Line: 413', 'call edit function: ', id);\r\n    // Configuration options for the modal dialog\r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the AddCompaniesComponent\r\n    const modalRef = this.modalService.open(\r\n      EmailTemplatesEditComponent,\r\n      NgbModalOptions\r\n    );\r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = id;\r\n    // modalRef.componentInstance.defaultPermissions = this.permissionArray;\r\n    // // Subscribe to the modal event when data is updated\r\n    // modalRef.componentInstance.passEntry.subscribe((receivedEntry: boolean) => {\r\n    //   if (receivedEntry === true) {\r\n    //     // Reload the table data after a successful update\r\n    //     this.loadTable();\r\n    //   }\r\n    // });\r\n  }\r\n\r\n  // Delete functionality removed\r\n\r\n  // Load categories for advanced filters\r\n  loadCategories(): void {\r\n    // Implementation for loading categories\r\n    console.log('Loading categories...');\r\n  }\r\n\r\n  // Load template statistics\r\n  loadTemplateStatistics(): void {\r\n    // Implementation for loading template statistics\r\n    console.log('Loading template statistics...');\r\n  }\r\n\r\n  // Setup search subscription\r\n  setupSearchSubscription(): void {\r\n    this.searchSubscription = this.searchTerms\r\n      .pipe(debounceTime(500), distinctUntilChanged())\r\n      .subscribe(() => {\r\n        this.page.pageNumber = 0;\r\n        this.skip = 0;\r\n        this.loadTable();\r\n      });\r\n  }\r\n  public toggleExpand(): void {\r\n    // Find grid container element and toggle fullscreen class\r\n    const gridContainer = document.querySelector(\r\n      '.grid-container'\r\n    ) as HTMLElement;\r\n    if (gridContainer) {\r\n      gridContainer.classList.toggle('fullscreen-grid');\r\n      this.isExpanded = !this.isExpanded;\r\n      // Refresh grid after resize to ensure proper rendering\r\n      if (this.grid) {\r\n        this.grid.refresh();\r\n      }\r\n    }\r\n  }\r\n\r\n  // Enhanced export functionality\r\n  public onExportClick(event: { item: { value: string } }): void {\r\n    switch (event.item.value) {\r\n      case 'all':\r\n        this.exportAllUsers();\r\n        break;\r\n      case 'selected':\r\n        this.exportSelectedUsers();\r\n        break;\r\n      case 'filtered':\r\n        this.exportFilteredUsers();\r\n        break;\r\n      default:\r\n        console.warn('Unknown export option:', event.item.value);\r\n    }\r\n  }\r\n\r\n  private exportAllUsers(): void {\r\n    const exportParams = {\r\n      filters: {},\r\n      format: 'excel',\r\n    };\r\n\r\n    this.usersService.exportUsers(exportParams).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.exportData) {\r\n          this.downloadExcel(response.exportData, 'All_Users');\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error exporting users:', error);\r\n        //alert('Error exporting users. Please try again.');\r\n      },\r\n    });\r\n  }\r\n\r\n  private exportSelectedUsers(): void {\r\n    if (this.selectedUsers.length === 0) {\r\n      //alert('Please select users to export.');\r\n      return;\r\n    }\r\n\r\n    const exportParams = {\r\n      filters: {\r\n        userIds: this.selectedUsers.map((user) => user.UserId),\r\n      },\r\n      format: 'excel',\r\n    };\r\n\r\n    this.usersService.exportUsers(exportParams).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.exportData) {\r\n          this.downloadExcel(response.exportData, 'Selected_Users');\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error exporting selected users:', error);\r\n        //alert('Error exporting selected users. Please try again.');\r\n      },\r\n    });\r\n  }\r\n\r\n  private exportFilteredUsers(): void {\r\n    const exportParams = {\r\n      filters: {\r\n        status: this.appliedFilters.status,\r\n        role: this.appliedFilters.role,\r\n        searchTerm: this.searchData,\r\n      },\r\n      format: 'excel',\r\n    };\r\n\r\n    this.usersService.exportUsers(exportParams).subscribe({\r\n      next: (response: any) => {\r\n        if (response && response.exportData) {\r\n          this.downloadExcel(response.exportData, 'Filtered_Users');\r\n        }\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error exporting filtered users:', error);\r\n        //alert('Error exporting filtered users. Please try again.');\r\n      },\r\n    });\r\n  }\r\n\r\n  private downloadExcel(data: any[], filename: string): void {\r\n    // This would typically use a library like xlsx or similar\r\n    // For now, we'll create a simple CSV download\r\n    const csvContent = this.convertToCSV(data);\r\n    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });\r\n    const link = document.createElement('a');\r\n    const url = URL.createObjectURL(blob);\r\n    link.setAttribute('href', url);\r\n    link.setAttribute(\r\n      'download',\r\n      `${filename}_${new Date().toISOString().split('T')[0]}.csv`\r\n    );\r\n    link.style.visibility = 'hidden';\r\n    document.body.appendChild(link);\r\n    link.click();\r\n    document.body.removeChild(link);\r\n  }\r\n\r\n  private convertToCSV(data: any[]): string {\r\n    if (data.length === 0) return '';\r\n\r\n    const headers = Object.keys(data[0]);\r\n    const csvRows = [headers.join(',')];\r\n\r\n    for (const row of data) {\r\n      const values = headers.map((header) => {\r\n        const value = row[header];\r\n        return typeof value === 'string' && value.includes(',')\r\n          ? `\"${value}\"`\r\n          : value;\r\n      });\r\n      csvRows.push(values.join(','));\r\n    }\r\n\r\n    return csvRows.join('\\n');\r\n  }\r\n\r\n  // NEW COLUMN VISIBILITY SYSTEM METHODS\r\n\r\n  /**\r\n   * Saves the current state of column visibility and order in the grid.\r\n   * This function categorizes columns into visible and hidden columns, records their titles,\r\n   * fields, and visibility status, and also captures the order of draggable columns.\r\n   * After gathering the necessary data, it sends this information to the backend for saving.\r\n   */\r\n  saveHead(): void {\r\n    // Check if loginUser is available\r\n    if (!this.loginUser || !this.loginUser.userId) {\r\n      console.error('loginUser not available:', this.loginUser);\r\n      this.customLayoutUtilsService.showError(\r\n        'User not logged in. Please refresh the page.',\r\n        ''\r\n      );\r\n      return;\r\n    }\r\n\r\n    const nonHiddenColumns: any[] = [];\r\n    const hiddenColumns: any[] = [];\r\n\r\n    if (this.grid && this.grid.columns) {\r\n      this.grid.columns.forEach((column: any) => {\r\n        if (!column.hidden) {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden,\r\n          };\r\n          nonHiddenColumns.push(columnData);\r\n        } else {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden,\r\n          };\r\n          hiddenColumns.push(columnData);\r\n        }\r\n      });\r\n    }\r\n\r\n    const draggableColumnsOrder = this.gridColumns\r\n      .filter((col) => !this.fixedColumns.includes(col))\r\n      .map((field, index) => ({\r\n        field,\r\n        orderIndex: index,\r\n      }));\r\n\r\n    // Prepare data for backend\r\n    const userData = {\r\n      pageName: 'Users',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: hiddenColumns,\r\n      kendoColOrder: draggableColumnsOrder,\r\n      LoggedId: this.loginUser.userId,\r\n    };\r\n\r\n    // Show loading state\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Save to backend\r\n    this.kendoColumnService.createHideFields(userData).subscribe({\r\n      next: (res) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (!res.isFault) {\r\n          // Update local state\r\n          this.hiddenData = hiddenColumns;\r\n          this.kendoColOrder = draggableColumnsOrder;\r\n          this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n          // Also save to localStorage as backup\r\n          this.kendoColumnService.saveToLocalStorage(userData);\r\n\r\n          this.customLayoutUtilsService.showSuccess(\r\n            res.message || 'Column settings saved successfully.',\r\n            ''\r\n          );\r\n        } else {\r\n          this.customLayoutUtilsService.showError(\r\n            res.message || 'Failed to save column settings.',\r\n            ''\r\n          );\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (error) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error saving column settings:', error);\r\n\r\n        // Fallback to localStorage on error\r\n        this.kendoColumnService.saveToLocalStorage(userData);\r\n\r\n        // Update local state\r\n        this.hiddenData = hiddenColumns;\r\n        this.kendoColOrder = draggableColumnsOrder;\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n        this.customLayoutUtilsService.showError(\r\n          'Failed to save to server. Settings saved locally.',\r\n          ''\r\n        );\r\n        this.cdr.markForCheck();\r\n      },\r\n    });\r\n  }\r\n\r\n\r\n  /**\r\n   * Loads and applies the saved column order from the user preferences or configuration.\r\n   * This function updates the grid column order, ensuring the fixed columns remain in place\r\n   * and the draggable columns are ordered according to the saved preferences.\r\n   */\r\n  loadSavedColumnOrder(kendoColOrder: any): void {\r\n    try {\r\n      const savedOrder = kendoColOrder;\r\n      if (savedOrder) {\r\n        const parsedOrder = savedOrder;\r\n        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {\r\n          // Get only the draggable columns from saved order\r\n          const savedDraggableColumns = parsedOrder\r\n            .sort((a, b) => a.orderIndex - b.orderIndex)\r\n            .map((col) => col.field)\r\n            .filter((field) => !this.fixedColumns.includes(field));\r\n\r\n          // Add any missing draggable columns at the end\r\n          const missingColumns = this.draggableColumns.filter(\r\n            (col) => !savedDraggableColumns.includes(col)\r\n          );\r\n\r\n          // Combine fixed columns with saved draggable columns\r\n          this.gridColumns = [\r\n            ...this.fixedColumns,\r\n            ...savedDraggableColumns,\r\n            ...missingColumns,\r\n          ];\r\n        } else {\r\n          this.gridColumns = [...this.defaultColumns];\r\n        }\r\n      } else {\r\n        this.gridColumns = [...this.defaultColumns];\r\n      }\r\n    } catch (error) {\r\n      this.gridColumns = [...this.defaultColumns];\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Checks if a given column is marked as hidden.\r\n   * This function searches the `hiddenFields` array to determine if the column should be hidden.\r\n   */\r\n  getHiddenField(columnName: any): boolean {\r\n    return this.hiddenFields.indexOf(columnName) > -1;\r\n  }\r\n\r\n  /**\r\n   * Handles the column reordering event triggered when a column is moved by the user.\r\n   * The function checks if the column being moved is in the fixed columns and prevents reordering\r\n   * of fixed columns.\r\n   */\r\n  onColumnReorder(event: any): void {\r\n    const { columns, newIndex, oldIndex } = event;\r\n\r\n    // Prevent reordering of fixed columns\r\n    if (\r\n      this.fixedColumns.includes(columns[oldIndex].field) ||\r\n      this.fixedColumns.includes(columns[newIndex].field)\r\n    ) {\r\n      return;\r\n    }\r\n\r\n    // Update the gridColumns array\r\n    const reorderedColumns = [...this.gridColumns];\r\n    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);\r\n    reorderedColumns.splice(newIndex, 0, movedColumn);\r\n\r\n    this.gridColumns = reorderedColumns;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  /**\r\n   * Handles column visibility changes from the Kendo Grid.\r\n   * Updates the local state when columns are shown or hidden.\r\n   */\r\n  updateColumnVisibility(event: any): void {\r\n    if (this.isExpanded === false) {\r\n      if (this.grid && this.grid.columns) {\r\n        this.grid.columns.forEach((column: any) => {\r\n          const columnData = {\r\n            title: column.title,\r\n            field: column.field,\r\n            hidden: column.hidden,\r\n          };\r\n          if (column.hidden) {\r\n            const exists = this.hiddenData.some(\r\n              (item: any) =>\r\n                item.field === columnData.field && item.hidden === true\r\n            );\r\n            if (!exists) {\r\n              this.hiddenData.push(columnData);\r\n            }\r\n          } else {\r\n            let indexExists = this.hiddenData.findIndex(\r\n              (item: any) =>\r\n                item.field === columnData.field && item.hidden === true\r\n            );\r\n            if (indexExists !== -1) {\r\n              this.hiddenData.splice(indexExists, 1);\r\n            }\r\n          }\r\n        });\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n        this.cdr.markForCheck();\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Loads the saved column configuration from the backend or localStorage as fallback.\r\n   * This method is called during component initialization to restore user preferences.\r\n   */\r\n  private loadColumnConfigFromDatabase(): void {\r\n    try {\r\n      // First try to load from backend\r\n      if (this.loginUser && this.loginUser.userId) {\r\n        this.kendoColumnService\r\n          .getHideFields({\r\n            pageName: 'Users',\r\n            userID: this.loginUser.userId,\r\n          })\r\n          .subscribe({\r\n            next: (res) => {\r\n              if (!res.isFault && res.Data) {\r\n                this.kendoHide = res.Data;\r\n                this.hiddenData = res.Data.hideData\r\n                  ? JSON.parse(res.Data.hideData)\r\n                  : [];\r\n                this.kendoInitColOrder = res.Data.kendoColOrder\r\n                  ? JSON.parse(res.Data.kendoColOrder)\r\n                  : [];\r\n                this.hiddenFields = this.hiddenData.map(\r\n                  (col: any) => col.field\r\n                );\r\n\r\n                // Update grid columns based on the hidden fields\r\n                if (this.grid && this.grid.columns) {\r\n                  this.grid.columns.forEach((column: any) => {\r\n                    if (\r\n                      this.hiddenData.some(\r\n                        (item: any) =>\r\n                          item.title === column.title && item.hidden\r\n                      )\r\n                    ) {\r\n                      column.includeInChooser = true;\r\n                      column.hidden = true;\r\n                    } else {\r\n                      column.hidden = false;\r\n                    }\r\n                  });\r\n                }\r\n\r\n                // Load saved column order and update grid\r\n                this.loadSavedColumnOrder(this.kendoInitColOrder);\r\n\r\n                // Also save to localStorage as backup\r\n                this.kendoColumnService.saveToLocalStorage({\r\n                  pageName: 'Users',\r\n                  userID: this.loginUser.userId,\r\n                  hiddenData: this.hiddenData,\r\n                  kendoColOrder: this.kendoInitColOrder,\r\n                });\r\n              }\r\n            },\r\n            error: (error) => {\r\n              console.error(\r\n                'Error loading from backend, falling back to localStorage:',\r\n                error\r\n              );\r\n              this.loadFromLocalStorageFallback();\r\n            },\r\n          });\r\n      } else {\r\n        // Fallback to localStorage if no user ID\r\n        this.loadFromLocalStorageFallback();\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading column configuration:', error);\r\n      this.loadFromLocalStorageFallback();\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Fallback method to load column configuration from localStorage\r\n   */\r\n  private loadFromLocalStorageFallback(): void {\r\n    try {\r\n      const savedConfig = this.kendoColumnService.getFromLocalStorage(\r\n        'Users',\r\n        this.loginUser?.UserId || 0\r\n      );\r\n      if (savedConfig) {\r\n        this.kendoHide = savedConfig;\r\n        this.hiddenData = savedConfig.hiddenData || [];\r\n        this.kendoInitColOrder = savedConfig.kendoColOrder || [];\r\n        this.hiddenFields = this.hiddenData.map((col: any) => col.field);\r\n\r\n        // Update grid columns based on the hidden fields\r\n        if (this.grid && this.grid.columns) {\r\n          this.grid.columns.forEach((column: any) => {\r\n            if (\r\n              this.hiddenData.some(\r\n                (item: any) => item.title === column.title && item.hidden\r\n              )\r\n            ) {\r\n              column.includeInChooser = true;\r\n              column.hidden = true;\r\n            } else {\r\n              column.hidden = false;\r\n            }\r\n          });\r\n        }\r\n\r\n        // Load saved column order and update grid\r\n        this.loadSavedColumnOrder(this.kendoInitColOrder);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading from localStorage fallback:', error);\r\n    }\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"spinner-border text-primary spinner-md\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<!-- Email Templates Card -->\r\n<div class=\"card mb-5 mb-xl-5\">\r\n  <div class=\"card-body pb-0 pt-0\">\r\n    <div class=\"d-flex justify-content-between align-items-center\">\r\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-3x border-transparent fs-5 fw-bold flex-nowrap\">\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer active\">\r\n            <PERSON>ail Templates\r\n          </a>\r\n        </li>\r\n      </ul>\r\n      \r\n      <!-- Back Button -->\r\n      <div class=\"d-flex align-items-center\">\r\n        <button \r\n          type=\"button\" \r\n          class=\"btn btn-sm btn-light-primary d-flex align-items-center back-button\"\r\n          (click)=\"goBack()\"\r\n          title=\"Go back to Settings Dashboard\">\r\n          <i class=\"fa fa-arrow-left me-2\"></i>\r\n          Back\r\n        </button>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"grid-container\">\r\n  <kendo-grid\r\n    #normalGrid\r\n    [data]=\"serverSideRowData\"\r\n    [pageSize]=\"page.size\"\r\n    [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [10, 15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (columnReorder)=\"onColumnReorder($event)\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto; overflow-x: auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"page.pageNumber * page.size\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\"\r\n  >\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox\r\n          [style.width.px]=\"500\"\r\n          placeholder=\"Search...\"\r\n          [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\"\r\n          (keydown)=\"onSearchKeyDown($event)\"\r\n          (ngModelChange)=\"onSearchChange()\"\r\n          (clear)=\"clearSearch()\"\r\n        ></kendo-textbox>\r\n        <!-- <button\r\n          kendoButton\r\n          [disabled]=\"!searchData || searchData.trim() === ''\"\r\n          (click)=\"loadTable()\"\r\n          class=\"ms-2\"\r\n        >\r\n          <i class=\"fas fa-search\"></i> Search\r\n        </button> -->\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted\">Total: </span>\r\n        <span class=\"fw-bold ms-1\">{{ page.totalElements || 0 }}</span>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <button type=\"button\" class=\"btn btn-icon btn-sm me-2\" (click)=\"add()\" title=\"Add Email Template\">\r\n        <span\r\n          [inlineSVG]=\"'./assets/media/icons/duotune/arrows/arr075.svg'\"\r\n          class=\"svg-icon svg-icon-3 text-primary\"\r\n        ></span>\r\n      </button>\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo text-warning\"></i>\r\n      </button>\r\n\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-secondary btn-sm me-2\"\r\n        (click)=\"toggleExpand()\"\r\n        title=\"Toggle Grid Expansion\"\r\n      >\r\n        <i\r\n          class=\"fas\"\r\n          [class.fa-expand]=\"!isExpanded\"\r\n          [class.fa-compress]=\"isExpanded\"\r\n        ></i>\r\n      </button>\r\n\r\n      <!-- <kendo-dropdownbutton\r\n        text=\"Export Excel\"\r\n        iconClass=\"fas fa-file-excel\"\r\n        [data]=\"exportOptions\"\r\n        class=\"custom-dropdown\"\r\n        (itemClick)=\"onExportClick($event)\"\r\n        title=\"Export\"\r\n      >\r\n      </kendo-dropdownbutton> -->\r\n\r\n      <!-- Save Column Settings Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-success btn-sm me-2\"\r\n        (click)=\"saveHead()\"\r\n        title=\"Save Column Settings\"\r\n      >\r\n        <i class=\"fas fa-save\"></i>\r\n      </button> -->\r\n\r\n      <!-- Reset Button -->\r\n      <!-- <button\r\n        type=\"button\"\r\n        class=\"btn btn-warning btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset\"\r\n      >\r\n        <i class=\"fas fa-undo\"></i>\r\n      </button> -->\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-info btn-sm me-2\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh\"\r\n      >\r\n        <i class=\"fas fa-sync-alt\"></i>\r\n      </button>\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <div\r\n        *ngIf=\"showAdvancedFilters\"\r\n        class=\"advanced-filters-panel p-3 bg-light border-bottom\"\r\n      >\r\n        <div class=\"row\">\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Status</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.status\"\r\n              [(ngModel)]=\"appliedFilters.status\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Status\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3\">\r\n            <label class=\"form-label\">Role</label>\r\n            <kendo-dropdownlist\r\n              [data]=\"advancedFilterOptions.roles\"\r\n              [(ngModel)]=\"appliedFilters.role\"\r\n              textField=\"text\"\r\n              valueField=\"value\"\r\n              placeholder=\"Select Role\"\r\n            >\r\n            </kendo-dropdownlist>\r\n          </div>\r\n          <div class=\"col-md-3 d-flex align-items-end\">\r\n            <button\r\n              kendoButton\r\n              (click)=\"applyAdvancedFilters()\"\r\n              class=\"btn-primary me-2\"\r\n            >\r\n              <i class=\"fas fa-check\"></i> Apply Filters\r\n            </button>\r\n            <button\r\n              kendoButton\r\n              (click)=\"clearAllFilters()\"\r\n              class=\"btn-secondary\"\r\n            >\r\n              <i class=\"fas fa-times\"></i> Clear\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n\r\n    <ng-container *ngFor=\"let column of gridColumns\">\r\n      <!-- Action Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'action'\"\r\n        title=\"Actions\"\r\n        [width]=\"125\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('action')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [columnMenu]=\"false\"\r\n        [style]=\"{ 'background-color': '#efefef !important' }\"\r\n        [hidden]=\"getHiddenField('action')\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <a\r\n            title=\"Edit\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"edit(dataItem.templatePID)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen055.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-primary\"\r\n            >\r\n            </span>\r\n          </a>\r\n          <!-- Delete button removed -->\r\n          <a\r\n            *ngIf=\"dataItem.IsLocked\"\r\n            title=\"Unlock\"\r\n            class=\"btn btn-icon btn-sm\"\r\n            (click)=\"unlockUser(dataItem)\"\r\n          >\r\n            <span\r\n              [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\r\n              class=\"svg-icon svg-icon-3 svg-icon-warning\"\r\n            >\r\n            </span>\r\n          </a>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- First Name Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'templateName'\"\r\n        field=\"templateName\"\r\n        title=\"Name\"\r\n        [width]=\"150\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('templateName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('templateName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.templateName }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Last Name Column -->\r\n      <!-- <kendo-grid-column *ngIf=\"column === 'lastName'\"\r\n        field=\"LastName\"\r\n        title=\"Last Name\"\r\n        [width]=\"150\"\r\n        [sticky]=\"true\"\r\n        [reorderable]=\"!fixedColumns.includes('LastName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3','font-weight':'600' }\"\r\n        [includeInChooser]=\"false\"\r\n        [hidden]=\"getHiddenField('LastName')\"\r\n        [filterable]=\"true\">\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.LastName }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu [column]=\"column\" [filter]=\"filter\" [extra]=\"true\">\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column> -->\r\n\r\n      <!-- Email Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'emailTo'\"\r\n        field=\"emailTo\"\r\n        title=\"Email\"\r\n        [width]=\"250\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('email')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('emailTo')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.emailTo }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Title Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'emailSubject'\"\r\n        field=\"emailSubject\"\r\n        title=\"Email Subject\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('title')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('title')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.emailSubject }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Phone Column -->\r\n      <!-- <kendo-grid-column\r\n        *ngIf=\"column === 'emailBody'\"\r\n        field=\"emailBody\"\r\n        title=\"Phone\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('phoneNo')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('phoneNo')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.emailBody }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column> -->\r\n\r\n      <!-- Role Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'category'\"\r\n        field=\"category\"\r\n        title=\"category\"\r\n        [width]=\"120\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('roleName')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('roleName')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <div>\r\n            <span class=\"fw-bolder cursor-pointer\">\r\n              {{ dataItem.category }}\r\n            </span>\r\n          </div>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-grid-string-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            [extra]=\"true\"\r\n          >\r\n            <kendo-filter-contains-operator></kendo-filter-contains-operator>\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>\r\n            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>\r\n          </kendo-grid-string-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Status Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'userStatus'\"\r\n        field=\"userStatus\"\r\n        title=\"Status\"\r\n        [width]=\"100\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('userStatus')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        [hidden]=\"getHiddenField('userStatus')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <span\r\n            *ngIf=\"dataItem.userStatus === 'Active'\"\r\n            ngbTooltip=\"Active\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen037.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-success\"\r\n            style=\"margin-left: 1.5rem\"\r\n          >\r\n          </span>\r\n          <span\r\n            *ngIf=\"dataItem.userStatus === 'Inactive'\"\r\n            ngbTooltip=\"Inactive\"\r\n            [inlineSVG]=\"'./assets/media/icons/duotune/general/gen040.svg'\"\r\n            class=\"svg-icon svg-icon-3 svg-icon-danger text-danger\"\r\n            style=\"margin-left: 1.5rem\"\r\n          >\r\n          </span>\r\n        </ng-template>\r\n        <ng-template kendoGridFilterMenuTemplate let-filter let-column=\"column\">\r\n          <kendo-dropdownlist\r\n            [data]=\"filterOptions\"\r\n            [value]=\"getFilterValue(filter, column)\"\r\n            (valueChange)=\"onStatusFilterChange($event, filter, column)\"\r\n            textField=\"text\"\r\n            valueField=\"value\"\r\n          >\r\n          </kendo-dropdownlist>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n\r\n      <!-- Updated Date Column -->\r\n      <kendo-grid-column\r\n        *ngIf=\"column === 'lastUpdatedDate'\"\r\n        field=\"lastUpdatedDate\"\r\n        title=\"Updated Date\"\r\n        [width]=\"160\"\r\n        [sticky]=\"false\"\r\n        [reorderable]=\"!fixedColumns.includes('lastUpdatedDate')\"\r\n        [headerStyle]=\"{ 'background-color': '#edf0f3', 'font-weight': '600' }\"\r\n        filter=\"date\"\r\n        format=\"MM/dd/yyyy\"\r\n        [maxResizableWidth]=\"240\"\r\n        [hidden]=\"getHiddenField('lastUpdatedDate')\"\r\n        [filterable]=\"true\"\r\n      >\r\n        \r\n        <ng-template kendoGridCellTemplate let-dataItem>\r\n          <span class=\"text-gray-600 fs-1r\">{{\r\n            dataItem.lastUpdatedDate | date : \"MM/dd/yyyy hh:mm a\"\r\n          }}</span>\r\n          <br /><span class=\"text-gray-600 fs-1r\">{{\r\n            dataItem.lastUpdatedByFullName\r\n          }}</span>\r\n        </ng-template>\r\n        <ng-template\r\n          kendoGridFilterMenuTemplate\r\n          let-filter\r\n          let-column=\"column\"\r\n          let-filterService=\"filterService\"\r\n        >\r\n          <kendo-grid-date-filter-menu\r\n            [column]=\"column\"\r\n            [filter]=\"filter\"\r\n            operator=\"eq\"\r\n            [filterService]=\"filterService\"\r\n          >\r\n            <kendo-filter-eq-operator></kendo-filter-eq-operator>\r\n            <kendo-filter-neq-operator></kendo-filter-neq-operator>\r\n            <kendo-filter-before-operator></kendo-filter-before-operator>\r\n            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>\r\n            <kendo-filter-after-operator></kendo-filter-after-operator>\r\n            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>\r\n          </kendo-grid-date-filter-menu>\r\n        </ng-template>\r\n      </kendo-grid-column>\r\n    </ng-container>\r\n\r\n    <ng-template kendoGridNoRecordsTemplate>\r\n      <div class=\"custom-no-records\" *ngIf=\"!loading && !isLoading\">\r\n        <div class=\"text-center\">\r\n          <i class=\"fas fa-envelope text-muted mb-2\" style=\"font-size: 2rem\"></i>\r\n          <p class=\"text-muted\">No email templates found</p>\r\n          <button kendoButton (click)=\"loadTable()\" class=\"btn-primary\">\r\n            <i class=\"fas fa-refresh me-2\"></i>Refresh\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n  </kendo-grid>\r\n  </div>\r\n</div>\r\n"], "mappings": ";AAeA,SACEA,OAAO,EACPC,YAAY,EACZC,oBAAoB,QAEf,MAAM;AACb,SAAiBC,eAAe,QAAwB,iBAAiB;AAWzE,SAASC,2BAA2B,QAAQ,wDAAwD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC5B9FC,EAHN,CAAAC,cAAA,cAAqE,cACtC,cACuC,eAClC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA6DEH,EADF,CAAAC,cAAA,cAA2D,wBASxD;IALCD,EAAA,CAAAI,gBAAA,2BAAAC,2FAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAIxBN,EAFA,CAAAc,UAAA,qBAAAC,qFAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,2BAAAD,2FAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAClBJ,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC,mBAAAC,mFAAA;MAAAlB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CACzBJ,MAAA,CAAAU,WAAA,EAAa;IAAA,EAAC;IAU3BnB,EATG,CAAAG,YAAA,EAAgB,EASb;IAENH,EAAA,CAAAoB,SAAA,wBAAuC;IAIrCpB,EADF,CAAAC,cAAA,cAA4C,eACjB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvCH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAGNH,EAAA,CAAAC,cAAA,iBAAkG;IAA3CD,EAAA,CAAAc,UAAA,mBAAAO,4EAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAa,GAAA,EAAK;IAAA,EAAC;IACpEtB,EAAA,CAAAoB,SAAA,eAGQ;IACVpB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAS,6EAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAe,UAAA,EAAY;IAAA,EAAC;IAGtBxB,EAAA,CAAAoB,SAAA,aAAwC;IAC1CpB,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAW,6EAAA;MAAAzB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAiB,YAAA,EAAc;IAAA,EAAC;IAGxB1B,EAAA,CAAAoB,SAAA,aAIK;IACPpB,EAAA,CAAAG,YAAA,EAAS;IAiCTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAa,6EAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmB,WAAA,EAAa;IAAA,EAAC;IAGvB5B,EAAA,CAAAoB,SAAA,aAA+B;IACjCpB,EAAA,CAAAG,YAAA,EAAS;;;;IA/FLH,EAAA,CAAA6B,SAAA,EAAsB;IAAtB7B,EAAA,CAAA8B,WAAA,oBAAsB;IAEtB9B,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAAG,UAAA,CAAwB;IACxBZ,EAAA,CAAAgC,UAAA,qBAAoB;IAoBKhC,EAAA,CAAA6B,SAAA,GAA6B;IAA7B7B,EAAA,CAAAiC,iBAAA,CAAAxB,MAAA,CAAAyB,IAAA,CAAAC,aAAA,MAA6B;IAMtDnC,EAAA,CAAA6B,SAAA,GAA8D;IAA9D7B,EAAA,CAAAgC,UAAA,+DAA8D;IAuB9DhC,EAAA,CAAA6B,SAAA,GAA+B;IAC/B7B,EADA,CAAAoC,WAAA,eAAA3B,MAAA,CAAA4B,UAAA,CAA+B,gBAAA5B,MAAA,CAAA4B,UAAA,CACC;;;;;;IAqD9BrC,EANN,CAAAC,cAAA,cAGC,cACkB,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAkC,sGAAAhC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAC,MAAA,EAAAnC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAC,MAAA,GAAAnC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAMvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAAsB,gBACM;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtCH,EAAA,CAAAC,cAAA,6BAMC;IAJCD,EAAA,CAAAI,gBAAA,2BAAAsC,sGAAApC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAA+B,cAAA,CAAAG,IAAA,EAAArC,MAAA,MAAAG,MAAA,CAAA+B,cAAA,CAAAG,IAAA,GAAArC,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAiC;IAMrCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,eAA6C,kBAK1C;IAFCD,EAAA,CAAAc,UAAA,mBAAA8B,mFAAA;MAAA5C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAoC,oBAAA,EAAsB;IAAA,EAAC;IAGhC7C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,uBAC/B;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAIC;IAFCD,EAAA,CAAAc,UAAA,mBAAAgC,mFAAA;MAAA9C,EAAA,CAAAO,aAAA,CAAAgC,GAAA;MAAA,MAAA9B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAsC,eAAA,EAAiB;IAAA,EAAC;IAG3B/C,EAAA,CAAAoB,SAAA,aAA4B;IAACpB,EAAA,CAAAE,MAAA,eAC/B;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IApCEH,EAAA,CAAA6B,SAAA,GAAqC;IAArC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAP,MAAA,CAAqC;IACrCzC,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAC,MAAA,CAAmC;IAUnCzC,EAAA,CAAA6B,SAAA,GAAoC;IAApC7B,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAuC,qBAAA,CAAAC,KAAA,CAAoC;IACpCjD,EAAA,CAAA+B,gBAAA,YAAAtB,MAAA,CAAA+B,cAAA,CAAAG,IAAA,CAAiC;;;;;IApBzC3C,EAAA,CAAAkD,UAAA,IAAAC,yDAAA,mBAGC;;;;IAFEnD,EAAA,CAAAgC,UAAA,SAAAvB,MAAA,CAAA2C,mBAAA,CAAyB;;;;;;IAyExBpD,EAAA,CAAAC,cAAA,YAKC;IADCD,EAAA,CAAAc,UAAA,mBAAAuC,8GAAA;MAAArD,EAAA,CAAAO,aAAA,CAAA+C,GAAA;MAAA,MAAAC,WAAA,GAAAvD,EAAA,CAAAU,aAAA,GAAA8C,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAgD,UAAA,CAAAF,WAAA,CAAoB;IAAA,EAAC;IAE9BvD,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;;;IAJAH,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;;;;;;IAnBnEhC,EAAA,CAAAC,cAAA,YAIC;IADCD,EAAA,CAAAc,UAAA,mBAAA4C,0GAAA;MAAA,MAAAH,WAAA,GAAAvD,EAAA,CAAAO,aAAA,CAAAoD,GAAA,EAAAH,SAAA;MAAA,MAAA/C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAmD,IAAA,CAAAL,WAAA,CAAAM,WAAA,CAA0B;IAAA,EAAC;IAEpC7D,EAAA,CAAAoB,SAAA,eAIO;IACTpB,EAAA,CAAAG,YAAA,EAAI;IAEJH,EAAA,CAAAkD,UAAA,IAAAY,0FAAA,gBAKC;;;;IAXG9D,EAAA,CAAA6B,SAAA,EAA+D;IAA/D7B,EAAA,CAAAgC,UAAA,gEAA+D;IAOhEhC,EAAA,CAAA6B,SAAA,EAAuB;IAAvB7B,EAAA,CAAAgC,UAAA,SAAAuB,WAAA,CAAAQ,QAAA,CAAuB;;;;;IA1B9B/D,EAAA,CAAAC,cAAA,4BAWC;IACCD,EAAA,CAAAkD,UAAA,IAAAc,sFAAA,0BAAgD;IA0BlDhE,EAAA,CAAAG,YAAA,EAAoB;;;;IA7BlBH,EAAA,CAAAiE,UAAA,CAAAjE,EAAA,CAAAkE,eAAA,IAAAC,GAAA,EAAsD;IACtDnE,EAPA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,WACiC,gBAAArE,EAAA,CAAAkE,eAAA,KAAAI,GAAA,EACuB,2BAC7C,qBACN,WAAA7D,MAAA,CAAA8D,cAAA,WAEe;;;;;IA6C/BvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAC,WAAA,CAAAC,YAAA,MACF;;;;;IAIF1E,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA2C,UAAA,CAAiB,WAAAC,SAAA,CACA,eACH;;;;;IAvBpB5E,EAAA,CAAAC,cAAA,4BAWC;IAQCD,EAPA,CAAAkD,UAAA,IAAA2B,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1E9E,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EANA,CAAAgC,UAAA,cAAa,gBACE,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,iBACuC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACiB,2BAC7C,WAAA7D,MAAA,CAAA8D,cAAA,iBACe,oBACtB;;;;;IAmEfvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAO,YAAA,CAAAC,OAAA,MACF;;;;;IAIFhF,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAAiD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAtBpBlF,EAAA,CAAAC,cAAA,4BAUC;IAQCD,EAPA,CAAAkD,UAAA,IAAAiC,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1EpF,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,YACnC,oBACjB;;;;;IAsCfvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAa,YAAA,CAAAC,YAAA,MACF;;;;;IAIFtF,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAAuD,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAtBpBxF,EAAA,CAAAC,cAAA,4BAUC;IAQCD,EAPA,CAAAkD,UAAA,IAAAuC,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1E1F,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,UAC+B,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACwB,WAAA7D,MAAA,CAAA8D,cAAA,UACrC,oBACf;;;;;IAwEfvE,EADF,CAAAC,cAAA,UAAK,eACoC;IACrCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;;;;IAFFH,EAAA,CAAA6B,SAAA,GACF;IADE7B,EAAA,CAAAwE,kBAAA,MAAAmB,YAAA,CAAAC,QAAA,MACF;;;;;IAIF5F,EAAA,CAAAC,cAAA,wCAIC;IAKCD,EAJA,CAAAoB,SAAA,qCAAiE,+BACZ,gCACE,uCACc,qCACJ;IACnEpB,EAAA,CAAAG,YAAA,EAAgC;;;;;IAP9BH,EAFA,CAAAgC,UAAA,WAAA6D,UAAA,CAAiB,WAAAC,UAAA,CACA,eACH;;;;;IAtBpB9F,EAAA,CAAAC,cAAA,4BAUC;IAQCD,EAPA,CAAAkD,UAAA,IAAA6C,sFAAA,0BAAgD,IAAAC,sFAAA,0BAOwB;IAa1EhG,EAAA,CAAAG,YAAA,EAAoB;;;;IAtBlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,aACkC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACqB,WAAA7D,MAAA,CAAA8D,cAAA,aAClC,oBAClB;;;;;IAqCjBvE,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAKjEhC,EAAA,CAAAoB,SAAA,eAOO;;;IAJLpB,EAAA,CAAAgC,UAAA,gEAA+D;;;;;IAHjEhC,EARA,CAAAkD,UAAA,IAAA+C,6FAAA,mBAMC,IAAAC,6FAAA,mBAQA;;;;IAbElG,EAAA,CAAAgC,UAAA,SAAAmE,YAAA,CAAAC,UAAA,cAAsC;IAQtCpG,EAAA,CAAA6B,SAAA,EAAwC;IAAxC7B,EAAA,CAAAgC,UAAA,SAAAmE,YAAA,CAAAC,UAAA,gBAAwC;;;;;;IAS3CpG,EAAA,CAAAC,cAAA,6BAMC;IAHCD,EAAA,CAAAc,UAAA,yBAAAuF,iIAAA/F,MAAA;MAAA,MAAAgG,OAAA,GAAAtG,EAAA,CAAAO,aAAA,CAAAgG,IAAA;MAAA,MAAAC,UAAA,GAAAF,OAAA,CAAA9C,SAAA;MAAA,MAAAiD,UAAA,GAAAH,OAAA,CAAAI,MAAA;MAAA,MAAAjG,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAeJ,MAAA,CAAAkG,oBAAA,CAAArG,MAAA,EAAAkG,UAAA,EAAAC,UAAA,CAA4C;IAAA,EAAC;IAI9DzG,EAAA,CAAAG,YAAA,EAAqB;;;;;;IALnBH,EADA,CAAAgC,UAAA,SAAAvB,MAAA,CAAAmG,aAAA,CAAsB,UAAAnG,MAAA,CAAAoG,cAAA,CAAAL,UAAA,EAAAC,UAAA,EACkB;;;;;IAhC9CzG,EAAA,CAAAC,cAAA,4BAUC;IAmBCD,EAlBA,CAAAkD,UAAA,IAAA4D,sFAAA,0BAAgD,IAAAC,sFAAA,0BAkBwB;IAU1E/G,EAAA,CAAAG,YAAA,EAAoB;;;;IA9BlBH,EALA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,eACoC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACmB,WAAA7D,MAAA,CAAA8D,cAAA,eAChC,oBACpB;;;;;IAiDjBvE,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEhC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACTH,EAAA,CAAAoB,SAAA,SAAM;IAAApB,EAAA,CAAAC,cAAA,eAAkC;IAAAD,EAAA,CAAAE,MAAA,GAEtC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IALyBH,EAAA,CAAA6B,SAAA,EAEhC;IAFgC7B,EAAA,CAAAiC,iBAAA,CAAAjC,EAAA,CAAAgH,WAAA,OAAAC,YAAA,CAAAC,eAAA,wBAEhC;IACsClH,EAAA,CAAA6B,SAAA,GAEtC;IAFsC7B,EAAA,CAAAiC,iBAAA,CAAAgF,YAAA,CAAAE,qBAAA,CAEtC;;;;;IAQFnH,EAAA,CAAAC,cAAA,sCAKC;IAMCD,EALA,CAAAoB,SAAA,+BAAqD,gCACE,mCACM,sCACM,kCACR,qCACM;IACnEpB,EAAA,CAAAG,YAAA,EAA8B;;;;;;IAR5BH,EAHA,CAAAgC,UAAA,WAAAoF,UAAA,CAAiB,WAAAC,UAAA,CACA,kBAAAC,iBAAA,CAEc;;;;;IAjCrCtH,EAAA,CAAAC,cAAA,4BAaC;IAUCD,EARA,CAAAkD,UAAA,IAAAqE,sFAAA,0BAAgD,IAAAC,sFAAA,0BAa/C;IAeHxH,EAAA,CAAAG,YAAA,EAAoB;;;;IA/BlBH,EARA,CAAAgC,UAAA,cAAa,iBACG,iBAAAvB,MAAA,CAAA2D,YAAA,CAAAC,QAAA,oBACyC,gBAAArE,EAAA,CAAAkE,eAAA,IAAAI,GAAA,EACc,0BAG9C,WAAA7D,MAAA,CAAA8D,cAAA,oBACmB,oBACzB;;;;;IAzSvBvE,EAAA,CAAAyH,uBAAA,GAAiD;IA6R/CzH,EA3RA,CAAAkD,UAAA,IAAAwE,wEAAA,iCAWC,IAAAC,wEAAA,gCAyCA,IAAAC,wEAAA,gCA+DA,IAAAC,wEAAA,gCAkCA,IAAAC,wEAAA,gCAoEA,IAAAC,wEAAA,gCAkCA,IAAAC,wEAAA,gCA6CA;;;;;IAvSEhI,EAAA,CAAA6B,SAAA,EAAyB;IAAzB7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,cAAyB;IAyCzBjI,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,oBAA+B;IAgE/BjI,EAAA,CAAA6B,SAAA,EAA0B;IAA1B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,eAA0B;IAkC1BjI,EAAA,CAAA6B,SAAA,EAA+B;IAA/B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,oBAA+B;IAoE/BjI,EAAA,CAAA6B,SAAA,EAA2B;IAA3B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,gBAA2B;IAkC3BjI,EAAA,CAAA6B,SAAA,EAA6B;IAA7B7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,kBAA6B;IA0C7BjI,EAAA,CAAA6B,SAAA,EAAkC;IAAlC7B,EAAA,CAAAgC,UAAA,SAAAiG,UAAA,uBAAkC;;;;;;IA+CnCjI,EADF,CAAAC,cAAA,cAA8D,cACnC;IACvBD,EAAA,CAAAoB,SAAA,YAAuE;IACvEpB,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAClDH,EAAA,CAAAC,cAAA,iBAA8D;IAA1CD,EAAA,CAAAc,UAAA,mBAAAoH,kFAAA;MAAAlI,EAAA,CAAAO,aAAA,CAAA4H,IAAA;MAAA,MAAA1H,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA2H,SAAA,EAAW;IAAA,EAAC;IACvCpI,EAAA,CAAAoB,SAAA,YAAmC;IAAApB,EAAA,CAAAE,MAAA,eACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IARNH,EAAA,CAAAkD,UAAA,IAAAmF,yDAAA,kBAA8D;;;;IAA9BrI,EAAA,CAAAgC,UAAA,UAAAvB,MAAA,CAAA6H,OAAA,KAAA7H,MAAA,CAAA8H,SAAA,CAA4B;;;ADzclE,OAAM,MAAOC,2BAA2B;EAqN5BC,YAAA;EACAC,GAAA;EACAC,MAAA;EACAC,KAAA;EACAC,YAAA;EACDC,UAAA;EACCC,wBAAA;EACAC,eAAA;EACAC,oBAAA;EACAC,kBAAA;EA7NeC,IAAI;EAE7B;EACOC,iBAAiB,GAAU,EAAE;EAC7BC,QAAQ,GAAU,EAAE;EACpBC,cAAc,GAAY,KAAK;EAE/BhB,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjCgB,SAAS,GAAQ,EAAE;EAEnB;EACO3I,UAAU,GAAW,EAAE;EACtB4I,WAAW,GAAG,IAAI7J,OAAO,EAAU;EACnC8J,kBAAkB;EAE1B;EACOC,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEAlD,aAAa,GAAkD,CACpE;IAAEmD,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,EACnC;IAAED,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAU,CAAE,CACxC;EAED;EACOhH,qBAAqB,GAAG;IAC7BP,MAAM,EAAE,CACN;MAAEsH,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,EACnC;MAAED,IAAI,EAAE,UAAU;MAAEC,KAAK,EAAE;IAAU,CAAE,CACS;IAClD/G,KAAK,EAAE,EAAmD;IAAE;IAC5DgH,UAAU,EAAE,EAAmD,CAAE;GAClE;EAED;EACO7G,mBAAmB,GAAG,KAAK;EAC3BZ,cAAc,GAGjB,EAAE;EAEN;EACO0H,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BpG,YAAY,GAAa,EAAE;EAC3BqG,gBAAgB,GAAa,EAAE;EAC/BC,UAAU;EACVC,YAAY;EACZtI,UAAU,GAAG,KAAK;EAEzB;EACOuI,gBAAgB,GAQlB,CACH;IACEC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,QAAQ;IACdC,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,IAAI;IACbC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR;EACD;EACA;IACEL,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,OAAO;IACdC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,WAAW;IAClBC,KAAK,EAAE,YAAY;IACnBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,UAAU;IACjBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,gBAAgB;IACvBC,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,GAAG;IACVE,IAAI,EAAE,QAAQ;IACdD,OAAO,EAAE,KAAK;IACdG,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,EACD;IACEL,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,GAAG;IACVC,OAAO,EAAE,KAAK;IACdC,IAAI,EAAE,MAAM;IACZE,UAAU,EAAE,IAAI;IAChBD,KAAK,EAAE;GACR,CACF;EAED;EACOE,iBAAiB,GAA4B,EAAE;EAEtD;EAEA;EACOC,IAAI,GAAqB,CAAC;IAAER,KAAK,EAAE,iBAAiB;IAAES,GAAG,EAAE;EAAM,CAAE,CAAC;EAE3E;EACOC,gBAAgB,GAA+C,EAAE;EAExE;EACQC,kBAAkB;EAE1B;EACiBC,cAAc,GAAG,2BAA2B;EAE7D;EACOvJ,IAAI,GAAe;IACxBwJ,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbxJ,aAAa,EAAE,CAAC;IAChByJ,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,iBAAiB;IAC1BC,QAAQ,EAAE;GACX;EACMC,IAAI,GAAW,CAAC;EAEvB;EACOC,aAAa,GAA2C,CAC7D;IAAEjC,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAE,EACpC;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,EAC9C;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,CAC/C;EAED;EACOiC,aAAa,GAAU,EAAE;EACzBC,aAAa,GAAY,KAAK;EAErC;EACOC,cAAc,GAMjB;IACFC,WAAW,EAAE,CAAC;IACdC,aAAa,EAAE,CAAC;IAChBC,cAAc,EAAE,CAAC;IACjBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE;GACb;EAED;EACOC,eAAe,GAAG,KAAK;EACvBC,gBAAgB,GAAW,QAAQ;EAE1C;EACOC,eAAe,GAAQ,EAAE;EAEhCC,YACUnE,YAAyB,EACzBC,GAAsB,EACtBC,MAAc,EACdC,KAAqB,EACrBC,YAAsB;EAAE;EACzBC,UAAsB,EACrBC,wBAAkD,EAClDC,eAAiC,EACjCC,oBAA0C,EAC1CC,kBAAsC;IATtC,KAAAT,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,oBAAoB,GAApBA,oBAAoB;IACpB,KAAAC,kBAAkB,GAAlBA,kBAAkB;EACzB;EAEH2D,MAAMA,CAAA;IACJ,IAAI,CAAClE,MAAM,CAACmE,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACxD,SAAS,GAAG,IAAI,CAACT,UAAU,CAACkE,eAAe,EAAE;IAClDC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE,IAAI,CAAC3D,SAAS,CAAC;IAEjD;IACA,IAAI,CAACE,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvC2D,IAAI,CAACvN,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/CuN,SAAS,CAAC,MAAK;MACd;MACA,IAAI,CAAC9E,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACrG,IAAI,CAACyJ,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MACb,IAAI,CAAC3D,SAAS,EAAE;IAClB,CAAC,CAAC;IAEJ;IACA,IAAI,CAACoD,kBAAkB,GAAG,IAAI,CAAC7C,MAAM,CAAC0E,MAAM,CAACD,SAAS,CAAEE,KAAK,IAAI;MAC/D,IAAIA,KAAK,YAAYxN,eAAe,EAAE;QACpC,IAAI,CAACyN,aAAa,EAAE;MACtB;IACF,CAAC,CAAC;IAEF;IACA,IAAI,CAACC,aAAa,EAAE;IAEpB;IACA,IAAI,CAACC,SAAS,EAAE;IAEhB;IACA;IAEA;IACA,IAAI,CAACC,UAAU,EAAE;IAEjB;IACA,IAAI,CAACC,gCAAgC,EAAE;IAEvC;IACAC,UAAU,CAAC,MAAK;MACd,IAAI,CAACC,4BAA4B,EAAE;IACrC,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;;;EAGQF,gCAAgCA,CAAA;IACtC;IACA,IAAI,CAACnD,cAAc,GAAG,IAAI,CAACI,gBAAgB,CAACkD,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAClD,KAAK,CAAC;IACnE,IAAI,CAACN,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAE3C;IACA,IAAI,CAACpG,YAAY,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC;IAEvD;IACA,IAAI,CAACqG,gBAAgB,GAAG,IAAI,CAACD,cAAc,CAACd,MAAM,CAC/CqE,GAAG,IAAK,CAAC,IAAI,CAAC3J,YAAY,CAACC,QAAQ,CAAC0J,GAAG,CAAC,CAC1C;IAED;IACA,IAAI,CAACrD,UAAU,GAAG,IAAI,CAACvB,IAAI;IAC3B,IAAI,CAACwB,YAAY,GAAG,IAAI,CAACxB,IAAI;IAE7B;IACAyE,UAAU,CAAC,MAAK;MACd,IAAI,CAACI,cAAc,EAAE;MACrB,IAAI,CAACC,sBAAsB,EAAE;IAC/B,CAAC,EAAE,GAAG,CAAC;EACT;EAEAC,eAAeA,CAAA;IACb;IACA;IACAN,UAAU,CAAC,MAAK;MACd,IAAI,CAACxF,SAAS,EAAE;IAClB,CAAC,EAAE,GAAG,CAAC;EACT;EAEA;EACA+F,cAAcA,CAAA;IACZ;IACA,IAAI,CAAC7F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACH,SAAS,EAAE;IAChB,IAAI,CAAC6F,sBAAsB,EAAE;EAC/B;EAEA;EACAP,UAAUA,CAAA;IACR;IACA,IAAI,CAACxL,IAAI,CAACyJ,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACV,IAAI,GAAG,CAAC;MAAER,KAAK,EAAE,iBAAiB;MAAES,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC5B,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAAChJ,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACwH,SAAS,EAAE;EAClB;EAEA;EACAxG,WAAWA,CAAA;IACT;IACA,IAAI,CAAC0G,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACrG,IAAI,CAACyJ,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACrC,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACtH,cAAc,GAAG,EAAE;IAExB;IACA,IAAI,CAAC5B,UAAU,GAAG,EAAE;IAEpB;IACA,IAAI,CAACwH,SAAS,EAAE;EAClB;EACAgG,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAAC5C,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC6C,WAAW,EAAE;IACvC;IACA,IAAI,IAAI,CAAC5E,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC4E,WAAW,EAAE;IACvC;IACA,IAAI,CAAC7E,WAAW,CAAC8E,QAAQ,EAAE;EAC7B;EACA;EACAC,0BAA0BA,CAAA;IACxB,IAAI,CAACjG,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACS,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAACrD,IAAI,CAACsD,MAAM,GAAG,CAAC,GACvC,IAAI,CAACtD,IAAI,GACT,CAAC;MAAER,KAAK,EAAE,iBAAiB;MAAES,GAAG,EAAE;IAAM,CAAE,CAAC;IAC/C,MAAMsD,KAAK,GAAG;MACZC,IAAI,EAAE,IAAI,CAAC3M,IAAI,CAACwJ,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfV,IAAI,EAAEqD,cAAc;MACpBhF,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBoF,MAAM,EAAE,IAAI,CAAClO,UAAU;MACvBmO,cAAc,EAAE,IAAI,CAACxF,SAAS,CAACyF;KAChC;IAED,IAAI,CAAC/F,oBAAoB,CAACgG,6BAA6B,CAACL,KAAK,CAAC,CAACxB,SAAS,CAAC;MACvEqB,IAAI,EAAGS,IAYN,IAAI;QACH;QACA,IACEA,IAAI,CAACC,OAAO,IACXD,IAAI,CAACE,YAAY,IAChBF,IAAI,CAACE,YAAY,CAACC,MAAM,IACxBH,IAAI,CAACE,YAAY,CAACC,MAAM,CAACV,MAAM,GAAG,CAAE,EACtC;UACA,MAAMU,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7DpC,OAAO,CAACqC,KAAK,CAAC,uBAAuB,EAAED,MAAM,CAAC;UAC9C,IAAI,CAACE,mBAAmB,EAAE;QAC5B,CAAC,MAAM;UACL;UACA,MAAMH,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMM,QAAQ,GAAGJ,YAAY,CAACF,IAAI,IAAI,EAAE;UACxC,MAAMO,KAAK,GAAGL,YAAY,CAACK,KAAK,IAAI,CAAC;UAErC,IAAI,CAACnG,cAAc,GAAGkG,QAAQ,CAACb,MAAM,KAAK,CAAC;UAC3C,IAAI,CAACvF,iBAAiB,GAAGoG,QAAQ;UACjC,IAAI,CAACnG,QAAQ,GAAG,IAAI,CAACD,iBAAiB;UACtC6D,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC7D,QAAQ,CAAC;UAC3C,IAAI,CAACnH,IAAI,CAACC,aAAa,GAAGsN,KAAK;UAC/B,IAAI,CAACvN,IAAI,CAAC0J,UAAU,GAAG8D,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACvN,IAAI,CAACwJ,IAAI,CAAC;QAC1D;QACA,IAAI,CAAC1C,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDa,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACC,mBAAmB,EAAE;QAC1B,IAAI,CAACjH,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACS,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDH,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAChG,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACS,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC/F,GAAG,CAACkH,aAAa,EAAE;MAC1B;KACD,CAAC;EACJ;EAEA;EACMxH,SAASA,CAAA;IAAA,IAAAyH,KAAA;IAAA,OAAAC,iBAAA;MACb;MACAD,KAAI,CAACtB,0BAA0B,EAAE;IAAC;EACpC;EAEQgB,mBAAmBA,CAAA;IACzB,IAAI,CAACjH,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACS,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;IAC/C,IAAI,CAACnF,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACnH,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAAC0J,UAAU,GAAG,CAAC;IACxB,IAAI,CAAClD,GAAG,CAACkH,aAAa,EAAE;EAC1B;EAEA;EACAzO,WAAWA,CAAA;IACT,IAAI,IAAI,CAACP,UAAU,KAAK,EAAE,EAAE;MAC1B,IAAI,CAAC4I,WAAW,CAACiF,IAAI,CAAC,EAAE,CAAC;IAC3B;EACF;EAEAxN,cAAcA,CAAA;IACZ,IAAI,CAACuI,WAAW,CAACiF,IAAI,CAAC,IAAI,CAAC7N,UAAU,IAAI,EAAE,CAAC;EAC9C;EAEA;EACAmC,eAAeA,CAAA;IACb,IAAI,CAACnC,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC8I,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACC,UAAU,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC/C,IAAI,CAACE,aAAa,GAAG,EAAE;IACvB,IAAI,CAACtH,cAAc,GAAG,EAAE;IACxB,IAAI,CAACN,IAAI,CAACyJ,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAAC3D,SAAS,EAAE;EAClB;EAEA;EACAvF,oBAAoBA,CAAA;IAClB,IAAI,CAACX,IAAI,CAACyJ,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAAC3D,SAAS,EAAE;EAClB;EAEA;EACA2H,qBAAqBA,CAAA;IACnB,IAAI,CAAC3M,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA;EACAqK,SAASA,CAAA;IACP,MAAMuC,WAAW,GAKb;MACFC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,UAAU;MACrBxE,UAAU,EAAE;KACb;IAED,IAAI,CAAClD,YAAY,CAAC2H,WAAW,CAACJ,WAAW,CAAC,CAAC5C,SAAS,CAAC;MACnDqB,IAAI,EAAGS,IAIN,IAAI;QACH,IAAIA,IAAI,IAAIA,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,CAACiB,OAAO,EAAE;UAC1D,IAAI,CAACrN,qBAAqB,CAACC,KAAK,GAAG,CACjC;YAAE8G,IAAI,EAAE,WAAW;YAAEC,KAAK,EAAE;UAAI,CAAE,EAClC,GAAGkF,IAAI,CAACE,YAAY,CAACiB,OAAO,CAACvC,GAAG,CAAEnL,IAA0B,KAAM;YAChEoH,IAAI,EAAEpH,IAAI,CAAC2N,QAAQ;YACnBtG,KAAK,EAAErH,IAAI,CAAC2N;WACb,CAAC,CAAC,CACJ;QACH,CAAC,MAAM;UACL;UACA,IAAI,CAACtN,qBAAqB,CAACiH,UAAU,GAAG,CACtC;YAAEF,IAAI,EAAE,gBAAgB;YAAEC,KAAK,EAAE;UAAI,CAAE,CACxC;QACH;MACF,CAAC;MACDsF,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C;QACA,IAAI,CAACtM,qBAAqB,CAACC,KAAK,GAAG,CAAC;UAAE8G,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAI,CAAE,CAAC;MACzE;KACD,CAAC;IACF,IAAI,CAACvB,YAAY,CACd8H,qBAAqB,CAAC,EAAE,CAAC,CACzBnD,SAAS,CAAEoD,WAAgB,IAAI;MAC9B,IAAI,CAAC7D,eAAe,GAAG6D,WAAW,CAACpB,YAAY;IACjD,CAAC,CAAC;EACN;EAEA;EACAqB,kBAAkBA,CAAA;IAChB,IAAI,CAAChI,YAAY,CAACiI,iBAAiB,EAAE,CAACtD,SAAS,CAAC;MAC9CqB,IAAI,EAAGS,IAAS,IAAI;QAClB,IAAIA,IAAI,IAAIA,IAAI,CAACyB,UAAU,EAAE;UAC3B,IAAI,CAACxE,cAAc,GAAG+C,IAAI,CAACyB,UAAU;QACvC;MACF,CAAC;MACDrB,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACxD;KACD,CAAC;EACJ;EAEA;EACAsB,iBAAiBA,CAACC,SAAc;IAC9B,IAAI,CAAC5E,aAAa,GAAG4E,SAAS,CAACC,YAAY,IAAI,EAAE;IACjD,IAAI,CAAC5E,aAAa,GAChB,IAAI,CAACD,aAAa,CAAC0C,MAAM,KAAK,IAAI,CAACvF,iBAAiB,CAACuF,MAAM;IAC7D,IAAI,CAAClC,eAAe,GAAG,IAAI,CAACR,aAAa,CAAC0C,MAAM,GAAG,CAAC;EACtD;EAEA;EACAoC,cAAcA,CAAA;IACZ,IAAI,IAAI,CAAC7E,aAAa,EAAE;MACtB,IAAI,CAACD,aAAa,GAAG,EAAE;MACvB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC5B,CAAC,MAAM;MACL,IAAI,CAACD,aAAa,GAAG,CAAC,GAAG,IAAI,CAAC7C,iBAAiB,CAAC;MAChD,IAAI,CAAC8C,aAAa,GAAG,IAAI;IAC3B;IACA,IAAI,CAACO,eAAe,GAAG,IAAI,CAACR,aAAa,CAAC0C,MAAM,GAAG,CAAC;EACtD;EAEA;EACAqC,UAAUA,CAACC,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACE,SAAS,IAAIF,IAAI,CAACG,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAAC9I,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAM8I,UAAU,GAAG;QACjBrC,MAAM,EAAEiC,IAAI,CAACjC,MAAM;QACnBD,cAAc,EAAE,IAAI,CAACxF,SAAS,CAACyF,MAAM,IAAI;OAC1C;MAED,IAAI,CAACvG,YAAY,CAACuI,UAAU,CAACK,UAAU,CAAC,CAACjE,SAAS,CAAC;QACjDqB,IAAI,EAAG6C,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACgB,IAAI,CAACxI,wBAAwB,CAACyI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE/E,IAAI,CAACnJ,SAAS,EAAE,CAAC,CAAC;YAClB;UACF;QACF,CAAC;QACDkH,KAAK,EAAGA,KAAc,IAAI;UACxBrC,OAAO,CAACqC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;UAC1B,IAAI,CAACvG,wBAAwB,CAAC0I,SAAS,CAAC,qBAAqB,EAAE,EAAE,CAAC;UAEpF;UACA;UACA,IAAI,CAACnJ,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACAmJ,oBAAoBA,CAAA;IAClB,IAAI,IAAI,CAACzF,aAAa,CAAC0C,MAAM,KAAK,CAAC,EAAE;MACb,IAAI,CAAC5F,wBAAwB,CAAC0I,SAAS,CAAC,+BAA+B,EAAE,EAAE,CAAC;MAClG;MAGA;IACF;IAEA,IACEP,OAAO,CACL,mCAAmC,IAAI,CAACjF,aAAa,CAAC0C,MAAM,qBAAqB,IAAI,CAACjC,gBAAgB,GAAG,CAC1G,EACD;MACA;MACA,IAAI,CAACpE,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAMoJ,cAAc,GAAG;QACrBC,OAAO,EAAE,IAAI,CAAC3F,aAAa,CAAC6B,GAAG,CAAEmD,IAAI,IAAKA,IAAI,CAACjC,MAAM,CAAC;QACtDvM,MAAM,EAAE,IAAI,CAACiK,gBAAgB;QAC7BqC,cAAc,EAAE,IAAI,CAACxF,SAAS,CAACyF,MAAM,IAAI;OAC1C;MAED,IAAI,CAACvG,YAAY,CAACiJ,oBAAoB,CAACC,cAAc,CAAC,CAACvE,SAAS,CAAC;QAC/DqB,IAAI,EAAG6C,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACgB,IAAI,CAACxI,wBAAwB,CAACyI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE/E,IAAI,CAACnJ,SAAS,EAAE,CAAC,CAAC;YAClB;YACA,IAAI,CAAC6D,aAAa,GAAG,EAAE,CAAC,CAAC;YACzB,IAAI,CAACQ,eAAe,GAAG,KAAK;UAC9B;QACF,CAAC;QACD6C,KAAK,EAAGA,KAAc,IAAI;UACxBrC,OAAO,CAACqC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC7C;UACkB,IAAI,CAACvG,wBAAwB,CAAC0I,SAAS,CAAC,sBAAsB,EAAE,EAAE,CAAC;UAErF;UACA,IAAI,CAACnJ,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEA;EACA9E,UAAUA,CAACwN,IAAS;IAClB,IACEC,OAAO,CACL,wCAAwCD,IAAI,CAACY,SAAS,IAAIZ,IAAI,CAACa,QAAQ,GAAG,CAC3E,EACD;MACA;MACA,IAAI,CAACxJ,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MAErB,MAAMwJ,UAAU,GAAG;QACjB/C,MAAM,EAAEiC,IAAI,CAACjC,MAAM;QACnBD,cAAc,EAAE,IAAI,CAACxF,SAAS,CAACyF,MAAM,IAAI;OAC1C;MAED,IAAI,CAACvG,YAAY,CAAChF,UAAU,CAACsO,UAAU,CAAC,CAAC3E,SAAS,CAAC;QACjDqB,IAAI,EAAG6C,QAAa,IAAI;UACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,OAAO,EAAE;YAChC;YACgB,IAAI,CAACxI,wBAAwB,CAACyI,WAAW,CAACF,QAAQ,CAACC,OAAO,EAAE,EAAE,CAAC;YAE/E,IAAI,CAACnJ,SAAS,EAAE,CAAC,CAAC;YAClB;UACF;QACF,CAAC;QACDkH,KAAK,EAAGA,KAAc,IAAI;UACxBrC,OAAO,CAACqC,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;UAC3B,IAAI,CAACvG,wBAAwB,CAACyI,WAAW,CAAC,wCAAwC,EAAE,EAAE,CAAC;UAEzG;UACA;UACA,IAAI,CAAClJ,OAAO,GAAG,KAAK;UACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACxB;OACD,CAAC;IACJ;EACF;EAEAvH,eAAeA,CAACsM,KAAoB;IAClC,IAAIA,KAAK,CAAC0E,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACxI,WAAW,CAACiF,IAAI,CAAC,IAAI,CAAC7N,UAAU,CAAC;IACxC;EACF;EAEA;EACAqR,mBAAmBA,CAAA;IASjB,IAAIvI,MAAM,GAQN;MACFwI,QAAQ,EAAE,IAAI;MACdpD,MAAM,EAAE,EAAE;MACVqD,YAAY,EAAE;KACf;IAED;IACA,IAAIC,UAAkB;IACtB,IAAI,IAAI,CAACxR,UAAU,KAAK,IAAI,IAAI,IAAI,CAACA,UAAU,KAAKyR,SAAS,EAAE;MAC7DD,UAAU,GAAG,EAAE;IACjB,CAAC,MAAM;MACLA,UAAU,GAAG,IAAI,CAACxR,UAAU;IAC9B;IACA8I,MAAM,CAACoF,MAAM,GAAGsD,UAAU,CAACE,IAAI,EAAE;IAEjC;IACA,IAAI,IAAI,CAACxI,aAAa,IAAI,IAAI,CAACA,aAAa,CAAC6E,MAAM,GAAG,CAAC,EAAE;MACvDjF,MAAM,CAACyI,YAAY,GAAG,CAAC,GAAG,IAAI,CAACrI,aAAa,CAAC;IAC/C;IAEA;IACA,IAAI,IAAI,CAACtH,cAAc,CAACC,MAAM,IAAI,IAAI,CAACD,cAAc,CAACC,MAAM,KAAK,IAAI,EAAE;MACrEiH,MAAM,CAACyI,YAAY,CAACI,IAAI,CAAC;QACvB1H,KAAK,EAAE,YAAY;QACnB2H,QAAQ,EAAE,IAAI;QACdxI,KAAK,EAAE,IAAI,CAACxH,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA,IAAI,IAAI,CAACD,cAAc,CAACG,IAAI,IAAI,IAAI,CAACH,cAAc,CAACG,IAAI,KAAK,IAAI,EAAE;MACjE+G,MAAM,CAACyI,YAAY,CAACI,IAAI,CAAC;QACvB1H,KAAK,EAAE,UAAU;QACjB2H,QAAQ,EAAE,IAAI;QACdxI,KAAK,EAAE,IAAI,CAACxH,cAAc,CAACG;OAC5B,CAAC;IACJ;IAEA,OAAO+G,MAAM;EACf;EAEA;EACO+I,UAAUA,CAACnF,KAAqC;IACrD,IAAI,CAACvB,IAAI,GAAGuB,KAAK,CAACvB,IAAI;IACtB,IAAI,CAAC7J,IAAI,CAACyJ,UAAU,GAAG2B,KAAK,CAACvB,IAAI,GAAGuB,KAAK,CAACuB,IAAI;IAC9C,IAAI,CAAC3M,IAAI,CAACwJ,IAAI,GAAG4B,KAAK,CAACuB,IAAI;IAC3B,IAAI,CAACzG,SAAS,EAAE;EAClB;EAEA;EAEOsK,YAAYA,CAACrH,IAAsB;IACxC;IACA,MAAMsH,YAAY,GAAGtH,IAAI,CAACsD,MAAM,GAAG,CAAC,IAAItD,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,KAAK+G,SAAS;IAE5E,IAAIM,YAAY,EAAE;MAChB;MACA,IAAI,CAACtH,IAAI,GAAG,EAAE;MACd,IAAI,CAACnJ,IAAI,CAAC2J,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAAC3J,IAAI,CAAC4J,QAAQ,GAAG,MAAM;IAC7B,CAAC,MAAM,IAAIT,IAAI,CAACsD,MAAM,GAAG,CAAC,IAAItD,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,EAAE;MACpD;MACA,IAAI,CAACD,IAAI,GAAGA,IAAI;MAChB,IAAI,CAACnJ,IAAI,CAAC2J,OAAO,GAAGR,IAAI,CAAC,CAAC,CAAC,CAACR,KAAK,IAAI,iBAAiB;MACtD,IAAI,CAAC3I,IAAI,CAAC4J,QAAQ,GAAGT,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG;IAClC,CAAC,MAAM;MACL;MACA,IAAI,CAACD,IAAI,GAAG,EAAE;MACd,IAAI,CAACnJ,IAAI,CAAC2J,OAAO,GAAG,iBAAiB;MACrC,IAAI,CAAC3J,IAAI,CAAC4J,QAAQ,GAAG,MAAM;IAC7B;IAEA,IAAI,CAAC1D,SAAS,EAAE;EAClB;EAEOwK,YAAYA,CAAClJ,MAAiC;IACnD,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,UAAU,GAAGH,MAAM;IACxB,IAAI,CAACI,aAAa,GAAG,IAAI,CAAC+I,cAAc,CAACnJ,MAAM,CAAC;IAChD,IAAI,CAACxH,IAAI,CAACyJ,UAAU,GAAG,CAAC;IACxB,IAAI,CAAC4B,aAAa,EAAE;IACpB;IACA,IAAI,CAACjF,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACwD,IAAI,GAAG,CAAC;IACb,IAAI,CAAC3D,SAAS,EAAE;EAClB;EAEA;EAEA;EACOvB,cAAcA,CACnB6C,MAAiC,EACjChD,MAAyB;IAEzB,IAAI,CAACgD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAAClD,MAAM,EAAE;MACzC,OAAO,IAAI;IACb;IACA,MAAMoM,SAAS,GAAGpJ,MAAM,CAACE,OAAO,CAACmJ,IAAI,CAClCC,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAACnI,KAAK,KAAKnE,MAAM,CAACmE,KAAK,CAC1D;IACD,OAAOiI,SAAS,IAAI,OAAO,IAAIA,SAAS,GAAGA,SAAS,CAAC9I,KAAK,GAAG,IAAI;EACnE;EAEA;EACOrD,oBAAoBA,CACzBqD,KAAoB,EACpBN,MAAiC,EACjChD,MAAyB;IAEzB,IAAI,CAACgD,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,IAAI,CAAClD,MAAM,EAAE;MACzCuG,OAAO,CAACqC,KAAK,CAAC,2BAA2B,EAAE;QAAE5F,MAAM;QAAEhD;MAAM,CAAE,CAAC;MAC9D;IACF;IAEA,MAAMuM,MAAM,GAAGvJ,MAAM,CAACE,OAAO,CAACsJ,SAAS,CACpCF,CAAM,IAAKA,CAAC,IAAI,OAAO,IAAIA,CAAC,IAAIA,CAAC,CAACnI,KAAK,KAAKnE,MAAM,CAACmE,KAAK,CAC1D;IACD,IAAIoI,MAAM,GAAG,CAAC,CAAC,EAAE;MACfvJ,MAAM,CAACE,OAAO,CAACuJ,MAAM,CAACF,MAAM,EAAE,CAAC,CAAC;IAClC;IAEA,IAAIjJ,KAAK,KAAK,IAAI,EAAE;MAClBN,MAAM,CAACE,OAAO,CAAC2I,IAAI,CAAC;QAClB1H,KAAK,EAAEnE,MAAM,CAACmE,KAAK;QACnB2H,QAAQ,EAAE,IAAI;QACdxI,KAAK,EAAEA;OACR,CAAC;IACJ;IAEA,IAAI,CAAC4I,YAAY,CAAClJ,MAAM,CAAC;EAC3B;EAEA;EACQmJ,cAAcA,CAACnJ,MAAiC;IAKtD,MAAME,OAAO,GAIR,EAAE;IAEP,IAAI,CAACF,MAAM,IAAI,CAACA,MAAM,CAACE,OAAO,EAAE;MAC9B,OAAOA,OAAO;IAChB;IAEAF,MAAM,CAACE,OAAO,CAACwJ,OAAO,CAAEJ,CAAM,IAAI;MAChC,IAAIA,CAAC,IAAI,OAAO,IAAIA,CAAC,EAAE;QACrB;QACApJ,OAAO,CAAC2I,IAAI,CAAC;UACX1H,KAAK,EAAEmI,CAAC,CAACnI,KAAK;UACd2H,QAAQ,EAAEQ,CAAC,CAACR,QAAQ;UACpBxI,KAAK,EAAEgJ,CAAC,CAAChJ;SACV,CAAC;MACJ,CAAC,MAAM,IAAIgJ,CAAC,IAAI,SAAS,IAAIA,CAAC,EAAE;QAC9B;QACApJ,OAAO,CAAC2I,IAAI,CAAC,GAAG,IAAI,CAACM,cAAc,CAACG,CAAC,CAAC,CAAC;MACzC;IACF,CAAC,CAAC;IAEF,OAAOpJ,OAAO;EAChB;EAEA;EACQ4D,aAAaA,CAAA;IACnB,IAAI;MACF,MAAM6F,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAAC9H,cAAc,CAAC;MAE5D,IAAI,CAAC4H,UAAU,EAAE;QACf;MACF;MAEA,MAAMzE,KAAK,GAwBP4E,IAAI,CAACC,KAAK,CAACJ,UAAU,CAAC;MAE1B;MACA,IAAIzE,KAAK,IAAIA,KAAK,CAACvD,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAGuD,KAAK,CAACvD,IAAI;QACtB,IAAI,IAAI,CAACA,IAAI,IAAI,IAAI,CAACA,IAAI,CAACsD,MAAM,GAAG,CAAC,IAAI,IAAI,CAACtD,IAAI,CAAC,CAAC,CAAC,EAAE;UACrD,IAAI,CAACnJ,IAAI,CAAC2J,OAAO,GAAG,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAACR,KAAK,IAAI,iBAAiB;UAC3D,IAAI,CAAC3I,IAAI,CAAC4J,QAAQ,GAAG,IAAI,CAACT,IAAI,CAAC,CAAC,CAAC,CAACC,GAAG,IAAI,MAAM;QACjD;MACF;MAEA;MACA,IAAIsD,KAAK,IAAIA,KAAK,CAAClF,MAAM,EAAE;QACzB,IAAI,CAACA,MAAM,GAAGkF,KAAK,CAAClF,MAAM;QAC1B,IAAI,CAACG,UAAU,GAAG+E,KAAK,CAAClF,MAAM;QAC9B,IAAI,CAACI,aAAa,GAAG8E,KAAK,CAAC9E,aAAa,IAAI,EAAE;MAChD;MAEA;MACA,IAAI8E,KAAK,IAAIA,KAAK,CAAC1M,IAAI,EAAE;QACvB,IAAI,CAACA,IAAI,GAAG0M,KAAK,CAAC1M,IAAI;MACxB;MAEA,IAAI0M,KAAK,IAAIA,KAAK,CAAC7C,IAAI,KAAKsG,SAAS,EAAE;QACrC,IAAI,CAACtG,IAAI,GAAG6C,KAAK,CAAC7C,IAAI;MACxB;MAEA;MACA,IAAI6C,KAAK,IAAIA,KAAK,CAACxD,iBAAiB,EAAE;QACpC,IAAI,CAACA,iBAAiB,GAAGwD,KAAK,CAACxD,iBAAiB;MAClD;MAEA;MACA,IAAIwD,KAAK,IAAIA,KAAK,CAAChO,UAAU,EAAE;QAC7B,IAAI,CAACA,UAAU,GAAGgO,KAAK,CAAChO,UAAU;MACpC;MAEA;MACA,IAAIgO,KAAK,IAAIA,KAAK,CAACpM,cAAc,EAAE;QACjC,IAAI,CAACA,cAAc,GAAGoM,KAAK,CAACpM,cAAc;MAC5C;MAEA,IAAIoM,KAAK,IAAIA,KAAK,CAACxL,mBAAmB,KAAKiP,SAAS,EAAE;QACpD,IAAI,CAACjP,mBAAmB,GAAGwL,KAAK,CAACxL,mBAAmB;MACtD;IACF,CAAC,CAAC,OAAOkM,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD;IACF;EACF;EAEA;;;EAGA9N,UAAUA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAAC+H,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACyF,MAAM,EAAE;MAC7C/B,OAAO,CAACqC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC/F,SAAS,CAAC;MACzD,IAAI,CAACR,wBAAwB,CAAC0I,SAAS,CAAC,4DAA4D,EAAE,EAAE,CAAC;MACzG;IACF;IAEA;IACA,IAAI,CAACvP,IAAI,CAACyJ,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACV,IAAI,GAAG,CAAC;MAAER,KAAK,EAAE,iBAAiB;MAAES,GAAG,EAAE;IAAM,CAAE,CAAC;IACvD,IAAI,CAAC5B,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAAChJ,UAAU,GAAG,EAAE;IACpB,IAAI,CAAC4B,cAAc,GAAG,EAAE;IACxB,IAAI,CAACY,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,IAAI,CAAC+F,IAAI,IAAI,IAAI,CAACA,IAAI,CAACuK,OAAO,EAAE;MAClC,IAAI,CAACvK,IAAI,CAACuK,OAAO,CAACN,OAAO,CAAE1M,MAAW,IAAI;QACxC,MAAMiN,KAAK,GAAG,IAAI,CAACpJ,WAAW,CAACqJ,OAAO,CAAClN,MAAM,CAACmE,KAAK,CAAC;QACpD,IAAI8I,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBjN,MAAM,CAACmN,UAAU,GAAGF,KAAK;QAC3B;QACA;QACA,IAAIjN,MAAM,CAACmE,KAAK,IAAInE,MAAM,CAACmE,KAAK,KAAK,QAAQ,EAAE;UAC7CnE,MAAM,CAACoN,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAAC3J,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACE,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,IAAI,CAACnB,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACT,IAAI,CAACkC,IAAI,GAAG,CAAC;QAAER,KAAK,EAAE,iBAAiB;QAAES,GAAG,EAAE;MAAM,CAAE,CAAC;MAE5D;MACA,IAAI,CAACnC,IAAI,CAAC4C,IAAI,GAAG,CAAC;MAClB,IAAI,CAAC5C,IAAI,CAAC8G,QAAQ,GAAG,IAAI,CAAC/N,IAAI,CAACwJ,IAAI;IACrC;IAEA;IACA,MAAM8D,QAAQ,GAAG;MACfuE,QAAQ,EAAE,gBAAgB;MAC1BC,MAAM,EAAE,IAAI,CAACzK,SAAS,CAACyF,MAAM;MAC7B7E,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjB6J,QAAQ,EAAE,IAAI,CAAC1K,SAAS,CAACyF;KAC1B;IAED;IACA,IAAI,CAAChG,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACvF,kBAAkB,CAACgL,gBAAgB,CAAC1E,QAAQ,CAAC,CAACpC,SAAS,CAAC;MAC3DqB,IAAI,EAAG0F,GAAG,IAAI;QACZ,IAAI,CAACnL,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC0F,GAAG,CAAChF,OAAO,EAAE;UAChB;UACA,IAAI,CAACjG,kBAAkB,CAACkL,qBAAqB,CAAC,gBAAgB,CAAC;UAC/D,IAAI,CAACrL,wBAAwB,CAACyI,WAAW,CAAC2C,GAAG,CAAC5C,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;QACrG,CAAC,MAAM;UACL,IAAI,CAACxI,wBAAwB,CAAC0I,SAAS,CAAC0C,GAAG,CAAC5C,OAAO,IAAI,kCAAkC,EAAE,EAAE,CAAC;QAChG;QAEA;QACA,IAAI,CAAC7I,GAAG,CAACkH,aAAa,EAAE;QAExB;QACAhC,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACzE,IAAI,EAAE;YACb,IAAI,CAACA,IAAI,CAACkL,OAAO,EAAE;UACrB;QACF,CAAC,EAAE,GAAG,CAAC;QAEP,IAAI,CAACjM,SAAS,EAAE;MAClB,CAAC;MACDkH,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtG,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CxB,OAAO,CAACqC,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAExD;QACA,IAAIA,KAAK,CAAC7M,MAAM,KAAK,GAAG,IAAK6M,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC7M,MAAM,KAAK,GAAI,EAAE;UACvE,IAAI,CAACsG,wBAAwB,CAAC0I,SAAS,CAAC,4CAA4C,EAAE,EAAE,CAAC;UACzF;QACF,CAAC,MAAM;UACL,IAAI,CAAC1I,wBAAwB,CAAC0I,SAAS,CAAC,oDAAoD,EAAE,EAAE,CAAC;QACnG;MACF;KACD,CAAC;EACJ;EAEA;EACQlE,aAAaA,CAAA;IACnB,MAAMqB,KAAK,GAwBP;MACFvD,IAAI,EAAE,IAAI,CAACA,IAAI;MACf3B,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBxH,IAAI,EAAE,IAAI,CAACA,IAAI;MACf6J,IAAI,EAAE,IAAI,CAACA,IAAI;MACfX,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCxK,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BkJ,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCtH,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCY,mBAAmB,EAAE,IAAI,CAACA;KAC3B;IAEDkQ,YAAY,CAACgB,OAAO,CAAC,IAAI,CAAC7I,cAAc,EAAE+H,IAAI,CAACe,SAAS,CAAC3F,KAAK,CAAC,CAAC;EAClE;EAEA;EACAtN,GAAGA,CAAA;IACD,IAAI,CAACsC,IAAI,CAAC,CAAC,CAAC;EACd;EAEA;EACAA,IAAIA,CAAC4Q,EAAU;IACbvH,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE,sBAAsB,EAAEsH,EAAE,CAAC;IACpD;IACA,MAAMC,eAAe,GAKjB;MACF/I,IAAI,EAAE,IAAI;MAAE;MACZgJ,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAACiM,IAAI,CACrC/U,2BAA2B,EAC3B0U,eAAe,CAChB;IACD;IACAI,QAAQ,CAACE,iBAAiB,CAACP,EAAE,GAAGA,EAAE;IAClC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACF;EAEA;EAEA;EACAxG,cAAcA,CAAA;IACZ;IACAf,OAAO,CAACC,GAAG,CAAC,uBAAuB,CAAC;EACtC;EAEA;EACAe,sBAAsBA,CAAA;IACpB;IACAhB,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;EAC/C;EAEA;EACA8H,uBAAuBA,CAAA;IACrB,IAAI,CAACvL,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvC2D,IAAI,CAACvN,YAAY,CAAC,GAAG,CAAC,EAAEC,oBAAoB,EAAE,CAAC,CAC/CuN,SAAS,CAAC,MAAK;MACd,IAAI,CAAClL,IAAI,CAACyJ,UAAU,GAAG,CAAC;MACxB,IAAI,CAACI,IAAI,GAAG,CAAC;MACb,IAAI,CAAC3D,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EACO1G,YAAYA,CAAA;IACjB;IACA,MAAMuT,aAAa,GAAGC,QAAQ,CAACC,aAAa,CAC1C,iBAAiB,CACH;IAChB,IAAIF,aAAa,EAAE;MACjBA,aAAa,CAACG,SAAS,CAACC,MAAM,CAAC,iBAAiB,CAAC;MACjD,IAAI,CAAChT,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU;MAClC;MACA,IAAI,IAAI,CAAC8G,IAAI,EAAE;QACb,IAAI,CAACA,IAAI,CAACkL,OAAO,EAAE;MACrB;IACF;EACF;EAEA;EACOiB,aAAaA,CAAChI,KAAkC;IACrD,QAAQA,KAAK,CAACiI,IAAI,CAACvL,KAAK;MACtB,KAAK,KAAK;QACR,IAAI,CAACwL,cAAc,EAAE;QACrB;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF,KAAK,UAAU;QACb,IAAI,CAACC,mBAAmB,EAAE;QAC1B;MACF;QACEzI,OAAO,CAAC0I,IAAI,CAAC,wBAAwB,EAAErI,KAAK,CAACiI,IAAI,CAACvL,KAAK,CAAC;IAC5D;EACF;EAEQwL,cAAcA,CAAA;IACpB,MAAMI,YAAY,GAAG;MACnBhM,OAAO,EAAE,EAAE;MACXiM,MAAM,EAAE;KACT;IAED,IAAI,CAACpN,YAAY,CAACqN,WAAW,CAACF,YAAY,CAAC,CAACxI,SAAS,CAAC;MACpDqB,IAAI,EAAG6C,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACyE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAC1E,QAAQ,CAACyE,UAAU,EAAE,WAAW,CAAC;QACtD;MACF,CAAC;MACDzG,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C;MACF;KACD,CAAC;EACJ;EAEQmG,mBAAmBA,CAAA;IACzB,IAAI,IAAI,CAACxJ,aAAa,CAAC0C,MAAM,KAAK,CAAC,EAAE;MACnC;MACA;IACF;IAEA,MAAMiH,YAAY,GAAG;MACnBhM,OAAO,EAAE;QACPgI,OAAO,EAAE,IAAI,CAAC3F,aAAa,CAAC6B,GAAG,CAAEmD,IAAI,IAAKA,IAAI,CAACgF,MAAM;OACtD;MACDJ,MAAM,EAAE;KACT;IAED,IAAI,CAACpN,YAAY,CAACqN,WAAW,CAACF,YAAY,CAAC,CAACxI,SAAS,CAAC;MACpDqB,IAAI,EAAG6C,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACyE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAC1E,QAAQ,CAACyE,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACDzG,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;MACF;KACD,CAAC;EACJ;EAEQoG,mBAAmBA,CAAA;IACzB,MAAME,YAAY,GAAG;MACnBhM,OAAO,EAAE;QACPnH,MAAM,EAAE,IAAI,CAACD,cAAc,CAACC,MAAM;QAClCE,IAAI,EAAE,IAAI,CAACH,cAAc,CAACG,IAAI;QAC9BuT,UAAU,EAAE,IAAI,CAACtV;OAClB;MACDiV,MAAM,EAAE;KACT;IAED,IAAI,CAACpN,YAAY,CAACqN,WAAW,CAACF,YAAY,CAAC,CAACxI,SAAS,CAAC;MACpDqB,IAAI,EAAG6C,QAAa,IAAI;QACtB,IAAIA,QAAQ,IAAIA,QAAQ,CAACyE,UAAU,EAAE;UACnC,IAAI,CAACC,aAAa,CAAC1E,QAAQ,CAACyE,UAAU,EAAE,gBAAgB,CAAC;QAC3D;MACF,CAAC;MACDzG,KAAK,EAAGA,KAAc,IAAI;QACxBrC,OAAO,CAACqC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;QACvD;MACF;KACD,CAAC;EACJ;EAEQ0G,aAAaA,CAAC9G,IAAW,EAAEiH,QAAgB;IACjD;IACA;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,YAAY,CAACnH,IAAI,CAAC;IAC1C,MAAMoH,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACH,UAAU,CAAC,EAAE;MAAEnL,IAAI,EAAE;IAAyB,CAAE,CAAC;IACxE,MAAMuL,IAAI,GAAGtB,QAAQ,CAACuB,aAAa,CAAC,GAAG,CAAC;IACxC,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACN,IAAI,CAAC;IACrCE,IAAI,CAACK,YAAY,CAAC,MAAM,EAAEH,GAAG,CAAC;IAC9BF,IAAI,CAACK,YAAY,CACf,UAAU,EACV,GAAGV,QAAQ,IAAI,IAAIW,IAAI,EAAE,CAACC,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM,CAC5D;IACDR,IAAI,CAACS,KAAK,CAACC,UAAU,GAAG,QAAQ;IAChChC,QAAQ,CAACiC,IAAI,CAACC,WAAW,CAACZ,IAAI,CAAC;IAC/BA,IAAI,CAACa,KAAK,EAAE;IACZnC,QAAQ,CAACiC,IAAI,CAACG,WAAW,CAACd,IAAI,CAAC;EACjC;EAEQH,YAAYA,CAACnH,IAAW;IAC9B,IAAIA,IAAI,CAACP,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAEhC,MAAM4I,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACvI,IAAI,CAAC,CAAC,CAAC,CAAC;IACpC,MAAMwI,OAAO,GAAG,CAACH,OAAO,CAACI,IAAI,CAAC,GAAG,CAAC,CAAC;IAEnC,KAAK,MAAMC,GAAG,IAAI1I,IAAI,EAAE;MACtB,MAAM2I,MAAM,GAAGN,OAAO,CAACzJ,GAAG,CAAEgK,MAAM,IAAI;QACpC,MAAM9N,KAAK,GAAG4N,GAAG,CAACE,MAAM,CAAC;QACzB,OAAO,OAAO9N,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAAC3F,QAAQ,CAAC,GAAG,CAAC,GACnD,IAAI2F,KAAK,GAAG,GACZA,KAAK;MACX,CAAC,CAAC;MACF0N,OAAO,CAACnF,IAAI,CAACsF,MAAM,CAACF,IAAI,CAAC,GAAG,CAAC,CAAC;IAChC;IAEA,OAAOD,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC;EAC3B;EAEA;EAEA;;;;;;EAMAI,QAAQA,CAAA;IACN;IACA,IAAI,CAAC,IAAI,CAACxO,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACyF,MAAM,EAAE;MAC7C/B,OAAO,CAACqC,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAAC/F,SAAS,CAAC;MACzD,IAAI,CAACR,wBAAwB,CAAC0I,SAAS,CACrC,8CAA8C,EAC9C,EAAE,CACH;MACD;IACF;IAEA,MAAMuG,gBAAgB,GAAU,EAAE;IAClC,MAAMC,aAAa,GAAU,EAAE;IAE/B,IAAI,IAAI,CAAC9O,IAAI,IAAI,IAAI,CAACA,IAAI,CAACuK,OAAO,EAAE;MAClC,IAAI,CAACvK,IAAI,CAACuK,OAAO,CAACN,OAAO,CAAE1M,MAAW,IAAI;QACxC,IAAI,CAACA,MAAM,CAACoN,MAAM,EAAE;UAClB,MAAMoE,UAAU,GAAG;YACjBpN,KAAK,EAAEpE,MAAM,CAACoE,KAAK;YACnBD,KAAK,EAAEnE,MAAM,CAACmE,KAAK;YACnBiJ,MAAM,EAAEpN,MAAM,CAACoN;WAChB;UACDkE,gBAAgB,CAACzF,IAAI,CAAC2F,UAAU,CAAC;QACnC,CAAC,MAAM;UACL,MAAMA,UAAU,GAAG;YACjBpN,KAAK,EAAEpE,MAAM,CAACoE,KAAK;YACnBD,KAAK,EAAEnE,MAAM,CAACmE,KAAK;YACnBiJ,MAAM,EAAEpN,MAAM,CAACoN;WAChB;UACDmE,aAAa,CAAC1F,IAAI,CAAC2F,UAAU,CAAC;QAChC;MACF,CAAC,CAAC;IACJ;IAEA,MAAMC,qBAAqB,GAAG,IAAI,CAAC5N,WAAW,CAC3Cb,MAAM,CAAEqE,GAAG,IAAK,CAAC,IAAI,CAAC3J,YAAY,CAACC,QAAQ,CAAC0J,GAAG,CAAC,CAAC,CACjDD,GAAG,CAAC,CAACjD,KAAK,EAAE8I,KAAK,MAAM;MACtB9I,KAAK;MACLgJ,UAAU,EAAEF;KACb,CAAC,CAAC;IAEL;IACA,MAAMnE,QAAQ,GAAG;MACfuE,QAAQ,EAAE,OAAO;MACjBC,MAAM,EAAE,IAAI,CAACzK,SAAS,CAACyF,MAAM;MAC7B7E,UAAU,EAAE8N,aAAa;MACzB7N,aAAa,EAAE+N,qBAAqB;MACpClE,QAAQ,EAAE,IAAI,CAAC1K,SAAS,CAACyF;KAC1B;IAED;IACA,IAAI,CAAChG,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACvF,kBAAkB,CAACgL,gBAAgB,CAAC1E,QAAQ,CAAC,CAACpC,SAAS,CAAC;MAC3DqB,IAAI,EAAG0F,GAAG,IAAI;QACZ,IAAI,CAACnL,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC0F,GAAG,CAAChF,OAAO,EAAE;UAChB;UACA,IAAI,CAAChF,UAAU,GAAG8N,aAAa;UAC/B,IAAI,CAAC7N,aAAa,GAAG+N,qBAAqB;UAC1C,IAAI,CAAC7N,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC2D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAAClD,KAAK,CAAC;UAEhE;UACA,IAAI,CAAC3B,kBAAkB,CAACkP,kBAAkB,CAAC5I,QAAQ,CAAC;UAEpD,IAAI,CAACzG,wBAAwB,CAACyI,WAAW,CACvC2C,GAAG,CAAC5C,OAAO,IAAI,qCAAqC,EACpD,EAAE,CACH;QACH,CAAC,MAAM;UACL,IAAI,CAACxI,wBAAwB,CAAC0I,SAAS,CACrC0C,GAAG,CAAC5C,OAAO,IAAI,iCAAiC,EAChD,EAAE,CACH;QACH;QACA,IAAI,CAAC7I,GAAG,CAAC2P,YAAY,EAAE;MACzB,CAAC;MACD/I,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACtG,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CxB,OAAO,CAACqC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QAErD;QACA,IAAI,CAACpG,kBAAkB,CAACkP,kBAAkB,CAAC5I,QAAQ,CAAC;QAEpD;QACA,IAAI,CAACrF,UAAU,GAAG8N,aAAa;QAC/B,IAAI,CAAC7N,aAAa,GAAG+N,qBAAqB;QAC1C,IAAI,CAAC7N,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC2D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAAClD,KAAK,CAAC;QAEhE,IAAI,CAAC9B,wBAAwB,CAAC0I,SAAS,CACrC,mDAAmD,EACnD,EAAE,CACH;QACD,IAAI,CAAC/I,GAAG,CAAC2P,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAGA;;;;;EAKAC,oBAAoBA,CAAClO,aAAkB;IACrC,IAAI;MACF,MAAMmO,UAAU,GAAGnO,aAAa;MAChC,IAAImO,UAAU,EAAE;QACd,MAAMC,WAAW,GAAGD,UAAU;QAC9B,IAAIE,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,IAAIA,WAAW,CAAC7J,MAAM,GAAG,CAAC,EAAE;UACxD;UACA,MAAMgK,qBAAqB,GAAGH,WAAW,CACtCnN,IAAI,CAAC,CAACuN,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAC/E,UAAU,GAAGgF,CAAC,CAAChF,UAAU,CAAC,CAC3C/F,GAAG,CAAEC,GAAG,IAAKA,GAAG,CAAClD,KAAK,CAAC,CACvBnB,MAAM,CAAEmB,KAAK,IAAK,CAAC,IAAI,CAACzG,YAAY,CAACC,QAAQ,CAACwG,KAAK,CAAC,CAAC;UAExD;UACA,MAAMiO,cAAc,GAAG,IAAI,CAACrO,gBAAgB,CAACf,MAAM,CAChDqE,GAAG,IAAK,CAAC4K,qBAAqB,CAACtU,QAAQ,CAAC0J,GAAG,CAAC,CAC9C;UAED;UACA,IAAI,CAACxD,WAAW,GAAG,CACjB,GAAG,IAAI,CAACnG,YAAY,EACpB,GAAGuU,qBAAqB,EACxB,GAAGG,cAAc,CAClB;QACH,CAAC,MAAM;UACL,IAAI,CAACvO,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;QAC7C;MACF,CAAC,MAAM;QACL,IAAI,CAACD,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO8E,KAAK,EAAE;MACd,IAAI,CAAC/E,WAAW,GAAG,CAAC,GAAG,IAAI,CAACC,cAAc,CAAC;IAC7C;EACF;EAEA;;;;EAIAjG,cAAcA,CAACwU,UAAe;IAC5B,OAAO,IAAI,CAACzO,YAAY,CAACsJ,OAAO,CAACmF,UAAU,CAAC,GAAG,CAAC,CAAC;EACnD;EAEA;;;;;EAKAC,eAAeA,CAAC1L,KAAU;IACxB,MAAM;MAAEoG,OAAO;MAAEuF,QAAQ;MAAEC;IAAQ,CAAE,GAAG5L,KAAK;IAE7C;IACA,IACE,IAAI,CAAClJ,YAAY,CAACC,QAAQ,CAACqP,OAAO,CAACwF,QAAQ,CAAC,CAACrO,KAAK,CAAC,IACnD,IAAI,CAACzG,YAAY,CAACC,QAAQ,CAACqP,OAAO,CAACuF,QAAQ,CAAC,CAACpO,KAAK,CAAC,EACnD;MACA;IACF;IAEA;IACA,MAAMsO,gBAAgB,GAAG,CAAC,GAAG,IAAI,CAAC5O,WAAW,CAAC;IAC9C,MAAM,CAAC6O,WAAW,CAAC,GAAGD,gBAAgB,CAAChG,MAAM,CAAC+F,QAAQ,EAAE,CAAC,CAAC;IAC1DC,gBAAgB,CAAChG,MAAM,CAAC8F,QAAQ,EAAE,CAAC,EAAEG,WAAW,CAAC;IAEjD,IAAI,CAAC7O,WAAW,GAAG4O,gBAAgB;IACnC,IAAI,CAACzQ,GAAG,CAAC2P,YAAY,EAAE;EACzB;EAEA;;;;EAIAgB,sBAAsBA,CAAC/L,KAAU;IAC/B,IAAI,IAAI,CAACjL,UAAU,KAAK,KAAK,EAAE;MAC7B,IAAI,IAAI,CAAC8G,IAAI,IAAI,IAAI,CAACA,IAAI,CAACuK,OAAO,EAAE;QAClC,IAAI,CAACvK,IAAI,CAACuK,OAAO,CAACN,OAAO,CAAE1M,MAAW,IAAI;UACxC,MAAMwR,UAAU,GAAG;YACjBpN,KAAK,EAAEpE,MAAM,CAACoE,KAAK;YACnBD,KAAK,EAAEnE,MAAM,CAACmE,KAAK;YACnBiJ,MAAM,EAAEpN,MAAM,CAACoN;WAChB;UACD,IAAIpN,MAAM,CAACoN,MAAM,EAAE;YACjB,MAAMb,MAAM,GAAG,IAAI,CAAC9I,UAAU,CAACmP,IAAI,CAChC/D,IAAS,IACRA,IAAI,CAAC1K,KAAK,KAAKqN,UAAU,CAACrN,KAAK,IAAI0K,IAAI,CAACzB,MAAM,KAAK,IAAI,CAC1D;YACD,IAAI,CAACb,MAAM,EAAE;cACX,IAAI,CAAC9I,UAAU,CAACoI,IAAI,CAAC2F,UAAU,CAAC;YAClC;UACF,CAAC,MAAM;YACL,IAAIqB,WAAW,GAAG,IAAI,CAACpP,UAAU,CAAC+I,SAAS,CACxCqC,IAAS,IACRA,IAAI,CAAC1K,KAAK,KAAKqN,UAAU,CAACrN,KAAK,IAAI0K,IAAI,CAACzB,MAAM,KAAK,IAAI,CAC1D;YACD,IAAIyF,WAAW,KAAK,CAAC,CAAC,EAAE;cACtB,IAAI,CAACpP,UAAU,CAACgJ,MAAM,CAACoG,WAAW,EAAE,CAAC,CAAC;YACxC;UACF;QACF,CAAC,CAAC;QACF,IAAI,CAACjP,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC2D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAAClD,KAAK,CAAC;QAChE,IAAI,CAACnC,GAAG,CAAC2P,YAAY,EAAE;MACzB;IACF;EACF;EAEA;;;;EAIQxK,4BAA4BA,CAAA;IAClC,IAAI;MACF;MACA,IAAI,IAAI,CAACtE,SAAS,IAAI,IAAI,CAACA,SAAS,CAACyF,MAAM,EAAE;QAC3C,IAAI,CAAC9F,kBAAkB,CACpBsQ,aAAa,CAAC;UACbzF,QAAQ,EAAE,OAAO;UACjBC,MAAM,EAAE,IAAI,CAACzK,SAAS,CAACyF;SACxB,CAAC,CACD5B,SAAS,CAAC;UACTqB,IAAI,EAAG0F,GAAG,IAAI;YACZ,IAAI,CAACA,GAAG,CAAChF,OAAO,IAAIgF,GAAG,CAACsF,IAAI,EAAE;cAC5B,IAAI,CAACvP,SAAS,GAAGiK,GAAG,CAACsF,IAAI;cACzB,IAAI,CAACtP,UAAU,GAAGgK,GAAG,CAACsF,IAAI,CAACC,QAAQ,GAC/BlG,IAAI,CAACC,KAAK,CAACU,GAAG,CAACsF,IAAI,CAACC,QAAQ,CAAC,GAC7B,EAAE;cACN,IAAI,CAACrP,iBAAiB,GAAG8J,GAAG,CAACsF,IAAI,CAACrP,aAAa,GAC3CoJ,IAAI,CAACC,KAAK,CAACU,GAAG,CAACsF,IAAI,CAACrP,aAAa,CAAC,GAClC,EAAE;cACN,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC2D,GAAG,CACpCC,GAAQ,IAAKA,GAAG,CAAClD,KAAK,CACxB;cAED;cACA,IAAI,IAAI,CAAC1B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACuK,OAAO,EAAE;gBAClC,IAAI,CAACvK,IAAI,CAACuK,OAAO,CAACN,OAAO,CAAE1M,MAAW,IAAI;kBACxC,IACE,IAAI,CAACyD,UAAU,CAACmP,IAAI,CACjB/D,IAAS,IACRA,IAAI,CAACzK,KAAK,KAAKpE,MAAM,CAACoE,KAAK,IAAIyK,IAAI,CAACzB,MAAM,CAC7C,EACD;oBACApN,MAAM,CAACiT,gBAAgB,GAAG,IAAI;oBAC9BjT,MAAM,CAACoN,MAAM,GAAG,IAAI;kBACtB,CAAC,MAAM;oBACLpN,MAAM,CAACoN,MAAM,GAAG,KAAK;kBACvB;gBACF,CAAC,CAAC;cACJ;cAEA;cACA,IAAI,CAACwE,oBAAoB,CAAC,IAAI,CAACjO,iBAAiB,CAAC;cAEjD;cACA,IAAI,CAACnB,kBAAkB,CAACkP,kBAAkB,CAAC;gBACzCrE,QAAQ,EAAE,OAAO;gBACjBC,MAAM,EAAE,IAAI,CAACzK,SAAS,CAACyF,MAAM;gBAC7B7E,UAAU,EAAE,IAAI,CAACA,UAAU;gBAC3BC,aAAa,EAAE,IAAI,CAACC;eACrB,CAAC;YACJ;UACF,CAAC;UACDiF,KAAK,EAAGA,KAAK,IAAI;YACfrC,OAAO,CAACqC,KAAK,CACX,2DAA2D,EAC3DA,KAAK,CACN;YACD,IAAI,CAACsK,4BAA4B,EAAE;UACrC;SACD,CAAC;MACN,CAAC,MAAM;QACL;QACA,IAAI,CAACA,4BAA4B,EAAE;MACrC;IACF,CAAC,CAAC,OAAOtK,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3D,IAAI,CAACsK,4BAA4B,EAAE;IACrC;EACF;EAEA;;;EAGQA,4BAA4BA,CAAA;IAClC,IAAI;MACF,MAAMC,WAAW,GAAG,IAAI,CAAC3Q,kBAAkB,CAAC4Q,mBAAmB,CAC7D,OAAO,EACP,IAAI,CAACvQ,SAAS,EAAE0M,MAAM,IAAI,CAAC,CAC5B;MACD,IAAI4D,WAAW,EAAE;QACf,IAAI,CAAC3P,SAAS,GAAG2P,WAAW;QAC5B,IAAI,CAAC1P,UAAU,GAAG0P,WAAW,CAAC1P,UAAU,IAAI,EAAE;QAC9C,IAAI,CAACE,iBAAiB,GAAGwP,WAAW,CAACzP,aAAa,IAAI,EAAE;QACxD,IAAI,CAACE,YAAY,GAAG,IAAI,CAACH,UAAU,CAAC2D,GAAG,CAAEC,GAAQ,IAAKA,GAAG,CAAClD,KAAK,CAAC;QAEhE;QACA,IAAI,IAAI,CAAC1B,IAAI,IAAI,IAAI,CAACA,IAAI,CAACuK,OAAO,EAAE;UAClC,IAAI,CAACvK,IAAI,CAACuK,OAAO,CAACN,OAAO,CAAE1M,MAAW,IAAI;YACxC,IACE,IAAI,CAACyD,UAAU,CAACmP,IAAI,CACjB/D,IAAS,IAAKA,IAAI,CAACzK,KAAK,KAAKpE,MAAM,CAACoE,KAAK,IAAIyK,IAAI,CAACzB,MAAM,CAC1D,EACD;cACApN,MAAM,CAACiT,gBAAgB,GAAG,IAAI;cAC9BjT,MAAM,CAACoN,MAAM,GAAG,IAAI;YACtB,CAAC,MAAM;cACLpN,MAAM,CAACoN,MAAM,GAAG,KAAK;YACvB;UACF,CAAC,CAAC;QACJ;QAEA;QACA,IAAI,CAACwE,oBAAoB,CAAC,IAAI,CAACjO,iBAAiB,CAAC;MACnD;IACF,CAAC,CAAC,OAAOiF,KAAK,EAAE;MACdrC,OAAO,CAACqC,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;IACnE;EACF;;qCA7mDW9G,2BAA2B,EAAAxI,EAAA,CAAA+Z,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAja,EAAA,CAAA+Z,iBAAA,CAAA/Z,EAAA,CAAAka,iBAAA,GAAAla,EAAA,CAAA+Z,iBAAA,CAAAI,EAAA,CAAAC,MAAA,GAAApa,EAAA,CAAA+Z,iBAAA,CAAAI,EAAA,CAAAE,cAAA,GAAAra,EAAA,CAAA+Z,iBAAA,CAAAO,EAAA,CAAAC,QAAA,GAAAva,EAAA,CAAA+Z,iBAAA,CAAAS,EAAA,CAAA1R,UAAA,GAAA9I,EAAA,CAAA+Z,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAA1a,EAAA,CAAA+Z,iBAAA,CAAAY,EAAA,CAAAC,gBAAA,GAAA5a,EAAA,CAAA+Z,iBAAA,CAAAc,EAAA,CAAAC,oBAAA,GAAA9a,EAAA,CAAA+Z,iBAAA,CAAAgB,EAAA,CAAAC,kBAAA;EAAA;;UAA3BxS,2BAA2B;IAAAyS,SAAA;IAAAC,SAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QC3FxCpb,EAAA,CAAAkD,UAAA,IAAAoY,0CAAA,iBAAqE;QAe3Dtb,EALV,CAAAC,cAAA,aAA+B,aACI,aACgC,YAC0C,YAChF,WACgD;QACjED,EAAA,CAAAE,MAAA,wBACF;QAEJF,EAFI,CAAAG,YAAA,EAAI,EACD,EACF;QAIHH,EADF,CAAAC,cAAA,aAAuC,gBAKG;QADtCD,EAAA,CAAAc,UAAA,mBAAAya,6DAAA;UAAAvb,EAAA,CAAAO,aAAA,CAAAib,GAAA;UAAA,OAAAxb,EAAA,CAAAa,WAAA,CAASwa,GAAA,CAAAxO,MAAA,EAAQ;QAAA,EAAC;QAElB7M,EAAA,CAAAoB,SAAA,aAAqC;QACrCpB,EAAA,CAAAE,MAAA,cACF;QAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;QAGNH,EADA,CAAAC,cAAA,eAA4B,yBA6B3B;QADCD,EAZA,CAAAc,UAAA,2BAAA2a,0EAAAnb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAib,GAAA;UAAA,OAAAxb,EAAA,CAAAa,WAAA,CAAiBwa,GAAA,CAAArC,eAAA,CAAA1Y,MAAA,CAAuB;QAAA,EAAC,6BAAAob,4EAAApb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAib,GAAA;UAAA,OAAAxb,EAAA,CAAAa,WAAA,CACtBwa,GAAA,CAAAzK,iBAAA,CAAAtQ,MAAA,CAAyB;QAAA,EAAC,0BAAAqb,yEAAArb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAib,GAAA;UAAA,OAAAxb,EAAA,CAAAa,WAAA,CAQ7Bwa,GAAA,CAAAzI,YAAA,CAAAtS,MAAA,CAAoB;QAAA,EAAC,wBAAAsb,uEAAAtb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAib,GAAA;UAAA,OAAAxb,EAAA,CAAAa,WAAA,CACvBwa,GAAA,CAAA5I,UAAA,CAAAnS,MAAA,CAAkB;QAAA,EAAC,wBAAAub,uEAAAvb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAib,GAAA;UAAA,OAAAxb,EAAA,CAAAa,WAAA,CACnBwa,GAAA,CAAA3I,YAAA,CAAApS,MAAA,CAAoB;QAAA,EAAC,oCAAAwb,mFAAAxb,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAib,GAAA;UAAA,OAAAxb,EAAA,CAAAa,WAAA,CACTwa,GAAA,CAAAhC,sBAAA,CAAA/Y,MAAA,CAA8B;QAAA,EAAC;QAoezDN,EAleA,CAAAkD,UAAA,KAAA6Y,mDAAA,4BAAsC,KAAAC,mDAAA,0BAuGA,KAAAC,oDAAA,2BAgDW,KAAAC,mDAAA,0BA2UT;QAa5Clc,EAFE,CAAAG,YAAA,EAAa,EACP,EACF;;;QAhjBAH,EAAA,CAAAgC,UAAA,SAAAqZ,GAAA,CAAA/S,OAAA,IAAA+S,GAAA,CAAA9S,SAAA,CAA0B;QAsC5BvI,EAAA,CAAA6B,SAAA,IAA0B;QAqB1B7B,EArBA,CAAAgC,UAAA,SAAAqZ,GAAA,CAAAjS,iBAAA,CAA0B,aAAAiS,GAAA,CAAAnZ,IAAA,CAAAwJ,IAAA,CACJ,SAAA2P,GAAA,CAAAhQ,IAAA,CACT,aAAArL,EAAA,CAAAmc,eAAA,KAAAC,GAAA,EAAApc,EAAA,CAAAkE,eAAA,KAAAmY,GAAA,GAOX,aAAArc,EAAA,CAAAkE,eAAA,KAAAoY,GAAA,EACgD,oBAC/B,eAAAtc,EAAA,CAAAkE,eAAA,KAAAqY,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAAlB,GAAA,CAAAnZ,IAAA,CAAAyJ,UAAA,GAAA0P,GAAA,CAAAnZ,IAAA,CAAAwJ,IAAA,CACsB,WAAA2P,GAAA,CAAA3R,MAAA,CACnB,eAAA1J,EAAA,CAAAkE,eAAA,KAAAsY,GAAA,EACc;QA6JExc,EAAA,CAAA6B,SAAA,GAAc;QAAd7B,EAAA,CAAAgC,UAAA,YAAAqZ,GAAA,CAAA9Q,WAAA,CAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import { Injectable } from '@angular/core';
import * as _ from 'lodash';
import { AppSettings } from 'src/app/app.settings';

import { Router } from '@angular/router';
import { CurrencyPipe } from '@angular/common';
import { AppEncryptDecryptService } from './app-encrypt-decrypt';
import { FormGroup } from '@angular/forms';
// import { DatePipe } from '@angular/common';
// import validator from 'validator';
@Injectable({
  providedIn: 'root'
})
export class AppService {
  constructor(
    private appEncryptDecryptService: AppEncryptDecryptService,
    private router: Router,
    // private datePipe: DatePipe
  ) {
  }
  // get the local storage item with decrypted value
  public getLocalStorageItem(key: string, parsingNeeded: boolean) {
    const itemVal = localStorage.getItem(key);
    if (itemVal == null) {
      return null;
    }
    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);
    if (!parsingNeeded) {
      return decryptedValue;
    } else {
      return JSON.parse(decryptedValue);
    }
  }
  // set the local storage item with encrypted value
  public setLocalStorageItem(key: string, value: any, parsingNeeded: boolean) {
    if (!parsingNeeded) {
      return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));
    } else {
      return localStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));
    }
  }

  // get the session storage item with decrypted value
  public getSessionStorageItem(key: string, parsingNeeded: boolean) {
    const itemVal = sessionStorage.getItem(key);
    if (itemVal == null) {
      return null;
    }
    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.SECRET_KEY, itemVal);
    if (!parsingNeeded) {
      return decryptedValue;
    } else {
      return JSON.parse(decryptedValue);
    }
  }
  // set the local storage item with encrypted value
  public setSessionStorageItem(key: string, value: any, parsingNeeded: boolean) {
    if (!parsingNeeded) {
      return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, value));
    } else {
      return sessionStorage.setItem(key, this.appEncryptDecryptService.set(AppSettings.SECRET_KEY, JSON.stringify(value)));
    }
  }

  public fromJsonDate(jDate: any): string {
    const bDate: Date = new Date(jDate);
    return bDate.toISOString().substring(0, 10);  //Ignore time
  }

  logout() {
    localStorage.removeItem('permitUser');
    localStorage.removeItem('permitToken');
    localStorage.removeItem('permit_access');
    localStorage.removeItem('permit_exp');
    const remember = this.getLocalStorageItem('permitRemember', false);
    if (remember !== 'true') {
      localStorage.removeItem('permitUserAuth');
    }
    this.router.navigate(['/auth/login']);
  }

  removeToken() {
    localStorage.removeItem('permit_access');
    localStorage.removeItem('permit_exp');
  }
  dollarAmount(data: any) {
    const currencyPipe = new CurrencyPipe('en-US');
    // Value to be formatted
    const value = data;
    // Format the currency
    const formattedValue = currencyPipe.transform(value, 'USD', 'symbol', '1.2-2');
    return formattedValue;
  }




  // get the time in sentence format
  timeConvert(num: number) {
    const hours = Math.floor(num / 60);
    const minutes = num % 60;
    switch (hours) {
      case 0:
        return minutes + 'min ago';
      case 1:
        return hours + 'hr' + minutes + 'min ago';
      default:
        return hours + 'hr' + minutes + 'min ago';
    }
  };

  // get the decrypted password
  getPasswordData(data: any) {
    const decryptedValue = this.appEncryptDecryptService.get(AppSettings.API_PASSWORD_SECRET_KEY, data);
    return JSON.parse(decryptedValue);
  }

  // get the date from Unix time
  unixDate(unixtime: any) {
    if (unixtime) {
      const u = new Date(unixtime);
      const month = String(u.getMonth() + 1).padStart(2, '0');
      const day = String(u.getDate()).padStart(2, '0');
      const dates = `${month}/${day}/${u.getFullYear()}`;
      return dates;
    } else {
      const dates = '';
      return dates;
    }
  };

  // get the date time from unix time
  public unixTime(unixtime: any) {
    if (unixtime) {
      const u = new Date(unixtime);
      const amOrPm = (u.getHours() < 12) ? 'AM' : 'PM';
      const hour = (u.getHours() < 12) ? u.getHours() : u.getHours() - 12;
      const month = u.getMonth() + 1;
      const minutes: number = u.getMinutes();
      let min: string;
      if (minutes < 10) {
        min = '0' + minutes.toString();
      } else {
        min = minutes.toString();
      }
      // const dates = month + '/' + u.getDate() + '/' + u.getFullYear() + ' ' + hour + ':' + min + ' ' + amOrPm;
      const dates = hour + ':' + min + ' ' + amOrPm;
      return dates;
    } else {
      const dates = '';
      return dates;
    }
  }

  public dateDiffInDays(a: any, b: any) {
    const _MS_PER_DAY = 1000 * 60 * 60 * 24;
    // Discard the time and time-zone information.
    const utc1 = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());
    const utc2 = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());

    return Math.floor((utc2 - utc1) / _MS_PER_DAY);
  }


  // get the format for phone number with /without extension
  getPhoneFormat(phoneNum: string): string {
  // Remove all non-digit characters
  const digitsOnly = phoneNum.replace(/\D/g, '');

  const baseLength = 10;
  if (digitsOnly.length < baseLength) {
    return phoneNum.trim(); // Not enough digits to format
  }

  const areaCode = digitsOnly.substring(0, 3);
  const mid = digitsOnly.substring(3, 6);
  const lastFour = digitsOnly.substring(6, 10);
  const extension = digitsOnly.length > baseLength ? digitsOnly.substring(10) : '';

  let formatted = `(${areaCode}) ${mid}-${lastFour}`;
  if (extension) {
    formatted += ` Ext. ${extension}`;
  }

  return formatted.trim();
}

  // get the format for email in href
  mailto(emailAddress: string) {
    return "mailto:" + emailAddress
  }

  onImgError(event: any) {
    event.target.src = './assets/media/avatars/blank.png';
  }

  public ImageUrl(name: any) {
    return AppSettings.IMAGEPATH + name;
  }

  formatDate(date: any): string {
    if (date === '' || date === null || date === undefined) {
      return '';
    }
    const d = new Date(date);
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const year = d.getFullYear();
    return `${month}/${day}/${year}`;
  }


  getTodayDate() {
    const today = new Date();
    let date = today.getDate() > 9 ? today.getDate() :
      `0${today.getDate()}`;
    let month = today.getMonth() > 9 ? today.getMonth() + 1 :
      `0${today.getMonth() + 1}`;
    let year = today.getFullYear();
    let todayDate = year + "-" + month + "-" + date;
    return todayDate;
  }

  customSearchFn(term: string, item: any) {
    term = term.toLocaleLowerCase();
    return item.email.toLocaleLowerCase().indexOf(term) > -1 ||
    item.UserFullName.toLocaleLowerCase().indexOf(term) > -1 ||
    item.firstname.toLocaleLowerCase().indexOf(term) > -1 ;
    // (item.code + " - " + item.name).toLocaleLowerCase().indexOf(term) > -1;
  }

  formatCurrency(amount: any): string {
    // Convert the input to a number, handling null, undefined, or empty string
    const amountFormatted = parseFloat(amount as string) || 0;

    // Use Intl.NumberFormat to format as USD currency
    const formatter = new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    });

    return formatter.format(amountFormatted);
  }

  formatMonthDate(dateString: any): any {
    if (dateString) {
      const u = new Date(dateString);
      const month = String(u.getUTCMonth() + 1).padStart(2, '0'); // Ensure two digits for the month
    const day = String(u.getUTCDate()).padStart(2, '0'); // Ensure two digits for the day
    const year = u.getUTCFullYear();
      return `${month}/${day}/${year}`;
    } else {
      return '';
    }
  }

  formatCommissionPercent(compAgentPercent: any): string {
    if (!compAgentPercent || compAgentPercent === 0 || compAgentPercent === 0.00) {
      return '0%';
    }
    return `${compAgentPercent}%`;
  }



  decodeHtmlEntities(input: string): string {
    const entities: { [key: string]: string } = {
      '&amp;': '&',
      '&lt;': '<',
      '&gt;': '>',
      '&quot;': '"',
      '&#39;': "'",
    };

    return input.replace(/&amp;|&lt;|&gt;|&quot;|&#39;/g, (match) => entities[match] || match);
  }
  // formatMonthDate(dateString: string): string | null {
  //   return this.datePipe.transform(dateString, 'MM/dd/yyyy');
  // }

  getDateWithoutTimezone(date: Date | string): string {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Add leading zero if needed
    const day = String(d.getDate()).padStart(2, '0'); // Add leading zero if needed
    return `${year}-${month}-${day}`;
}
// Utility to check if the field is a date field
isDateField(columns:any,field: string): boolean {
  const dateColumnNames =columns; // List of date fields
  return dateColumnNames.includes(field);
}
convertDateFilters(filters: any, dateFields: string[]) {
  return filters.map((group: any) => {
    const updatedFilters = group.filters.map((f: any) => {
      if (dateFields.includes(f.field) && typeof f.value === 'string') {
        return {
          ...f,
          value: f.value ? new Date(f.value) : null
        };
      }
      return f;
    });

    return {
      ...group,
      filters: updatedFilters
    };
  });
}


/**
 * Converts file size from bytes to kilobytes (KB).
 *
 * - Takes the file size in bytes as input.
 * - Divides by 1024 to convert it to KB.
 * - Rounds the result to the nearest whole number.
 *
 * @param size - The file size in bytes.
 * @returns The file size in kilobytes (rounded).
 */
getFileSize(size: number) {
  return Math.round(size / 1024);
}

updateColumnConfig(hiddenData:any, columnReorderData:any, columnJSONFormat:any) {
    // Create a deep copy of the original array to avoid modifying the input directly
    const updatedColumns = columnJSONFormat;
    for (const column of updatedColumns) {
      column.hidden = false;
    }
    // Step 1: Update hidden property based on hiddenData
    for (const hiddenItem of hiddenData) {
      const fieldToUpdate = updatedColumns.find((item:any) => item.field === hiddenItem.field);
      if (fieldToUpdate) {
        fieldToUpdate.hidden = hiddenItem.hidden;
      }
    }
    // Step 2: Create a map of fields to their new order
    const orderMap :any= {};
    columnReorderData.forEach((item:any) => {
      orderMap[item.field] = item.orderIndex;
    });
    // Step 3: Update order based on columnReorderData
    for (let column of updatedColumns) {
      if (orderMap.hasOwnProperty(column.field)) {
        column.order = orderMap[column.field];
      }
    }
    // Step 4: Sort the array by order
    updatedColumns.sort((a:any, b:any) => a.order - b.order);
    return updatedColumns;
  }

   // function to check whether the form has any error
  controlHasError(validation: any, controlName: string | number, name: FormGroup): boolean {
    const control = name.controls[controlName];
    if (!control) {
      return false;
    }
    let result = control.hasError(validation) && (control.dirty || control.touched);
    return result;
  }

  getLoggedInUser(){
    let loggedInUser = this.getLocalStorageItem('permitUser', true);
    return loggedInUser;
  }

  // Derive initials from various possible user shapes
  getUserInitials(user: any): string {
    if (!user) { return '?'; }

    const tryString = (val: any) => (val || '').toString().trim();

    // Prefer explicit first/last
    const first = tryString(user.firstname || user.firstName || user.FirstName);
    const last = tryString(user.lastname || user.lastName || user.LastName);
    if (first || last) {
      const fi = first ? first.charAt(0).toUpperCase() : '';
      const li = last ? last.charAt(0).toUpperCase() : '';
      return (fi + li) || '?';
    }

    // Try full name fields
    const full = tryString(user.UserFullName || user.userFullName || user.fullName || user.FullName || user.name);
    if (full) {
      const parts = full.split(/\s+/).filter(Boolean);
      if (parts.length === 1) {
        return parts[0].charAt(0).toUpperCase();
      }
      return (parts[0].charAt(0) + parts[parts.length - 1].charAt(0)).toUpperCase();
    }

    // Fallback to email/username
    const email = tryString(user.email || user.Email);
    const username = tryString(user.userName || user.username || user.UserName);
    const source = email || username;
    if (source) {
      // take letters from before @ or start of username
      const base = email ? source.split('@')[0] : source;
      const letters = base.replace(/[^A-Za-z]/g, '');
      if (letters.length >= 2) {
        return (letters[0] + letters[1]).toUpperCase();
      }
      if (letters.length === 1) {
        return letters[0].toUpperCase();
      }
    }

    return '?';
  }
}

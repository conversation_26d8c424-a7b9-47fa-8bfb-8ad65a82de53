import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { AppSettings } from 'src/app/app.settings';

export interface KendoColumnConfig {
  pageName: string;
  userID: number;
  hiddenData?: any[];
  kendoColOrder?: any[];
  LoggedId?: number;
}

export interface KendoColumnResponse {
  message: string;
  Data: {
    page_id: number;
    user_id: number;
    pageName: string;
    hideData: string;
    kendoColOrder: string;
    createdBy: number;
    createdDate: string;
    lastUpdatedBy: number;
    lastUpdatedDate: string;
  };
  isFault: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class KendoColumnService {

  private baseUrl = AppSettings.REST_ENDPOINT;

  constructor(private http: HttpClient) { }

  /**
   * Creates or updates column visibility and order preferences
   * @param config - Column configuration data
   * @returns Observable with the response
   */
  createHideFields(config: KendoColumnConfig): Observable<KendoColumnResponse> {
    return this.http.post<KendoColumnResponse>(`${this.baseUrl}/createHideFields`, config);
  }

  /**
   * Retrieves column visibility and order preferences
   * @param config - Request configuration with pageName and userID
   * @returns Observable with the response
   */
  getHideFields(config: { pageName: string; userID: number }): Observable<KendoColumnResponse> {
    return this.http.post<KendoColumnResponse>(`${this.baseUrl}/getHideFields`, config);
  }

  /**
   * Deletes column visibility preferences
   * @param config - Request configuration with pageName and userID
   * @returns Observable with the response
   */
  deleteHideFields(config: { pageName: string; userID: number }): Observable<KendoColumnResponse> {
    return this.http.post<KendoColumnResponse>(`${this.baseUrl}/deleteHideFields`, config);
  }

  /**
   * Saves column configuration to localStorage as fallback
   * @param config - Column configuration data
   */
  saveToLocalStorage(config: KendoColumnConfig): void {
    const key = `${config.pageName.toLowerCase()}-column-config`;
    localStorage.setItem(key, JSON.stringify(config));
  }

  /**
   * Retrieves column configuration from localStorage as fallback
   * @param pageName - Name of the page
   * @param userID - User ID
   * @returns Column configuration or null if not found
   */
  getFromLocalStorage(pageName: string, userID: number): KendoColumnConfig | null {
    const key = `${pageName.toLowerCase()}-column-config`;
    const stored = localStorage.getItem(key);
    if (stored) {
      const config = JSON.parse(stored);
      if (config.userID === userID) {
        return config;
      }
    }
    return null;
  }

  /**
   * Clears column configuration from localStorage
   * @param pageName - Name of the page
   */
  clearFromLocalStorage(pageName: string): void {
    const key = `${pageName.toLowerCase()}-column-config`;
    localStorage.removeItem(key);
  }
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, mergeMap } from 'rxjs/operators';
import { AppSettings } from 'src/app/app.settings';

@Injectable({
  providedIn: 'root'
})
export class EmailTemplateService {

  constructor(private http: HttpClient) { }

  // Get all email templates with basic pagination
  public getAllEmailTemplates(queryParams: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllEmailTemplates`, queryParams)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getAllEmailTemplates:', err);
          return of({
            data: [],
            total: 0,
            errors: ['Error retrieving email templates']
          });
        })
      );
  }

  // Get email templates for Kendo UI Grid
  public getEmailTemplatesForKendoGrid(state: any): Observable<any> {
    const requestBody = {
      take: state.take || 10,
      skip: state.skip || 0,
      sort: state.sort || [],
      filter: state.filter || { logic: 'and', filters: [] },
      search: state.search || ''
    };

    return this.http.post(`${AppSettings.REST_ENDPOINT}/getEmailTemplatesForKendoGrid`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getEmailTemplatesForKendoGrid:', err);
          return of({
            data: [],
            total: 0,
            errors: ['Error retrieving email templates']
          });
        })
      );
  }

  // Create new email template
  public createEmailTemplate(data: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/createEmailTemplate`, data);
  }

  // Update existing email template
  public updateEmailTemplate(data: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/editEmailTemplate`, data);
  }

  // Get email template by ID
  public getEmailTemplate(id: number): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getEmailTemplateById`, { templatePID: id });
  }

  // Get email categories
  public getEmailCategories(): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getEmailCategory`, {});
  }
}

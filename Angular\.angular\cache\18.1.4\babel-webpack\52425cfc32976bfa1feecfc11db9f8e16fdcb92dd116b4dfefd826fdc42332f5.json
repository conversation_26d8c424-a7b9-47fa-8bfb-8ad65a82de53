{"ast": null, "code": "import jsPDF from 'jspdf';\nimport autoTable from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"../../services/custom-layout.utils.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@angular/common\";\nfunction EditExternalReviewComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 22)(1, \"div\", 23)(2, \"div\", 24)(3, \"span\", 25);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 26);\n    i0.ɵɵtext(6, \"Loading review data...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction EditExternalReviewComponent_span_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"span\", 27);\n  }\n}\nexport let EditExternalReviewComponent = /*#__PURE__*/(() => {\n  class EditExternalReviewComponent {\n    fb;\n    modal;\n    modalService;\n    customLayoutUtilsService;\n    permitsService;\n    permitId = null;\n    reviewData = {}; // For edit mode\n    permitDetails = {};\n    loggedInUserId = 'user'; // Should be passed from parent\n    reviewForm;\n    isLoading = false;\n    constructor(fb, modal, modalService, customLayoutUtilsService, permitsService) {\n      this.fb = fb;\n      this.modal = modal;\n      this.modalService = modalService;\n      this.customLayoutUtilsService = customLayoutUtilsService;\n      this.permitsService = permitsService;\n    }\n    ngOnInit() {\n      this.reviewForm = this.fb.group({\n        // cityComments: [this.reviewData?.comments || ''],\n        EORAOROwner_Response: [this.reviewData?.EORAOROwner_Response || ''],\n        commentResponsedBy: [this.reviewData?.commentResponsedBy || '']\n      });\n    }\n    formatDateForInput(date) {\n      if (!date) return '';\n      const d = new Date(date);\n      return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type=\"date\"]\n    }\n    onSubmit() {\n      this.isLoading = true;\n      const formData = {\n        // cityComments:this.reviewForm.controls.cityComments.value,\n        EORAOROwner_Response: this.reviewForm.controls.EORAOROwner_Response.value,\n        commentResponsedBy: this.reviewForm.controls.commentResponsedBy.value,\n        permitId: this.permitId,\n        commentsId: this.reviewData.commentsId,\n        loggedInUserId: this.loggedInUserId\n      };\n      this.permitsService.updateExternalReview(formData).subscribe({\n        next: res => {\n          this.isLoading = false;\n          if (res?.isFault === false) {\n            //alert(res.responseData.message);\n            this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n            this.modal.close('updated');\n          } else {\n            this.customLayoutUtilsService.showError(res.responseData.message || 'Failed to update external review', '');\n            //alert(res.responseData.message || 'Failed to update external review');\n          }\n        },\n        error: err => {\n          this.isLoading = false;\n          this.customLayoutUtilsService.showError('error updating external review', '');\n          //alert('Error updating external review');\n          console.error(err);\n        }\n      });\n    }\n    getPdf() {\n      const permitNumber = this.permitDetails?.permitNumber || '';\n      const projectName = this.permitDetails?.projectName || '';\n      const applicantName = this.permitDetails?.applicantName || '';\n      const reviewer = (this.reviewData?.reviewer || this.reviewData?.municipalityReviewer || '').toString();\n      const cityComments = (this.reviewData?.comments || (this.reviewData?.cityComments ?? '')).toString();\n      const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.reviewData?.EORAOROwner_Response ?? '').toString();\n      const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.reviewData?.commentResponsedBy ?? '').toString();\n      const cycle = (this.reviewData?.cycle || this.reviewData?.Cycle || '').toString();\n      const status = (this.reviewData?.status || this.reviewData?.commentstatus || '').toString();\n      const reviewCategory = (this.reviewData?.reviewCategory || '').toString();\n      const dueDate = this.reviewData?.dueDate ? new Date(this.reviewData.dueDate).toLocaleDateString() : '';\n      const completedDate = this.reviewData?.completedDate ? new Date(this.reviewData.completedDate).toLocaleDateString() : '';\n      const createdDate = this.reviewData?.createdDate ? new Date(this.reviewData.createdDate).toLocaleDateString() : '';\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'pt',\n        format: 'a4'\n      });\n      // Page metrics and margins\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const pageHeight = doc.internal.pageSize.getHeight();\n      const margin = {\n        left: 50,\n        right: 50,\n        top: 60,\n        bottom: 60\n      };\n      let currentY = margin.top;\n      // Professional Header with Company Branding\n      const drawHeader = () => {\n        // Company Logo/Title Area\n        doc.setFillColor(41, 128, 185); // Professional blue\n        doc.rect(0, 0, pageWidth, 50, 'F');\n        doc.setTextColor(255, 255, 255);\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(18);\n        doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);\n        doc.setFontSize(12);\n        doc.text('Review Report', pageWidth - margin.right - 80, 25);\n        // Reset text color\n        doc.setTextColor(0, 0, 0);\n        // Permit Information Header\n        currentY = 70;\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(14);\n        doc.text('REVIEW DETAILS', margin.left, currentY);\n        currentY += 20;\n      };\n      drawHeader();\n      // Permit Information Section\n      const drawPermitInfo = () => {\n        const infoData = [['Permit #:', permitNumber || 'N/A'], ['Project Name:', projectName || 'N/A'], ['Applicant Name:', applicantName || 'N/A'], ['Review Category:', reviewCategory || 'N/A'], ['Reviewer:', reviewer || 'N/A'], ['Status:', status || 'N/A'], ['Due Date:', dueDate || 'N/A'], ['Completed Date:', completedDate || 'N/A'], ['Created Date:', createdDate || 'N/A']];\n        autoTable(doc, {\n          startY: currentY,\n          body: infoData,\n          margin: {\n            left: margin.left,\n            right: margin.right\n          },\n          styles: {\n            font: 'helvetica',\n            fontSize: 10,\n            cellPadding: 8,\n            overflow: 'linebreak',\n            cellWidth: 'wrap'\n          },\n          columnStyles: {\n            0: {\n              cellWidth: 120,\n              fontStyle: 'bold',\n              fillColor: [240, 240, 240],\n              overflow: 'linebreak'\n            },\n            1: {\n              cellWidth: 200,\n              overflow: 'linebreak'\n            }\n          },\n          theme: 'grid'\n        });\n        currentY = doc.lastAutoTable.finalY + 20;\n      };\n      drawPermitInfo();\n      // City Comments Section\n      if (cityComments) {\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(12);\n        doc.setTextColor(192, 0, 0); // Red color for city comments\n        doc.text('CITY COMMENTS', margin.left, currentY);\n        currentY += 15;\n        doc.setTextColor(0, 0, 0);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(10);\n        // Split long text into multiple lines\n        const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);\n        doc.text(splitText, margin.left, currentY);\n        currentY += splitText.length * 12 + 20;\n      }\n      // Owner Response Section\n      if (ownerResponse) {\n        doc.setFont('helvetica', 'bold');\n        doc.setFontSize(12);\n        doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);\n        currentY += 15;\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(10);\n        const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);\n        doc.text(splitText, margin.left, currentY);\n        currentY += splitText.length * 12 + 20;\n        // Response details\n        if (respondedBy) {\n          doc.setFont('helvetica', 'bold');\n          doc.setFontSize(10);\n          doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);\n          currentY += 15;\n        }\n      }\n      // Footer\n      const drawFooter = () => {\n        const pageCount = doc.getNumberOfPages();\n        for (let i = 1; i <= pageCount; i++) {\n          doc.setPage(i);\n          // Footer line\n          doc.setDrawColor(200, 200, 200);\n          doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n          // Footer text\n          doc.setFont('helvetica', 'normal');\n          doc.setFontSize(8);\n          doc.setTextColor(100, 100, 100);\n          doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n          doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n        }\n      };\n      drawFooter();\n      // Save the PDF\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    }\n    onCancel() {\n      this.modal.dismiss('cancelled');\n    }\n    getStatusClass(status) {\n      if (!status) return 'status-n-a';\n      return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    }\n    static ɵfac = function EditExternalReviewComponent_Factory(__ngFactoryType__) {\n      return new (__ngFactoryType__ || EditExternalReviewComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.NgbActiveModal), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i3.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i4.PermitsService));\n    };\n    static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: EditExternalReviewComponent,\n      selectors: [[\"app-edit-external-review\"]],\n      inputs: {\n        permitId: \"permitId\",\n        reviewData: \"reviewData\",\n        permitDetails: \"permitDetails\",\n        loggedInUserId: \"loggedInUserId\"\n      },\n      decls: 58,\n      vars: 20,\n      consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"dates-section\"], [1, \"date-row\"], [1, \"date-item\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"date-value\"], [1, \"review-status\", 3, \"ngClass\"], [\"novalidate\", \"\", 3, \"ngSubmit\", \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [\"formControlName\", \"EORAOROwner_Response\", \"rows\", \"3\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [\"type\", \"text\", \"formControlName\", \"commentResponsedBy\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\", 3, \"disabled\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"me-2\", 3, \"click\", \"disabled\"], [\"type\", \"submit\", 1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"class\", \"spinner-border spinner-border-sm me-2\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"fs-5\"], [1, \"spinner-border\", \"spinner-border-sm\", \"me-2\"]],\n      template: function EditExternalReviewComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n          i0.ɵɵelementContainerStart(3);\n          i0.ɵɵelementStart(4, \"div\");\n          i0.ɵɵtext(5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"div\", 3)(7, \"i\", 4);\n          i0.ɵɵlistener(\"click\", function EditExternalReviewComponent_Template_i_click_7_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(8, \"div\", 5);\n          i0.ɵɵtemplate(9, EditExternalReviewComponent_div_9_Template, 7, 0, \"div\", 6);\n          i0.ɵɵelementStart(10, \"div\", 7)(11, \"div\", 8)(12, \"div\", 9)(13, \"label\", 10);\n          i0.ɵɵtext(14, \"Reviwer\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"span\", 11);\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"div\", 9)(18, \"label\", 10);\n          i0.ɵɵtext(19, \"Status\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"span\", 12);\n          i0.ɵɵtext(21);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(22, \"div\", 9)(23, \"label\", 10);\n          i0.ɵɵtext(24, \"Due Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"span\", 11);\n          i0.ɵɵtext(26);\n          i0.ɵɵpipe(27, \"date\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"div\", 9)(29, \"label\", 10);\n          i0.ɵɵtext(30, \"Completed Date\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"span\", 11);\n          i0.ɵɵtext(32);\n          i0.ɵɵpipe(33, \"date\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(34, \"div\", 8)(35, \"div\", 9)(36, \"label\", 10);\n          i0.ɵɵtext(37, \"Comments\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"span\", 11);\n          i0.ɵɵtext(39);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(40, \"form\", 13);\n          i0.ɵɵlistener(\"ngSubmit\", function EditExternalReviewComponent_Template_form_ngSubmit_40_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(41, \"div\", 14)(42, \"div\", 15)(43, \"label\", 10);\n          i0.ɵɵtext(44, \"EOR / AOR / Owner Response\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(45, \"textarea\", 16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"div\", 14)(47, \"div\", 15)(48, \"label\", 10);\n          i0.ɵɵtext(49, \"Comment Responded By\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(50, \"input\", 17);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(51, \"div\", 18)(52, \"div\")(53, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function EditExternalReviewComponent_Template_button_click_53_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(54, \" Cancel \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(55, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function EditExternalReviewComponent_Template_button_click_55_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(56, EditExternalReviewComponent_span_56_Template, 1, 0, \"span\", 21);\n          i0.ɵɵtext(57, \" Update \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate1(\"Edit External Review - \", ctx.reviewData == null ? null : ctx.reviewData.name, \"\");\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.reviewData == null ? null : ctx.reviewData.reviewer);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngClass\", ctx.getStatusClass(ctx.reviewData.status));\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.reviewData == null ? null : ctx.reviewData.status, \" \");\n          i0.ɵɵadvance(5);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(27, 14, ctx.reviewData == null ? null : ctx.reviewData.dueDate, \"MM/dd/yyyy\"));\n          i0.ɵɵadvance(6);\n          i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(33, 17, ctx.reviewData == null ? null : ctx.reviewData.completedDate, \"MM/dd/yyyy\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.reviewData == null ? null : ctx.reviewData.comments);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"formGroup\", ctx.reviewForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgClass, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i5.DatePipe],\n      styles: [\".dates-section[_ngcontent-%COMP%]{padding:1rem 1.5rem;border-bottom:1px solid #e5eaee;background-color:#f8f9fa}[_nghost-%COMP%]     .modal{--bs-modal-padding: 1rem !important}.date-row[_ngcontent-%COMP%]{display:grid;grid-template-columns:1fr 1fr 1fr 1fr;gap:2rem}.date-item[_ngcontent-%COMP%]{display:flex;flex-direction:column;gap:.25rem}.date-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%]{font-size:.75rem;font-weight:600;color:#6c7293;text-transform:uppercase;letter-spacing:.05rem}.date-item[_ngcontent-%COMP%]   .date-value[_ngcontent-%COMP%]{font-size:.875rem;color:#3f4254;font-weight:500}.review-status[_ngcontent-%COMP%]{font-size:.75rem;padding:.25rem .5rem;border-radius:.25rem;font-weight:500;border:1px solid transparent}.review-status.status-approved[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.review-status.status-pending[_ngcontent-%COMP%]{background-color:#fff3e0;color:#e65100;border:1px solid #ffcc02}.review-status.status-under-review[_ngcontent-%COMP%]{background-color:#e8eaf6;color:#3949ab;border:1px solid #c5cae9}.review-status.status-rejected[_ngcontent-%COMP%]{background-color:#ffebee;color:#c62828;border:1px solid #ffcdd2}.review-status.status-requires-re-submit[_ngcontent-%COMP%]{background-color:#fff8e1;color:#f57f17;border:1px solid #ffecb3}.review-status.status-approved-w-conditions[_ngcontent-%COMP%]{background-color:#f3e5f5;color:#7b1fa2;border:1px solid #e1bee7}.review-status.status-complete[_ngcontent-%COMP%]{background-color:#e8f5e8;color:#1b5e20;border:1px solid #c8e6c9}.review-status.status-n-a[_ngcontent-%COMP%]{background-color:#f5f5f5;color:#757575;border:1px solid #e0e0e0}\"]\n    });\n  }\n  return EditExternalReviewComponent;\n})();", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}
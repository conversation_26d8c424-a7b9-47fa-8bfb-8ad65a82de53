{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';\nimport { ResponseModalComponent } from '../response-modal/response-modal.component';\nimport { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport { autoTable } from 'jspdf-autotable';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/permits.service\";\nimport * as i7 from \"@angular/common\";\nimport * as i8 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 4)(1, \"div\", 5)(2, \"div\", 6)(3, \"span\", 7);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 8);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_li_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_li_20_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showTab(\"internal\", $event));\n    });\n    i0.ɵɵtext(2, \" Internal Reviews \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.selectedTab === \"internal\"));\n  }\n}\nfunction PermitViewComponent_div_2_li_21_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 22)(1, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_li_21_Template_a_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.showTab(\"external\", $event));\n    });\n    i0.ɵɵtext(2, \" External Reviews \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(1, _c0, ctx_r1.selectedTab === \"external\"));\n  }\n}\nfunction PermitViewComponent_div_2_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editPermit());\n    });\n    i0.ɵɵelement(1, \"i\", 31);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitViewComponent_div_2_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_24_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadInternalReviewsPdf());\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_24_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.addPopUp());\n    });\n    i0.ɵɵelement(4, \"i\", 35);\n    i0.ɵɵtext(5, \"Add Review \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 17)(1, \"button\", 36);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_25_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.downloadExternalReviewsPdf());\n    });\n    i0.ɵɵelement(2, \"i\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_25_Template_button_click_3_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleAllSubmittals());\n    });\n    i0.ɵɵelement(4, \"i\", 38);\n    i0.ɵɵelementStart(5, \"span\", 39);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_25_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.syncPermits(true));\n    });\n    i0.ɵɵelement(8, \"i\", 41);\n    i0.ɵɵtext(9, \" Sync \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 42);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_div_25_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.goToPortal());\n    });\n    i0.ɵɵelement(11, \"i\", 43);\n    i0.ɵɵtext(12, \" Portal \");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || !ctx_r1.selectedExternalSubmittalId || ctx_r1.getExternalReviewsForSelectedSubmittal().length === 0);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading || ctx_r1.externalSubmittals.length === 0)(\"title\", ctx_r1.areAllSubmittalsExpanded() ? \"Collapse all submittals\" : \"Expand all submittals\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.areAllSubmittalsExpanded() ? \"fa-compress-arrows-alt\" : \"fa-expand-arrows-alt\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.areAllSubmittalsExpanded() ? \"Collapse All\" : \"Expand All\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", !(ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID))(\"title\", (ctx_r1.permit == null ? null : ctx_r1.permit.permitEntityID) ? \"Open Portal\" : \"Portal not available - Permit Entity ID required\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_27_span_71_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 48);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.internalReviewStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalReviewStatus);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 44)(2, \"div\", 45)(3, \"div\", 46)(4, \"label\");\n    i0.ɵɵtext(5, \"Project Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 47);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 46)(9, \"label\");\n    i0.ɵɵtext(10, \"Permit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 47);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 46)(14, \"label\");\n    i0.ɵɵtext(15, \"Primary Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 47);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 46)(19, \"label\");\n    i0.ɵɵtext(20, \"Permit Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 48);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 46)(24, \"label\");\n    i0.ɵɵtext(25, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 47);\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(28, \"div\", 46)(29, \"label\");\n    i0.ɵɵtext(30, \"Category\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"span\", 47);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(33, \"div\", 46)(34, \"label\");\n    i0.ɵɵtext(35, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"span\", 47);\n    i0.ɵɵtext(37);\n    i0.ɵɵpipe(38, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\", 49);\n    i0.ɵɵtext(40);\n    i0.ɵɵpipe(41, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(42, \"div\", 46)(43, \"label\");\n    i0.ɵɵtext(44, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"span\", 47);\n    i0.ɵɵtext(46);\n    i0.ɵɵpipe(47, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(48, \"div\", 46)(49, \"label\");\n    i0.ɵɵtext(50, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 47);\n    i0.ɵɵtext(52);\n    i0.ɵɵpipe(53, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(54, \"div\", 46)(55, \"label\");\n    i0.ɵɵtext(56, \"Complete Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(57, \"span\", 47);\n    i0.ɵɵtext(58);\n    i0.ɵɵpipe(59, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(60, \"div\", 50)(61, \"div\", 10)(62, \"h4\");\n    i0.ɵɵtext(63, \"Notes / Action\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(64, \"button\", 51);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_27_Template_button_click_64_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      const notesActionsTemplate_r9 = i0.ɵɵreference(4);\n      return i0.ɵɵresetView(ctx_r1.onEdit(notesActionsTemplate_r9));\n    });\n    i0.ɵɵelement(65, \"i\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(66, \"div\", 44)(67, \"div\", 52)(68, \"div\", 53)(69, \"label\");\n    i0.ɵɵtext(70, \"Internal Review Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(71, PermitViewComponent_div_2_ng_container_27_span_71_Template, 2, 2, \"span\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(72, \"div\", 53)(73, \"label\");\n    i0.ɵɵtext(74, \"Attention Reason (ball in court)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(75, \"span\", 47);\n    i0.ɵɵtext(76);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(77, \"div\", 53)(78, \"label\");\n    i0.ɵɵtext(79, \"Internal Notes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(80, \"span\", 47);\n    i0.ɵɵtext(81);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(82, \"div\", 53)(83, \"label\");\n    i0.ɵɵtext(84, \"Action Taken\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(85, \"span\", 47);\n    i0.ɵɵtext(86);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.projectName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitType || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.primaryContact || \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.permit.permitStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitStatus || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.location || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCategory || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitIssueDate ? i0.ɵɵpipeBind2(38, 16, ctx_r1.permit.permitIssueDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" Applied on \", ctx_r1.permit.permitAppliedDate ? i0.ɵɵpipeBind2(41, 19, ctx_r1.permit.permitAppliedDate, \"MM/dd/yyyy\") : \"\", \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitExpirationDate ? i0.ɵɵpipeBind2(47, 22, ctx_r1.permit.permitExpirationDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitFinalDate ? i0.ɵɵpipeBind2(53, 25, ctx_r1.permit.permitFinalDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitCompleteDate ? i0.ɵɵpipeBind2(59, 28, ctx_r1.permit.permitCompleteDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(13);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.permit.internalReviewStatus);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.attentionReason || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.internalNotes || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.actionTaken || \"\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58);\n    i0.ɵɵelement(2, \"i\", 59);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No internal reviews found for this permit.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\", 63);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template_tr_click_0_listener() {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.selectAudit(i_r11));\n    });\n    i0.ɵɵelementStart(1, \"td\", 64)(2, \"div\", 65)(3, \"h6\", 66);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 67);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 68)(8, \"small\", 69);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 70)(11, \"div\", 71)(12, \"div\", 72)(13, \"small\", 73);\n    i0.ɵɵtext(14, \"Reviewed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 74);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 72)(19, \"small\", 73);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 74);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 72)(25, \"small\", 73);\n    i0.ɵɵtext(26, \"Reviewer\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 74);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"td\", 75)(30, \"i\", 76);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template_i_click_30_listener($event) {\n      const i_r11 = i0.ɵɵrestoreView(_r10).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      ctx_r1.editInternalReview(i_r11);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const audit_r12 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"table-active\", ctx_r1.selectedAuditIndex === i_r11);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(audit_r12.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(audit_r12.internalVerificationStatus))(\"ngStyle\", ctx_r1.getStatusStyle(audit_r12.internalVerificationStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", audit_r12.internalVerificationStatus || \"Pending\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(audit_r12.typeCodeDrawing || \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(audit_r12.reviewedDate ? i0.ɵɵpipeBind2(17, 10, audit_r12.reviewedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r12.completedDate ? i0.ɵɵpipeBind2(23, 13, audit_r12.completedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(audit_r12.internalReviewer || \"\");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60)(1, \"table\", 61)(2, \"tbody\");\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template, 31, 16, \"tr\", 62);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.auditEntries);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, PermitViewComponent_div_2_ng_container_28_div_1_Template, 5, 0, \"div\", 55)(2, PermitViewComponent_div_2_ng_container_28_div_2_Template, 4, 1, \"div\", 56);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.auditEntries.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r13 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 80)(2, \"div\", 58);\n    i0.ɵɵelement(3, \"i\", 81);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"button\", 82);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_3_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r13);\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.fetchExternalReviews());\n    });\n    i0.ɵɵelement(7, \"i\", 83);\n    i0.ɵɵtext(8, \" Retry \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 57)(2, \"div\", 58);\n    i0.ɵɵelement(3, \"i\", 84);\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5, \"No external reviews found for this permit.\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r18 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"i\", 118);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_20_Template_i_click_1_listener($event) {\n      i0.ɵɵrestoreView(_r18);\n      const review_r17 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      ctx_r1.downloadReviewPDF(review_r17);\n      return i0.ɵɵresetView($event.stopPropagation());\n    });\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r19 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 141);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r19);\n      const correction_r20 = i0.ɵɵnextContext().$implicit;\n      const review_r17 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r20, review_r17));\n    });\n    i0.ɵɵtext(1, \" Respond \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r21 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 142);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r21);\n      const correction_r20 = i0.ɵɵnextContext().$implicit;\n      const review_r17 = i0.ɵɵnextContext(2).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.openResponseModal(correction_r20, review_r17));\n    });\n    i0.ɵɵtext(1, \" Edit Response \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(8);\n    i0.ɵɵproperty(\"disabled\", ctx_r1.isLoading);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"label\", 136);\n    i0.ɵɵtext(2, \" Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 143);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r20.Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"label\", 136);\n    i0.ɵɵtext(2, \" EOR / AOR / Owner Response \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 144);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r20.EORAOROwner_Response, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"label\", 136);\n    i0.ɵɵtext(2, \" Comment Responded By \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 145);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const correction_r20 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", correction_r20.commentResponsedBy, \" \");\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 146);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 123)(2, \"div\", 124);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 125)(5, \"div\", 126)(6, \"div\", 127)(7, \"span\", 128);\n    i0.ɵɵtext(8, \"Correction Type: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 129);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 127)(12, \"span\", 128);\n    i0.ɵɵtext(13, \"Category: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"span\", 129);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 127)(17, \"span\", 128);\n    i0.ɵɵtext(18, \"Resolved: \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 130);\n    i0.ɵɵtext(20);\n    i0.ɵɵpipe(21, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(22, \"div\", 131);\n    i0.ɵɵtemplate(23, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_23_Template, 2, 1, \"button\", 132)(24, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_24_Template, 2, 1, \"button\", 133);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 134)(26, \"div\", 135)(27, \"label\", 136);\n    i0.ɵɵtext(28, \" Corrective Action \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 137);\n    i0.ɵɵtext(30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 135)(32, \"label\", 136);\n    i0.ɵɵtext(33, \" Comment \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 138);\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_36_Template, 5, 1, \"div\", 139)(37, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_37_Template, 5, 1, \"div\", 139)(38, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_38_Template, 5, 1, \"div\", 139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(39, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_39_Template, 1, 0, \"div\", 140);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const correction_r20 = ctx.$implicit;\n    const i_r22 = ctx.index;\n    const review_r17 = i0.ɵɵnextContext(2).$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i_r22 + 1);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(correction_r20.CorrectionTypeName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r20.CorrectionCategoryName || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(correction_r20.ResolvedDate ? i0.ɵɵpipeBind2(21, 12, correction_r20.ResolvedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !correction_r20.EORAOROwner_Response && !correction_r20.commentResponsedBy && ctx_r1.shouldShowEditResponseButton(correction_r20));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (correction_r20.EORAOROwner_Response || correction_r20.commentResponsedBy) && ctx_r1.shouldShowEditResponseButton(correction_r20));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\" \", correction_r20.CorrectiveAction || \"\", \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", correction_r20.Comments || \"No comment provided\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r20.Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r20.EORAOROwner_Response);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", correction_r20.commentResponsedBy);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r22 < review_r17.corrections.length - 1);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 119)(1, \"h6\", 120);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_Template, 40, 15, \"div\", 121);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const review_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"Corrections (\", review_r17.corrections.length, \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", review_r17.corrections);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 147)(1, \"h6\", 120);\n    i0.ɵɵtext(2, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 148)(4, \"div\", 149);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r17 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(review_r17.comments);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 150)(1, \"div\", 151);\n    i0.ɵɵelement(2, \"i\", 152);\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4, \" No corrections or comments available for this review.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 99)(1, \"div\", 100);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template_div_click_1_listener() {\n      const review_r17 = i0.ɵɵrestoreView(_r16).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.toggleReviewAccordion(review_r17.commentsId));\n    });\n    i0.ɵɵelementStart(2, \"div\", 101);\n    i0.ɵɵelement(3, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h6\", 102);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 103)(7, \"span\", 104);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 105)(10, \"span\", 106);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(12, \"div\", 107)(13, \"span\", 108);\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 109)(17, \"span\", 110);\n    i0.ɵɵtext(18);\n    i0.ɵɵpipe(19, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_20_Template, 2, 0, \"div\", 111);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"div\", 112)(22, \"div\", 113);\n    i0.ɵɵtemplate(23, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_Template, 4, 2, \"div\", 114)(24, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_Template, 6, 1, \"div\", 115)(25, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_25_Template, 5, 0, \"div\", 116);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const review_r17 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isReviewExpanded(review_r17.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isReviewExpanded(review_r17.commentsId) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", review_r17.FailureFlag ? \"red\" : \"green\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r17.name, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(review_r17.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", review_r17.status || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(review_r17.reviewer || \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", review_r17.dueDate ? \"Due: \" + i0.ɵɵpipeBind2(15, 17, review_r17.dueDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", review_r17.completedDate ? \"Completed: \" + i0.ɵɵpipeBind2(19, 20, review_r17.completedDate, \"MM/dd/yyyy\") : \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (review_r17 == null ? null : review_r17.corrections) && review_r17.corrections.length > 0 || (review_r17 == null ? null : review_r17.comments) && review_r17.comments !== \"\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isReviewExpanded(review_r17.commentsId));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", (review_r17 == null ? null : review_r17.corrections) && review_r17.corrections.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r17 == null ? null : review_r17.corrections) || review_r17.corrections.length === 0) && (review_r17 == null ? null : review_r17.comments));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (!(review_r17 == null ? null : review_r17.corrections) || review_r17.corrections.length === 0) && !(review_r17 == null ? null : review_r17.comments));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"tr\", 89);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template_tr_click_1_listener() {\n      const i_r15 = i0.ɵɵrestoreView(_r14).index;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.toggleSubmittalAccordion(i_r15));\n    });\n    i0.ɵɵelementStart(2, \"td\", 90)(3, \"div\", 65)(4, \"div\", 91);\n    i0.ɵɵelement(5, \"i\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h6\", 66);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"span\", 92);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"td\", 93)(11, \"div\", 71)(12, \"div\", 72)(13, \"small\", 73);\n    i0.ɵɵtext(14, \"Due\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 74);\n    i0.ɵɵtext(16);\n    i0.ɵɵpipe(17, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 72)(19, \"small\", 73);\n    i0.ɵɵtext(20, \"Completed\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 74);\n    i0.ɵɵtext(22);\n    i0.ɵɵpipe(23, \"date\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(24, \"tr\", 94)(25, \"td\", 95)(26, \"div\", 96)(27, \"div\", 97);\n    i0.ɵɵtemplate(28, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template, 26, 23, \"div\", 98);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const sub_r23 = ctx.$implicit;\n    const i_r15 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"expanded\", ctx_r1.isSubmittalExpanded(i_r15));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.isSubmittalExpanded(i_r15) ? \"fa-chevron-down\" : \"fa-chevron-right\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(sub_r23.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(sub_r23.submittalStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", sub_r23.submittalStatus, \" \");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(17, 11, sub_r23.dueDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(23, 14, sub_r23.receivedDate, \"MM/dd/yyyy\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassProp(\"d-none\", !ctx_r1.isSubmittalExpanded(i_r15));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getExternalReviewsForSubmittal(sub_r23.id));\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"div\", 86)(2, \"table\", 87)(3, \"tbody\");\n    i0.ɵɵtemplate(4, PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template, 29, 17, \"ng-container\", 88);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.externalSubmittals);\n  }\n}\nfunction PermitViewComponent_div_2_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 77)(2, \"div\", 9);\n    i0.ɵɵtemplate(3, PermitViewComponent_div_2_ng_container_29_div_3_Template, 9, 2, \"div\", 78)(4, PermitViewComponent_div_2_ng_container_29_div_4_Template, 6, 0, \"div\", 78)(5, PermitViewComponent_div_2_ng_container_29_div_5_Template, 5, 1, \"div\", 79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.reviewsError);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.reviewsError && ctx_r1.externalSubmittals.length > 0);\n  }\n}\nfunction PermitViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"div\", 10)(2, \"div\", 11)(3, \"div\", 12)(4, \"div\", 13)(5, \"span\", 14);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 15);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 16);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"div\", 17)(12, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(13, \"i\", 19);\n    i0.ɵɵtext(14, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"ul\", 21)(17, \"li\", 22)(18, \"a\", 23);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_div_2_Template_a_click_18_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(19, \" Permit Details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(20, PermitViewComponent_div_2_li_20_Template, 3, 3, \"li\", 24)(21, PermitViewComponent_div_2_li_21_Template, 3, 3, \"li\", 24);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 25);\n    i0.ɵɵtemplate(23, PermitViewComponent_div_2_button_23_Template, 2, 0, \"button\", 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(24, PermitViewComponent_div_2_div_24_Template, 6, 2, \"div\", 27)(25, PermitViewComponent_div_2_div_25_Template, 13, 8, \"div\", 27);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"div\", 28);\n    i0.ɵɵtemplate(27, PermitViewComponent_div_2_ng_container_27_Template, 87, 31, \"ng-container\", 29)(28, PermitViewComponent_div_2_ng_container_28_Template, 3, 2, \"ng-container\", 29)(29, PermitViewComponent_div_2_ng_container_29_Template, 6, 3, \"ng-container\", 29);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"Permit # \", ctx_r1.permit.permitNumber || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.permit.permitReviewType || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.permit.permitName || \"\", \" \");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isExternalReviewEnabled());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"details\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"internal\" && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"external\" && ctx_r1.isExternalReviewEnabled());\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.permit);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"internal\" && ctx_r1.permit && ctx_r1.isInternalReviewEnabled());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"external\" && ctx_r1.permit && ctx_r1.isExternalReviewEnabled());\n  }\n}\nfunction PermitViewComponent_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r24 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 153)(1, \"div\", 154)(2, \"div\", 155);\n    i0.ɵɵelementContainerStart(3);\n    i0.ɵɵelementStart(4, \"div\");\n    i0.ɵɵtext(5, \"Edit Notes/Actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\", 156)(7, \"i\", 157);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_i_click_7_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(8, \"div\", 158)(9, \"form\", 159)(10, \"div\", 160)(11, \"div\", 161)(12, \"div\", 162)(13, \"label\", 163);\n    i0.ɵɵtext(14, \"\\nInternal Review Status \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(15, \"ng-select\", 164);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(16, \"div\", 160)(17, \"div\", 161)(18, \"div\", 162)(19, \"label\", 165);\n    i0.ɵɵtext(20, \" Attention Reason \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(21, \"textarea\", 166);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 160)(23, \"div\", 161)(24, \"div\", 162)(25, \"label\", 167);\n    i0.ɵɵtext(26, \" Internal Notes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(27, \"textarea\", 168);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"div\", 160)(29, \"div\", 161)(30, \"div\", 162)(31, \"label\", 163);\n    i0.ɵɵtext(32, \" Action Taken \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(33, \"textarea\", 169);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(34, \"div\", 170)(35, \"div\")(36, \"button\", 171);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_36_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.closModal());\n    });\n    i0.ɵɵtext(37, \" Cancel\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(38, \"\\u00A0 \");\n    i0.ɵɵelementStart(39, \"button\", 172);\n    i0.ɵɵlistener(\"click\", function PermitViewComponent_ng_template_3_Template_button_click_39_listener() {\n      i0.ɵɵrestoreView(_r24);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editNotesandactions());\n    });\n    i0.ɵɵtext(40, \"Update \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"formGroup\", ctx_r1.notesForm);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r1.internalStatusArray)(\"clearable\", false)(\"multiple\", false);\n  }\n}\nexport class PermitViewComponent {\n  route;\n  router;\n  modalService;\n  cdr;\n  fb;\n  appService;\n  customLayoutUtilsService;\n  permitsService;\n  notesForm;\n  internalStatusArray = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n  permitId = null;\n  permit = null;\n  isLoading = false; // Main page loader\n  auditEntries = [];\n  selectedAuditIndex = 0; // Set to 0 to make first item initially active\n  selectedAuditName = '';\n  selectedAuditStatus = '';\n  selectedTab = 'details';\n  permitReviewType = '';\n  externalReviews = [];\n  reviewsError = '';\n  externalSubmittals = [];\n  selectedExternalSubmittalId = null;\n  internalReviews = [];\n  loginUser = {};\n  isAdmin = false;\n  singlePermit;\n  expandedSubmittals = new Set();\n  expandedReviews = new Set();\n  reviewSelectedTabs = {};\n  routeSubscription = new Subscription();\n  queryParamsSubscription = new Subscription();\n  statusList = [{\n    text: 'Pending',\n    value: 'Pending'\n  }, {\n    text: 'In Progress',\n    value: 'In Progress'\n  }, {\n    text: 'Completed',\n    value: 'Completed'\n  }, {\n    text: 'On Hold',\n    value: 'On Hold'\n  }];\n  // Navigation tracking\n  previousPage = 'permit-list'; // Default fallback\n  projectId = null;\n  constructor(route, router, modalService, cdr, fb,\n  // private modal: NgbActiveModal,\n  appService, customLayoutUtilsService, permitsService) {\n    this.route = route;\n    this.router = router;\n    this.modalService = modalService;\n    this.cdr = cdr;\n    this.fb = fb;\n    this.appService = appService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.permitsService = permitsService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.isAdmin = this.checkIfAdmin();\n    // Read query parameters for navigation tracking\n    this.queryParamsSubscription = this.route.queryParams.subscribe(params => {\n      this.previousPage = params['from'] || 'permit-list';\n      this.projectId = params['projectId'] ? Number(params['projectId']) : null;\n      console.log('Permit view - query params:', {\n        previousPage: this.previousPage,\n        projectId: this.projectId\n      });\n    });\n    // Listen for route parameter changes\n    this.routeSubscription = this.route.paramMap.subscribe(params => {\n      const idParam = params.get('id');\n      this.permitId = idParam ? Number(idParam) : null;\n      if (this.permitId) {\n        this.fetchPermitDetails();\n        this.fetchExternalReviews();\n        this.fetchInternalReviews();\n      }\n    });\n    this.loadForm();\n  }\n  loadForm() {\n    this.notesForm = this.fb.group({\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      internalReviewStatus: [null]\n    });\n    // Trigger change detection to update the view\n    this.cdr.detectChanges();\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.queryParamsSubscription) {\n      this.queryParamsSubscription.unsubscribe();\n    }\n  }\n  fetchPermitDetails() {\n    if (!this.permitId) {\n      return;\n    }\n    this.isLoading = true;\n    this.permitsService.getPermit({\n      permitId: this.permitId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Permit API Response:', res);\n        if (!res?.isFault) {\n          this.permit = res.responseData?.data || res.responseData || null;\n          console.log('Permit data assigned:', this.permit);\n          console.log('Permit permitName field:', this.permit?.permitName);\n          console.log('All permit fields:', Object.keys(this.permit || {}));\n          this.permitReviewType = this.permit?.permitReviewType || '';\n          // Default to details tab, user can navigate to reviews as needed\n          this.selectedTab = 'details';\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.permit = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchExternalReviews() {\n    if (!this.permitId) {\n      return;\n    }\n    this.isLoading = true;\n    this.reviewsError = '';\n    this.permitsService.getAllReviews({\n      permitId: this.permitId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault) {\n          this.reviewsError = res.faultMessage || 'Failed to load reviews';\n        } else {\n          const reviews = res.responseData?.reviews || [];\n          this.externalReviews = reviews.map(r => ({\n            commentsId: r.commentsId,\n            name: r.TypeName,\n            reviewer: r.AssignedTo,\n            status: r.StatusText,\n            completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,\n            dueDate: r.DueDate ? new Date(r.DueDate) : null,\n            receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,\n            comments: r.Comments,\n            corrections: r.Corrections || [],\n            submittalId: r.SubmittalId,\n            FailureFlag: r.FailureFlag,\n            reviewCategory: r.reviewCategory,\n            EORAOROwner_Response: r.EORAOROwner_Response,\n            commentResponsedBy: r.commentResponsedBy\n          }));\n          // Build submittal list grouped from reviews\n          const idToReviews = {};\n          this.externalReviews.forEach(rv => {\n            const key = String(rv.submittalId ?? 'unknown');\n            if (!idToReviews[key]) {\n              idToReviews[key] = [];\n            }\n            idToReviews[key].push(rv);\n          });\n          this.externalSubmittals = Object.keys(idToReviews).map(key => {\n            const items = idToReviews[key];\n            // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved\n            const statusOrder = {\n              'Requires Re-submit': 4,\n              'Under Review': 3,\n              'Approved w/ Conditions': 2,\n              'Approved': 1\n            };\n            const submittalStatus = items.reduce((acc, it) => {\n              const a = statusOrder[acc] || 0;\n              const b = statusOrder[it.status] || 0;\n              return b > a ? it.status : acc;\n            }, '');\n            // Aggregate dates\n            const dueDate = items.reduce((acc, it) => {\n              if (!it.dueDate) {\n                return acc;\n              }\n              if (!acc) {\n                return it.dueDate;\n              }\n              return acc > it.dueDate ? it.dueDate : acc; // earliest due date\n            }, null);\n            const completedDate = items.reduce((acc, it) => {\n              if (!it.completedDate) {\n                return acc;\n              }\n              if (!acc) {\n                return it.completedDate;\n              }\n              return acc < it.completedDate ? it.completedDate : acc; // latest completed date\n            }, null);\n            // Get received date from the first item that has it\n            const receivedDate = items.find(it => it.receivedDate)?.receivedDate || items.find(it => it.createdDate)?.createdDate || null;\n            // Get submittal name from the first item (all items in this group have same submittalId)\n            const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;\n            return {\n              id: key,\n              title: reviewCategory,\n              submittalStatus: submittalStatus || items[0]?.status || '',\n              receivedDate: receivedDate ? new Date(receivedDate) : null,\n              dueDate: dueDate,\n              completedDate: completedDate\n            };\n          }).sort((a, b) => {\n            // Sort by received date in descending order (latest first)\n            if (!a.receivedDate && !b.receivedDate) return 0;\n            if (!a.receivedDate) return 1;\n            if (!b.receivedDate) return -1;\n            return b.receivedDate.getTime() - a.receivedDate.getTime();\n          });\n          // Select first submittal by default\n          if (this.externalSubmittals.length > 0) {\n            this.selectedExternalSubmittalId = this.externalSubmittals[0].id;\n            this.selectedAuditName = this.externalSubmittals[0].title;\n            this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        this.reviewsError = 'Failed to load reviews';\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchInternalReviews() {\n    if (!this.permitId) {\n      return;\n    }\n    this.isLoading = true;\n    this.permitsService.getInternalReviews({\n      permitId: this.permitId,\n      take: 50,\n      skip: 0\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault) {\n          console.error('Failed to load internal reviews:', res.faultMessage);\n        } else {\n          this.internalReviews = res.responseData?.data || res.data || [];\n          // Transform internal reviews to match the auditEntries format\n          this.auditEntries = this.internalReviews.map(review => ({\n            commentsId: review.commentsId,\n            title: review.reviewCategory,\n            reviewCategory: review.reviewCategory,\n            // Preserve reviewCategory for edit modal\n            typeCodeDrawing: review.typeCodeDrawing,\n            reviewComments: review.reviewComments,\n            nonComplianceItems: review.nonComplianceItems,\n            aeResponse: review.aeResponse,\n            internalReviewer: review.internalReviewer,\n            internalVerificationStatus: review.internalVerificationStatus,\n            reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n            completedDate: review.completedDate ? new Date(review.completedDate) : null,\n            reviews: [{\n              name: review.reviewCategory,\n              typeCodeDrawing: review.typeCodeDrawing,\n              reviewComments: review.reviewComments,\n              nonComplianceItems: review.nonComplianceItems,\n              aeResponse: review.aeResponse,\n              internalReviewer: review.internalReviewer,\n              internalVerificationStatus: review.internalVerificationStatus,\n              reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n              completedDate: review.completedDate ? new Date(review.completedDate) : null\n            }]\n          }));\n          // Select first internal review by default\n          if (this.auditEntries.length > 0) {\n            this.selectedAuditIndex = 0;\n            this.selectedAuditName = this.auditEntries[0].title;\n            this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error loading internal reviews:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  downloadInternalReviewsPdf() {\n    if (!this.internalReviews || this.internalReviews.length === 0) {\n      return;\n    }\n    const grouped = {};\n    this.internalReviews.forEach(r => {\n      const key = r.reviewCategory || 'Uncategorized';\n      if (!grouped[key]) {\n        grouped[key] = [];\n      }\n      grouped[key].push(r);\n    });\n    const doc = new jsPDF({\n      orientation: 'portrait',\n      unit: 'pt',\n      format: 'a4'\n    });\n    const pageWidth = doc.internal.pageSize.getWidth();\n    // Use more horizontal space: reduce side margins to ~0.5 inch (36pt)\n    const margin = {\n      left: 36,\n      right: 36,\n      top: 40\n    };\n    let y = margin.top;\n    // Add Permit # header at the top\n    doc.setFont('helvetica', 'bold');\n    doc.setFontSize(10);\n    doc.text(`Permit #: ${this.permit?.permitNumber || ''}`, margin.left, y);\n    y += 16;\n    const addCategory = (category, items) => {\n      // Category block title (centered, uppercase)\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`${category.toUpperCase()} REVIEW COMMENTS`, pageWidth / 2, y, {\n        align: 'center'\n      });\n      y += 12;\n      // Reviewer line (take distinct non-empty names, join with comma)\n      const reviewers = Array.from(new Set(items.map(it => (it.internalReviewer || '').toString().trim()).filter(v => v)));\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Reviewer: ${reviewers.join(', ') || ''}`, pageWidth / 2, y, {\n        align: 'center'\n      });\n      y += 12;\n      // Dates line (Reviewed Date / Responses Date)\n      const reviewedDate = items.find(it => it.reviewedDate)?.reviewedDate;\n      const responsesDate = items.find(it => it.completedDate)?.completedDate;\n      doc.setFont('helvetica', 'italic');\n      doc.setFontSize(9);\n      doc.text(`Reviewed Date: ${reviewedDate ? new Date(reviewedDate).toLocaleDateString() : ''}`, margin.left, y);\n      doc.text(`Response Date: ${responsesDate ? new Date(responsesDate).toLocaleDateString() : ''}`, pageWidth - margin.right, y, {\n        align: 'right'\n      });\n      y += 6;\n      const rows = items.map((it, idx) => [(idx + 1).toString(), it.typeCodeDrawing || '', (it.reviewComments || '').toString(), (it.aeResponse || '').toString(), it.internalVerificationStatus || '']);\n      autoTable(doc, {\n        startY: y + 5,\n        head: [['#', 'Drawing #', 'Review Comments', 'A/E Response', 'Status']],\n        body: rows,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 8,\n          cellPadding: 5,\n          valign: 'top',\n          overflow: 'linebreak',\n          cellWidth: 'wrap'\n        },\n        headStyles: {\n          fillColor: [33, 150, 243],\n          textColor: 255,\n          halign: 'center',\n          fontSize: 9\n        },\n        // Fit exactly into available width (pageWidth - margins)\n        // A4 width ~595pt; with 36pt margins each side → 523pt content width\n        columnStyles: {\n          0: {\n            cellWidth: 24,\n            halign: 'center',\n            overflow: 'linebreak'\n          },\n          // #\n          1: {\n            cellWidth: 55,\n            overflow: 'linebreak'\n          },\n          // Drawing # (even narrower)\n          2: {\n            cellWidth: 198,\n            overflow: 'linebreak'\n          },\n          // Review Comments (half of remaining)\n          3: {\n            cellWidth: 197,\n            overflow: 'linebreak'\n          },\n          // A/E Response (other half)\n          4: {\n            cellWidth: 49,\n            overflow: 'linebreak'\n          } // Verification Status (fits)\n        },\n        theme: 'grid'\n      });\n      // update y for next section\n      // @ts-ignore\n      y = doc.lastAutoTable.finalY + 20;\n      // add page if needed\n      if (y > doc.internal.pageSize.getHeight() - 100) {\n        doc.addPage();\n        y = margin.top;\n      }\n    };\n    Object.keys(grouped).forEach((category, idx) => {\n      addCategory(category, grouped[category]);\n    });\n    // Footer on each page: line + left date + centered company + right pagination\n    const companyName = 'Pacifica Engineering Services';\n    const pageCountInternal = doc.getNumberOfPages();\n    const pageWidthInternal = doc.internal.pageSize.getWidth();\n    const pageHeightInternal = doc.internal.pageSize.getHeight();\n    const generatedOn = new Date().toLocaleString();\n    for (let i = 1; i <= pageCountInternal; i++) {\n      doc.setPage(i);\n      doc.setDrawColor(200, 200, 200);\n      doc.line(margin.left, pageHeightInternal - 30, pageWidthInternal - margin.right, pageHeightInternal - 30);\n      doc.setFont('helvetica', 'normal');\n      doc.setFontSize(8);\n      doc.setTextColor(100, 100, 100);\n      doc.text(`Generated on: ${generatedOn}`, margin.left, pageHeightInternal - 15);\n      doc.text(companyName, pageWidthInternal / 2, pageHeightInternal - 15, {\n        align: 'center'\n      });\n      doc.text(`Page ${i} of ${pageCountInternal}`, pageWidthInternal - margin.right - 50, pageHeightInternal - 15);\n    }\n    const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  }\n  selectExternalSubmittal(id) {\n    this.selectedExternalSubmittalId = id;\n    const sel = this.externalSubmittals.find(s => String(s.id) === String(id));\n    if (sel) {\n      this.selectedAuditName = sel.title;\n      this.selectedAuditStatus = sel.submittalStatus;\n    }\n  }\n  getExternalReviewsForSelectedSubmittal() {\n    if (!this.selectedExternalSubmittalId) {\n      return [];\n    }\n    return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);\n  }\n  getExternalReviewsForSubmittal(submittalId) {\n    const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));\n    // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)\n    return reviews.sort((a, b) => {\n      // If both have the same FailureFlag value, maintain original order (reverse for desc)\n      if (a.FailureFlag === b.FailureFlag) {\n        return 0;\n      }\n      // False reviews (FailureFlag = false) come first\n      if (!a.FailureFlag && b.FailureFlag) {\n        return -1;\n      }\n      // True reviews (FailureFlag = true) come after false reviews\n      if (a.FailureFlag && !b.FailureFlag) {\n        return 1;\n      }\n      return 0;\n    }).reverse(); // Reverse to get descending order within each group\n  }\n  toggleSubmittalAccordion(index) {\n    if (this.expandedSubmittals.has(index)) {\n      this.expandedSubmittals.delete(index);\n    } else {\n      this.expandedSubmittals.add(index);\n    }\n  }\n  isSubmittalExpanded(index) {\n    return this.expandedSubmittals.has(index);\n  }\n  areAllSubmittalsExpanded() {\n    return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;\n  }\n  toggleAllSubmittals() {\n    if (!this.externalSubmittals || this.externalSubmittals.length === 0) {\n      return;\n    }\n    if (this.areAllSubmittalsExpanded()) {\n      this.expandedSubmittals.clear();\n    } else {\n      this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));\n    }\n    this.cdr.markForCheck();\n  }\n  toggleReviewAccordion(reviewId) {\n    if (this.expandedReviews.has(reviewId)) {\n      this.expandedReviews.delete(reviewId);\n    } else {\n      this.expandedReviews.add(reviewId);\n      // Set default tab for this review if not already set\n      if (!this.reviewSelectedTabs[reviewId]) {\n        this.reviewSelectedTabs[reviewId] = 'corrections';\n      }\n    }\n  }\n  isReviewExpanded(reviewId) {\n    return this.expandedReviews.has(reviewId);\n  }\n  showReviewTab(reviewId, tab, $event) {\n    $event.stopPropagation();\n    this.reviewSelectedTabs[reviewId] = tab;\n    this.cdr.markForCheck();\n  }\n  updateReviewResponse(review) {\n    this.isLoading = true;\n    const formData = {\n      EORAOROwner_Response: review.EORAOROwner_Response,\n      commentResponsedBy: review.commentResponsedBy,\n      permitId: this.permitId,\n      commentsId: review.commentsId,\n      loggedInUserId: this.loginUser.userId\n    };\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh external reviews to get updated data\n          this.fetchExternalReviews();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error syncing permit', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('❌ Error updating review response', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  isSelectedSubmittal(submittalId) {\n    return String(this.selectedExternalSubmittalId) === String(submittalId);\n  }\n  selectAudit(index) {\n    this.selectedAuditIndex = index;\n    this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;\n    this.selectedAuditStatus = this.auditEntries[this.selectedAuditIndex].submittalStatus;\n  }\n  getReviewsForSelectedAudit() {\n    if (this.selectedAuditIndex === null || this.selectedAuditIndex >= this.auditEntries.length) {\n      return [];\n    }\n    return this.auditEntries[this.selectedAuditIndex].reviews || [];\n  }\n  goBack() {\n    console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);\n    if (this.previousPage === 'project' && this.projectId) {\n      // Navigate back to the specific project view with permits tab active\n      console.log('Navigating to project view with permits tab active');\n      this.router.navigate(['/projects/view', this.projectId], {\n        queryParams: {\n          activeTab: 'permits'\n        }\n      });\n    } else {\n      // Default to permit list\n      console.log('Navigating to permit list');\n      this.router.navigate(['/permits/list']);\n    }\n  }\n  goToPortal() {\n    window.open(`${this.permit.cityReviewLink + this.permit.permitEntityID}`, '_blank');\n  }\n  openReviewDetails(review) {\n    const modalRef = this.modalService.open(ReviewDetailsModalComponent, {\n      size: 'lg'\n    });\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.permitDetails = this.permit;\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    console.log('Status mapping - Original:', status, 'Normalized:', normalized);\n    // Map common synonyms from API to a single class we style\n    const aliasMap = {\n      'in-review': 'in-review',\n      'requires-re-submit': 'requires-re-submit',\n      'approved-w-conditions': 'approved-w-conditions',\n      'in-progress': 'in-progress',\n      'completed': 'completed',\n      'verified': 'verified',\n      'pending': 'pending',\n      'rejected': 'rejected',\n      'approved': 'approved',\n      'under-review': 'under-review',\n      'requires-resubmit': 'requires-resubmit',\n      'pacifica-verification': 'pacifica-verification',\n      'dis-approved': 'dis-approved',\n      'not-required': 'not-required',\n      '1-cycle-completed': '1-cycle-completed',\n      '1 cycle completed': '1-cycle-completed',\n      'cycle completed': '1-cycle-completed'\n    };\n    const resolved = aliasMap[normalized] || normalized;\n    const finalClass = 'status-' + resolved;\n    console.log('Final status class:', finalClass);\n    console.log('Available CSS classes for debugging:', ['status-pending', 'status-in-progress', 'status-completed', 'status-verified', 'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit', 'status-pacifica-verification', 'status-dis-approved', 'status-not-required', 'status-in-review', 'status-1-cycle-completed']);\n    return finalClass;\n  }\n  getStatusStyle(status) {\n    if (!status) return {};\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    const styleMap = {\n      '1-cycle-completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      '1 cycle completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      'cycle completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#2e7d32',\n        border: '1px solid #a5d6a7'\n      },\n      'pacifica-verification': {\n        backgroundColor: '#e1f5fe',\n        color: '#0277bd',\n        border: '1px solid #81d4fa'\n      },\n      'dis-approved': {\n        backgroundColor: '#ffebee',\n        color: '#c62828',\n        border: '1px solid #ffcdd2'\n      },\n      'not-required': {\n        backgroundColor: '#f5f5f5',\n        color: '#757575',\n        border: '1px solid #e0e0e0'\n      },\n      'in-review': {\n        backgroundColor: '#e8eaf6',\n        color: '#3949ab',\n        border: '1px solid #c5cae9'\n      },\n      'pending': {\n        backgroundColor: '#fff3e0',\n        color: '#e65100',\n        border: '1px solid #ffcc02'\n      },\n      'approved': {\n        backgroundColor: '#e8f5e8',\n        color: '#1b5e20',\n        border: '1px solid #c8e6c9'\n      },\n      'completed': {\n        backgroundColor: '#e8f5e8',\n        color: '#1b5e20',\n        border: '1px solid #c8e6c9'\n      },\n      'rejected': {\n        backgroundColor: '#ffebee',\n        color: '#c62828',\n        border: '1px solid #ffcdd2'\n      }\n    };\n    return styleMap[normalized] || {};\n  }\n  showTab(tab, $event) {\n    if (tab === 'internal' && !this.isInternalReviewEnabled()) {\n      return;\n    }\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  isInternalReviewEnabled() {\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n    // show only when type is 'internal' or 'both'\n    return type === 'internal' || type === 'both';\n  }\n  isExternalReviewEnabled() {\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n    // show only when type is 'external' or 'both'\n    return type === 'external' || type === 'both';\n  }\n  addPopUp() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  editInternalReview(reviewIndex) {\n    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false,\n      scrollable: true\n    };\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n    // Pass data to the modal for editing\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch(error => {\n      console.log('Modal dismissed');\n    });\n  }\n  editPopUp() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(AddEditInternalReviewComponent, NgbModalOptions);\n  }\n  editPermit() {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(PermitPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.permitId;\n    modalRef.componentInstance.isHideInternalReviewStatus = false;\n    // Listen for passEntry event to refresh permit details when permit is saved\n    modalRef.componentInstance.passEntry.subscribe(saved => {\n      if (saved) {\n        this.fetchPermitDetails();\n      }\n    });\n  }\n  syncPermits(i) {\n    this.isLoading = true;\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({\n      permitId: this.permitId,\n      singlePermit: this.singlePermit,\n      autoLogin: true\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Sync response:', res);\n        console.log('Response type:', typeof res);\n        console.log('Response keys:', Object.keys(res || {}));\n        console.log('Response success:', res?.success);\n        console.log('Response message:', res?.message);\n        console.log('Response responseData:', res?.responseData);\n        // Handle various response structures\n        let responseData = res;\n        // Check different possible response structures\n        if (res?.responseData) {\n          responseData = res.responseData;\n        } else if (res?.body?.responseData) {\n          responseData = res.body.responseData;\n        } else if (res?.body) {\n          responseData = res.body;\n        }\n        console.log('Final responseData:', responseData);\n        console.log('Final success:', responseData?.success);\n        console.log('Final message:', responseData?.message);\n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n          this.customLayoutUtilsService.showError(responseData.faultMessage, '');\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (responseData.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else if (responseData?.success === true || responseData?.data) {\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n          //alert('✅ Permit synced successfully');\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        } else {\n          // Fallback for unknown response structure\n          console.log('Unknown response structure, showing generic success');\n          //alert('✅ Permit synced successfully');\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n            this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n            //alert(`❌ ${err.error.message}`);\n          }\n        } else if (err?.status === 404) {\n          this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  editExternalReview(review) {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(EditExternalReviewComponent, NgbModalOptions);\n    console.log('reviewData ', review);\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = review;\n    modalRef.componentInstance.permitDetails = this.permit;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    // Handle modal result\n    modalRef.result.then(result => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch(error => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n  openResponseModal(correction, review) {\n    // Open the modal using NgbModal\n    const modalRef = this.modalService.open(ResponseModalComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false\n    });\n    // Pass data to the modal\n    modalRef.componentInstance.correction = correction;\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;\n    modalRef.componentInstance.isAdmin = this.isAdmin;\n    // Handle modal result\n    modalRef.componentInstance.responseSubmitted.subscribe(formData => {\n      this.submitResponse(formData, modalRef);\n    });\n    // Handle response completion to reset loading state\n    modalRef.componentInstance.responseCompleted.subscribe(success => {\n      if (!success) {\n        // Reset loading state if submission failed\n        modalRef.componentInstance.isLoading = false;\n      }\n    });\n    modalRef.result.then(() => {\n      // Modal was closed\n    }).catch(() => {\n      // Modal was dismissed\n    });\n  }\n  submitResponse(formData, modalRef) {\n    if (!formData) {\n      return;\n    }\n    this.isLoading = true;\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: res => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          //alert(res.responseData.message || 'Response submitted successfully');\n          this.fetchExternalReviews(); // Refresh external reviews to get updated data\n          // Emit success completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(true);\n          }\n          // Close the modal if it was passed\n          if (modalRef) {\n            modalRef.close();\n          }\n        } else {\n          //alert(res.faultMessage || 'Failed to submit response');\n          this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to submit response', '');\n          // Emit failure completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(false);\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        //alert('Error submitting response');\n        console.error(err);\n        this.customLayoutUtilsService.showSuccess('Error submitting response', '');\n        // Emit failure completion event\n        if (modalRef && modalRef.componentInstance) {\n          modalRef.componentInstance.responseCompleted.emit(false);\n        }\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  downloadReviewPDF(review) {\n    if (!review) {\n      return;\n    }\n    try {\n      const permitNumber = this.permit?.permitNumber || '';\n      const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();\n      const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();\n      const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();\n      // Calculate submittal count for this review\n      const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;\n      const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();\n      const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();\n      const displayDate = review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate ? new Date(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate).toLocaleDateString() : new Date().toLocaleDateString();\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'pt',\n        format: 'a4'\n      });\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const pageHeight = doc.internal.pageSize.getHeight();\n      const margin = {\n        left: 40,\n        right: 40,\n        top: 40,\n        bottom: 40\n      };\n      // Header: Permit #\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Permit #: ${permitNumber || ''}`, margin.left, margin.top);\n      const startY = margin.top + 20;\n      // Table with one row matching the screenshot\n      const headers = ['REVIEWED BY', 'CITY COMMENTS', 'EOR/AOR/OWNER COMMENT RESPONSE', 'CYCLE', 'STATUS', 'DATE'];\n      // Build body rows. If there are corrections, show one row per correction; otherwise single row\n      const rawCorrections = (review && review.Corrections) ?? review.corrections ?? [];\n      const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : rawCorrections ? [rawCorrections] : [];\n      const bodyRows = correctionsArray.length > 0 ? correctionsArray.map(c => [reviewer || '', c?.Comments || cityComments || '', c?.Response || c?.EORAOROwner_Response || ownerResponse || '', cycle || '', status || '', (c?.ResolvedDate ? new Date(c.ResolvedDate).toLocaleDateString() : displayDate) || '']) : [[reviewer || '', cityComments || '', ownerResponse || '', cycle || '', status || '', displayDate || '']];\n      autoTable(doc, {\n        startY,\n        head: [headers],\n        body: bodyRows,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 8,\n          cellPadding: 5,\n          overflow: 'linebreak',\n          cellWidth: 'wrap'\n        },\n        headStyles: {\n          fillColor: [240, 240, 240],\n          textColor: [0, 0, 0],\n          fontStyle: 'bold',\n          fontSize: 8\n        },\n        columnStyles: {\n          0: {\n            cellWidth: 90,\n            overflow: 'linebreak'\n          },\n          1: {\n            cellWidth: (pageWidth - margin.left - margin.right) * 0.26,\n            overflow: 'linebreak'\n          },\n          2: {\n            cellWidth: (pageWidth - margin.left - margin.right) * 0.24,\n            overflow: 'linebreak'\n          },\n          3: {\n            cellWidth: 45,\n            halign: 'center',\n            overflow: 'linebreak'\n          },\n          4: {\n            cellWidth: 60,\n            halign: 'center',\n            overflow: 'linebreak'\n          },\n          5: {\n            cellWidth: 60,\n            halign: 'center',\n            overflow: 'linebreak'\n          }\n        },\n        didParseCell: data => {\n          // City comments text in red\n          if (data.section === 'body' && data.column.index === 1) {\n            data.cell.styles.textColor = [192, 0, 0];\n          }\n          // Set background color and text color for status column\n          if (data.section === 'body' && data.column.index === 4) {\n            const value = String(data.cell.raw || '');\n            const isApproved = value.toLowerCase() === 'approved';\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\n            const textColor = [255, 255, 255];\n            data.cell.styles.fillColor = bg;\n            data.cell.styles.textColor = textColor;\n            data.cell.styles.fontStyle = 'bold';\n            data.cell.styles.halign = 'center';\n            data.cell.styles.valign = 'middle';\n          }\n        },\n        theme: 'grid'\n      });\n      // Footer (existing info) + company name\n      const pageCount = doc.getNumberOfPages();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n        // Company name centered\n        doc.text('Pacifica Engineering Services', pageWidth / 2, pageHeight - 15, {\n          align: 'center'\n        });\n      }\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');\n      //alert('Error generating PDF. Please try again.');\n    }\n  }\n  downloadExternalReviewsPdf() {\n    // Generate a PDF for the currently selected submittal's reviews in a one-row-per-review format\n    if (!this.selectedExternalSubmittalId) {\n      return;\n    }\n    const reviews = this.getExternalReviewsForSelectedSubmittal();\n    if (!reviews || reviews.length === 0) {\n      return;\n    }\n    try {\n      const permitNumber = this.permit?.permitNumber || '';\n      const doc = new jsPDF({\n        orientation: 'portrait',\n        unit: 'pt',\n        format: 'a4'\n      });\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const margin = {\n        left: 36,\n        right: 36,\n        top: 40\n      };\n      // Header\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Permit #: ${permitNumber}`, margin.left, margin.top);\n      const headers = ['DISCIPLINE', 'REVIEWER', 'STATUS', 'VERSION', 'CYCLE DATE', 'CITY ACTION\\nNEEDED', 'AEC ACTION\\nNEEDED'];\n      const body = reviews.map(r => [r?.name || '', (r?.AssignedTo || r?.municipalityReviewer || r?.reviewer || '').toString(), (r?.StatusName || r?.commentstatus || r?.status || '').toString(), (this.externalSubmittals?.length || r?.cycle || r?.Cycle || '').toString(), r?.CompletedDate || r?.completedDate || r?.DueDate || r?.createdDate ? new Date(r?.CompletedDate || r?.completedDate || r?.DueDate || r?.createdDate).toLocaleDateString() : '', r?.cityActionNeeded ? 'YES' : 'NO', r?.aocActionNeeded ? 'YES' : r?.EORAOROwner_Response ? 'NO' : 'YES']);\n      autoTable(doc, {\n        startY: margin.top + 18,\n        head: [headers],\n        body,\n        margin: {\n          left: margin.left,\n          right: margin.right\n        },\n        styles: {\n          font: 'helvetica',\n          fontSize: 8,\n          cellPadding: 5,\n          overflow: 'linebreak',\n          valign: 'top',\n          cellWidth: 'wrap'\n        },\n        headStyles: {\n          fillColor: [33, 150, 243],\n          textColor: 255,\n          halign: 'center',\n          fontSize: 7,\n          cellPadding: 3\n        },\n        columnStyles: {\n          // Use remaining width across first two columns (adjusted to make Version fit)\n          0: {\n            cellWidth: 90,\n            overflow: 'linebreak'\n          },\n          1: {\n            cellWidth: 103,\n            overflow: 'linebreak'\n          },\n          2: {\n            cellWidth: 65,\n            halign: 'center',\n            overflow: 'linebreak'\n          },\n          // Keep Version in a single line (widened to avoid header wrap)\n          3: {\n            cellWidth: 50,\n            halign: 'center',\n            overflow: 'ellipsize'\n          },\n          4: {\n            cellWidth: 65,\n            halign: 'center',\n            overflow: 'linebreak'\n          },\n          5: {\n            cellWidth: 60,\n            halign: 'center',\n            overflow: 'linebreak'\n          },\n          6: {\n            cellWidth: 65,\n            halign: 'center',\n            overflow: 'linebreak'\n          }\n        },\n        didParseCell: data => {\n          // Set background color and text color for status column\n          if (data.section === 'body' && data.column.index === 2) {\n            const value = String(data.cell.raw || '');\n            const isApproved = value.toLowerCase() === 'approved';\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\n            const textColor = [255, 255, 255];\n            data.cell.styles.fillColor = bg;\n            data.cell.styles.textColor = textColor;\n            data.cell.styles.fontStyle = 'bold';\n            data.cell.styles.halign = 'center';\n            data.cell.styles.valign = 'middle';\n          }\n        },\n        theme: 'grid'\n      });\n      // Footer on each page: line + left date + centered company + right pagination\n      const companyName = 'Pacifica Engineering Services';\n      const pageCount = doc.getNumberOfPages();\n      const pageWidthFooter = doc.internal.pageSize.getWidth();\n      const pageHeightFooter = doc.internal.pageSize.getHeight();\n      const generatedOnFooter = new Date().toLocaleString();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeightFooter - 30, pageWidthFooter - margin.right, pageHeightFooter - 30);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${generatedOnFooter}`, margin.left, pageHeightFooter - 15);\n        doc.text(companyName, pageWidthFooter / 2, pageHeightFooter - 15, {\n          align: 'center'\n        });\n        doc.text(`Page ${i} of ${pageCount}`, pageWidthFooter - margin.right - 50, pageHeightFooter - 15);\n      }\n      const fileName = `External_Reviews_${permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    } catch (e) {\n      console.error('Error generating external reviews PDF', e);\n    }\n  }\n  checkIfAdmin() {\n    // Check if the user is an admin based on roleId\n    // Assuming roleId 1 is admin - adjust this based on your role system\n    return this.loginUser && this.loginUser.roleId === 1;\n  }\n  shouldShowEditResponseButton(correction) {\n    // Show edit response button if:\n    // 1. User is admin (can always edit)\n    // 2. User is not admin but lockResponse is false (unlocked by admin)\n    if (this.isAdmin) {\n      return true;\n    }\n    // For non-admin users, only show if lockResponse is explicitly false\n    return correction.lockResponse === false;\n  }\n  onEdit(template) {\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    this.modalService.open(template, NgbModalOptions);\n    // console.log(\"this.permit\", this.permit)\n    this.notesForm.patchValue({\n      actionTaken: this.permit.actionTaken,\n      attentionReason: this.permit.attentionReason,\n      internalNotes: this.permit.internalNotes,\n      internalReviewStatus: this.permit.internalReviewStatus\n      // actionTaken:'helo',\n    });\n  }\n  closModal() {\n    this.modalService.dismissAll();\n  }\n  editNotesandactions() {\n    this.isLoading = true;\n    const formData = {\n      permitId: this.permitId,\n      actionTaken: this.notesForm.value.actionTaken,\n      internalNotes: this.notesForm.value.internalNotes,\n      attentionReason: this.notesForm.value.attentionReason,\n      internalReviewStatus: this.notesForm.value.internalReviewStatus\n    };\n    this.permitsService.editNotesAndActions(formData).subscribe({\n      next: res => {\n        this.closModal();\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh permit details to get updated data from server\n          this.fetchPermitDetails();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage || '❌ Error in update notes and actions', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  static ɵfac = function PermitViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.NgbModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i3.FormBuilder), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.PermitsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitViewComponent,\n    selectors: [[\"app-permit-view\"]],\n    decls: 5,\n    vars: 2,\n    consts: [[\"notesActionsTemplate\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"permit-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"permit-details-header\"], [1, \"header-content\", \"w-100\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"permit-title\"], [1, \"status-text\", \"status-under-review\"], [1, \"permit-number-line\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"nav-item\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", 2, \"margin-right\", \"16px\"], [\"type\", \"button\", \"class\", \"btn btn-link p-0\", \"title\", \"Edit Permit\", 3, \"click\", 4, \"ngIf\"], [\"class\", \"button-group\", 4, \"ngIf\"], [1, \"card-body\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Edit Permit\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", 2, \"font-size\", \"1.1rem\"], [\"type\", \"button\", \"title\", \"Download Internal Reviews PDF\", 1, \"btn\", \"btn-link\", \"p-0\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-download\", \"download-pdf-icon\", 2, \"color\", \"#3699ff\"], [1, \"btn\", \"btn-success\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"bi\", \"bi-plus-lg\"], [\"type\", \"button\", \"title\", \"Download External Reviews PDF\", 1, \"btn\", \"btn-link\", \"p-0\", \"me-3\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", 3, \"ngClass\"], [1, \"ms-1\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-sync-alt\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", 3, \"click\", \"disabled\", \"title\"], [1, \"fas\", \"fa-external-link-alt\"], [1, \"permit-details-content\"], [1, \"permit-details-grid\"], [1, \"permit-detail-item\"], [1, \"permit-value\"], [1, \"status-text\", 3, \"ngClass\"], [1, \"text-gray-500\", \"fs-7\", \"p-0\"], [1, \"permit-details-card\"], [\"type\", \"button\", \"title\", \"Edit Notes/Actions\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"notes-actions-container\"], [1, \"permit-detail-item-full\"], [\"class\", \"status-text\", 3, \"ngClass\", 4, \"ngIf\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-clipboard-list\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"table-hover\"], [\"class\", \"audit-table-row\", 3, \"table-active\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"audit-table-row\", 3, \"click\"], [1, \"audit-title-cell\", \"px-4\"], [1, \"d-flex\", \"align-items-center\"], [1, \"mb-0\", \"me-3\"], [1, \"audit-status-badge\", 3, \"ngClass\", \"ngStyle\"], [1, \"mt-1\"], [1, \"text-muted\"], [1, \"audit-dates-cell\", \"px-3\"], [1, \"d-flex\", \"gap-4\"], [1, \"date-item\"], [1, \"text-muted\", \"d-block\"], [1, \"fw-medium\"], [1, \"audit-actions-cell\", \"px-4\"], [\"title\", \"Edit Review\", 1, \"fas\", \"fa-edit\", \"action-icon\", \"edit-icon\", 3, \"click\"], [1, \"external-reviews-card\"], [\"class\", \"card-body\", 4, \"ngIf\"], [\"class\", \"card-body p-0\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-danger\"], [1, \"fas\", \"fa-exclamation-triangle\", \"fa-3x\", \"mb-3\"], [1, \"btn\", \"btn-outline-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [1, \"fas\", \"fa-redo\"], [1, \"fas\", \"fa-external-link-alt\", \"fa-3x\", \"mb-3\"], [1, \"card-body\", \"p-0\"], [1, \"table-responsive\", \"external-reviews-table\"], [1, \"table\", \"table-hover\", \"mb-0\"], [4, \"ngFor\", \"ngForOf\"], [1, \"external-submittal-row\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"submittal-title-cell\", \"px-3\"], [1, \"accordion-toggle\", \"me-2\"], [1, \"submittal-status-badge\", 3, \"ngClass\"], [1, \"submittal-dates-cell\", \"px-3\"], [1, \"accordion-content-row\"], [\"colspan\", \"2\", 1, \"p-0\"], [1, \"accordion-content\"], [1, \"reviews-container\", \"p-3\"], [\"class\", \"review-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"review-item\"], [1, \"review-single-line\", 2, \"cursor\", \"pointer\", 3, \"click\"], [1, \"review-accordion-toggle\", \"me-2\"], [1, \"review-title\"], [1, \"review-status-container\"], [1, \"review-status\", 3, \"ngClass\"], [1, \"reviewer-container\"], [1, \"reviewer\"], [1, \"due-date-container\"], [1, \"due-date\"], [1, \"completed-date-container\"], [1, \"completed-date\"], [\"class\", \"review-actions-container\", 4, \"ngIf\"], [1, \"review-details-accordion\"], [1, \"review-details-content\"], [\"class\", \"corrections-section p-3\", \"style\", \"padding-bottom: 0px !important;\", 4, \"ngIf\"], [\"class\", \"comments-section p-3\", 4, \"ngIf\"], [\"class\", \"no-data-section p-3\", 4, \"ngIf\"], [1, \"review-actions-container\"], [\"title\", \"Download Review PDF\", 1, \"fas\", \"fa-download\", \"download-pdf-icon\", 3, \"click\"], [1, \"corrections-section\", \"p-3\", 2, \"padding-bottom\", \"0px !important\"], [1, \"section-title\"], [\"class\", \"correction-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"correction-item\"], [1, \"correction-header\", \"d-flex\", \"align-items-center\"], [1, \"correction-number\"], [1, \"correction-meta\", \"flex-grow-1\", \"ms-3\", \"d-flex\", \"align-items-center\", \"justify-content-between\"], [1, \"meta-fields\", \"d-flex\", \"align-items-center\", \"w-100\"], [1, \"meta-field\", \"flex-fill\"], [1, \"meta-label\", \"fw-bold\"], [1, \"meta-value\"], [1, \"meta-value\", \"resolved-date\"], [1, \"respond-buttons\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Respond to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"class\", \"btn btn-primary btn-sm me-3\", \"title\", \"Edit response to this correction\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [1, \"correction-content\"], [1, \"correction-field\"], [1, \"field-label\"], [1, \"field-content\", \"corrective-action\"], [1, \"field-content\", \"comment\"], [\"class\", \"correction-field\", 4, \"ngIf\"], [\"class\", \"correction-separator\", 4, \"ngIf\"], [\"title\", \"Respond to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [\"title\", \"Edit response to this correction\", 1, \"btn\", \"btn-primary\", \"btn-sm\", \"me-3\", 3, \"click\", \"disabled\"], [1, \"field-content\", \"response\"], [1, \"field-content\", \"eor-response\"], [1, \"field-content\", \"responded-by\"], [1, \"correction-separator\"], [1, \"comments-section\", \"p-3\"], [1, \"comment-content\"], [1, \"comment-text\"], [1, \"no-data-section\", \"p-3\"], [1, \"no-data-message\", \"text-center\", \"text-muted\"], [1, \"fas\", \"fa-info-circle\"], [1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"row\", \"mt-4\"], [1, \"col-xl-12\"], [1, \"form-group\"], [\"for\", \"internalProjectNo\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"dropdownPosition\", \"top\", \"formControlName\", \"internalReviewStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"for\", \"projectName\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"attentionReason\", \"rows\", \"2\", \"formControlName\", \"attentionReason\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [\"for\", \"projectDescription\", 1, \"fw-bold\", \"form-label\", \"mb-2\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"internalNotes\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [\"id\", \"internalNotes\", \"rows\", \"3\", \"formControlName\", \"actionTaken\", \"placeholder\", \"Type here\", 1, \"form-control\", \"form-control-sm\"], [1, \"modal-footer\", \"justify-content-end\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function PermitViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, PermitViewComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2);\n        i0.ɵɵtemplate(2, PermitViewComponent_div_2_Template, 30, 14, \"div\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(3, PermitViewComponent_ng_template_3_Template, 41, 4, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.permit);\n      }\n    },\n    dependencies: [i7.NgClass, i7.NgForOf, i7.NgIf, i7.NgStyle, i3.ɵNgNoValidate, i3.DefaultValueAccessor, i3.NgControlStatus, i3.NgControlStatusGroup, i3.FormGroupDirective, i3.FormControlName, i8.NgSelectComponent, i7.DatePipe],\n    styles: [\"@charset \\\"UTF-8\\\";\\n.permit-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n  margin-left: auto; \\n\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem; \\n\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1; \\n\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.permit-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 2rem; \\n\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  background-color: transparent;\\n  color: #3699ff !important;\\n  text-decoration: none;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: color 0.2s ease;\\n  line-height: 1;\\n}\\n.card-header[_ngcontent-%COMP%]   .d-flex.align-items-center.gap-2[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #3699ff !important;\\n}\\n\\n.btn[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.6;\\n  cursor: not-allowed;\\n  background-color: #f5f5f5 !important;\\n  color: #999 !important;\\n  border-color: #ddd !important;\\n}\\n.btn[_ngcontent-%COMP%]:disabled:hover {\\n  background-color: #f5f5f5 !important;\\n  color: #999 !important;\\n  border-color: #ddd !important;\\n  box-shadow: none !important;\\n}\\n\\n.permit-details-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  margin: 1rem 0;\\n  overflow: hidden;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%] {\\n  background: #f5f6f8;\\n  padding: 0.75rem 1.25rem;\\n  border-bottom: 1px solid #e3e6ea;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.permit-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  height: 2rem; \\n\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover {\\n  background-color: transparent;\\n  color: #3699ff !important;\\n  text-decoration: none;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus {\\n  box-shadow: none;\\n  outline: none;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: color 0.2s ease;\\n  line-height: 1;\\n}\\n.permit-details-card[_ngcontent-%COMP%]   .permit-details-header[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover   i[_ngcontent-%COMP%] {\\n  color: #3699ff !important;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  background: #1b7e6c;\\n  color: #fff;\\n  border: none;\\n  border-radius: 6px;\\n  padding: 0.35rem 0.75rem;\\n  font-size: 0.85rem;\\n  cursor: pointer;\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  transition: background 0.2s ease;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover {\\n  background: #166354;\\n}\\n\\n.permit-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-radius: 0.75rem;\\n  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);\\n  margin: 1rem 0;\\n  overflow: hidden;\\n}\\n\\n.permit-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 2fr 1fr 1fr 1fr; \\n\\n  gap: 1.5rem;\\n}\\n\\n.notes-actions-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n  width: 100%;\\n}\\n.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  display: block; \\n\\n  margin-bottom: 0.15rem;\\n  font-size: 15px;\\n  display: block;\\n  line-height: 1.2;\\n}\\n.permit-detail-item-full[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  padding: 0.5rem 0;\\n  border-bottom: none;\\n  word-wrap: break-word;\\n  white-space: pre-wrap;\\n}\\n\\n[_nghost-%COMP%]     .modal {\\n  --bs-modal-padding: 1rem !important;\\n}\\n\\n.permit-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.4rem;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  display: block;\\n  line-height: 1.2;\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #111111;\\n  text-transform: capitalize;\\n  letter-spacing: 0.1rem;\\n  margin: 0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%], \\n.permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  margin: 0; \\n\\n  padding: 0.25rem 0; \\n\\n  border-bottom: none;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .permit-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0; \\n\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left; \\n\\n  background: transparent; \\n\\n  border: none; \\n\\n  min-width: 0; \\n\\n  border-radius: 0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-detail-item[_ngcontent-%COMP%]   .status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n\\n.loading-section[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 0.475rem;\\n  padding: 2rem;\\n  text-align: center;\\n  border: 1px solid #e5eaee;\\n}\\n.loading-section[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 3rem;\\n  height: 3rem;\\n}\\n.loading-section[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n  margin: 0;\\n}\\n.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%] {\\n  color: #a1a5b7 !important;\\n  font-size: 0.875rem;\\n}\\n.loading-section[_ngcontent-%COMP%]   .loading-tips[_ngcontent-%COMP%]   .text-muted[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n  color: #3699ff;\\n}\\n\\n.no-permit-data[_ngcontent-%COMP%] {\\n  margin-bottom: 1rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%] {\\n  border-radius: 0.475rem;\\n  border: 1px solid #e5eaee;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 0.5rem 0;\\n  color: #6c7293;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%] {\\n  margin: 0 0 1rem 0;\\n  padding-left: 1.5rem;\\n  color: #6c7293;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%] {\\n  margin-bottom: 0.25rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n}\\n.no-permit-data[_ngcontent-%COMP%]   .alert[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.5rem;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.external-reviews-table[_ngcontent-%COMP%] {\\n  padding-top: 0 !important;\\n  margin-top: 0 !important;\\n}\\n\\n.card-body[_ngcontent-%COMP%] {\\n  padding-top: 0 !important;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  border-bottom: 1px solid #e5eaee;\\n  background: #ffffff;\\n  border-radius: 0.5rem;\\n  margin-bottom: 0.5rem;\\n  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row[_ngcontent-%COMP%]:hover, \\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row.expanded[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row.expanded[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border-color: #2196f3;\\n  box-shadow: 0 2px 8px rgba(33, 150, 243, 0.2);\\n}\\n.table[_ngcontent-%COMP%]   .audit-table-row.table-active[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .external-submittal-row.table-active[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%] {\\n  width: 75%;\\n  vertical-align: middle;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  padding: 0.2rem 0.4rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n  display: inline-block;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-re-submit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved-w-conditions[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-complete[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-n-a[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pending[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-progress[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-progress[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #90caf9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-verified[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-verified[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #2e7d32;\\n  border: 1px solid #a5d6a7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-rejected[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-under-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-requires-resubmit[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-on-hold[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-on-hold[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-pacifica-verification[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-dis-approved[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-not-required[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-in-review[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .audit-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .audit-status-badge.status-1-cycle-completed[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-title-cell[_ngcontent-%COMP%]   .submittal-status-badge.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  vertical-align: middle;\\n  text-align: right;\\n  padding-right: 0;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n  gap: 1rem;\\n  justify-content: flex-end;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%] {\\n  text-align: center;\\n  width: 120px; \\n\\n  min-width: 120px;\\n  max-width: 120px;\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  font-weight: 500;\\n  display: block; \\n\\n}\\n.table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%], \\n.table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .date-item[_ngcontent-%COMP%]   .fw-medium[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  display: block; \\n\\n  min-height: 1.25rem; \\n\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n  vertical-align: middle;\\n  text-align: center;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3699ff;\\n  cursor: pointer;\\n  transition: color 0.2s ease, transform 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n}\\n.table[_ngcontent-%COMP%]   .audit-actions-cell[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  transition: transform 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\\n  border-left: 4px solid #3699ff;\\n  box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%] {\\n  border-top: 1px solid #d1e7ff;\\n  background: transparent;\\n}\\n.table[_ngcontent-%COMP%]   .accordion-content-row[_ngcontent-%COMP%]   .accordion-content[_ngcontent-%COMP%]   .reviews-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 0.375rem;\\n  margin: 0.5rem;\\n  padding: 1rem;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none)) {\\n  background: linear-gradient(135deg, #f0f8ff 0%, #e8f2ff 100%);\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.2);\\n  transition: all 0.3s ease;\\n  margin-right: 0;\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:has( + .accordion-content-row[_ngcontent-%COMP%]:not(.d-none))   .submittal-title-cell[_ngcontent-%COMP%]   .accordion-toggle[_ngcontent-%COMP%] {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%] {\\n  transition: all 0.3s ease;\\n}\\n.table[_ngcontent-%COMP%]   .external-submittal-row[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n.section-header[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: #f8f9fa;\\n  border-radius: 0.475rem 0.475rem 0 0;\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.section-header[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n.section-header[_ngcontent-%COMP%]   .section-actions[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.section-header[_ngcontent-%COMP%]   .submittal-status.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.reviews-container[_ngcontent-%COMP%] {\\n  max-height: 400px;\\n  overflow-y: auto;\\n}\\n\\n.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #1b5e20 !important;\\n  border: 1px solid #c8e6c9 !important;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0 !important;\\n  color: #e65100 !important;\\n  border: 1px solid #ffcc02 !important;\\n}\\n\\n.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n\\n.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd !important;\\n  color: #1565c0 !important;\\n  border: 1px solid #bbdefb !important;\\n}\\n\\n.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #1b5e20 !important;\\n  border: 1px solid #c8e6c9 !important;\\n}\\n\\n.status-1-cycle-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8 !important;\\n  color: #2e7d32 !important;\\n  border: 1px solid #a5d6a7 !important;\\n}\\n\\n.status-pacifica-verification[_ngcontent-%COMP%] {\\n  background-color: #e1f5fe !important;\\n  color: #0277bd !important;\\n  border: 1px solid #81d4fa !important;\\n}\\n\\n.status-dis-approved[_ngcontent-%COMP%] {\\n  background-color: #ffebee !important;\\n  color: #c62828 !important;\\n  border: 1px solid #ffcdd2 !important;\\n}\\n\\n.status-not-required[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5 !important;\\n  color: #757575 !important;\\n  border: 1px solid #e0e0e0 !important;\\n}\\n\\n.status-in-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6 !important;\\n  color: #3949ab !important;\\n  border: 1px solid #c5cae9 !important;\\n}\\n\\n.reviews-container[_ngcontent-%COMP%] {\\n  max-height: 500px;\\n  overflow-y: auto;\\n  padding: 0.75rem;\\n}\\n\\n.review-item[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5eaee;\\n  padding: 0.5rem 0;\\n  background: transparent;\\n  transition: background-color 0.2s ease;\\n}\\n.review-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.review-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.review-single-line[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: 20px 1fr 120px 120px 140px 140px 30px;\\n  align-items: center;\\n  gap: 0.75rem;\\n  cursor: pointer;\\n  padding: 0.5rem;\\n  background: transparent;\\n  border-radius: 0.375rem;\\n  transition: all 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n.review-single-line.expanded[_ngcontent-%COMP%] {\\n  background: #e3f2fd;\\n  border: 1px solid #2196f3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .reviewer-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .due-date-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .completed-date-container[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  min-height: 20px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-actions-container[_ngcontent-%COMP%] {\\n  gap: 0.5rem;\\n  justify-content: flex-end;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n  border: 1px solid transparent;\\n  white-space: nowrap;\\n  display: inline-block;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-status.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n  font-size: 0.7rem;\\n  color: #6c7293;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3699ff;\\n  cursor: pointer;\\n  transition: color 0.2s ease, transform 0.2s ease;\\n  margin-left: 0.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.edit-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%] {\\n  color: #6c7293;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .action-icon.view-icon[_ngcontent-%COMP%]:hover {\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  transition: transform 0.2s ease;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  width: 20px;\\n  height: 20px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-accordion-toggle[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  transition: transform 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%] {\\n  color: #28a745;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 0.25rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:hover {\\n  color: #1e7e34;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .edit-review-icon[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 1rem;\\n  cursor: pointer;\\n  transition: all 0.2s ease;\\n  padding: 0.25rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:hover {\\n  color: #2b7ce0;\\n  transform: scale(1.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%] {\\n  border: none;\\n  background: none;\\n  box-shadow: none;\\n  text-decoration: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:hover, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:focus, .review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:active {\\n  background: none;\\n  box-shadow: none;\\n  text-decoration: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.5;\\n  cursor: not-allowed;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%] {\\n  cursor: not-allowed;\\n  color: #6c757d;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-link[_ngcontent-%COMP%]:disabled   .download-pdf-icon[_ngcontent-%COMP%]:hover {\\n  transform: none;\\n  color: #6c757d;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card[_ngcontent-%COMP%] {\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding: 1.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .external-reviews-card[_ngcontent-%COMP%]   .table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%] {\\n  margin-top: 1rem;\\n  border-top: 1px solid #e5eaee;\\n  background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);\\n  border-radius: 0.375rem;\\n  box-shadow: inset 0 2px 4px rgba(54, 153, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem;\\n  background: rgba(255, 255, 255, 0.7);\\n  border-radius: 0.375rem;\\n  margin: 0.5rem;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-radius: 0.75rem !important;\\n  padding: 2rem !important;\\n  border: 1px solid #e5eaee !important;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;\\n  margin: 0 !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem !important;\\n  font-weight: 800 !important;\\n  color: #1a202c !important;\\n  margin-bottom: 2rem !important;\\n  padding-bottom: 1rem !important;\\n  border-bottom: 3px solid #3699ff !important;\\n  position: relative !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 1rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: 1.8rem;\\n  filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .section-title::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 2px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%] {\\n  background: #fff !important;\\n  border: 1px solid #e5eaee !important;\\n  border-radius: 0.75rem !important;\\n  padding: 1.5rem !important;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\\n  position: relative !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  overflow: hidden !important;\\n  margin-bottom: 2rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #3699ff, #1bc5bd);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before {\\n  background: linear-gradient(180deg, #1bc5bd, #3699ff);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1.25rem;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f1f3f4;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  width: 50px;\\n  height: 2px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 1px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;\\n  color: white !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  border-radius: 50% !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  font-size: 0.9rem !important;\\n  font-weight: 800 !important;\\n  box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;\\n  flex-shrink: 0 !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-number::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd);\\n  border-radius: 50%;\\n  z-index: -1;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_pulse 2s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 0.75rem !important;\\n  padding: 0.5rem 0 !important;\\n  background: transparent !important;\\n  border-radius: 0 !important;\\n  border: none !important;\\n  transition: all 0.2s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 700;\\n  color: #4a5568;\\n  text-transform: capitalize;\\n  letter-spacing: 0.05rem;\\n  min-width: 80px;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%] {\\n  color: inherit;\\n  background: transparent;\\n  padding: 0.25rem 0;\\n  border-radius: 0;\\n  font-weight: 700;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 800;\\n  color: #2d3748;\\n  text-transform: capitalize;\\n  letter-spacing: 0.15rem;\\n  margin-bottom: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label i, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.9rem;\\n  width: 16px;\\n  text-align: center;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-label::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  animation: _ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_slideRight {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  50% {\\n    transform: translateX(3px);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  color: #2d3748 !important;\\n  line-height: 1.7 !important;\\n  padding: 1.25rem !important;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;\\n  border-radius: 0.75rem !important;\\n  border: 1px solid #e2e8f0 !important;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #e2e8f0, #cbd5e0);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\\n  border-color: #fed7d7;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #feb2b2, #fc8181);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);\\n  border-color: #bee3f8;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.comment::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #90cdf4, #63b3ed);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);\\n  border-color: #c6f6d5;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.response::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  color: #38a169;\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  animation: _ngcontent-%COMP%_checkmark 0.6s ease-in-out;\\n}\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0) rotate(0deg);\\n  }\\n  50% {\\n    transform: scale(1.2) rotate(180deg);\\n  }\\n  100% {\\n    transform: scale(1) rotate(360deg);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;\\n  border-color: #c6f6d5 !important;\\n  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.eor-response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%) !important;\\n  border-color: #c6f6d5 !important;\\n  box-shadow: inset 0 2px 8px rgba(56, 161, 105, 0.1) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-content .correction-field .field-content.responded-by::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent, #e5eaee, transparent);\\n  margin: 2rem 0;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]     .corrections-section .correction-item .correction-separator::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .review-details-content[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 6px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 3px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%] {\\n  background: #ffffff !important;\\n  border-radius: 0.75rem !important;\\n  padding: 2rem !important;\\n  border: 1px solid #e5eaee !important;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08) !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1.4rem !important;\\n  font-weight: 800 !important;\\n  color: #1a202c !important;\\n  margin-bottom: 2rem !important;\\n  padding-bottom: 1rem !important;\\n  border-bottom: 3px solid #3699ff !important;\\n  position: relative !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  gap: 1rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::before {\\n  content: \\\"\\uD83D\\uDCCB\\\";\\n  font-size: 1.8rem;\\n  filter: drop-shadow(0 2px 4px rgba(54, 153, 255, 0.3));\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .section-title::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -3px;\\n  left: 0;\\n  width: 60px;\\n  height: 3px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 2px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.4);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%] {\\n  background: #fff !important;\\n  border: 1px solid #e5eaee !important;\\n  border-radius: 0.75rem !important;\\n  padding: 1.5rem !important;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;\\n  position: relative !important;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;\\n  overflow: hidden !important;\\n  margin-bottom: 2rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #3699ff, #1bc5bd);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(54, 153, 255, 0.15);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]:hover::before {\\n  background: linear-gradient(180deg, #1bc5bd, #3699ff);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 1.25rem;\\n  margin-bottom: 1.5rem;\\n  padding-bottom: 1rem;\\n  border-bottom: 2px solid #f1f3f4;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]::after {\\n  content: \\\"\\\";\\n  position: absolute;\\n  bottom: -2px;\\n  left: 0;\\n  width: 50px;\\n  height: 2px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 1px;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd) !important;\\n  color: white !important;\\n  width: 32px !important;\\n  height: 32px !important;\\n  border-radius: 50% !important;\\n  display: flex !important;\\n  align-items: center !important;\\n  justify-content: center !important;\\n  font-size: 0.9rem !important;\\n  font-weight: 800 !important;\\n  box-shadow: 0 4px 12px rgba(54, 153, 255, 0.4) !important;\\n  flex-shrink: 0 !important;\\n  position: relative !important;\\n  transition: all 0.3s ease !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-number::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-number[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: -2px;\\n  right: -2px;\\n  bottom: -2px;\\n  background: linear-gradient(135deg, #3699ff, #1bc5bd);\\n  border-radius: 50%;\\n  z-index: -1;\\n  opacity: 0.3;\\n  animation: _ngcontent-%COMP%_pulse 2s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n    opacity: 0.3;\\n  }\\n  50% {\\n    transform: scale(1.1);\\n    opacity: 0.6;\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.75rem;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  padding: 0.5rem 0;\\n  background: transparent;\\n  border-radius: 0;\\n  border: none;\\n  transition: all 0.2s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]:hover {\\n  background: transparent;\\n  transform: none;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 700;\\n  color: #4a5568;\\n  text-transform: capitalize;\\n  letter-spacing: 0.05rem;\\n  min-width: 80px;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .meta-value, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .meta-value[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #2d3748;\\n  flex: 1;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .resolved-date, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .resolved-date[_ngcontent-%COMP%] {\\n  color: inherit;\\n  background: transparent;\\n  padding: 0.25rem 0;\\n  border-radius: 0;\\n  font-weight: 700;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-header .correction-meta .meta-row .respond-btn, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-header[_ngcontent-%COMP%]   .correction-meta[_ngcontent-%COMP%]   .meta-row[_ngcontent-%COMP%]   .respond-btn[_ngcontent-%COMP%] {\\n  margin-left: 0.5rem !important;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%] {\\n  margin-bottom: 1.5rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  font-weight: 800;\\n  color: #2d3748;\\n  text-transform: capitalize;\\n  letter-spacing: 0.15rem;\\n  margin-bottom: 0.75rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 0.75rem;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label i, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #3699ff;\\n  font-size: 0.9rem;\\n  width: 16px;\\n  text-align: center;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-label::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-label[_ngcontent-%COMP%]::before {\\n  content: \\\"\\u25B6\\\";\\n  color: #3699ff;\\n  font-size: 0.8rem;\\n  animation: _ngcontent-%COMP%_slideRight 1.5s ease-in-out infinite;\\n}\\n@keyframes _ngcontent-%COMP%_slideRight {\\n  0%, 100% {\\n    transform: translateX(0);\\n  }\\n  50% {\\n    transform: translateX(3px);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #2d3748;\\n  line-height: 1.7;\\n  padding: 1.25rem;\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);\\n  border-radius: 0.75rem;\\n  border: 1px solid #e2e8f0;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04);\\n  position: relative;\\n  transition: all 0.3s ease;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  width: 4px;\\n  height: 100%;\\n  background: linear-gradient(180deg, #e2e8f0, #cbd5e0);\\n  border-radius: 0.75rem 0 0 0.75rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content:hover, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content[_ngcontent-%COMP%]:hover {\\n  box-shadow: inset 0 2px 12px rgba(0, 0, 0, 0.08);\\n  transform: translateY(-1px);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);\\n  border-color: #fed7d7;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.corrective-action::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.corrective-action[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #feb2b2, #fc8181);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f0f9ff 0%, #ffffff 100%);\\n  border-color: #bee3f8;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.comment::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.comment[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #90cdf4, #63b3ed);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #e8f5e8 0%, #f0fff4 100%);\\n  border-color: #c6f6d5;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a);\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-content .correction-field .field-content.response::after, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-content[_ngcontent-%COMP%]   .correction-field[_ngcontent-%COMP%]   .field-content.response[_ngcontent-%COMP%]::after {\\n  content: \\\"\\u2713\\\";\\n  position: absolute;\\n  top: 1rem;\\n  right: 1rem;\\n  color: #38a169;\\n  font-weight: bold;\\n  font-size: 1.2rem;\\n  animation: _ngcontent-%COMP%_checkmark 0.6s ease-in-out;\\n}\\n@keyframes _ngcontent-%COMP%_checkmark {\\n  0% {\\n    transform: scale(0) rotate(0deg);\\n  }\\n  50% {\\n    transform: scale(1.2) rotate(180deg);\\n  }\\n  100% {\\n    transform: scale(1) rotate(360deg);\\n  }\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%], \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%] {\\n  height: 2px;\\n  background: linear-gradient(90deg, transparent, #e5eaee, transparent);\\n  margin: 2rem 0;\\n  position: relative;\\n}\\n.review-single-line[_ngcontent-%COMP%]     .review-details-accordion .corrections-section .correction-item .correction-separator::before, \\n.review-single-line[_ngcontent-%COMP%]   .review-details-accordion[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before, \\n.review-single-line[_ngcontent-%COMP%]   .corrections-section[_ngcontent-%COMP%]   .correction-item[_ngcontent-%COMP%]   .correction-separator[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  position: absolute;\\n  top: -2px;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 60px;\\n  height: 6px;\\n  background: linear-gradient(90deg, #3699ff, #1bc5bd);\\n  border-radius: 3px;\\n  box-shadow: 0 2px 8px rgba(54, 153, 255, 0.3);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .section-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  margin-bottom: 1rem;\\n  padding-bottom: 0.5rem;\\n  border-bottom: 2px solid #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n  padding: 1rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .comments-section[_ngcontent-%COMP%]   .comment-content[_ngcontent-%COMP%]   .comment-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  color: #3f4254;\\n  line-height: 1.5;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  text-align: center;\\n  color: #6c7293;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n  margin-bottom: 0.5rem;\\n  display: block;\\n  color: #3699ff;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .no-data-section[_ngcontent-%COMP%]   .no-data-message[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-label[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  margin-bottom: 0.5rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%] {\\n  border: 1px solid #e5eaee;\\n  border-radius: 0.375rem;\\n  padding: 0.5rem 0.75rem;\\n  font-size: 0.875rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .form-control[_ngcontent-%COMP%]:focus {\\n  border-color: #3699ff;\\n  box-shadow: 0 0 0 0.2rem rgba(54, 153, 255, 0.25);\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%] {\\n  background-color: #1bc5bd;\\n  border-color: #1bc5bd;\\n  font-size: 0.875rem;\\n  padding: 0.5rem 1rem;\\n}\\n.review-single-line[_ngcontent-%COMP%]   .review-form-section[_ngcontent-%COMP%]   .btn-success[_ngcontent-%COMP%]:hover {\\n  background-color: #17a2b8;\\n  border-color: #17a2b8;\\n}\\n\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 1rem;\\n  margin-bottom: 0.75rem;\\n  font-size: 0.75rem;\\n  color: #6c7293;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 0.25rem;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%]::before, \\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%]::before {\\n  content: \\\"\\\";\\n  width: 4px;\\n  height: 4px;\\n  background-color: #6c7293;\\n  border-radius: 50%;\\n}\\n.review-body[_ngcontent-%COMP%]   .review-meta[_ngcontent-%COMP%]   .view-btn[_ngcontent-%COMP%] {\\n  margin-left: auto;\\n  font-size: 0.65rem;\\n  padding: 6px 10px;\\n  line-height: 1;\\n  border-radius: 0.25rem;\\n  font-weight: 500;\\n}\\n\\n.no-selection[_ngcontent-%COMP%] {\\n  padding: 2rem;\\n  text-align: center;\\n  color: #6c7293;\\n}\\n\\n@media (max-width: 768px) {\\n  .table[_ngcontent-%COMP%]   .audit-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%], \\n   .table[_ngcontent-%COMP%]   .submittal-dates-cell[_ngcontent-%COMP%]   .d-flex[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 0.5rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%] {\\n    grid-template-columns: 20px 1fr 100px 100px 120px 120px 30px;\\n    gap: 0.5rem;\\n    font-size: 0.8rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.2rem 0.4rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n    font-size: 0.9rem;\\n  }\\n}\\n@media (max-width: 480px) {\\n  .review-single-line[_ngcontent-%COMP%] {\\n    grid-template-columns: 20px 1fr 80px 80px 100px 100px 25px;\\n    gap: 0.25rem;\\n    padding: 0.25rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .review-status[_ngcontent-%COMP%] {\\n    font-size: 0.65rem;\\n    padding: 0.15rem 0.3rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .reviewer[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .due-date[_ngcontent-%COMP%], \\n   .review-single-line[_ngcontent-%COMP%]   .completed-date[_ngcontent-%COMP%] {\\n    font-size: 0.6rem;\\n  }\\n  .review-single-line[_ngcontent-%COMP%]   .download-pdf-icon[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.permit-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%], \\n.correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%) !important;\\n  border-color: #e2e8f0 !important;\\n  box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.04) !important;\\n}\\n.correction-content[_ngcontent-%COMP%]   .field-content.eor-response[_ngcontent-%COMP%]::before, \\n.correction-content[_ngcontent-%COMP%]   .field-content.responded-by[_ngcontent-%COMP%]::before {\\n  background: linear-gradient(180deg, #38a169, #2f855a) !important;\\n  width: 4px !important;\\n  height: 100% !important;\\n  border-radius: 0.75rem 0 0 0.75rem !important;\\n}\\n\\n\\n\\n.notes-actions-card[_ngcontent-%COMP%] {\\n  background: #fff;\\n  border-radius: 12px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);\\n  padding: 1.5rem;\\n  max-width: 700px;\\n  margin: auto;\\n}\\n\\n\\n\\n.notes-actions-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 1.2rem;\\n  border-bottom: 1px solid #e5e5e5;\\n  padding-bottom: 0.8rem;\\n}\\n\\n.notes-actions-header[_ngcontent-%COMP%]   .card-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  margin: 0;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%] {\\n  background: #007bff;\\n  color: #fff;\\n  border: none;\\n  padding: 6px 14px;\\n  border-radius: 6px;\\n  font-size: 0.9rem;\\n  display: flex;\\n  align-items: center;\\n  gap: 6px;\\n  cursor: pointer;\\n  transition: background 0.3s ease;\\n}\\n\\n.edit-btn[_ngcontent-%COMP%]:hover {\\n  background: #0056b3;\\n}\\n\\n\\n\\n.notes-actions-body[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1rem;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  margin-bottom: 0.4rem;\\n  color: #333;\\n}\\n\\n.permit-detail-item-full[_ngcontent-%COMP%]   textarea[_ngcontent-%COMP%] {\\n  width: 100%;\\n  min-height: 60px;\\n  padding: 0.6rem;\\n  border: 1px solid #d1d5db;\\n  border-radius: 8px;\\n  resize: vertical;\\n  font-size: 0.95rem;\\n}\\n\\n\\n\\n.kendo-dropdown[_ngcontent-%COMP%] {\\n  width: 100%;\\n  border-radius: 8px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "ReviewDetailsModalComponent", "ResponseModalComponent", "AddEditInternalReviewComponent", "PermitPopupComponent", "EditExternalReviewComponent", "jsPDF", "autoTable", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "PermitViewComponent_div_2_li_20_Template_a_click_1_listener", "$event", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "showTab", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction1", "_c0", "selectedTab", "PermitViewComponent_div_2_li_21_Template_a_click_1_listener", "_r4", "PermitViewComponent_div_2_button_23_Template_button_click_0_listener", "_r5", "editPermit", "ɵɵelement", "PermitViewComponent_div_2_div_24_Template_button_click_1_listener", "_r6", "downloadInternalReviewsPdf", "PermitViewComponent_div_2_div_24_Template_button_click_3_listener", "addPopUp", "isLoading", "auditEntries", "length", "PermitViewComponent_div_2_div_25_Template_button_click_1_listener", "_r7", "downloadExternalReviewsPdf", "PermitViewComponent_div_2_div_25_Template_button_click_3_listener", "toggleAllSubmittals", "PermitViewComponent_div_2_div_25_Template_button_click_7_listener", "syncPermits", "PermitViewComponent_div_2_div_25_Template_button_click_10_listener", "goToPortal", "selectedExternalSubmittalId", "getExternalReviewsForSelectedSubmittal", "externalSubmittals", "areAllSubmittalsExpanded", "ɵɵtextInterpolate", "permit", "permitEntityID", "getStatusClass", "internalReviewStatus", "ɵɵelementContainerStart", "PermitViewComponent_div_2_ng_container_27_Template_button_click_64_listener", "_r8", "notesActionsTemplate_r9", "ɵɵreference", "onEdit", "ɵɵtemplate", "PermitViewComponent_div_2_ng_container_27_span_71_Template", "projectName", "permitType", "primaryContact", "permitStatus", "location", "permitCategory", "permitIssueDate", "ɵɵpipeBind2", "ɵɵtextInterpolate1", "permitAppliedDate", "permitExpirationDate", "permitFinalDate", "permitCompleteDate", "attentionReason", "internalNotes", "actionTaken", "PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template_tr_click_0_listener", "i_r11", "_r10", "index", "selectAudit", "PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template_i_click_30_listener", "editInternalReview", "stopPropagation", "ɵɵclassProp", "selectedAuditIndex", "audit_r12", "title", "internalVerificationStatus", "getStatusStyle", "typeCodeDrawing", "reviewedDate", "completedDate", "internalReviewer", "PermitViewComponent_div_2_ng_container_28_div_2_tr_3_Template", "PermitViewComponent_div_2_ng_container_28_div_1_Template", "PermitViewComponent_div_2_ng_container_28_div_2_Template", "PermitViewComponent_div_2_ng_container_29_div_3_Template_button_click_6_listener", "_r13", "fetchExternalReviews", "reviewsError", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_20_Template_i_click_1_listener", "_r18", "review_r17", "$implicit", "downloadReviewPDF", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_23_Template_button_click_0_listener", "_r19", "correction_r20", "openResponseModal", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_24_Template_button_click_0_listener", "_r21", "Response", "EORAOROwner_Response", "commentResponsedBy", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_23_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_button_24_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_36_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_37_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_38_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_div_39_Template", "i_r22", "CorrectionTypeName", "CorrectionCategoryName", "ResolvedDate", "shouldShowEditResponseButton", "CorrectiveAction", "Comments", "corrections", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_div_3_Template", "comments", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template_div_click_1_listener", "_r16", "toggleReviewAccordion", "commentsId", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_20_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_23_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_24_Template", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_div_25_Template", "isReviewExpanded", "ɵɵstyleProp", "FailureFlag", "name", "status", "reviewer", "dueDate", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template_tr_click_1_listener", "i_r15", "_r14", "toggleSubmittalA<PERSON>rdion", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_div_28_Template", "isSubmittalExpanded", "sub_r23", "submittalStatus", "receivedDate", "getExternalReviewsForSubmittal", "id", "PermitViewComponent_div_2_ng_container_29_div_5_ng_container_4_Template", "PermitViewComponent_div_2_ng_container_29_div_3_Template", "PermitViewComponent_div_2_ng_container_29_div_4_Template", "PermitViewComponent_div_2_ng_container_29_div_5_Template", "PermitViewComponent_div_2_Template_button_click_12_listener", "_r1", "goBack", "PermitViewComponent_div_2_Template_a_click_18_listener", "PermitViewComponent_div_2_li_20_Template", "PermitViewComponent_div_2_li_21_Template", "PermitViewComponent_div_2_button_23_Template", "PermitViewComponent_div_2_div_24_Template", "PermitViewComponent_div_2_div_25_Template", "PermitViewComponent_div_2_ng_container_27_Template", "PermitViewComponent_div_2_ng_container_28_Template", "PermitViewComponent_div_2_ng_container_29_Template", "permitNumber", "permitReviewType", "permitName", "isInternalReviewEnabled", "isExternalReviewEnabled", "PermitViewComponent_ng_template_3_Template_i_click_7_listener", "_r24", "closModal", "PermitViewComponent_ng_template_3_Template_button_click_36_listener", "PermitViewComponent_ng_template_3_Template_button_click_39_listener", "editNotesandactions", "notesForm", "internalStatusArray", "PermitViewComponent", "route", "router", "modalService", "cdr", "fb", "appService", "customLayoutUtilsService", "permitsService", "permitId", "selectedAuditName", "selectedAuditStatus", "externalReviews", "internalReviews", "loginUser", "isAdmin", "singlePermit", "expandedSubmittals", "Set", "expandedReviews", "reviewSelectedTabs", "routeSubscription", "queryParamsSubscription", "statusList", "text", "value", "previousPage", "projectId", "constructor", "ngOnInit", "getLoggedInUser", "checkIfAdmin", "queryParams", "subscribe", "params", "Number", "console", "log", "paramMap", "idParam", "get", "fetchPermitDetails", "fetchInternalReviews", "loadForm", "group", "detectChanges", "ngOnDestroy", "unsubscribe", "get<PERSON><PERSON><PERSON>", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "data", "Object", "keys", "error", "faultMessage", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAllReviews", "reviews", "map", "r", "TypeName", "AssignedTo", "StatusText", "CompletedDate", "Date", "DueDate", "Corrections", "submittalId", "SubmittalId", "reviewCategory", "idToReviews", "for<PERSON>ach", "rv", "key", "String", "push", "items", "statusOrder", "reduce", "acc", "it", "a", "b", "find", "createdDate", "sort", "getTime", "err", "getInternalReviews", "take", "skip", "review", "reviewComments", "nonComplianceItems", "aeResponse", "grouped", "doc", "orientation", "unit", "format", "pageWidth", "internal", "pageSize", "getWidth", "margin", "left", "right", "top", "y", "setFont", "setFontSize", "addCategory", "category", "toUpperCase", "align", "reviewers", "Array", "from", "toString", "trim", "filter", "v", "join", "responsesDate", "toLocaleDateString", "rows", "idx", "startY", "head", "body", "styles", "font", "fontSize", "cellPadding", "valign", "overflow", "cellWidth", "headStyles", "fillColor", "textColor", "halign", "columnStyles", "theme", "lastAutoTable", "finalY", "getHeight", "addPage", "companyName", "pageCountInternal", "getNumberOfPages", "pageWidthInternal", "pageHeightInternal", "generatedOn", "toLocaleString", "i", "setPage", "setDrawColor", "line", "setTextColor", "fileName", "toISOString", "split", "save", "selectExternalSubmittal", "sel", "s", "reverse", "has", "delete", "add", "size", "clear", "_", "reviewId", "showReviewTab", "tab", "updateReviewResponse", "formData", "loggedInUserId", "userId", "updateExternalReview", "showSuccess", "message", "showError", "isSelectedSubmittal", "getReviewsForSelectedAudit", "navigate", "activeTab", "window", "open", "cityReviewLink", "openReviewDetails", "modalRef", "componentInstance", "permitDetails", "result", "then", "catch", "normalized", "toLowerCase", "replace", "aliasMap", "resolved", "finalClass", "styleMap", "backgroundColor", "color", "border", "type", "NgbModalOptions", "backdrop", "keyboard", "scrollable", "reviewIndex", "reviewData", "editPopUp", "isHideInternalReviewStatus", "passEntry", "saved", "autoLogin", "success", "editEx<PERSON><PERSON><PERSON><PERSON>ie<PERSON>", "correction", "responseSubmitted", "submitResponse", "responseCompleted", "emit", "close", "municipalityReviewer", "cityComments", "ownerResponse", "submittalCount", "cycle", "Cycle", "StatusName", "commentstatus", "displayDate", "pageHeight", "bottom", "headers", "rawCorrections", "correctionsArray", "isArray", "bodyRows", "c", "fontStyle", "didParseCell", "section", "column", "cell", "raw", "isApproved", "bg", "pageCount", "cityActionNeeded", "aocActionNeeded", "page<PERSON><PERSON><PERSON><PERSON><PERSON>er", "pageHeight<PERSON>ooter", "generated<PERSON>n<PERSON><PERSON>er", "e", "roleId", "lockResponse", "template", "patchValue", "dismissAll", "editNotesAndActions", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "NgbModal", "ChangeDetectorRef", "i3", "FormBuilder", "i4", "AppService", "i5", "CustomLayoutUtilsService", "i6", "PermitsService", "selectors", "decls", "vars", "consts", "PermitViewComponent_Template", "rf", "ctx", "PermitViewComponent_div_0_Template", "PermitViewComponent_div_2_Template", "PermitViewComponent_ng_template_3_Template", "ɵɵtemplateRefExtractor"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-view\\permit-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-view\\permit-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\nimport { ActivatedRoute, ActivationEnd, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\nimport { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ReviewDetailsModalComponent } from '../review-details-modal/review-details-modal.component';\nimport { ResponseModalComponent } from '../response-modal/response-modal.component';\nimport { AddEditInternalReviewComponent } from '../add-edit-internal-review/add-edit-internal-review.component';\nimport { PermitPopupComponent } from '../permit-popup/permit-popup.component';\nimport { PermitsService } from '../../services/permits.service';\nimport { EditExternalReviewComponent } from '../edit-external-review/edit-external-review.component';\nimport { AppService } from '../../services/app.service';\nimport jsPDF from 'jspdf';\nimport 'jspdf-autotable';\nimport { autoTable } from 'jspdf-autotable';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\n\n@Component({\n  selector: 'app-permit-view',\n  templateUrl: './permit-view.component.html',\n  styleUrls: ['./permit-view.component.scss'],\n})\nexport class PermitViewComponent implements OnInit, OnDestroy {\n    notesForm: FormGroup;\n  internalStatusArray =['Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed']\n\n  public permitId: number | null = null;\n  public permit: any = null;\n  public isLoading: boolean = false; // Main page loader\n  public auditEntries: any[] = [];\n  public selectedAuditIndex: number = 0; // Set to 0 to make first item initially active\n  public selectedAuditName: any = '';\n  public selectedAuditStatus: any = '';\n  selectedTab: any = 'details';\n  permitReviewType:any = '';\n  public externalReviews: any[] = [];\n  public reviewsError: string = '';\n  public externalSubmittals: Array<{ id: any; title: string; submittalStatus: string; receivedDate: Date | null; dueDate: Date | null; completedDate: Date | null; }> = [];\n  public selectedExternalSubmittalId: any = null;\n  public internalReviews: any[] = [];\n  public loginUser:any ={};\n  public isAdmin: boolean = false;\n  singlePermit: boolean;\n  public expandedSubmittals: Set<number> = new Set();\n  public expandedReviews: Set<string> = new Set();\n  public reviewSelectedTabs: { [key: string]: string } = {};\n  private routeSubscription: Subscription = new Subscription();\n  private queryParamsSubscription: Subscription = new Subscription();\nstatusList = [\n  { text: 'Pending', value: 'Pending' },\n  { text: 'In Progress', value: 'In Progress' },\n  { text: 'Completed', value: 'Completed' },\n  { text: 'On Hold', value: 'On Hold' }\n];\n  // Navigation tracking\n  public previousPage: string = 'permit-list'; // Default fallback\n  public projectId: number | null = null;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private modalService: NgbModal,\n    private cdr: ChangeDetectorRef,\n    private fb: FormBuilder,\n// private modal: NgbActiveModal,\n\n    private appService:AppService,\n        private customLayoutUtilsService: CustomLayoutUtilsService,\n\n    private permitsService: PermitsService\n  ) {}\n\n  ngOnInit(): void {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.isAdmin = this.checkIfAdmin();\n\n    // Read query parameters for navigation tracking\n    this.queryParamsSubscription = this.route.queryParams.subscribe(params => {\n      this.previousPage = params['from'] || 'permit-list';\n      this.projectId = params['projectId'] ? Number(params['projectId']) : null;\n      console.log('Permit view - query params:', { previousPage: this.previousPage, projectId: this.projectId });\n    });\n\n    // Listen for route parameter changes\n    this.routeSubscription = this.route.paramMap.subscribe(params => {\n      const idParam = params.get('id');\n      this.permitId = idParam ? Number(idParam) : null;\n\n      if (this.permitId) {\n        this.fetchPermitDetails();\n        this.fetchExternalReviews();\n        this.fetchInternalReviews();\n      }\n    });\n    this.loadForm()\n  }\n  loadForm() {\n    this.notesForm = this.fb.group({\n     attentionReason:[''],\n     internalNotes:[''],\n     actionTaken:[''],\n     internalReviewStatus:[null],\n    });\n\n    // Trigger change detection to update the view\n    this.cdr.detectChanges();\n  }\n  ngOnDestroy(): void {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n    if (this.queryParamsSubscription) {\n      this.queryParamsSubscription.unsubscribe();\n    }\n  }\n\n  public fetchPermitDetails(): void {\n    if (!this.permitId) { return; }\n    this.isLoading = true;\n    this.permitsService.getPermit({ permitId: this.permitId }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Permit API Response:', res);\n        if (!res?.isFault) {\n          this.permit = res.responseData?.data || res.responseData || null;\n          console.log('Permit data assigned:', this.permit);\n          console.log('Permit permitName field:', this.permit?.permitName);\n          console.log('All permit fields:', Object.keys(this.permit || {}));\n          this.permitReviewType = this.permit?.permitReviewType || '';\n          // Default to details tab, user can navigate to reviews as needed\n          this.selectedTab = 'details'\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.permit = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public fetchExternalReviews(): void {\n    if (!this.permitId) { return; }\n    this.isLoading = true;\n    this.reviewsError = '';\n    this.permitsService.getAllReviews({ permitId: this.permitId }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        if (res?.isFault) {\n          this.reviewsError = res.faultMessage || 'Failed to load reviews';\n        } else {\n          const reviews = res.responseData?.reviews || [];\n          this.externalReviews = reviews.map((r: any) => ({\n            commentsId:r.commentsId,\n            name: r.TypeName,\n            reviewer: r.AssignedTo,\n            status: r.StatusText,\n            completedDate: r.CompletedDate ? new Date(r.CompletedDate) : null,\n            dueDate: r.DueDate ? new Date(r.DueDate) : null,\n            receivedDate: r.receivedDate ? new Date(r.receivedDate) : null,\n            comments: r.Comments,\n            corrections: r.Corrections || [],\n            submittalId: r.SubmittalId,\n            FailureFlag: r.FailureFlag,\n            reviewCategory: r.reviewCategory,\n            EORAOROwner_Response: r.EORAOROwner_Response,\n            commentResponsedBy: r.commentResponsedBy\n          }));\n\n          // Build submittal list grouped from reviews\n          const idToReviews: { [key: string]: any[] } = {};\n          this.externalReviews.forEach((rv: any) => {\n            const key = String(rv.submittalId ?? 'unknown');\n            if (!idToReviews[key]) { idToReviews[key] = []; }\n            idToReviews[key].push(rv);\n          });\n\n          this.externalSubmittals = Object.keys(idToReviews).map((key) => {\n            const items = idToReviews[key];\n            // Determine status priority: Requires Re-submit > Under Review > Approved w/ Conditions > Approved\n            const statusOrder: any = {\n              'Requires Re-submit': 4,\n              'Under Review': 3,\n              'Approved w/ Conditions': 2,\n              'Approved': 1\n            };\n            const submittalStatus = items.reduce((acc: string, it: any) => {\n              const a = statusOrder[acc] || 0; const b = statusOrder[it.status] || 0; return b > a ? it.status : acc;\n            }, '');\n\n            // Aggregate dates\n            const dueDate = items.reduce((acc: Date | null, it: any) => {\n              if (!it.dueDate) { return acc; }\n              if (!acc) { return it.dueDate; }\n              return acc > it.dueDate ? it.dueDate : acc; // earliest due date\n            }, null as Date | null);\n\n            const completedDate = items.reduce((acc: Date | null, it: any) => {\n              if (!it.completedDate) { return acc; }\n              if (!acc) { return it.completedDate; }\n              return acc < it.completedDate ? it.completedDate : acc; // latest completed date\n            }, null as Date | null);\n\n            // Get received date from the first item that has it\n            const receivedDate = items.find((it: any) => it.receivedDate)?.receivedDate || \n                                items.find((it: any) => it.createdDate)?.createdDate || \n                                null;\n\n            // Get submittal name from the first item (all items in this group have same submittalId)\n            const reviewCategory = items[0]?.reviewCategory || `Submittal ${key}`;\n\n            return {\n              id: key,\n              title: reviewCategory,\n              submittalStatus: submittalStatus || (items[0]?.status || ''),\n              receivedDate: receivedDate ? new Date(receivedDate) : null,\n              dueDate: dueDate,\n              completedDate: completedDate\n            };\n          }).sort((a, b) => {\n            // Sort by received date in descending order (latest first)\n            if (!a.receivedDate && !b.receivedDate) return 0;\n            if (!a.receivedDate) return 1;\n            if (!b.receivedDate) return -1;\n            return b.receivedDate.getTime() - a.receivedDate.getTime();\n          });\n\n          // Select first submittal by default\n          if (this.externalSubmittals.length > 0) {\n            this.selectedExternalSubmittalId = this.externalSubmittals[0].id;\n            this.selectedAuditName = this.externalSubmittals[0].title;\n            this.selectedAuditStatus = this.externalSubmittals[0].submittalStatus;\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        this.reviewsError = 'Failed to load reviews';\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public fetchInternalReviews(): void {\n    if (!this.permitId) { return; }\n    this.isLoading = true;\n\n    this.permitsService.getInternalReviews({\n      permitId: this.permitId,\n      take: 50,\n      skip: 0\n    }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        if (res?.isFault) {\n          console.error('Failed to load internal reviews:', res.faultMessage);\n        } else {\n          this.internalReviews = res.responseData?.data || res.data || [];\n\n          // Transform internal reviews to match the auditEntries format\n          this.auditEntries = this.internalReviews.map((review: any) => ({\n            commentsId: review.commentsId,\n            title: review.reviewCategory,\n            reviewCategory: review.reviewCategory, // Preserve reviewCategory for edit modal\n            typeCodeDrawing: review.typeCodeDrawing,\n            reviewComments: review.reviewComments,\n            nonComplianceItems: review.nonComplianceItems,\n            aeResponse: review.aeResponse,\n            internalReviewer: review.internalReviewer,\n            internalVerificationStatus: review.internalVerificationStatus,\n            reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n            completedDate: review.completedDate ? new Date(review.completedDate) : null,\n            reviews: [{\n              name: review.reviewCategory,\n              typeCodeDrawing: review.typeCodeDrawing,\n              reviewComments: review.reviewComments,\n              nonComplianceItems: review.nonComplianceItems,\n              aeResponse: review.aeResponse,\n              internalReviewer: review.internalReviewer,\n              internalVerificationStatus: review.internalVerificationStatus,\n              reviewedDate: review.reviewedDate ? new Date(review.reviewedDate) : null,\n              completedDate: review.completedDate ? new Date(review.completedDate) : null\n            }]\n          }));\n\n          // Select first internal review by default\n          if (this.auditEntries.length > 0) {\n            this.selectedAuditIndex = 0;\n            this.selectedAuditName = this.auditEntries[0].title;\n            this.selectedAuditStatus = this.auditEntries[0].internalVerificationStatus || 'Pending';\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        console.error('Error loading internal reviews:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public downloadInternalReviewsPdf(): void {\n    if (!this.internalReviews || this.internalReviews.length === 0) { return; }\n\n    const grouped: { [category: string]: any[] } = {};\n    this.internalReviews.forEach((r: any) => {\n      const key = r.reviewCategory || 'Uncategorized';\n      if (!grouped[key]) { grouped[key] = []; }\n      grouped[key].push(r);\n    });\n\n    const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\n    const pageWidth = doc.internal.pageSize.getWidth();\n    // Use more horizontal space: reduce side margins to ~0.5 inch (36pt)\n    const margin = { left: 36, right: 36, top: 40 };\n    let y = margin.top;\n\n    // Add Permit # header at the top\n    doc.setFont('helvetica', 'bold');\n    doc.setFontSize(10);\n    doc.text(`Permit #: ${this.permit?.permitNumber || ''}` , margin.left, y);\n    y += 16;\n\n    const addCategory = (category: string, items: any[]) => {\n      // Category block title (centered, uppercase)\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`${category.toUpperCase()} REVIEW COMMENTS`, pageWidth / 2, y, {\n        align: 'center'\n      });\n      y += 12;\n\n      // Reviewer line (take distinct non-empty names, join with comma)\n      const reviewers = Array.from(new Set(items\n        .map((it: any) => (it.internalReviewer || '').toString().trim())\n        .filter((v: string) => v)));\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Reviewer: ${reviewers.join(', ') || ''}`, pageWidth / 2, y, { align: 'center' });\n      y += 12;\n\n      // Dates line (Reviewed Date / Responses Date)\n      const reviewedDate = items.find((it: any) => it.reviewedDate)?.reviewedDate;\n      const responsesDate = items.find((it: any) => it.completedDate)?.completedDate;\n      doc.setFont('helvetica', 'italic');\n      doc.setFontSize(9);\n      doc.text(\n        `Reviewed Date: ${reviewedDate ? new Date(reviewedDate).toLocaleDateString() : ''}`,\n        margin.left,\n        y\n      );\n      doc.text(\n        `Response Date: ${responsesDate ? new Date(responsesDate).toLocaleDateString() : ''}`,\n        pageWidth - margin.right,\n        y,\n        { align: 'right' }\n      );\n      y += 6;\n\n      const rows = items.map((it, idx) => [\n        (idx + 1).toString(),\n        it.typeCodeDrawing || '',\n        (it.reviewComments || '').toString(),\n        (it.aeResponse || '').toString(),\n        it.internalVerificationStatus || ''\n      ]);\n\n      autoTable(doc, {\n        startY: y + 5,\n        head: [[\n          '#',\n          'Drawing #',\n          'Review Comments',\n          'A/E Response',\n          'Status'\n        ]],\n        body: rows,\n        margin: { left: margin.left, right: margin.right },\n        styles: { \n          font: 'helvetica', \n          fontSize: 8, \n          cellPadding: 5, \n          valign: 'top',\n          overflow: 'linebreak',\n          cellWidth: 'wrap'\n        },\n        headStyles: { fillColor: [33, 150, 243], textColor: 255, halign: 'center', fontSize: 9 },\n        // Fit exactly into available width (pageWidth - margins)\n        // A4 width ~595pt; with 36pt margins each side → 523pt content width\n        columnStyles: {\n          0: { cellWidth: 24, halign: 'center', overflow: 'linebreak' },   // #\n          1: { cellWidth: 55, overflow: 'linebreak' },                     // Drawing # (even narrower)\n          2: { cellWidth: 198, overflow: 'linebreak' },                    // Review Comments (half of remaining)\n          3: { cellWidth: 197, overflow: 'linebreak' },                    // A/E Response (other half)\n          4: { cellWidth: 49, overflow: 'linebreak' }                      // Verification Status (fits)\n        },\n        theme: 'grid'\n      });\n\n      // update y for next section\n      // @ts-ignore\n      y = (doc as any).lastAutoTable.finalY + 20;\n\n      // add page if needed\n      if (y > doc.internal.pageSize.getHeight() - 100) {\n        doc.addPage();\n        y = margin.top;\n      }\n    };\n\n    Object.keys(grouped).forEach((category, idx) => {\n      addCategory(category, grouped[category]);\n    });\n\n    // Footer on each page: line + left date + centered company + right pagination\n    const companyName = 'Pacifica Engineering Services';\n    const pageCountInternal = doc.getNumberOfPages();\n    const pageWidthInternal = doc.internal.pageSize.getWidth();\n    const pageHeightInternal = doc.internal.pageSize.getHeight();\n    const generatedOn = new Date().toLocaleString();\n    for (let i = 1; i <= pageCountInternal; i++) {\n      doc.setPage(i);\n      doc.setDrawColor(200, 200, 200);\n      doc.line(margin.left, pageHeightInternal - 30, pageWidthInternal - margin.right, pageHeightInternal - 30);\n      doc.setFont('helvetica', 'normal');\n      doc.setFontSize(8);\n      doc.setTextColor(100, 100, 100);\n      doc.text(`Generated on: ${generatedOn}`, margin.left, pageHeightInternal - 15);\n      doc.text(companyName, pageWidthInternal / 2, pageHeightInternal - 15, { align: 'center' });\n      doc.text(`Page ${i} of ${pageCountInternal}`, pageWidthInternal - margin.right - 50, pageHeightInternal - 15);\n    }\n\n    const fileName = `Internal_Reviews_${this.permit?.permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\n    doc.save(fileName);\n  }\n\n  public selectExternalSubmittal(id: any): void {\n    this.selectedExternalSubmittalId = id;\n    const sel = this.externalSubmittals.find(s => String(s.id) === String(id));\n    if (sel) {\n      this.selectedAuditName = sel.title;\n      this.selectedAuditStatus = sel.submittalStatus;\n    }\n  }\n\n  public getExternalReviewsForSelectedSubmittal(): any[] {\n    if (!this.selectedExternalSubmittalId) { return []; }\n    return this.getExternalReviewsForSubmittal(this.selectedExternalSubmittalId);\n  }\n\n  public getExternalReviewsForSubmittal(submittalId: any): any[] {\n    const reviews = this.externalReviews.filter(r => String(r.submittalId) === String(submittalId));\n    \n    // Sort by FailureFlag: false reviews first (desc), then true reviews (desc)\n    return reviews.sort((a, b) => {\n      // If both have the same FailureFlag value, maintain original order (reverse for desc)\n      if (a.FailureFlag === b.FailureFlag) {\n        return 0;\n      }\n      \n      // False reviews (FailureFlag = false) come first\n      if (!a.FailureFlag && b.FailureFlag) {\n        return -1;\n      }\n      \n      // True reviews (FailureFlag = true) come after false reviews\n      if (a.FailureFlag && !b.FailureFlag) {\n        return 1;\n      }\n      \n      return 0;\n    }).reverse(); // Reverse to get descending order within each group\n  }\n\n  public toggleSubmittalAccordion(index: number): void {\n    if (this.expandedSubmittals.has(index)) {\n      this.expandedSubmittals.delete(index);\n    } else {\n      this.expandedSubmittals.add(index);\n    }\n  }\n\n  public isSubmittalExpanded(index: number): boolean {\n    return this.expandedSubmittals.has(index);\n  }\n\n  public areAllSubmittalsExpanded(): boolean {\n    return this.externalSubmittals && this.externalSubmittals.length > 0 && this.expandedSubmittals.size === this.externalSubmittals.length;\n  }\n\n  public toggleAllSubmittals(): void {\n    if (!this.externalSubmittals || this.externalSubmittals.length === 0) {\n      return;\n    }\n    if (this.areAllSubmittalsExpanded()) {\n      this.expandedSubmittals.clear();\n    } else {\n      this.expandedSubmittals = new Set(this.externalSubmittals.map((_, idx) => idx));\n    }\n    this.cdr.markForCheck();\n  }\n\n  public toggleReviewAccordion(reviewId: string): void {\n    if (this.expandedReviews.has(reviewId)) {\n      this.expandedReviews.delete(reviewId);\n    } else {\n      this.expandedReviews.add(reviewId);\n      // Set default tab for this review if not already set\n      if (!this.reviewSelectedTabs[reviewId]) {\n        this.reviewSelectedTabs[reviewId] = 'corrections';\n      }\n    }\n  }\n\n  public isReviewExpanded(reviewId: string): boolean {\n    return this.expandedReviews.has(reviewId);\n  }\n\n  public showReviewTab(reviewId: string, tab: string, $event: any): void {\n    $event.stopPropagation();\n    this.reviewSelectedTabs[reviewId] = tab;\n    this.cdr.markForCheck();\n  }\n\n  public updateReviewResponse(review: any): void {\n    this.isLoading = true;\n    const formData = {\n      EORAOROwner_Response: review.EORAOROwner_Response,\n      commentResponsedBy: review.commentResponsedBy,\n      permitId: this.permitId,\n      commentsId: review.commentsId,\n      loggedInUserId: this.loginUser.userId\n    };\n\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh external reviews to get updated data\n          this.fetchExternalReviews();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error syncing permit', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('❌ Error updating review response', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public isSelectedSubmittal(submittalId: any): boolean {\n    return String(this.selectedExternalSubmittalId) === String(submittalId);\n  }\n\n  public selectAudit(index: number): void {\n    this.selectedAuditIndex = index;\n    this.selectedAuditName = this.auditEntries[this.selectedAuditIndex].title;\n    this.selectedAuditStatus =\n      this.auditEntries[this.selectedAuditIndex].submittalStatus;\n  }\n\n  public getReviewsForSelectedAudit(): any[] {\n    if (\n      this.selectedAuditIndex === null ||\n      this.selectedAuditIndex >= this.auditEntries.length\n    ) {\n      return [];\n    }\n\n    return this.auditEntries[this.selectedAuditIndex].reviews || [];\n  }\n\n\n\n  public goBack(): void {\n    console.log('goBack called - previousPage:', this.previousPage, 'projectId:', this.projectId);\n    if (this.previousPage === 'project' && this.projectId) {\n      // Navigate back to the specific project view with permits tab active\n      console.log('Navigating to project view with permits tab active');\n      this.router.navigate(['/projects/view', this.projectId], { \n        queryParams: { activeTab: 'permits' } \n      });\n    } else {\n      // Default to permit list\n      console.log('Navigating to permit list');\n      this.router.navigate(['/permits/list']);\n    }\n  }\n\n  public goToPortal(): void {\n    window.open(\n      `${this.permit.cityReviewLink + this.permit.permitEntityID}`,\n      '_blank'\n    );\n  }\n\n  public openReviewDetails(review: any): void {\n    const modalRef = this.modalService.open(ReviewDetailsModalComponent, {\n      size: 'lg',\n    });\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.permitDetails = this.permit;\n\n    // Handle modal result\n    modalRef.result.then((result) => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch((error) => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n\n  public getStatusClass(status: string): string {\n    if (!status) return 'status-n-a';\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    console.log('Status mapping - Original:', status, 'Normalized:', normalized);\n    // Map common synonyms from API to a single class we style\n    const aliasMap: { [k: string]: string } = {\n      'in-review': 'in-review',\n      'requires-re-submit': 'requires-re-submit',\n      'approved-w-conditions': 'approved-w-conditions',\n      'in-progress': 'in-progress',\n      'completed': 'completed',\n      'verified': 'verified',\n      'pending': 'pending',\n      'rejected': 'rejected',\n      'approved': 'approved',\n      'under-review': 'under-review',\n      'requires-resubmit': 'requires-resubmit',\n      'pacifica-verification': 'pacifica-verification',\n      'dis-approved': 'dis-approved',\n      'not-required': 'not-required',\n      '1-cycle-completed': '1-cycle-completed',\n      '1 cycle completed': '1-cycle-completed',\n      'cycle completed': '1-cycle-completed'\n    };\n    const resolved = aliasMap[normalized] || normalized;\n    const finalClass = 'status-' + resolved;\n    console.log('Final status class:', finalClass);\n    console.log('Available CSS classes for debugging:', [\n      'status-pending', 'status-in-progress', 'status-completed', 'status-verified',\n      'status-rejected', 'status-approved', 'status-under-review', 'status-requires-resubmit',\n      'status-pacifica-verification', 'status-dis-approved', 'status-not-required',\n      'status-in-review', 'status-1-cycle-completed'\n    ]);\n    return finalClass;\n  }\n\n  public getStatusStyle(status: string): any {\n    if (!status) return {};\n    const normalized = status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n    \n    const styleMap: { [k: string]: any } = {\n      '1-cycle-completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\n      '1 cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\n      'cycle completed': { backgroundColor: '#e8f5e8', color: '#2e7d32', border: '1px solid #a5d6a7' },\n      'pacifica-verification': { backgroundColor: '#e1f5fe', color: '#0277bd', border: '1px solid #81d4fa' },\n      'dis-approved': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' },\n      'not-required': { backgroundColor: '#f5f5f5', color: '#757575', border: '1px solid #e0e0e0' },\n      'in-review': { backgroundColor: '#e8eaf6', color: '#3949ab', border: '1px solid #c5cae9' },\n      'pending': { backgroundColor: '#fff3e0', color: '#e65100', border: '1px solid #ffcc02' },\n      'approved': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },\n      'completed': { backgroundColor: '#e8f5e8', color: '#1b5e20', border: '1px solid #c8e6c9' },\n      'rejected': { backgroundColor: '#ffebee', color: '#c62828', border: '1px solid #ffcdd2' }\n    };\n    \n    return styleMap[normalized] || {};\n  }\n\n  showTab(tab: any, $event: any) {\n    if (tab === 'internal' && !this.isInternalReviewEnabled()) {\n      return;\n    }\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n\n  public isInternalReviewEnabled(): boolean {\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n    // show only when type is 'internal' or 'both'\n    return type === 'internal' || type === 'both';\n  }\n\n  public isExternalReviewEnabled(): boolean {\n    const type = (this.permitReviewType || this.permit?.permitReviewType || '').toString().toLowerCase();\n    // show only when type is 'external' or 'both'\n    return type === 'external' || type === 'both';\n  }\n\n  addPopUp() {\n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(\n      AddEditInternalReviewComponent,\n      NgbModalOptions\n    );\n\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n\n    // Handle modal result\n    modalRef.result.then((result) => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch((error) => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n\n  editInternalReview(reviewIndex: number) {\n    if (reviewIndex < 0 || reviewIndex >= this.auditEntries.length) {\n      return;\n    }\n\n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false,\n      scrollable: true,\n    };\n\n    const modalRef = this.modalService.open(\n      AddEditInternalReviewComponent,\n      NgbModalOptions\n    );\n\n    // Pass data to the modal for editing\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = this.auditEntries[reviewIndex];\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n    modalRef.componentInstance.permitNumber = this.permit?.permitNumber || '';\n\n    // Handle modal result\n    modalRef.result.then((result) => {\n      if (result === 'updated') {\n        // Refresh internal reviews\n        this.fetchInternalReviews();\n      }\n    }).catch((error) => {\n      console.log('Modal dismissed');\n    });\n  }\n\n  editPopUp() {\n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(\n      AddEditInternalReviewComponent,\n      NgbModalOptions\n    );\n  }\n  editPermit() {\n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(\n      PermitPopupComponent,\n      NgbModalOptions\n    );\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.permitId;\n    modalRef.componentInstance.isHideInternalReviewStatus = false;\n    \n    // Listen for passEntry event to refresh permit details when permit is saved\n    modalRef.componentInstance.passEntry.subscribe((saved: boolean) => {\n      if (saved) {\n        this.fetchPermitDetails();\n      }\n    });\n  }\n\n  syncPermits(i: any) {\n    this.isLoading = true;\n    this.singlePermit = i || false;\n    this.permitsService.syncPermits({ permitId: this.permitId, singlePermit: this.singlePermit, autoLogin: true }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Sync response:', res);\n        console.log('Response type:', typeof res);\n        console.log('Response keys:', Object.keys(res || {}));\n        console.log('Response success:', res?.success);\n        console.log('Response message:', res?.message);\n        console.log('Response responseData:', res?.responseData);\n        \n        // Handle various response structures\n        let responseData = res;\n        \n        // Check different possible response structures\n        if (res?.responseData) {\n          responseData = res.responseData;\n        } else if (res?.body?.responseData) {\n          responseData = res.body.responseData;\n        } else if (res?.body) {\n          responseData = res.body;\n        }\n        \n        console.log('Final responseData:', responseData);\n        console.log('Final success:', responseData?.success);\n        console.log('Final message:', responseData?.message);\n        \n        if (responseData?.isFault) {\n          //alert(responseData.faultMessage || 'Failed to sync permit');\n          this.customLayoutUtilsService.showError(responseData.faultMessage, '');\n        } else if (responseData?.success === false) {\n          // Handle specific error messages from the API\n          if (responseData.message === 'Permit not found in Energov system') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (responseData.message === 'No permits found for any keywords') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert(`❌ ${responseData.message || 'Failed to sync permit'}`);\n          }\n        } else if (responseData?.success === true || responseData?.data) {\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n          //alert('✅ Permit synced successfully');\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        } else {\n          // Fallback for unknown response structure\n          console.log('Unknown response structure, showing generic success');\n          //alert('✅ Permit synced successfully');\n          this.customLayoutUtilsService.showSuccess('Permit synced successfully', '');\n\n          this.fetchPermitDetails();\n          this.fetchExternalReviews();\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        // Handle HTTP error responses\n        console.log('Error response:', err);\n        console.log('Error type:', typeof err);\n        console.log('Error keys:', Object.keys(err || {}));\n        console.log('Error status:', err?.status);\n        console.log('Error message:', err?.message);\n        console.log('Error error:', err?.error);\n        \n        // The interceptor passes err.error to the error handler\n        // So err might actually be the response data\n        if (err?.success === false) {\n          // Handle specific error messages from the API\n          if (err.message === 'Permit not found in Energov system') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.message === 'No permits found for any keywords') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert(`❌ ${err.message || 'Failed to sync permit'}`);\n          }\n        } else if (err?.error?.message) {\n          if (err.error.message === 'Permit not found in Energov system') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n          } else if (err.error.message === 'No permits found for any keywords') {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert('❌ No permits found in Energov system. Please verify the permit numbers and try again.');\n          } else {\n                                    this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n            //alert(`❌ ${err.error.message}`);\n          }\n        } else if (err?.status === 404) {\n                                  this.customLayoutUtilsService.showError('❌ Permit not found in Energov system. Please verify the permit number and try again.', '');\n\n          // Handle 404 specifically for permit not found\n          //alert('❌ Permit not found in Energov system. Please verify the permit number and try again.');\n        } else {\n          //alert('❌ Error syncing permit');\n        }\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  editExternalReview(review:any) {\n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the AddEditInternalReviewComponent\n    const modalRef = this.modalService.open(\n      EditExternalReviewComponent,\n      NgbModalOptions\n    );\n\n    console.log('reviewData ', review)\n    // Pass data to the modal\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.reviewData = review;\n    modalRef.componentInstance.permitDetails = this.permit;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId; // Replace with actual logged in user ID\n\n    // Handle modal result\n    modalRef.result.then((result) => {\n      if (result === 'created' || result === 'updated') {\n        // Refresh internal reviews\n        this.fetchExternalReviews();\n      }\n    }).catch((error) => {\n      // Modal was dismissed\n      console.log('Modal dismissed');\n    });\n  }\n\n  public openResponseModal(correction: any, review: any): void {\n    // Open the modal using NgbModal\n    const modalRef = this.modalService.open(ResponseModalComponent, {\n      size: 'lg',\n      backdrop: 'static',\n      keyboard: false\n    });\n\n    // Pass data to the modal\n    modalRef.componentInstance.correction = correction;\n    modalRef.componentInstance.review = review;\n    modalRef.componentInstance.permitId = this.permitId;\n    modalRef.componentInstance.loggedInUserId = this.loginUser.userId;\n    modalRef.componentInstance.isAdmin = this.isAdmin;\n\n    // Handle modal result\n    modalRef.componentInstance.responseSubmitted.subscribe((formData: any) => {\n      this.submitResponse(formData, modalRef);\n    });\n\n    // Handle response completion to reset loading state\n    modalRef.componentInstance.responseCompleted.subscribe((success: boolean) => {\n      if (!success) {\n        // Reset loading state if submission failed\n        modalRef.componentInstance.isLoading = false;\n      }\n    });\n\n    modalRef.result.then(() => {\n      // Modal was closed\n    }).catch(() => {\n      // Modal was dismissed\n    });\n  }\n\n  public submitResponse(formData: any, modalRef?: any): void {\n    if (!formData) {\n      return;\n    }\n\n    this.isLoading = true;\n\n    this.permitsService.updateExternalReview(formData).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        if (res?.isFault === false) {\n                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n\n          //alert(res.responseData.message || 'Response submitted successfully');\n          this.fetchExternalReviews(); // Refresh external reviews to get updated data\n          \n          // Emit success completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(true);\n          }\n          \n          // Close the modal if it was passed\n          if (modalRef) {\n            modalRef.close();\n          }\n        } else {\n          //alert(res.faultMessage || 'Failed to submit response');\n                                  this.customLayoutUtilsService.showError(res.faultMessage||'Failed to submit response', '');\n\n          // Emit failure completion event\n          if (modalRef && modalRef.componentInstance) {\n            modalRef.componentInstance.responseCompleted.emit(false);\n          }\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        //alert('Error submitting response');\n        console.error(err);\n                                this.customLayoutUtilsService.showSuccess('Error submitting response', '');\n\n        // Emit failure completion event\n        if (modalRef && modalRef.componentInstance) {\n          modalRef.componentInstance.responseCompleted.emit(false);\n        }\n        \n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public downloadReviewPDF(review: any): void {\n    if (!review) {\n      return;\n    }\n\n    try {\n      const permitNumber = this.permit?.permitNumber || '';\n      const reviewer = (review?.AssignedTo || review?.municipalityReviewer || review?.reviewer || '').toString();\n      const cityComments = (review?.Comments || (review?.cityComments ?? '')).toString();\n      const ownerResponse = (review?.EORAOROwner_Response ?? '').toString();\n      \n      // Calculate submittal count for this review\n      const submittalCount = this.externalSubmittals ? this.externalSubmittals.length : 0;\n      const cycle = submittalCount > 0 ? submittalCount.toString() : (review?.cycle || review?.Cycle || '').toString();\n      \n      const status = (review?.StatusName || review?.commentstatus || review?.status || '').toString();\n      const displayDate = (review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate)\n        ? new Date(review?.CompletedDate || review?.completedDate || review?.DueDate || review?.createdDate).toLocaleDateString()\n        : new Date().toLocaleDateString();\n\n      const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\n\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const pageHeight = doc.internal.pageSize.getHeight();\n      const margin = { left: 40, right: 40, top: 40, bottom: 40 };\n\n      // Header: Permit #\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Permit #: ${permitNumber || ''}`, margin.left, margin.top);\n\n      const startY = margin.top + 20;\n\n      // Table with one row matching the screenshot\n      const headers = [\n        'REVIEWED BY',\n        'CITY COMMENTS',\n        'EOR/AOR/OWNER COMMENT RESPONSE',\n        'CYCLE',\n        'STATUS',\n        'DATE'\n      ];\n\n      // Build body rows. If there are corrections, show one row per correction; otherwise single row\n      const rawCorrections: any = (review && (review as any).Corrections) ?? (review as any).corrections ?? [];\n      const correctionsArray = Array.isArray(rawCorrections) ? rawCorrections : (rawCorrections ? [rawCorrections] : []);\n      const bodyRows: any[] = correctionsArray.length > 0\n        ? correctionsArray.map((c: any) => [\n            reviewer || '',\n            c?.Comments || cityComments || '',\n            c?.Response || c?.EORAOROwner_Response || ownerResponse || '',\n            cycle || '',\n            status || '',\n            (c?.ResolvedDate ? new Date(c.ResolvedDate).toLocaleDateString() : displayDate) || ''\n          ])\n        : [[\n            reviewer || '',\n            cityComments || '',\n            ownerResponse || '',\n            cycle || '',\n            status || '',\n            displayDate || ''\n          ]];\n\n      autoTable(doc, {\n        startY,\n        head: [headers],\n        body: bodyRows,\n        margin: { left: margin.left, right: margin.right },\n        styles: { \n          font: 'helvetica', \n          fontSize: 8, \n          cellPadding: 5, \n          overflow: 'linebreak',\n          cellWidth: 'wrap'\n        },\n        headStyles: { fillColor: [240, 240, 240], textColor: [0, 0, 0], fontStyle: 'bold', fontSize: 8 },\n        columnStyles: {\n          0: { cellWidth: 90, overflow: 'linebreak' },\n          1: { cellWidth: (pageWidth - margin.left - margin.right) * 0.26, overflow: 'linebreak' },\n          2: { cellWidth: (pageWidth - margin.left - margin.right) * 0.24, overflow: 'linebreak' },\n          3: { cellWidth: 45, halign: 'center', overflow: 'linebreak' },\n          4: { cellWidth: 60, halign: 'center', overflow: 'linebreak' },\n          5: { cellWidth: 60, halign: 'center', overflow: 'linebreak' }\n        },\n        didParseCell: (data: any) => {\n          // City comments text in red\n          if (data.section === 'body' && data.column.index === 1) {\n            data.cell.styles.textColor = [192, 0, 0];\n          }\n          \n          // Set background color and text color for status column\n          if (data.section === 'body' && data.column.index === 4) {\n            const value = String(data.cell.raw || '');\n            const isApproved = value.toLowerCase() === 'approved';\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\n            const textColor = [255, 255, 255];\n            \n            data.cell.styles.fillColor = bg;\n            data.cell.styles.textColor = textColor;\n            data.cell.styles.fontStyle = 'bold';\n            data.cell.styles.halign = 'center';\n            data.cell.styles.valign = 'middle';\n          }\n        },\n        theme: 'grid'\n      });\n\n      // Footer (existing info) + company name\n      const pageCount = doc.getNumberOfPages();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);\n        doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);\n        // Company name centered\n        doc.text('Pacifica Engineering Services', pageWidth / 2, pageHeight - 15, { align: 'center' });\n      }\n\n      const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n      \n    } catch (error) {\n      console.error('Error generating PDF:', error);\n                              this.customLayoutUtilsService.showSuccess('Error generation PDF. Please try agaiin', '');\n\n      //alert('Error generating PDF. Please try again.');\n    }\n  }\n\n  public downloadExternalReviewsPdf(): void {\n    // Generate a PDF for the currently selected submittal's reviews in a one-row-per-review format\n    if (!this.selectedExternalSubmittalId) { return; }\n    const reviews = this.getExternalReviewsForSelectedSubmittal();\n    if (!reviews || reviews.length === 0) { return; }\n\n    try {\n      const permitNumber = this.permit?.permitNumber || '';\n      const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });\n\n      const pageWidth = doc.internal.pageSize.getWidth();\n      const margin = { left: 36, right: 36, top: 40 };\n\n      // Header\n      doc.setFont('helvetica', 'bold');\n      doc.setFontSize(10);\n      doc.text(`Permit #: ${permitNumber}`, margin.left, margin.top);\n\n      const headers = [\n        'DISCIPLINE',\n        'REVIEWER',\n        'STATUS',\n        'VERSION',\n        'CYCLE DATE',\n        'CITY ACTION\\nNEEDED',\n        'AEC ACTION\\nNEEDED'\n      ];\n\n      const body = reviews.map((r: any) => [\n        r?.name || '',\n        (r?.AssignedTo || r?.municipalityReviewer || r?.reviewer || '').toString(),\n        (r?.StatusName || r?.commentstatus || r?.status || '').toString(),\n        (this.externalSubmittals?.length || r?.cycle || r?.Cycle || '').toString(),\n        (r?.CompletedDate || r?.completedDate || r?.DueDate || r?.createdDate)\n          ? new Date(r?.CompletedDate || r?.completedDate || r?.DueDate || r?.createdDate).toLocaleDateString()\n          : '',\n        (r?.cityActionNeeded ? 'YES' : 'NO'),\n        (r?.aocActionNeeded ? 'YES' : (r?.EORAOROwner_Response ? 'NO' : 'YES'))\n      ]);\n\n      autoTable(doc, {\n        startY: margin.top + 18,\n        head: [headers],\n        body,\n        margin: { left: margin.left, right: margin.right },\n        styles: { \n          font: 'helvetica', \n          fontSize: 8, \n          cellPadding: 5, \n          overflow: 'linebreak', \n          valign: 'top',\n          cellWidth: 'wrap'\n        },\n        headStyles: { fillColor: [33, 150, 243], textColor: 255, halign: 'center', fontSize: 7, cellPadding: 3 },\n        columnStyles: {\n          // Use remaining width across first two columns (adjusted to make Version fit)\n          0: { cellWidth: 90, overflow: 'linebreak' },\n          1: { cellWidth: 103, overflow: 'linebreak' },\n          2: { cellWidth: 65, halign: 'center', overflow: 'linebreak' },\n          // Keep Version in a single line (widened to avoid header wrap)\n          3: { cellWidth: 50, halign: 'center', overflow: 'ellipsize' },\n          4: { cellWidth: 65, halign: 'center', overflow: 'linebreak' },\n          5: { cellWidth: 60, halign: 'center', overflow: 'linebreak' },\n          6: { cellWidth: 65, halign: 'center', overflow: 'linebreak' }\n        },\n        didParseCell: (data: any) => {\n          // Set background color and text color for status column\n          if (data.section === 'body' && data.column.index === 2) {\n            const value = String(data.cell.raw || '');\n            const isApproved = value.toLowerCase() === 'approved';\n            const bg = isApproved ? [22, 163, 74] : [220, 53, 69];\n            const textColor = [255, 255, 255];\n            \n            data.cell.styles.fillColor = bg;\n            data.cell.styles.textColor = textColor;\n            data.cell.styles.fontStyle = 'bold';\n            data.cell.styles.halign = 'center';\n            data.cell.styles.valign = 'middle';\n          }\n        },\n        theme: 'grid'\n      });\n\n      // Footer on each page: line + left date + centered company + right pagination\n      const companyName = 'Pacifica Engineering Services';\n      const pageCount = doc.getNumberOfPages();\n      const pageWidthFooter = doc.internal.pageSize.getWidth();\n      const pageHeightFooter = doc.internal.pageSize.getHeight();\n      const generatedOnFooter = new Date().toLocaleString();\n      for (let i = 1; i <= pageCount; i++) {\n        doc.setPage(i);\n        doc.setDrawColor(200, 200, 200);\n        doc.line(margin.left, pageHeightFooter - 30, pageWidthFooter - margin.right, pageHeightFooter - 30);\n        doc.setFont('helvetica', 'normal');\n        doc.setFontSize(8);\n        doc.setTextColor(100, 100, 100);\n        doc.text(`Generated on: ${generatedOnFooter}`, margin.left, pageHeightFooter - 15);\n        doc.text(companyName, pageWidthFooter / 2, pageHeightFooter - 15, { align: 'center' });\n        doc.text(`Page ${i} of ${pageCount}`, pageWidthFooter - margin.right - 50, pageHeightFooter - 15);\n      }\n\n      const fileName = `External_Reviews_${permitNumber || ''}_${new Date().toISOString().split('T')[0]}.pdf`;\n      doc.save(fileName);\n    } catch (e) {\n      console.error('Error generating external reviews PDF', e);\n    }\n  }\n\n  public checkIfAdmin(): boolean {\n    // Check if the user is an admin based on roleId\n    // Assuming roleId 1 is admin - adjust this based on your role system\n    return this.loginUser && this.loginUser.roleId === 1;\n  }\n\n  public shouldShowEditResponseButton(correction: any): boolean {\n    // Show edit response button if:\n    // 1. User is admin (can always edit)\n    // 2. User is not admin but lockResponse is false (unlocked by admin)\n    if (this.isAdmin) {\n      return true;\n    }\n    \n    // For non-admin users, only show if lockResponse is explicitly false\n    return correction.lockResponse === false;\n  }\n  onEdit(template: any){\n const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n    this.modalService.open(template,NgbModalOptions)\n\n    // console.log(\"this.permit\", this.permit)\n    this.notesForm.patchValue({\n      actionTaken:this.permit.actionTaken,\n      attentionReason:this.permit.attentionReason,\n      internalNotes:this.permit.internalNotes,\n      internalReviewStatus:this.permit.internalReviewStatus,\n      // actionTaken:'helo',\n    })\n\n  }\n\n  closModal(){\n    this.modalService.dismissAll()\n  }\n\n  \n  public editNotesandactions(): void {\n    this.isLoading = true;\n    const formData = {\n     permitId: this.permitId,\n      actionTaken: this.notesForm.value.actionTaken,\n      internalNotes: this.notesForm.value.internalNotes,\n      attentionReason: this.notesForm.value.attentionReason,\n      internalReviewStatus: this.notesForm.value.internalReviewStatus,\n      \n    };\n\n    this.permitsService.editNotesAndActions(formData).subscribe({\n      next: (res: any) => {\n        this.closModal()\n        this.isLoading = false;\n        if (res?.isFault === false) {\n          //alert(res.responseData.message);\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          // Refresh permit details to get updated data from server\n          this.fetchPermitDetails();\n        } else {\n          this.customLayoutUtilsService.showError(res.faultMessage||'❌ Error in update notes and actions', '');\n          //alert(res.faultMessage || 'Failed to update review response');\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        this.customLayoutUtilsService.showError('❌ Error in update notes and actions', '');\n        //alert('Error updating review response');\n        console.error(err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n}\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"permit-view-container\">\r\n  <!-- Permit Details Card -->\r\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"permit\">\r\n    <!-- Permit Details Header -->\r\n    <div class=\"permit-details-header\">\r\n      <div class=\"header-content w-100\">\r\n        <div class=\"title-wrap\">\r\n          <div class=\"title-line\">\r\n            <span class=\"permit-title\">Permit # {{\r\n              permit.permitNumber || \"\"\r\n              }}</span>\r\n            <span class=\"status-text status-under-review\">{{ permit.permitReviewType || \"\"\r\n              }}</span>\r\n          </div>\r\n          <div class=\"permit-number-line\">\r\n            {{ permit.permitName || \"\" }}\r\n          </div>\r\n        </div>\r\n        <div class=\"button-group\">\r\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center\" (click)=\"goBack()\">\r\n            <i class=\"fas fa-arrow-left me-2\"></i>\r\n            Back\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- Card Header with Tabs -->\r\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\r\n      <!-- Tabs -->\r\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\r\n            (click)=\"showTab('details', $event)\">\r\n            Permit Details\r\n          </a>\r\n        </li>\r\n        <li class=\"nav-item\" *ngIf=\"isInternalReviewEnabled()\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'internal' }\"\r\n            (click)=\"showTab('internal', $event)\">\r\n            Internal Reviews\r\n          </a>\r\n        </li>\r\n        <li class=\"nav-item\" *ngIf=\"isExternalReviewEnabled()\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'external' }\"\r\n            (click)=\"showTab('external', $event)\">\r\n            External Reviews\r\n          </a>\r\n        </li>\r\n      </ul>\r\n\r\n      <!-- Right side buttons -->\r\n      <div class=\"d-flex align-items-center gap-2\" style=\"margin-right: 16px;\">\r\n        <!-- Edit icon - only show when permit details tab is active -->\r\n        <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"editPermit()\" *ngIf=\"selectedTab === 'details'\" title=\"Edit Permit\">\r\n          <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\r\n        </button>\r\n      </div>\r\n\r\n      <!-- Action Buttons -->\r\n      <div class=\"button-group\" *ngIf=\"selectedTab === 'internal' && isInternalReviewEnabled()\">\r\n        <button type=\"button\" class=\"btn btn-link p-0 me-3\" (click)=\"downloadInternalReviewsPdf()\" [disabled]=\"isLoading || auditEntries.length === 0\" title=\"Download Internal Reviews PDF\">\r\n          <i class=\"fas fa-download download-pdf-icon\" style=\"color:#3699ff\"></i>\r\n        </button>\r\n        <button class=\"btn btn-success btn-sm\" (click)=\"addPopUp()\" [disabled]=\"isLoading\">\r\n          <i class=\"bi bi-plus-lg\"></i>Add Review\r\n        </button>\r\n      </div>\r\n       <div class=\"button-group\" *ngIf=\"selectedTab === 'external' && isExternalReviewEnabled()\">\r\n         <button type=\"button\" class=\"btn btn-link p-0 me-3\" (click)=\"downloadExternalReviewsPdf()\" [disabled]=\"isLoading || !selectedExternalSubmittalId || getExternalReviewsForSelectedSubmittal().length === 0\" title=\"Download External Reviews PDF\">\r\n           <i class=\"fas fa-download download-pdf-icon\" style=\"color:#3699ff\"></i>\r\n         </button>\r\n         <button type=\"button\" class=\"btn btn-secondary btn-sm me-3\" (click)=\"toggleAllSubmittals()\" [disabled]=\"isLoading || externalSubmittals.length === 0\" [title]=\"areAllSubmittalsExpanded() ? 'Collapse all submittals' : 'Expand all submittals'\">\r\n           <i class=\"fas\" [ngClass]=\"areAllSubmittalsExpanded() ? 'fa-compress-arrows-alt' : 'fa-expand-arrows-alt'\"></i>\r\n           <span class=\"ms-1\">{{ areAllSubmittalsExpanded() ? 'Collapse All' : 'Expand All' }}</span>\r\n         </button>\r\n         <button type=\"button\" class=\"btn btn-primary btn-sm me-3\" (click)=\"syncPermits(true)\" [disabled]=\"isLoading\">\r\n           <i class=\"fas fa-sync-alt\"></i> Sync\r\n         </button>\r\n         <button type=\"button\" class=\"btn btn-secondary btn-sm\" \r\n                 (click)=\"goToPortal()\" \r\n                 [disabled]=\"!permit?.permitEntityID\"\r\n                 [title]=\"permit?.permitEntityID ? 'Open Portal' : 'Portal not available - Permit Entity ID required'\">\r\n           <i class=\"fas fa-external-link-alt\"></i> Portal\r\n         </button>\r\n       </div>\r\n    </div>\r\n\r\n\r\n    <!-- Card Body with Tab Content -->\r\n    <div class=\"card-body\">\r\n      <!-- Permit Details Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'details' && permit\">\r\n      <div class=\" permit-details-content\">\r\n        <div class=\"permit-details-grid\">\r\n          <div class=\"permit-detail-item\">\r\n            <label>Project Name</label>\r\n            <span class=\"permit-value\">{{ permit.projectName || \"\" }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Permit Type</label>\r\n            <span class=\"permit-value\">{{ permit.permitType || \"\" }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Primary Contact</label>\r\n            <span class=\"permit-value\">{{\r\n              permit.primaryContact || \"\"\r\n              }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Permit Status</label>\r\n            <span class=\"status-text\" [ngClass]=\"getStatusClass(permit.permitStatus)\">{{ permit.permitStatus || \"\"\r\n            }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Location</label>\r\n            <span class=\"permit-value\">{{ permit.location || \"\" }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Category</label>\r\n            <span class=\"permit-value\">{{\r\n              permit.permitCategory || \"\"\r\n              }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Issue Date</label>\r\n            <span class=\"permit-value\">{{\r\n              permit.permitIssueDate\r\n              ? (permit.permitIssueDate | date : \"MM/dd/yyyy\")\r\n              : \"\"\r\n              }}</span>\r\n            <span class=\"text-gray-500 fs-7 p-0\"> Applied on {{\r\n              permit.permitAppliedDate\r\n              ? (permit.permitAppliedDate | date : \"MM/dd/yyyy\")\r\n              : \"\"\r\n              }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Expiration Date</label>\r\n            <span class=\"permit-value\">{{\r\n              permit.permitExpirationDate\r\n              ? (permit.permitExpirationDate | date : \"MM/dd/yyyy\")\r\n              : \"\"\r\n              }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Final Date</label>\r\n            <span class=\"permit-value\">{{\r\n              permit.permitFinalDate \r\n              ? (permit.permitFinalDate | date : \"MM/dd/yyyy\")\r\n              : \"\"\r\n              }}</span>\r\n          </div>\r\n          <div class=\"permit-detail-item\">\r\n            <label>Complete Date</label>\r\n            <span class=\"permit-value\">{{\r\n              permit.permitCompleteDate\r\n              ? (permit.permitCompleteDate | date : \"MM/dd/yyyy\")\r\n              : \"\"\r\n              }}</span>\r\n          </div>\r\n          <!-- <div class=\"permit-detail-item\">\r\n            <label>internal Review Status</label>\r\n            <span class=\"status-text\" [ngClass]=\"getStatusClass(permit.internalReviewStatus)\">{{ permit.internalReviewStatus || \"\"\r\n            }}</span>\r\n          </div> -->\r\n        </div>\r\n      </div>\r\n        \r\n        <!-- Notes & Actions fields (inline with other details) -->\r\n         <div class=\"permit-details-card\"> \r\n          <div class=\"permit-details-header\">\r\n            <h4>Notes / Action</h4>\r\n            <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"onEdit(notesActionsTemplate)\" title=\"Edit Notes/Actions\">\r\n              <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\r\n            </button>\r\n          </div>\r\n        <div class=\"permit-details-content\">\r\n          <div class=\"notes-actions-container\">\r\n            <div class=\"permit-detail-item-full\">\r\n              <label>Internal Review Status</label>\r\n            <span *ngIf=\"permit.internalReviewStatus\" class=\"status-text\" [ngClass]=\"getStatusClass(permit.internalReviewStatus)\">{{ permit.internalReviewStatus \r\n            }}</span>\r\n          </div>\r\n            <div class=\"permit-detail-item-full\">\r\n              <label>Attention Reason (ball in court)</label>\r\n            <span class=\"permit-value\">{{ permit.attentionReason || \"\" }}</span>\r\n          </div>\r\n            <div class=\"permit-detail-item-full\">\r\n              <label>Internal Notes</label>\r\n            <span class=\"permit-value\">{{ permit.internalNotes || \"\" }}</span>\r\n          </div>\r\n            <div class=\"permit-detail-item-full\">\r\n            <label>Action Taken</label>\r\n            <span class=\"permit-value\">{{\r\n              permit.actionTaken || \"\"\r\n              }}</span>\r\n          </div>\r\n        </div>\r\n      </div>\r\n      </div>\r\n      </ng-container>\r\n\r\n      <!-- Internal Reviews Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'internal' && permit && isInternalReviewEnabled()\">\r\n        <!-- Empty State for Internal Reviews -->\r\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"auditEntries.length === 0\">\r\n          <div class=\"text-center\">\r\n            <i class=\"fas fa-clipboard-list fa-3x mb-3\"></i>\r\n            <p>No internal reviews found for this permit.</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Internal Reviews Table -->\r\n        <div class=\"table-responsive\" *ngIf=\"auditEntries.length > 0\">\r\n          <table class=\"table table-hover\">\r\n            <tbody>\r\n              <tr *ngFor=\"let audit of auditEntries; let i = index\" [class.table-active]=\"selectedAuditIndex === i\"\r\n                (click)=\"selectAudit(i)\" class=\"audit-table-row\">\r\n                <td class=\"audit-title-cell px-4\">\r\n                  <div class=\"d-flex align-items-center\">\r\n                    <h6 class=\"mb-0 me-3\">{{ audit.title }}</h6>\r\n                    <span class=\"audit-status-badge\" \r\n                          [ngClass]=\"getStatusClass(audit.internalVerificationStatus)\"\r\n                          [ngStyle]=\"getStatusStyle(audit.internalVerificationStatus)\">\r\n                      {{ audit.internalVerificationStatus || 'Pending' }}\r\n                    </span>\r\n                  </div>\r\n                  <div class=\"mt-1\">\r\n                    <small class=\"text-muted\">{{ audit.typeCodeDrawing || '' }}</small>\r\n                  </div>\r\n                </td>\r\n                <td class=\"audit-dates-cell px-3\">\r\n                  <div class=\"d-flex gap-4\">\r\n                    <div class=\"date-item\">\r\n                      <small class=\"text-muted d-block\">Reviewed</small>\r\n                      <span class=\"fw-medium\">{{ audit.reviewedDate ? (audit.reviewedDate | date : \"MM/dd/yyyy\") : '' }}</span>\r\n                    </div>\r\n                    <div class=\"date-item\">\r\n                      <small class=\"text-muted d-block\">Completed</small>\r\n                      <span class=\"fw-medium\">{{ audit.completedDate ? (audit.completedDate | date : \"MM/dd/yyyy\") : '' }}</span>\r\n                    </div>\r\n                    <div class=\"date-item\">\r\n                      <small class=\"text-muted d-block\">Reviewer</small>\r\n                      <span class=\"fw-medium\">{{ audit.internalReviewer || '' }}</span>\r\n                    </div>\r\n                  </div>\r\n                </td>\r\n                <td class=\"audit-actions-cell px-4\">\r\n                  <i class=\"fas fa-edit action-icon edit-icon\" (click)=\"editInternalReview(i); $event.stopPropagation()\"\r\n                    title=\"Edit Review\"></i>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <!-- External Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'external' && permit && isExternalReviewEnabled()\">\r\n         <div class=\"external-reviews-card\">\r\n           <div class=\"card shadow-sm rounded-3\">\r\n        <!-- Error State for External Reviews -->\r\n           <div class=\"card-body\" *ngIf=\"reviewsError\">\r\n             <div class=\"d-flex justify-content-center align-items-center py-5 text-danger\">\r\n          <div class=\"text-center\">\r\n            <i class=\"fas fa-exclamation-triangle fa-3x mb-3\"></i>\r\n            <p>{{ reviewsError }}</p>\r\n            <button class=\"btn btn-outline-primary btn-sm\" (click)=\"fetchExternalReviews()\" [disabled]=\"isLoading\">\r\n              <i class=\"fas fa-redo\"></i> Retry\r\n            </button>\r\n               </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Empty State for External Reviews -->\r\n           <div class=\"card-body\" *ngIf=\"!reviewsError && externalSubmittals.length === 0\">\r\n             <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\">\r\n          <div class=\"text-center\">\r\n            <i class=\"fas fa-external-link-alt fa-3x mb-3\"></i>\r\n            <p>No external reviews found for this permit.</p>\r\n               </div>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- External Reviews Table with Accordion -->\r\n           <div class=\"card-body p-0\" *ngIf=\"!reviewsError && externalSubmittals.length > 0\">\r\n             <div class=\"table-responsive external-reviews-table\">\r\n               <table class=\"table table-hover mb-0\">\r\n            <tbody>\r\n              <ng-container *ngFor=\"let sub of externalSubmittals; let i = index\">\r\n                <!-- Submittal Row -->\r\n                     <tr class=\"external-submittal-row\" \r\n                       (click)=\"toggleSubmittalAccordion(i)\"\r\n                       [class.expanded]=\"isSubmittalExpanded(i)\"\r\n                       style=\"cursor: pointer;\">\r\n                  <td class=\"submittal-title-cell px-3\">\r\n                    <div class=\"d-flex align-items-center\">\r\n                           <div class=\"accordion-toggle me-2\">\r\n                        <i class=\"fas\" [ngClass]=\"isSubmittalExpanded(i) ? 'fa-chevron-down' : 'fa-chevron-right'\"></i>\r\n                           </div>\r\n                      <h6 class=\"mb-0 me-3\">{{ sub.title }}</h6>\r\n                      <span class=\"submittal-status-badge\" [ngClass]=\"getStatusClass(sub.submittalStatus)\">\r\n                        {{ sub.submittalStatus }}\r\n                      </span>\r\n                    </div>\r\n                  </td>\r\n                  <td class=\"submittal-dates-cell px-3\">\r\n                    <div class=\"d-flex gap-4\">\r\n                      <div class=\"date-item\">\r\n                        <small class=\"text-muted d-block\">Due</small>\r\n                        <span class=\"fw-medium\">{{ sub.dueDate | date : \"MM/dd/yyyy\" }}</span>\r\n                      </div>\r\n                      <div class=\"date-item\">\r\n                        <small class=\"text-muted d-block\">Completed</small>\r\n                        <span class=\"fw-medium\">{{ sub.receivedDate | date : \"MM/dd/yyyy\" }}</span>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n                <!-- Accordion Content for External Reviews - positioned directly below its submittal row -->\r\n                <tr class=\"accordion-content-row\" [class.d-none]=\"!isSubmittalExpanded(i)\">\r\n                  <td colspan=\"2\" class=\"p-0\">\r\n                    <div class=\"accordion-content\">\r\n                      <div class=\"reviews-container p-3\">\r\n                        <div class=\"review-item\" *ngFor=\"let review of getExternalReviewsForSubmittal(sub.id)\">\r\n                                <div class=\"review-single-line\" \r\n                                  (click)=\"toggleReviewAccordion(review.commentsId)\"\r\n                                  [class.expanded]=\"isReviewExpanded(review.commentsId)\"\r\n                                  style=\"cursor: pointer;\">\r\n                                  <div class=\"review-accordion-toggle me-2\">\r\n                                    <i class=\"fas\" [ngClass]=\"isReviewExpanded(review.commentsId) ? 'fa-chevron-down' : 'fa-chevron-right'\"></i>\r\n                                  </div>\r\n                                  <h6 class=\"review-title\" [style.color]=\"review.FailureFlag ? 'red' : 'green'\">\r\n                                    {{ review.name }}\r\n                                  </h6>\r\n                                  <div class=\"review-status-container\">\r\n                                    <span class=\"review-status\" [ngClass]=\"getStatusClass(review.status)\">\r\n                                      {{ review.status || '' }}\r\n                                    </span>\r\n                                  </div>\r\n                                  <div class=\"reviewer-container\">\r\n                                    <span class=\"reviewer\">{{ review.reviewer || '' }}</span>\r\n                                  </div>\r\n                                  <div class=\"due-date-container\">\r\n                                    <span class=\"due-date\">\r\n                                      {{ review.dueDate ? ('Due: ' + (review.dueDate | date : \"MM/dd/yyyy\")) : '' }}\r\n                                    </span>\r\n                                  </div>\r\n                                  <div class=\"completed-date-container\">\r\n                                    <span class=\"completed-date\">\r\n                                      {{ review.completedDate ? ('Completed: ' + (review.completedDate | date : \"MM/dd/yyyy\")) : '' }}\r\n                                    </span>\r\n                                  </div>\r\n                                  <div class=\"review-actions-container\" *ngIf=\"(review?.corrections && review.corrections.length > 0) || (review?.comments && review.comments !== '')\">\r\n                                    <i class=\"fas fa-download download-pdf-icon\" \r\n                                      (click)=\"downloadReviewPDF(review); $event.stopPropagation()\"\r\n                                      title=\"Download Review PDF\"></i>\r\n                                  </div>\r\n                                </div>\r\n                                \r\n                                <!-- Review Details Accordion Content -->\r\n                                <div class=\"review-details-accordion\" [class.d-none]=\"!isReviewExpanded(review.commentsId)\">\r\n                                  <div class=\"review-details-content\">\r\n                                    <!-- Corrections Section -->\r\n                                    <div class=\"corrections-section p-3\" *ngIf=\"review?.corrections && review.corrections.length > 0\" style=\"padding-bottom: 0px !important;\">\r\n                                      <h6 class=\"section-title\">Corrections ({{ review.corrections.length }})</h6>\r\n                                      <div class=\"correction-item\" *ngFor=\"let correction of review.corrections; let i = index\">\r\n                                        <div class=\"correction-header d-flex align-items-center\">\r\n                                          <div class=\"correction-number\">{{ i + 1 }}</div>\r\n                                          <div class=\"correction-meta flex-grow-1 ms-3 d-flex align-items-center justify-content-between\">\r\n                                            <div class=\"meta-fields d-flex align-items-center w-100\">\r\n                                              <div class=\"meta-field flex-fill\">\r\n                                                <span class=\"meta-label fw-bold\">Correction Type: </span>\r\n                                                <span class=\"meta-value\">{{ correction.CorrectionTypeName || '' }}</span>\r\n                                              </div>\r\n                                              <div class=\"meta-field flex-fill\">\r\n                                                <span class=\"meta-label fw-bold\">Category: </span>\r\n                                                <span class=\"meta-value\">{{ correction.CorrectionCategoryName || '' }}</span>\r\n                                              </div>\r\n                                              <div class=\"meta-field flex-fill\">\r\n                                                <span class=\"meta-label fw-bold\">Resolved: </span>\r\n                                                <span class=\"meta-value resolved-date\">{{ correction.ResolvedDate ? (correction.ResolvedDate | date:'MM/dd/yyyy') : '' }}</span>\r\n                                              </div>\r\n                                            </div>\r\n                                          </div>\r\n                                          <div class=\"respond-buttons\">\r\n                                            <button class=\"btn btn-primary btn-sm me-3\" \r\n                                              *ngIf=\"!correction.EORAOROwner_Response && !correction.commentResponsedBy && shouldShowEditResponseButton(correction)\"\r\n                                              (click)=\"openResponseModal(correction, review)\"\r\n                                              [disabled]=\"isLoading\"\r\n                                              title=\"Respond to this correction\">\r\n                                              Respond\r\n                                            </button>\r\n                                            <button class=\"btn btn-primary btn-sm me-3\" \r\n                                              *ngIf=\"(correction.EORAOROwner_Response || correction.commentResponsedBy) && shouldShowEditResponseButton(correction)\"\r\n                                              (click)=\"openResponseModal(correction, review)\"\r\n                                              [disabled]=\"isLoading\"\r\n                                              title=\"Edit response to this correction\">\r\n                                              Edit Response\r\n                                            </button>\r\n                                          </div>\r\n                                        </div>\r\n                                        \r\n                                        <div class=\"correction-content\">\r\n                                          <div class=\"correction-field\">\r\n                                            <label class=\"field-label\">\r\n                                              Corrective Action\r\n                                            </label>\r\n                                            <div class=\"field-content corrective-action\">\r\n                                              {{ correction.CorrectiveAction || '' }}\r\n                                            </div>\r\n                                          </div>\r\n                                          \r\n                                          <div class=\"correction-field\">\r\n                                            <label class=\"field-label\">\r\n                                              Comment\r\n                                            </label>\r\n                                            <div class=\"field-content comment\">\r\n                                              {{ correction.Comments || 'No comment provided' }}\r\n                                            </div>\r\n                                          </div>\r\n                                          \r\n                                          <div class=\"correction-field\" *ngIf=\"correction.Response\">\r\n                                            <label class=\"field-label\">\r\n                                              Response\r\n                                            </label>\r\n                                            <div class=\"field-content response\">\r\n                                              {{ correction.Response }}\r\n                                            </div>\r\n                                          </div>\r\n                                          \r\n                                          <div class=\"correction-field\" *ngIf=\"correction.EORAOROwner_Response\">\r\n                                            <label class=\"field-label\">\r\n                                              EOR / AOR / Owner Response\r\n                                            </label>\r\n                                            <div class=\"field-content eor-response\">\r\n                                              {{ correction.EORAOROwner_Response }}\r\n                                            </div>\r\n                                          </div>\r\n                                          \r\n                                          <div class=\"correction-field\" *ngIf=\"correction.commentResponsedBy\">\r\n                                            <label class=\"field-label\">\r\n                                              Comment Responded By\r\n                                            </label>\r\n                                            <div class=\"field-content responded-by\">\r\n                                              {{ correction.commentResponsedBy }}\r\n                                            </div>\r\n                                          </div>\r\n                                        </div>\r\n                                        \r\n                                        <div class=\"correction-separator\" *ngIf=\"i < review.corrections.length - 1\"></div>\r\n                                      </div>\r\n                                    </div>\r\n\r\n                                    <!-- Comments Section (fallback when no corrections) -->\r\n                                    <div class=\"comments-section p-3\"\r\n                                      *ngIf=\"(!review?.corrections || review.corrections.length === 0) && review?.comments\">\r\n                                      <h6 class=\"section-title\">Comments</h6>\r\n                                      <div class=\"comment-content\">\r\n                                        <div class=\"comment-text\">{{ review.comments }}</div>\r\n                                      </div>\r\n                                    </div>\r\n\r\n                                    <!-- No Data Message -->\r\n                                    <div class=\"no-data-section p-3\" \r\n                                      *ngIf=\"(!review?.corrections || review.corrections.length === 0) && !review?.comments\">\r\n                                      <div class=\"no-data-message text-center text-muted\">\r\n                                        <i class=\"fas fa-info-circle\"></i>\r\n                                        <span> No corrections or comments available for this review.</span>\r\n                                      </div>\r\n                                    </div>\r\n                                  </div>\r\n                          </div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </td>\r\n                </tr>\r\n              </ng-container>\r\n            </tbody>\r\n          </table>\r\n             </div>\r\n           </div>\r\n         </div>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n\r\n</div>\r\n\r\n\r\n<ng-template #notesActionsTemplate let-permit=\"permit\">\r\n <div class=\"modal-content h-auto\">\r\n  <div class=\"modal-header bg-light-primary\">\r\n    <div class=\"modal-title h5 fs-3\">\r\n      <ng-container>\r\n       \r\n        <div>Edit Notes/Actions</div>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n      <i\r\n        class=\"fa-solid fs-2 fa-xmark text-white\"\r\n        (click)=\"closModal()\"\r\n      ></i>\r\n    </div>\r\n  </div>\r\n\r\n  <div\r\n    class=\"modal-body \"\r\n    \r\n  >\r\n\r\n  <form class=\"form form-label-right\" [formGroup]=\"notesForm\">\r\n\r\n      <div class=\"row mt-4\">\r\n    <div class=\"col-xl-12\">\r\n      <div class=\"form-group\">\r\n        <label for=\"internalProjectNo\" class=\"fw-bold form-label mb-2\">\r\nInternal Review Status        </label>\r\n        \r\n  <ng-select [items]=\"internalStatusArray\" [clearable]=\"false\" [multiple]=\"false\" dropdownPosition=\"top\"  \r\n              formControlName=\"internalReviewStatus\" placeholder=\"Select Status\">\r\n            </ng-select>\r\n\r\n      </div>\r\n    </div>\r\n\r\n    \r\n  </div>\r\n\r\n  <!-- Project Name -->\r\n  <div class=\"row mt-4\">\r\n    <div class=\"col-xl-12\">\r\n      <div class=\"form-group\">\r\n        <label for=\"projectName\" class=\"fw-bold form-label mb-2\">\r\n          Attention Reason\r\n        </label>\r\n        <textarea\r\n          id=\"attentionReason\"\r\n          class=\"form-control form-control-sm\"\r\n          rows=\"2\"\r\n          formControlName=\"attentionReason\"\r\n          placeholder=\"Type here\"\r\n        ></textarea>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Description -->\r\n  <div class=\"row mt-4\">\r\n    <div class=\"col-xl-12\">\r\n      <div class=\"form-group\">\r\n        <label for=\"projectDescription\" class=\"fw-bold form-label mb-2\">\r\n          Internal Notes\r\n        </label>\r\n        <textarea\r\n          id=\"internalNotes\"\r\n          class=\"form-control form-control-sm\"\r\n          rows=\"3\"\r\n          formControlName=\"internalNotes\"\r\n          placeholder=\"Type here\"\r\n        ></textarea>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Internal Project Number & Start Date -->\r\n  <div class=\"row mt-4\">\r\n    <div class=\"col-xl-12\">\r\n      <div class=\"form-group\">\r\n        <label for=\"internalProjectNo\" class=\"fw-bold form-label mb-2\">\r\n          Action Taken \r\n        </label>\r\n          <textarea\r\n          id=\"internalNotes\"\r\n          class=\"form-control form-control-sm\"\r\n          rows=\"3\"\r\n          formControlName=\"actionTaken\"\r\n          placeholder=\"Type here\"\r\n        ></textarea>\r\n      </div>\r\n    </div>\r\n\r\n    \r\n  </div>\r\n\r\n\r\n</form>\r\n\r\n  </div>\r\n\r\n  <div class=\"modal-footer justify-content-end\">\r\n    <div>\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-danger btn-sm btn-elevate mr-2\"\r\n        (click)=\"closModal()\"\r\n      >\r\n        Cancel</button\r\n      >&nbsp;\r\n      <button\r\n        \r\n        type=\"button\"\r\n        class=\"btn btn-primary btn-sm\"\r\n        \r\n       (click)=\"editNotesandactions()\"\r\n      >Update\r\n       <!-- (click)=\"save()\" -->\r\n        <!-- [disabled]=\"projectForm?.invalid\" -->\r\n        <!-- {{ id ? \"Update\" : \"Save\" }} -->\r\n      </button>\r\n     \r\n    </div>\r\n  </div>\r\n</div>\r\n\r\n</ng-template>\r\n\r\n\r\n"], "mappings": "AAEA,SAASA,YAAY,QAAQ,MAAM;AAEnC,SAASC,2BAA2B,QAAQ,wDAAwD;AACpG,SAASC,sBAAsB,QAAQ,4CAA4C;AACnF,SAASC,8BAA8B,QAAQ,gEAAgE;AAC/G,SAASC,oBAAoB,QAAQ,wCAAwC;AAE7E,SAASC,2BAA2B,QAAQ,wDAAwD;AAEpG,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,iBAAiB;AACxB,SAASC,SAAS,QAAQ,iBAAiB;;;;;;;;;;;;;;;ICTrCC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IAuCIH,EADF,CAAAC,cAAA,aAAuD,YAEb;IAAtCD,EAAA,CAAAI,UAAA,mBAAAC,4DAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,UAAU,EAAAN,MAAA,CAAS;IAAA,EAAC;IACrCN,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;;;;IAJyDH,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,iBAAkD;;;;;;IAM9GjB,EADF,CAAAC,cAAA,aAAuD,YAEb;IAAtCD,EAAA,CAAAI,UAAA,mBAAAc,4DAAAZ,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAY,GAAA;MAAA,MAAAV,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,UAAU,EAAAN,MAAA,CAAS;IAAA,EAAC;IACrCN,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;;;;IAJyDH,EAAA,CAAAa,SAAA,EAAkD;IAAlDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,IAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,iBAAkD;;;;;;IAUhHjB,EAAA,CAAAC,cAAA,iBAA4H;IAA7ED,EAAA,CAAAI,UAAA,mBAAAgB,qEAAA;MAAApB,EAAA,CAAAO,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,UAAA,EAAY;IAAA,EAAC;IACnEtB,EAAA,CAAAuB,SAAA,YAAmE;IACrEvB,EAAA,CAAAG,YAAA,EAAS;;;;;;IAKTH,EADF,CAAAC,cAAA,cAA0F,iBAC6F;IAAjID,EAAA,CAAAI,UAAA,mBAAAoB,kEAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiB,0BAAA,EAA4B;IAAA,EAAC;IACxF1B,EAAA,CAAAuB,SAAA,YAAuE;IACzEvB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAmF;IAA5CD,EAAA,CAAAI,UAAA,mBAAAuB,kEAAA;MAAA3B,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAmB,QAAA,EAAU;IAAA,EAAC;IACzD5B,EAAA,CAAAuB,SAAA,YAA6B;IAAAvB,EAAA,CAAAE,MAAA,kBAC/B;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IANuFH,EAAA,CAAAa,SAAA,EAAmD;IAAnDb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,IAAApB,MAAA,CAAAqB,YAAA,CAAAC,MAAA,OAAmD;IAGlF/B,EAAA,CAAAa,SAAA,GAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,CAAsB;;;;;;IAKjF7B,EADF,CAAAC,cAAA,cAA0F,iBACyJ;IAA7LD,EAAA,CAAAI,UAAA,mBAAA4B,kEAAA;MAAAhC,EAAA,CAAAO,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyB,0BAAA,EAA4B;IAAA,EAAC;IACxFlC,EAAA,CAAAuB,SAAA,YAAuE;IACzEvB,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,iBAAiP;IAArLD,EAAA,CAAAI,UAAA,mBAAA+B,kEAAA;MAAAnC,EAAA,CAAAO,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA2B,mBAAA,EAAqB;IAAA,EAAC;IACzFpC,EAAA,CAAAuB,SAAA,YAA8G;IAC9GvB,EAAA,CAAAC,cAAA,eAAmB;IAAAD,EAAA,CAAAE,MAAA,GAAgE;IACrFF,EADqF,CAAAG,YAAA,EAAO,EACnF;IACTH,EAAA,CAAAC,cAAA,iBAA6G;IAAnDD,EAAA,CAAAI,UAAA,mBAAAiC,kEAAA;MAAArC,EAAA,CAAAO,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA6B,WAAA,CAAY,IAAI,CAAC;IAAA,EAAC;IACnFtC,EAAA,CAAAuB,SAAA,YAA+B;IAACvB,EAAA,CAAAE,MAAA,aAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAG8G;IAFtGD,EAAA,CAAAI,UAAA,mBAAAmC,mEAAA;MAAAvC,EAAA,CAAAO,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA+B,UAAA,EAAY;IAAA,EAAC;IAG5BxC,EAAA,CAAAuB,SAAA,aAAwC;IAACvB,EAAA,CAAAE,MAAA,gBAC3C;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;IAhBuFH,EAAA,CAAAa,SAAA,EAA+G;IAA/Gb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,KAAApB,MAAA,CAAAgC,2BAAA,IAAAhC,MAAA,CAAAiC,sCAAA,GAAAX,MAAA,OAA+G;IAG9G/B,EAAA,CAAAa,SAAA,GAAyD;IAACb,EAA1D,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,IAAApB,MAAA,CAAAkC,kBAAA,CAAAZ,MAAA,OAAyD,UAAAtB,MAAA,CAAAmC,wBAAA,yDAA2F;IAC/N5C,EAAA,CAAAa,SAAA,EAA0F;IAA1Fb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAmC,wBAAA,uDAA0F;IACtF5C,EAAA,CAAAa,SAAA,GAAgE;IAAhEb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAmC,wBAAA,mCAAgE;IAEC5C,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,CAAsB;IAKpG7B,EAAA,CAAAa,SAAA,GAAoC;IACpCb,EADA,CAAAc,UAAA,eAAAL,MAAA,CAAAqC,MAAA,kBAAArC,MAAA,CAAAqC,MAAA,CAAAC,cAAA,EAAoC,WAAAtC,MAAA,CAAAqC,MAAA,kBAAArC,MAAA,CAAAqC,MAAA,CAAAC,cAAA,uEACiE;;;;;IAmG1G/C,EAAA,CAAAC,cAAA,eAAsH;IAAAD,EAAA,CAAAE,MAAA,GACpH;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADqDH,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuC,cAAA,CAAAvC,MAAA,CAAAqC,MAAA,CAAAG,oBAAA,EAAuD;IAACjD,EAAA,CAAAa,SAAA,EACpH;IADoHb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAG,oBAAA,CACpH;;;;;;IA1FRjD,EAAA,CAAAkD,uBAAA,GAAyD;IAInDlD,EAHN,CAAAC,cAAA,cAAqC,cACF,cACC,YACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3BH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAA8B;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;IAEJH,EADF,CAAAC,cAAA,cAAgC,YACvB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,gBAA0E;IAAAD,EAAA,CAAAE,MAAA,IACxE;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IACxDF,EADwD,CAAAG,YAAA,EAAO,EACzD;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAC,cAAA,gBAAqC;IAACD,EAAA,CAAAE,MAAA,IAIlC;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IAEJH,EADF,CAAAC,cAAA,eAAgC,aACvB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC5BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAIvB;;IAQVF,EARU,CAAAG,YAAA,EAAO,EACP,EAMF,EACF;IAKAH,EAFH,CAAAC,cAAA,eAAiC,eACG,UAC7B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,kBAAiH;IAAlED,EAAA,CAAAI,UAAA,mBAAA+C,4EAAA;MAAAnD,EAAA,CAAAO,aAAA,CAAA6C,GAAA;MAAA,MAAA3C,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,MAAA2C,uBAAA,GAAArD,EAAA,CAAAsD,WAAA;MAAA,OAAAtD,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8C,MAAA,CAAAF,uBAAA,CAA4B;IAAA,EAAC;IACnFrD,EAAA,CAAAuB,SAAA,aAAmE;IAEvEvB,EADE,CAAAG,YAAA,EAAS,EACL;IAIFH,EAHN,CAAAC,cAAA,eAAoC,eACG,eACE,aAC5B;IAAAD,EAAA,CAAAE,MAAA,8BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAwD,UAAA,KAAAC,0DAAA,mBAAsH;IAExHzD,EAAA,CAAAG,YAAA,EAAM;IAEFH,EADF,CAAAC,cAAA,eAAqC,aAC5B;IAAAD,EAAA,CAAAE,MAAA,wCAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACjDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC/DF,EAD+D,CAAAG,YAAA,EAAO,EAChE;IAEFH,EADF,CAAAC,cAAA,eAAqC,aAC5B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC/BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAC7DF,EAD6D,CAAAG,YAAA,EAAO,EAC9D;IAEJH,EADA,CAAAC,cAAA,eAAqC,aAC9B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAEvB;IAIVF,EAJU,CAAAG,YAAA,EAAO,EACP,EACF,EACF,EACA;;;;;IAvG2BH,EAAA,CAAAa,SAAA,GAA8B;IAA9Bb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAY,WAAA,OAA8B;IAI9B1D,EAAA,CAAAa,SAAA,GAA6B;IAA7Bb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAa,UAAA,OAA6B;IAI7B3D,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAc,cAAA,OAEvB;IAIsB5D,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuC,cAAA,CAAAvC,MAAA,CAAAqC,MAAA,CAAAe,YAAA,EAA+C;IAAC7D,EAAA,CAAAa,SAAA,EACxE;IADwEb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAe,YAAA,OACxE;IAIyB7D,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAgB,QAAA,OAA2B;IAI3B9D,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAiB,cAAA,OAEvB;IAIuB/D,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAkB,eAAA,GAAAhE,EAAA,CAAAiE,WAAA,SAAAxD,MAAA,CAAAqC,MAAA,CAAAkB,eAAA,qBAIvB;IACkChE,EAAA,CAAAa,SAAA,GAIlC;IAJkCb,EAAA,CAAAkE,kBAAA,iBAAAzD,MAAA,CAAAqC,MAAA,CAAAqB,iBAAA,GAAAnE,EAAA,CAAAiE,WAAA,SAAAxD,MAAA,CAAAqC,MAAA,CAAAqB,iBAAA,yBAIlC;IAIuBnE,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAsB,oBAAA,GAAApE,EAAA,CAAAiE,WAAA,SAAAxD,MAAA,CAAAqC,MAAA,CAAAsB,oBAAA,qBAIvB;IAIuBpE,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAuB,eAAA,GAAArE,EAAA,CAAAiE,WAAA,SAAAxD,MAAA,CAAAqC,MAAA,CAAAuB,eAAA,qBAIvB;IAIuBrE,EAAA,CAAAa,SAAA,GAIvB;IAJuBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAwB,kBAAA,GAAAtE,EAAA,CAAAiE,WAAA,SAAAxD,MAAA,CAAAqC,MAAA,CAAAwB,kBAAA,qBAIvB;IAsBGtE,EAAA,CAAAa,SAAA,IAAiC;IAAjCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAqC,MAAA,CAAAG,oBAAA,CAAiC;IAKbjD,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAAyB,eAAA,OAAkC;IAIlCvE,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAA0B,aAAA,OAAgC;IAIhCxE,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAA2B,WAAA,OAEvB;;;;;IAWNzE,EADF,CAAAC,cAAA,cAAgH,cACrF;IACvBD,EAAA,CAAAuB,SAAA,YAAgD;IAChDvB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAEjDF,EAFiD,CAAAG,YAAA,EAAI,EAC7C,EACF;;;;;;IAMAH,EAAA,CAAAC,cAAA,aACmD;IAAjDD,EAAA,CAAAI,UAAA,mBAAAsE,kFAAA;MAAA,MAAAC,KAAA,GAAA3E,EAAA,CAAAO,aAAA,CAAAqE,IAAA,EAAAC,KAAA;MAAA,MAAApE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAqE,WAAA,CAAAH,KAAA,CAAc;IAAA,EAAC;IAGpB3E,EAFJ,CAAAC,cAAA,aAAkC,cACO,aACf;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,eAEmE;IACjED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,cAAkB,gBACU;IAAAD,EAAA,CAAAE,MAAA,GAAiC;IAE/DF,EAF+D,CAAAG,YAAA,EAAQ,EAC/D,EACH;IAICH,EAHN,CAAAC,cAAA,cAAkC,eACN,eACD,iBACa;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA0E;;IACpGF,EADoG,CAAAG,YAAA,EAAO,EACrG;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4E;;IACtGF,EADsG,CAAAG,YAAA,EAAO,EACvG;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAClDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAGhEF,EAHgE,CAAAG,YAAA,EAAO,EAC7D,EACF,EACH;IAEHH,EADF,CAAAC,cAAA,cAAoC,aAEZ;IADuBD,EAAA,CAAAI,UAAA,mBAAA2E,kFAAAzE,MAAA;MAAA,MAAAqE,KAAA,GAAA3E,EAAA,CAAAO,aAAA,CAAAqE,IAAA,EAAAC,KAAA;MAAA,MAAApE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAAuE,kBAAA,CAAAL,KAAA,CAAqB;MAAA,OAAA3E,EAAA,CAAAW,WAAA,CAAEL,MAAA,CAAA2E,eAAA,EAAwB;IAAA,EAAC;IAG1GjF,EAF0B,CAAAG,YAAA,EAAI,EACvB,EACF;;;;;;IAnCiDH,EAAA,CAAAkF,WAAA,iBAAAzE,MAAA,CAAA0E,kBAAA,KAAAR,KAAA,CAA+C;IAIzE3E,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAA6C,iBAAA,CAAAuC,SAAA,CAAAC,KAAA,CAAiB;IAEjCrF,EAAA,CAAAa,SAAA,EAA4D;IAC5Db,EADA,CAAAc,UAAA,YAAAL,MAAA,CAAAuC,cAAA,CAAAoC,SAAA,CAAAE,0BAAA,EAA4D,YAAA7E,MAAA,CAAA8E,cAAA,CAAAH,SAAA,CAAAE,0BAAA,EACA;IAChEtF,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAkB,SAAA,CAAAE,0BAAA,mBACF;IAG0BtF,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAA6C,iBAAA,CAAAuC,SAAA,CAAAI,eAAA,OAAiC;IAOjCxF,EAAA,CAAAa,SAAA,GAA0E;IAA1Eb,EAAA,CAAA6C,iBAAA,CAAAuC,SAAA,CAAAK,YAAA,GAAAzF,EAAA,CAAAiE,WAAA,SAAAmB,SAAA,CAAAK,YAAA,qBAA0E;IAI1EzF,EAAA,CAAAa,SAAA,GAA4E;IAA5Eb,EAAA,CAAA6C,iBAAA,CAAAuC,SAAA,CAAAM,aAAA,GAAA1F,EAAA,CAAAiE,WAAA,SAAAmB,SAAA,CAAAM,aAAA,qBAA4E;IAI5E1F,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAA6C,iBAAA,CAAAuC,SAAA,CAAAO,gBAAA,OAAkC;;;;;IA5BpE3F,EAFJ,CAAAC,cAAA,cAA8D,gBAC3B,YACxB;IACLD,EAAA,CAAAwD,UAAA,IAAAoC,6DAAA,mBACmD;IAqCzD5F,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAtCsBH,EAAA,CAAAa,SAAA,GAAiB;IAAjBb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAqB,YAAA,CAAiB;;;;;IAb/C9B,EAAA,CAAAkD,uBAAA,GAAuF;IAUrFlD,EARA,CAAAwD,UAAA,IAAAqC,wDAAA,kBAAgH,IAAAC,wDAAA,kBAQlD;;;;;IARiB9F,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAqB,YAAA,CAAAC,MAAA,OAA+B;IAQ/E/B,EAAA,CAAAa,SAAA,EAA6B;IAA7Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAqB,YAAA,CAAAC,MAAA,KAA6B;;;;;;IAmD1D/B,EAFC,CAAAC,cAAA,cAA4C,cACqC,cACzD;IACvBD,EAAA,CAAAuB,SAAA,YAAsD;IACtDvB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACzBH,EAAA,CAAAC,cAAA,iBAAuG;IAAxDD,EAAA,CAAAI,UAAA,mBAAA2F,iFAAA;MAAA/F,EAAA,CAAAO,aAAA,CAAAyF,IAAA;MAAA,MAAAvF,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwF,oBAAA,EAAsB;IAAA,EAAC;IAC7EjG,EAAA,CAAAuB,SAAA,YAA2B;IAACvB,EAAA,CAAAE,MAAA,cAC9B;IAGJF,EAHI,CAAAG,YAAA,EAAS,EACA,EACL,EACF;;;;IANCH,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAyF,YAAA,CAAkB;IAC2DlG,EAAA,CAAAa,SAAA,EAAsB;IAAtBb,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,CAAsB;;;;;IAUxG7B,EAFC,CAAAC,cAAA,cAAgF,cACA,cACxD;IACvBD,EAAA,CAAAuB,SAAA,YAAmD;IACnDvB,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,iDAA0C;IAGjDF,EAHiD,CAAAG,YAAA,EAAI,EACxC,EACL,EACF;;;;;;IAwEsBH,EADF,CAAAC,cAAA,eAAqJ,aAGrH;IAD5BD,EAAA,CAAAI,UAAA,mBAAA+F,yGAAA7F,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAA6F,IAAA;MAAA,MAAAC,UAAA,GAAArG,EAAA,CAAAU,aAAA,GAAA4F,SAAA;MAAA,MAAA7F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAASD,MAAA,CAAA8F,iBAAA,CAAAF,UAAA,CAAyB;MAAA,OAAArG,EAAA,CAAAW,WAAA,CAAEL,MAAA,CAAA2E,eAAA,EAAwB;IAAA,EAAC;IAEjEjF,EADgC,CAAAG,YAAA,EAAI,EAC9B;;;;;;IA6BIH,EAAA,CAAAC,cAAA,kBAIqC;IAFnCD,EAAA,CAAAI,UAAA,mBAAAoG,8HAAA;MAAAxG,EAAA,CAAAO,aAAA,CAAAkG,IAAA;MAAA,MAAAC,cAAA,GAAA1G,EAAA,CAAAU,aAAA,GAAA4F,SAAA;MAAA,MAAAD,UAAA,GAAArG,EAAA,CAAAU,aAAA,IAAA4F,SAAA;MAAA,MAAA7F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkG,iBAAA,CAAAD,cAAA,EAAAL,UAAA,CAAqC;IAAA,EAAC;IAG/CrG,EAAA,CAAAE,MAAA,gBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,CAAsB;;;;;;IAIxB7B,EAAA,CAAAC,cAAA,kBAI2C;IAFzCD,EAAA,CAAAI,UAAA,mBAAAwG,8HAAA;MAAA5G,EAAA,CAAAO,aAAA,CAAAsG,IAAA;MAAA,MAAAH,cAAA,GAAA1G,EAAA,CAAAU,aAAA,GAAA4F,SAAA;MAAA,MAAAD,UAAA,GAAArG,EAAA,CAAAU,aAAA,IAAA4F,SAAA;MAAA,MAAA7F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAkG,iBAAA,CAAAD,cAAA,EAAAL,UAAA,CAAqC;IAAA,EAAC;IAG/CrG,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAHPH,EAAA,CAAAc,UAAA,aAAAL,MAAA,CAAAoB,SAAA,CAAsB;;;;;IA2BxB7B,EADF,CAAAC,cAAA,eAA0D,iBAC7B;IACzBD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAoC;IAClCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAwC,cAAA,CAAAI,QAAA,MACF;;;;;IAIA9G,EADF,CAAAC,cAAA,eAAsE,iBACzC;IACzBD,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAwC,cAAA,CAAAK,oBAAA,MACF;;;;;IAIA/G,EADF,CAAAC,cAAA,eAAoE,iBACvC;IACzBD,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,eAAwC;IACtCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAFFH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAwC,cAAA,CAAAM,kBAAA,MACF;;;;;IAIJhH,EAAA,CAAAuB,SAAA,eAAkF;;;;;IAlFhFvB,EAFJ,CAAAC,cAAA,eAA0F,eAC/B,eACxB;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAI1CH,EAHN,CAAAC,cAAA,eAAgG,eACrC,eACrB,gBACC;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAyC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAEJH,EADF,CAAAC,cAAA,gBAAkC,iBACC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,IAA6C;IACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;IAEJH,EADF,CAAAC,cAAA,gBAAkC,iBACC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAE,MAAA,IAAkF;;IAG/HF,EAH+H,CAAAG,YAAA,EAAO,EAC5H,EACF,EACF;IACNH,EAAA,CAAAC,cAAA,gBAA6B;IAQ3BD,EAPA,CAAAwD,UAAA,KAAAyD,qGAAA,sBAIqC,KAAAC,qGAAA,sBAOM;IAI/ClH,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,gBAAgC,gBACA,kBACD;IACzBD,EAAA,CAAAE,MAAA,2BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAA6C;IAC3CD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,gBAA8B,kBACD;IACzBD,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAC,cAAA,gBAAmC;IACjCD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAoBNH,EAlBA,CAAAwD,UAAA,KAAA2D,kGAAA,mBAA0D,KAAAC,kGAAA,mBASY,KAAAC,kGAAA,mBASF;IAQtErH,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAwD,UAAA,KAAA8D,kGAAA,mBAA4E;IAC9EtH,EAAA,CAAAG,YAAA,EAAM;;;;;;;IAnF6BH,EAAA,CAAAa,SAAA,GAAW;IAAXb,EAAA,CAAA6C,iBAAA,CAAA0E,KAAA,KAAW;IAKXvH,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAA6C,iBAAA,CAAA6D,cAAA,CAAAc,kBAAA,OAAyC;IAIzCxH,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAA6C,iBAAA,CAAA6D,cAAA,CAAAe,sBAAA,OAA6C;IAI/BzH,EAAA,CAAAa,SAAA,GAAkF;IAAlFb,EAAA,CAAA6C,iBAAA,CAAA6D,cAAA,CAAAgB,YAAA,GAAA1H,EAAA,CAAAiE,WAAA,SAAAyC,cAAA,CAAAgB,YAAA,qBAAkF;IAM1H1H,EAAA,CAAAa,SAAA,GAAoH;IAApHb,EAAA,CAAAc,UAAA,UAAA4F,cAAA,CAAAK,oBAAA,KAAAL,cAAA,CAAAM,kBAAA,IAAAvG,MAAA,CAAAkH,4BAAA,CAAAjB,cAAA,EAAoH;IAOpH1G,EAAA,CAAAa,SAAA,EAAoH;IAApHb,EAAA,CAAAc,UAAA,UAAA4F,cAAA,CAAAK,oBAAA,IAAAL,cAAA,CAAAM,kBAAA,KAAAvG,MAAA,CAAAkH,4BAAA,CAAAjB,cAAA,EAAoH;IAerH1G,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAwC,cAAA,CAAAkB,gBAAA,YACF;IAQE5H,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAwC,cAAA,CAAAmB,QAAA,+BACF;IAG6B7H,EAAA,CAAAa,SAAA,EAAyB;IAAzBb,EAAA,CAAAc,UAAA,SAAA4F,cAAA,CAAAI,QAAA,CAAyB;IASzB9G,EAAA,CAAAa,SAAA,EAAqC;IAArCb,EAAA,CAAAc,UAAA,SAAA4F,cAAA,CAAAK,oBAAA,CAAqC;IASrC/G,EAAA,CAAAa,SAAA,EAAmC;IAAnCb,EAAA,CAAAc,UAAA,SAAA4F,cAAA,CAAAM,kBAAA,CAAmC;IAUjChH,EAAA,CAAAa,SAAA,EAAuC;IAAvCb,EAAA,CAAAc,UAAA,SAAAyG,KAAA,GAAAlB,UAAA,CAAAyB,WAAA,CAAA/F,MAAA,KAAuC;;;;;IArF5E/B,EADF,CAAAC,cAAA,eAA0I,cAC9G;IAAAD,EAAA,CAAAE,MAAA,GAA6C;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5EH,EAAA,CAAAwD,UAAA,IAAAuE,2FAAA,qBAA0F;IAsF5F/H,EAAA,CAAAG,YAAA,EAAM;;;;IAvFsBH,EAAA,CAAAa,SAAA,GAA6C;IAA7Cb,EAAA,CAAAkE,kBAAA,kBAAAmC,UAAA,CAAAyB,WAAA,CAAA/F,MAAA,MAA6C;IACnB/B,EAAA,CAAAa,SAAA,EAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAuF,UAAA,CAAAyB,WAAA,CAAuB;;;;;IA2F3E9H,EAFF,CAAAC,cAAA,eACwF,cAC5D;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAErCH,EADF,CAAAC,cAAA,eAA6B,eACD;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAEnDF,EAFmD,CAAAG,YAAA,EAAM,EACjD,EACF;;;;IAFwBH,EAAA,CAAAa,SAAA,GAAqB;IAArBb,EAAA,CAAA6C,iBAAA,CAAAwD,UAAA,CAAA2B,QAAA,CAAqB;;;;;IAOjDhI,EAFF,CAAAC,cAAA,eACyF,eACnC;IAClDD,EAAA,CAAAuB,SAAA,aAAkC;IAClCvB,EAAA,CAAAC,cAAA,WAAM;IAACD,EAAA,CAAAE,MAAA,6DAAqD;IAEhEF,EAFgE,CAAAG,YAAA,EAAO,EAC/D,EACF;;;;;;IAjJVH,EADR,CAAAC,cAAA,cAAuF,eAIpD;IAFzBD,EAAA,CAAAI,UAAA,mBAAA6H,oGAAA;MAAA,MAAA5B,UAAA,GAAArG,EAAA,CAAAO,aAAA,CAAA2H,IAAA,EAAA5B,SAAA;MAAA,MAAA7F,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0H,qBAAA,CAAA9B,UAAA,CAAA+B,UAAA,CAAwC;IAAA,EAAC;IAGlDpI,EAAA,CAAAC,cAAA,eAA0C;IACxCD,EAAA,CAAAuB,SAAA,YAA4G;IAC9GvB,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,cAA8E;IAC5ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAAqC,gBACmC;IACpED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,eAAgC,iBACP;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;IAEJH,EADF,CAAAC,cAAA,gBAAgC,iBACP;IACrBD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IAEJH,EADF,CAAAC,cAAA,gBAAsC,iBACP;IAC3BD,EAAA,CAAAE,MAAA,IACF;;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAwD,UAAA,KAAA6E,qFAAA,mBAAqJ;IAKvJrI,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,gBAA4F,gBACtD;IAsGlCD,EApGA,CAAAwD,UAAA,KAAA8E,qFAAA,mBAA0I,KAAAC,qFAAA,mBA4FlD,KAAAC,qFAAA,mBASC;IAQrGxI,EAFU,CAAAG,YAAA,EAAM,EACR,EACF;;;;;IAlJIH,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAAkF,WAAA,aAAAzE,MAAA,CAAAgI,gBAAA,CAAApC,UAAA,CAAA+B,UAAA,EAAsD;IAGrCpI,EAAA,CAAAa,SAAA,GAAwF;IAAxFb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAgI,gBAAA,CAAApC,UAAA,CAAA+B,UAAA,2CAAwF;IAEhFpI,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAA0I,WAAA,UAAArC,UAAA,CAAAsC,WAAA,mBAAoD;IAC3E3I,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAmC,UAAA,CAAAuC,IAAA,MACF;IAE8B5I,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuC,cAAA,CAAAqD,UAAA,CAAAwC,MAAA,EAAyC;IACnE7I,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAmC,UAAA,CAAAwC,MAAA,YACF;IAGuB7I,EAAA,CAAAa,SAAA,GAA2B;IAA3Bb,EAAA,CAAA6C,iBAAA,CAAAwD,UAAA,CAAAyC,QAAA,OAA2B;IAIhD9I,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAmC,UAAA,CAAA0C,OAAA,aAAA/I,EAAA,CAAAiE,WAAA,SAAAoC,UAAA,CAAA0C,OAAA,0BACF;IAIE/I,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAmC,UAAA,CAAAX,aAAA,mBAAA1F,EAAA,CAAAiE,WAAA,SAAAoC,UAAA,CAAAX,aAAA,0BACF;IAEqC1F,EAAA,CAAAa,SAAA,GAA4G;IAA5Gb,EAAA,CAAAc,UAAA,UAAAuF,UAAA,kBAAAA,UAAA,CAAAyB,WAAA,KAAAzB,UAAA,CAAAyB,WAAA,CAAA/F,MAAA,SAAAsE,UAAA,kBAAAA,UAAA,CAAA2B,QAAA,KAAA3B,UAAA,CAAA2B,QAAA,QAA4G;IAQ/GhI,EAAA,CAAAa,SAAA,EAAqD;IAArDb,EAAA,CAAAkF,WAAA,YAAAzE,MAAA,CAAAgI,gBAAA,CAAApC,UAAA,CAAA+B,UAAA,EAAqD;IAGjDpI,EAAA,CAAAa,SAAA,GAA0D;IAA1Db,EAAA,CAAAc,UAAA,UAAAuF,UAAA,kBAAAA,UAAA,CAAAyB,WAAA,KAAAzB,UAAA,CAAAyB,WAAA,CAAA/F,MAAA,KAA0D;IA4F7F/B,EAAA,CAAAa,SAAA,EAAmF;IAAnFb,EAAA,CAAAc,UAAA,YAAAuF,UAAA,kBAAAA,UAAA,CAAAyB,WAAA,KAAAzB,UAAA,CAAAyB,WAAA,CAAA/F,MAAA,YAAAsE,UAAA,kBAAAA,UAAA,CAAA2B,QAAA,EAAmF;IASnFhI,EAAA,CAAAa,SAAA,EAAoF;IAApFb,EAAA,CAAAc,UAAA,YAAAuF,UAAA,kBAAAA,UAAA,CAAAyB,WAAA,KAAAzB,UAAA,CAAAyB,WAAA,CAAA/F,MAAA,aAAAsE,UAAA,kBAAAA,UAAA,CAAA2B,QAAA,EAAoF;;;;;;IAhL7GhI,EAAA,CAAAkD,uBAAA,GAAoE;IAE7DlD,EAAA,CAAAC,cAAA,aAG2B;IAFzBD,EAAA,CAAAI,UAAA,mBAAA4I,4FAAA;MAAA,MAAAC,KAAA,GAAAjJ,EAAA,CAAAO,aAAA,CAAA2I,IAAA,EAAArE,KAAA;MAAA,MAAApE,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA0I,wBAAA,CAAAF,KAAA,CAA2B;IAAA,EAAC;IAKjCjJ,EAFT,CAAAC,cAAA,aAAsC,cACG,cACG;IACtCD,EAAA,CAAAuB,SAAA,YAA+F;IAC5FvB,EAAA,CAAAG,YAAA,EAAM;IACXH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1CH,EAAA,CAAAC,cAAA,eAAqF;IACnFD,EAAA,CAAAE,MAAA,GACF;IAEJF,EAFI,CAAAG,YAAA,EAAO,EACH,EACH;IAICH,EAHN,CAAAC,cAAA,cAAsC,eACV,eACD,iBACa;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7CH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAAuC;;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAuB,iBACa;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnDH,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA4C;;IAI5EF,EAJ4E,CAAAG,YAAA,EAAO,EACvE,EACF,EACH,EACF;IAKCH,EAHN,CAAAC,cAAA,cAA2E,cAC7C,eACK,eACM;IACjCD,EAAA,CAAAwD,UAAA,KAAA4F,8EAAA,oBAAuF;IAyJ/FpJ,EAHM,CAAAG,YAAA,EAAM,EACF,EACH,EACF;;;;;;;IAxLEH,EAAA,CAAAa,SAAA,EAAyC;IAAzCb,EAAA,CAAAkF,WAAA,aAAAzE,MAAA,CAAA4I,mBAAA,CAAAJ,KAAA,EAAyC;IAKzBjJ,EAAA,CAAAa,SAAA,GAA2E;IAA3Eb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAA4I,mBAAA,CAAAJ,KAAA,2CAA2E;IAEtEjJ,EAAA,CAAAa,SAAA,GAAe;IAAfb,EAAA,CAAA6C,iBAAA,CAAAyG,OAAA,CAAAjE,KAAA,CAAe;IACArF,EAAA,CAAAa,SAAA,EAA+C;IAA/Cb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAuC,cAAA,CAAAsG,OAAA,CAAAC,eAAA,EAA+C;IAClFvJ,EAAA,CAAAa,SAAA,EACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAoF,OAAA,CAAAC,eAAA,MACF;IAO0BvJ,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAiE,WAAA,SAAAqF,OAAA,CAAAP,OAAA,gBAAuC;IAIvC/I,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAA6C,iBAAA,CAAA7C,EAAA,CAAAiE,WAAA,SAAAqF,OAAA,CAAAE,YAAA,gBAA4C;IAM1CxJ,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAkF,WAAA,YAAAzE,MAAA,CAAA4I,mBAAA,CAAAJ,KAAA,EAAwC;IAItBjJ,EAAA,CAAAa,SAAA,GAAyC;IAAzCb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAgJ,8BAAA,CAAAH,OAAA,CAAAI,EAAA,EAAyC;;;;;IApCjG1J,EAHD,CAAAC,cAAA,cAAkF,cAC3B,gBACb,YAClC;IACLD,EAAA,CAAAwD,UAAA,IAAAmG,uEAAA,6BAAoE;IAiMvE3J,EAHC,CAAAG,YAAA,EAAQ,EACF,EACC,EACF;;;;IAjM2BH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,YAAAL,MAAA,CAAAkC,kBAAA,CAAuB;;;;;IA/B7D3C,EAAA,CAAAkD,uBAAA,GAAuF;IAElFlD,EADF,CAAAC,cAAA,cAAmC,aACK;IAyBtCD,EAvBA,CAAAwD,UAAA,IAAAoG,wDAAA,kBAA4C,IAAAC,wDAAA,kBAaoC,IAAAC,wDAAA,kBAUE;IAuMrF9J,EADC,CAAAG,YAAA,EAAM,EACD;;;;;IA9NqBH,EAAA,CAAAa,SAAA,GAAkB;IAAlBb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAyF,YAAA,CAAkB;IAalBlG,EAAA,CAAAa,SAAA,EAAsD;IAAtDb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAyF,YAAA,IAAAzF,MAAA,CAAAkC,kBAAA,CAAAZ,MAAA,OAAsD;IAUlD/B,EAAA,CAAAa,SAAA,EAAoD;IAApDb,EAAA,CAAAc,UAAA,UAAAL,MAAA,CAAAyF,YAAA,IAAAzF,MAAA,CAAAkC,kBAAA,CAAAZ,MAAA,KAAoD;;;;;;IArR/E/B,EANV,CAAAC,cAAA,aAAqD,cAEhB,cACC,cACR,cACE,eACK;IAAAD,EAAA,CAAAE,MAAA,GAEvB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACXH,EAAA,CAAAC,cAAA,eAA8C;IAAAD,EAAA,CAAAE,MAAA,GAC1C;IACNF,EADM,CAAAG,YAAA,EAAO,EACP;IACNH,EAAA,CAAAC,cAAA,cAAgC;IAC9BD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,eAA0B,kBACgF;IAAnBD,EAAA,CAAAI,UAAA,mBAAA2J,4DAAA;MAAA/J,EAAA,CAAAO,aAAA,CAAAyJ,GAAA;MAAA,MAAAvJ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAwJ,MAAA,EAAQ;IAAA,EAAC;IACrGjK,EAAA,CAAAuB,SAAA,aAAsC;IACtCvB,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAA8J,uDAAA5J,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAyJ,GAAA;MAAA,MAAAvJ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,OAAA,CAAQ,SAAS,EAAAN,MAAA,CAAS;IAAA,EAAC;IACpCN,EAAA,CAAAE,MAAA,wBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAOLH,EANA,CAAAwD,UAAA,KAAA2G,wCAAA,iBAAuD,KAAAC,wCAAA,iBAMA;IAMzDpK,EAAA,CAAAG,YAAA,EAAK;IAGLH,EAAA,CAAAC,cAAA,eAAyE;IAEvED,EAAA,CAAAwD,UAAA,KAAA6G,4CAAA,qBAA4H;IAG9HrK,EAAA,CAAAG,YAAA,EAAM;IAWLH,EARD,CAAAwD,UAAA,KAAA8G,yCAAA,kBAA0F,KAAAC,yCAAA,mBAQC;IAkB7FvK,EAAA,CAAAG,YAAA,EAAM;IAINH,EAAA,CAAAC,cAAA,eAAuB;IAyKrBD,EAvKA,CAAAwD,UAAA,KAAAgH,kDAAA,6BAAyD,KAAAC,kDAAA,2BAgH8B,KAAAC,kDAAA,2BAuDA;IAqO3F1K,EADE,CAAAG,YAAA,EAAM,EACF;;;;IA/d+BH,EAAA,CAAAa,SAAA,GAEvB;IAFuBb,EAAA,CAAAkE,kBAAA,cAAAzD,MAAA,CAAAqC,MAAA,CAAA6H,YAAA,WAEvB;IAC0C3K,EAAA,CAAAa,SAAA,GAC1C;IAD0Cb,EAAA,CAAA6C,iBAAA,CAAApC,MAAA,CAAAqC,MAAA,CAAA8H,gBAAA,OAC1C;IAGJ5K,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAkE,kBAAA,MAAAzD,MAAA,CAAAqC,MAAA,CAAA+H,UAAA,YACF;IAe4D7K,EAAA,CAAAa,SAAA,GAAiD;IAAjDb,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAe,eAAA,KAAAC,GAAA,EAAAP,MAAA,CAAAQ,WAAA,gBAAiD;IAKzFjB,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAqK,uBAAA,GAA+B;IAM/B9K,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAsK,uBAAA,GAA+B;IAWkB/K,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,eAA+B;IAM7EjB,EAAA,CAAAa,SAAA,EAA6D;IAA7Db,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,mBAAAR,MAAA,CAAAqK,uBAAA,GAA6D;IAQ5D9K,EAAA,CAAAa,SAAA,EAA6D;IAA7Db,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,mBAAAR,MAAA,CAAAsK,uBAAA,GAA6D;IAwB1E/K,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,iBAAAR,MAAA,CAAAqC,MAAA,CAAwC;IAgHxC9C,EAAA,CAAAa,SAAA,EAAsE;IAAtEb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,kBAAAR,MAAA,CAAAqC,MAAA,IAAArC,MAAA,CAAAqK,uBAAA,GAAsE;IAuDtE9K,EAAA,CAAAa,SAAA,EAAsE;IAAtEb,EAAA,CAAAc,UAAA,SAAAL,MAAA,CAAAQ,WAAA,kBAAAR,MAAA,CAAAqC,MAAA,IAAArC,MAAA,CAAAsK,uBAAA,GAAsE;;;;;;IA6OvF/K,EAFH,CAAAC,cAAA,eAAkC,eACU,eACR;IAC/BD,EAAA,CAAAkD,uBAAA,GAAc;IAEZlD,EAAA,CAAAC,cAAA,UAAK;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAM;;IAEjCH,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAyB,aAItB;IADCD,EAAA,CAAAI,UAAA,mBAAA4K,8DAAA;MAAAhL,EAAA,CAAAO,aAAA,CAAA0K,IAAA;MAAA,MAAAxK,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyK,SAAA,EAAW;IAAA,EAAC;IAG3BlL,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;IAYAH,EAVN,CAAAC,cAAA,eAGC,gBAE2D,gBAElC,gBACD,gBACG,kBACyC;IACvED,EAAA,CAAAE,MAAA,iCAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAEpCH,EAAA,CAAAuB,SAAA,sBAEsB;IAMtBvB,EAJI,CAAAG,YAAA,EAAM,EACF,EAGF;IAMAH,EAHN,CAAAC,cAAA,gBAAsB,gBACG,gBACG,kBACmC;IACvDD,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAuB,SAAA,qBAMY;IAGlBvB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAsB,gBACG,gBACG,kBAC0C;IAC9DD,EAAA,CAAAE,MAAA,wBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACRH,EAAA,CAAAuB,SAAA,qBAMY;IAGlBvB,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAMAH,EAHN,CAAAC,cAAA,gBAAsB,gBACG,gBACG,kBACyC;IAC7DD,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACNH,EAAA,CAAAuB,SAAA,qBAMU;IAUlBvB,EATI,CAAAG,YAAA,EAAM,EACF,EAGF,EAGD,EAEC;IAIFH,EAFJ,CAAAC,cAAA,gBAA8C,WACvC,mBAKF;IADCD,EAAA,CAAAI,UAAA,mBAAA+K,oEAAA;MAAAnL,EAAA,CAAAO,aAAA,CAAA0K,IAAA;MAAA,MAAAxK,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyK,SAAA,EAAW;IAAA,EAAC;IAErBlL,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EACP;IAAAH,EAAA,CAAAE,MAAA,eACD;IAAAF,EAAA,CAAAC,cAAA,mBAMC;IADAD,EAAA,CAAAI,UAAA,mBAAAgL,oEAAA;MAAApL,EAAA,CAAAO,aAAA,CAAA0K,IAAA;MAAA,MAAAxK,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA4K,mBAAA,EAAqB;IAAA,EAAC;IAC/BrL,EAAA,CAAAE,MAAA,eACA;IAOPF,EAJM,CAAAG,YAAA,EAAS,EAEL,EACF,EACF;;;;IAtGgCH,EAAA,CAAAa,SAAA,GAAuB;IAAvBb,EAAA,CAAAc,UAAA,cAAAL,MAAA,CAAA6K,SAAA,CAAuB;IAQhDtL,EAAA,CAAAa,SAAA,GAA6B;IAAqBb,EAAlD,CAAAc,UAAA,UAAAL,MAAA,CAAA8K,mBAAA,CAA6B,oBAAoB,mBAAmB;;;AD9fjF,OAAM,MAAOC,mBAAmB;EAqCpBC,KAAA;EACAC,MAAA;EACAC,YAAA;EACAC,GAAA;EACAC,EAAA;EAGAC,UAAA;EACIC,wBAAA;EAEJC,cAAA;EA9CRV,SAAS;EACXC,mBAAmB,GAAE,CAAC,UAAU,EAAC,uBAAuB,EAAC,cAAc,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,mBAAmB,CAAC;EAE1HU,QAAQ,GAAkB,IAAI;EAC9BnJ,MAAM,GAAQ,IAAI;EAClBjB,SAAS,GAAY,KAAK,CAAC,CAAC;EAC5BC,YAAY,GAAU,EAAE;EACxBqD,kBAAkB,GAAW,CAAC,CAAC,CAAC;EAChC+G,iBAAiB,GAAQ,EAAE;EAC3BC,mBAAmB,GAAQ,EAAE;EACpClL,WAAW,GAAQ,SAAS;EAC5B2J,gBAAgB,GAAO,EAAE;EAClBwB,eAAe,GAAU,EAAE;EAC3BlG,YAAY,GAAW,EAAE;EACzBvD,kBAAkB,GAA6I,EAAE;EACjKF,2BAA2B,GAAQ,IAAI;EACvC4J,eAAe,GAAU,EAAE;EAC3BC,SAAS,GAAM,EAAE;EACjBC,OAAO,GAAY,KAAK;EAC/BC,YAAY;EACLC,kBAAkB,GAAgB,IAAIC,GAAG,EAAE;EAC3CC,eAAe,GAAgB,IAAID,GAAG,EAAE;EACxCE,kBAAkB,GAA8B,EAAE;EACjDC,iBAAiB,GAAiB,IAAIrN,YAAY,EAAE;EACpDsN,uBAAuB,GAAiB,IAAItN,YAAY,EAAE;EACpEuN,UAAU,GAAG,CACX;IAAEC,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACrC;IAAED,IAAI,EAAE,aAAa;IAAEC,KAAK,EAAE;EAAa,CAAE,EAC7C;IAAED,IAAI,EAAE,WAAW;IAAEC,KAAK,EAAE;EAAW,CAAE,EACzC;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,CACtC;EACC;EACOC,YAAY,GAAW,aAAa,CAAC,CAAC;EACtCC,SAAS,GAAkB,IAAI;EAEtCC,YACU3B,KAAqB,EACrBC,MAAc,EACdC,YAAsB,EACtBC,GAAsB,EACtBC,EAAe;EAC3B;EAEYC,UAAqB,EACjBC,wBAAkD,EAEtDC,cAA8B;IAV9B,KAAAP,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,YAAY,GAAZA,YAAY;IACZ,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,EAAE,GAAFA,EAAE;IAGF,KAAAC,UAAU,GAAVA,UAAU;IACN,KAAAC,wBAAwB,GAAxBA,wBAAwB;IAE5B,KAAAC,cAAc,GAAdA,cAAc;EACrB;EAEHqB,QAAQA,CAAA;IACN,IAAI,CAACf,SAAS,GAAG,IAAI,CAACR,UAAU,CAACwB,eAAe,EAAE;IAClD,IAAI,CAACf,OAAO,GAAG,IAAI,CAACgB,YAAY,EAAE;IAElC;IACA,IAAI,CAACT,uBAAuB,GAAG,IAAI,CAACrB,KAAK,CAAC+B,WAAW,CAACC,SAAS,CAACC,MAAM,IAAG;MACvE,IAAI,CAACR,YAAY,GAAGQ,MAAM,CAAC,MAAM,CAAC,IAAI,aAAa;MACnD,IAAI,CAACP,SAAS,GAAGO,MAAM,CAAC,WAAW,CAAC,GAAGC,MAAM,CAACD,MAAM,CAAC,WAAW,CAAC,CAAC,GAAG,IAAI;MACzEE,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE;QAAEX,YAAY,EAAE,IAAI,CAACA,YAAY;QAAEC,SAAS,EAAE,IAAI,CAACA;MAAS,CAAE,CAAC;IAC5G,CAAC,CAAC;IAEF;IACA,IAAI,CAACN,iBAAiB,GAAG,IAAI,CAACpB,KAAK,CAACqC,QAAQ,CAACL,SAAS,CAACC,MAAM,IAAG;MAC9D,MAAMK,OAAO,GAAGL,MAAM,CAACM,GAAG,CAAC,IAAI,CAAC;MAChC,IAAI,CAAC/B,QAAQ,GAAG8B,OAAO,GAAGJ,MAAM,CAACI,OAAO,CAAC,GAAG,IAAI;MAEhD,IAAI,IAAI,CAAC9B,QAAQ,EAAE;QACjB,IAAI,CAACgC,kBAAkB,EAAE;QACzB,IAAI,CAAChI,oBAAoB,EAAE;QAC3B,IAAI,CAACiI,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC;IACF,IAAI,CAACC,QAAQ,EAAE;EACjB;EACAA,QAAQA,CAAA;IACN,IAAI,CAAC7C,SAAS,GAAG,IAAI,CAACO,EAAE,CAACuC,KAAK,CAAC;MAC9B7J,eAAe,EAAC,CAAC,EAAE,CAAC;MACpBC,aAAa,EAAC,CAAC,EAAE,CAAC;MAClBC,WAAW,EAAC,CAAC,EAAE,CAAC;MAChBxB,oBAAoB,EAAC,CAAC,IAAI;KAC1B,CAAC;IAEF;IACA,IAAI,CAAC2I,GAAG,CAACyC,aAAa,EAAE;EAC1B;EACAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAAC0B,WAAW,EAAE;IACtC;IACA,IAAI,IAAI,CAACzB,uBAAuB,EAAE;MAChC,IAAI,CAACA,uBAAuB,CAACyB,WAAW,EAAE;IAC5C;EACF;EAEON,kBAAkBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAACpK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmK,cAAc,CAACwC,SAAS,CAAC;MAAEvC,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE,CAAC,CAACwB,SAAS,CAAC;MACnEgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7M,SAAS,GAAG,KAAK;QACtB+L,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEa,GAAG,CAAC;QACxC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB,IAAI,CAAC7L,MAAM,GAAG4L,GAAG,CAACE,YAAY,EAAEC,IAAI,IAAIH,GAAG,CAACE,YAAY,IAAI,IAAI;UAChEhB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE,IAAI,CAAC/K,MAAM,CAAC;UACjD8K,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,IAAI,CAAC/K,MAAM,EAAE+H,UAAU,CAAC;UAChE+C,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACjM,MAAM,IAAI,EAAE,CAAC,CAAC;UACjE,IAAI,CAAC8H,gBAAgB,GAAG,IAAI,CAAC9H,MAAM,EAAE8H,gBAAgB,IAAI,EAAE;UAC3D;UACA,IAAI,CAAC3J,WAAW,GAAG,SAAS;QAC9B,CAAC,MAAM;UACL2M,OAAO,CAACoB,KAAK,CAAC,qBAAqB,EAAEN,GAAG,CAACO,YAAY,CAAC;UACtD,IAAI,CAACnM,MAAM,GAAG,IAAI;QACpB;QACA,IAAI,CAAC8I,GAAG,CAACsD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACnN,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC+J,GAAG,CAACsD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOjJ,oBAAoBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACgG,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAACpK,SAAS,GAAG,IAAI;IACrB,IAAI,CAACqE,YAAY,GAAG,EAAE;IACtB,IAAI,CAAC8F,cAAc,CAACmD,aAAa,CAAC;MAAElD,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE,CAAC,CAACwB,SAAS,CAAC;MACvEgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7M,SAAS,GAAG,KAAK;QACtB,IAAI6M,GAAG,EAAEC,OAAO,EAAE;UAChB,IAAI,CAACzI,YAAY,GAAGwI,GAAG,CAACO,YAAY,IAAI,wBAAwB;QAClE,CAAC,MAAM;UACL,MAAMG,OAAO,GAAGV,GAAG,CAACE,YAAY,EAAEQ,OAAO,IAAI,EAAE;UAC/C,IAAI,CAAChD,eAAe,GAAGgD,OAAO,CAACC,GAAG,CAAEC,CAAM,KAAM;YAC9ClH,UAAU,EAACkH,CAAC,CAAClH,UAAU;YACvBQ,IAAI,EAAE0G,CAAC,CAACC,QAAQ;YAChBzG,QAAQ,EAAEwG,CAAC,CAACE,UAAU;YACtB3G,MAAM,EAAEyG,CAAC,CAACG,UAAU;YACpB/J,aAAa,EAAE4J,CAAC,CAACI,aAAa,GAAG,IAAIC,IAAI,CAACL,CAAC,CAACI,aAAa,CAAC,GAAG,IAAI;YACjE3G,OAAO,EAAEuG,CAAC,CAACM,OAAO,GAAG,IAAID,IAAI,CAACL,CAAC,CAACM,OAAO,CAAC,GAAG,IAAI;YAC/CpG,YAAY,EAAE8F,CAAC,CAAC9F,YAAY,GAAG,IAAImG,IAAI,CAACL,CAAC,CAAC9F,YAAY,CAAC,GAAG,IAAI;YAC9DxB,QAAQ,EAAEsH,CAAC,CAACzH,QAAQ;YACpBC,WAAW,EAAEwH,CAAC,CAACO,WAAW,IAAI,EAAE;YAChCC,WAAW,EAAER,CAAC,CAACS,WAAW;YAC1BpH,WAAW,EAAE2G,CAAC,CAAC3G,WAAW;YAC1BqH,cAAc,EAAEV,CAAC,CAACU,cAAc;YAChCjJ,oBAAoB,EAAEuI,CAAC,CAACvI,oBAAoB;YAC5CC,kBAAkB,EAAEsI,CAAC,CAACtI;WACvB,CAAC,CAAC;UAEH;UACA,MAAMiJ,WAAW,GAA6B,EAAE;UAChD,IAAI,CAAC7D,eAAe,CAAC8D,OAAO,CAAEC,EAAO,IAAI;YACvC,MAAMC,GAAG,GAAGC,MAAM,CAACF,EAAE,CAACL,WAAW,IAAI,SAAS,CAAC;YAC/C,IAAI,CAACG,WAAW,CAACG,GAAG,CAAC,EAAE;cAAEH,WAAW,CAACG,GAAG,CAAC,GAAG,EAAE;YAAE;YAChDH,WAAW,CAACG,GAAG,CAAC,CAACE,IAAI,CAACH,EAAE,CAAC;UAC3B,CAAC,CAAC;UAEF,IAAI,CAACxN,kBAAkB,GAAGmM,MAAM,CAACC,IAAI,CAACkB,WAAW,CAAC,CAACZ,GAAG,CAAEe,GAAG,IAAI;YAC7D,MAAMG,KAAK,GAAGN,WAAW,CAACG,GAAG,CAAC;YAC9B;YACA,MAAMI,WAAW,GAAQ;cACvB,oBAAoB,EAAE,CAAC;cACvB,cAAc,EAAE,CAAC;cACjB,wBAAwB,EAAE,CAAC;cAC3B,UAAU,EAAE;aACb;YACD,MAAMjH,eAAe,GAAGgH,KAAK,CAACE,MAAM,CAAC,CAACC,GAAW,EAAEC,EAAO,KAAI;cAC5D,MAAMC,CAAC,GAAGJ,WAAW,CAACE,GAAG,CAAC,IAAI,CAAC;cAAE,MAAMG,CAAC,GAAGL,WAAW,CAACG,EAAE,CAAC9H,MAAM,CAAC,IAAI,CAAC;cAAE,OAAOgI,CAAC,GAAGD,CAAC,GAAGD,EAAE,CAAC9H,MAAM,GAAG6H,GAAG;YACxG,CAAC,EAAE,EAAE,CAAC;YAEN;YACA,MAAM3H,OAAO,GAAGwH,KAAK,CAACE,MAAM,CAAC,CAACC,GAAgB,EAAEC,EAAO,KAAI;cACzD,IAAI,CAACA,EAAE,CAAC5H,OAAO,EAAE;gBAAE,OAAO2H,GAAG;cAAE;cAC/B,IAAI,CAACA,GAAG,EAAE;gBAAE,OAAOC,EAAE,CAAC5H,OAAO;cAAE;cAC/B,OAAO2H,GAAG,GAAGC,EAAE,CAAC5H,OAAO,GAAG4H,EAAE,CAAC5H,OAAO,GAAG2H,GAAG,CAAC,CAAC;YAC9C,CAAC,EAAE,IAAmB,CAAC;YAEvB,MAAMhL,aAAa,GAAG6K,KAAK,CAACE,MAAM,CAAC,CAACC,GAAgB,EAAEC,EAAO,KAAI;cAC/D,IAAI,CAACA,EAAE,CAACjL,aAAa,EAAE;gBAAE,OAAOgL,GAAG;cAAE;cACrC,IAAI,CAACA,GAAG,EAAE;gBAAE,OAAOC,EAAE,CAACjL,aAAa;cAAE;cACrC,OAAOgL,GAAG,GAAGC,EAAE,CAACjL,aAAa,GAAGiL,EAAE,CAACjL,aAAa,GAAGgL,GAAG,CAAC,CAAC;YAC1D,CAAC,EAAE,IAAmB,CAAC;YAEvB;YACA,MAAMlH,YAAY,GAAG+G,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACnH,YAAY,CAAC,EAAEA,YAAY,IACvD+G,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACI,WAAW,CAAC,EAAEA,WAAW,IACpD,IAAI;YAExB;YACA,MAAMf,cAAc,GAAGO,KAAK,CAAC,CAAC,CAAC,EAAEP,cAAc,IAAI,aAAaI,GAAG,EAAE;YAErE,OAAO;cACL1G,EAAE,EAAE0G,GAAG;cACP/K,KAAK,EAAE2K,cAAc;cACrBzG,eAAe,EAAEA,eAAe,IAAKgH,KAAK,CAAC,CAAC,CAAC,EAAE1H,MAAM,IAAI,EAAG;cAC5DW,YAAY,EAAEA,YAAY,GAAG,IAAImG,IAAI,CAACnG,YAAY,CAAC,GAAG,IAAI;cAC1DT,OAAO,EAAEA,OAAO;cAChBrD,aAAa,EAAEA;aAChB;UACH,CAAC,CAAC,CAACsL,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,KAAI;YACf;YACA,IAAI,CAACD,CAAC,CAACpH,YAAY,IAAI,CAACqH,CAAC,CAACrH,YAAY,EAAE,OAAO,CAAC;YAChD,IAAI,CAACoH,CAAC,CAACpH,YAAY,EAAE,OAAO,CAAC;YAC7B,IAAI,CAACqH,CAAC,CAACrH,YAAY,EAAE,OAAO,CAAC,CAAC;YAC9B,OAAOqH,CAAC,CAACrH,YAAY,CAACyH,OAAO,EAAE,GAAGL,CAAC,CAACpH,YAAY,CAACyH,OAAO,EAAE;UAC5D,CAAC,CAAC;UAEF;UACA,IAAI,IAAI,CAACtO,kBAAkB,CAACZ,MAAM,GAAG,CAAC,EAAE;YACtC,IAAI,CAACU,2BAA2B,GAAG,IAAI,CAACE,kBAAkB,CAAC,CAAC,CAAC,CAAC+G,EAAE;YAChE,IAAI,CAACwC,iBAAiB,GAAG,IAAI,CAACvJ,kBAAkB,CAAC,CAAC,CAAC,CAAC0C,KAAK;YACzD,IAAI,CAAC8G,mBAAmB,GAAG,IAAI,CAACxJ,kBAAkB,CAAC,CAAC,CAAC,CAAC4G,eAAe;UACvE;QACF;QACA,IAAI,CAACqC,GAAG,CAACsD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACrP,SAAS,GAAG,KAAK;QACtB,IAAI,CAACqE,YAAY,GAAG,wBAAwB;QAC5C,IAAI,CAAC0F,GAAG,CAACsD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOhB,oBAAoBA,CAAA;IACzB,IAAI,CAAC,IAAI,CAACjC,QAAQ,EAAE;MAAE;IAAQ;IAC9B,IAAI,CAACpK,SAAS,GAAG,IAAI;IAErB,IAAI,CAACmK,cAAc,CAACmF,kBAAkB,CAAC;MACrClF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBmF,IAAI,EAAE,EAAE;MACRC,IAAI,EAAE;KACP,CAAC,CAAC5D,SAAS,CAAC;MACXgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7M,SAAS,GAAG,KAAK;QACtB,IAAI6M,GAAG,EAAEC,OAAO,EAAE;UAChBf,OAAO,CAACoB,KAAK,CAAC,kCAAkC,EAAEN,GAAG,CAACO,YAAY,CAAC;QACrE,CAAC,MAAM;UACL,IAAI,CAAC5C,eAAe,GAAGqC,GAAG,CAACE,YAAY,EAAEC,IAAI,IAAIH,GAAG,CAACG,IAAI,IAAI,EAAE;UAE/D;UACA,IAAI,CAAC/M,YAAY,GAAG,IAAI,CAACuK,eAAe,CAACgD,GAAG,CAAEiC,MAAW,KAAM;YAC7DlJ,UAAU,EAAEkJ,MAAM,CAAClJ,UAAU;YAC7B/C,KAAK,EAAEiM,MAAM,CAACtB,cAAc;YAC5BA,cAAc,EAAEsB,MAAM,CAACtB,cAAc;YAAE;YACvCxK,eAAe,EAAE8L,MAAM,CAAC9L,eAAe;YACvC+L,cAAc,EAAED,MAAM,CAACC,cAAc;YACrCC,kBAAkB,EAAEF,MAAM,CAACE,kBAAkB;YAC7CC,UAAU,EAAEH,MAAM,CAACG,UAAU;YAC7B9L,gBAAgB,EAAE2L,MAAM,CAAC3L,gBAAgB;YACzCL,0BAA0B,EAAEgM,MAAM,CAAChM,0BAA0B;YAC7DG,YAAY,EAAE6L,MAAM,CAAC7L,YAAY,GAAG,IAAIkK,IAAI,CAAC2B,MAAM,CAAC7L,YAAY,CAAC,GAAG,IAAI;YACxEC,aAAa,EAAE4L,MAAM,CAAC5L,aAAa,GAAG,IAAIiK,IAAI,CAAC2B,MAAM,CAAC5L,aAAa,CAAC,GAAG,IAAI;YAC3E0J,OAAO,EAAE,CAAC;cACRxG,IAAI,EAAE0I,MAAM,CAACtB,cAAc;cAC3BxK,eAAe,EAAE8L,MAAM,CAAC9L,eAAe;cACvC+L,cAAc,EAAED,MAAM,CAACC,cAAc;cACrCC,kBAAkB,EAAEF,MAAM,CAACE,kBAAkB;cAC7CC,UAAU,EAAEH,MAAM,CAACG,UAAU;cAC7B9L,gBAAgB,EAAE2L,MAAM,CAAC3L,gBAAgB;cACzCL,0BAA0B,EAAEgM,MAAM,CAAChM,0BAA0B;cAC7DG,YAAY,EAAE6L,MAAM,CAAC7L,YAAY,GAAG,IAAIkK,IAAI,CAAC2B,MAAM,CAAC7L,YAAY,CAAC,GAAG,IAAI;cACxEC,aAAa,EAAE4L,MAAM,CAAC5L,aAAa,GAAG,IAAIiK,IAAI,CAAC2B,MAAM,CAAC5L,aAAa,CAAC,GAAG;aACxE;WACF,CAAC,CAAC;UAEH;UACA,IAAI,IAAI,CAAC5D,YAAY,CAACC,MAAM,GAAG,CAAC,EAAE;YAChC,IAAI,CAACoD,kBAAkB,GAAG,CAAC;YAC3B,IAAI,CAAC+G,iBAAiB,GAAG,IAAI,CAACpK,YAAY,CAAC,CAAC,CAAC,CAACuD,KAAK;YACnD,IAAI,CAAC8G,mBAAmB,GAAG,IAAI,CAACrK,YAAY,CAAC,CAAC,CAAC,CAACwD,0BAA0B,IAAI,SAAS;UACzF;QACF;QACA,IAAI,CAACsG,GAAG,CAACsD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACrP,SAAS,GAAG,KAAK;QACtB+L,OAAO,CAACoB,KAAK,CAAC,iCAAiC,EAAEkC,GAAG,CAAC;QACrD,IAAI,CAACtF,GAAG,CAACsD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOxN,0BAA0BA,CAAA;IAC/B,IAAI,CAAC,IAAI,CAAC2K,eAAe,IAAI,IAAI,CAACA,eAAe,CAACtK,MAAM,KAAK,CAAC,EAAE;MAAE;IAAQ;IAE1E,MAAM2P,OAAO,GAAkC,EAAE;IACjD,IAAI,CAACrF,eAAe,CAAC6D,OAAO,CAAEZ,CAAM,IAAI;MACtC,MAAMc,GAAG,GAAGd,CAAC,CAACU,cAAc,IAAI,eAAe;MAC/C,IAAI,CAAC0B,OAAO,CAACtB,GAAG,CAAC,EAAE;QAAEsB,OAAO,CAACtB,GAAG,CAAC,GAAG,EAAE;MAAE;MACxCsB,OAAO,CAACtB,GAAG,CAAC,CAACE,IAAI,CAAChB,CAAC,CAAC;IACtB,CAAC,CAAC;IAEF,MAAMqC,GAAG,GAAG,IAAI7R,KAAK,CAAC;MAAE8R,WAAW,EAAE,UAAU;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAI,CAAE,CAAC;IAC5E,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;IAClD;IACA,MAAMC,MAAM,GAAG;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,GAAG,EAAE;IAAE,CAAE;IAC/C,IAAIC,CAAC,GAAGJ,MAAM,CAACG,GAAG;IAElB;IACAX,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;IAChCb,GAAG,CAACc,WAAW,CAAC,EAAE,CAAC;IACnBd,GAAG,CAAC3E,IAAI,CAAC,aAAa,IAAI,CAAClK,MAAM,EAAE6H,YAAY,IAAI,EAAE,EAAE,EAAGwH,MAAM,CAACC,IAAI,EAAEG,CAAC,CAAC;IACzEA,CAAC,IAAI,EAAE;IAEP,MAAMG,WAAW,GAAGA,CAACC,QAAgB,EAAEpC,KAAY,KAAI;MACrD;MACAoB,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCb,GAAG,CAACc,WAAW,CAAC,EAAE,CAAC;MACnBd,GAAG,CAAC3E,IAAI,CAAC,GAAG2F,QAAQ,CAACC,WAAW,EAAE,kBAAkB,EAAEb,SAAS,GAAG,CAAC,EAAEQ,CAAC,EAAE;QACtEM,KAAK,EAAE;OACR,CAAC;MACFN,CAAC,IAAI,EAAE;MAEP;MACA,MAAMO,SAAS,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAItG,GAAG,CAAC6D,KAAK,CACvClB,GAAG,CAAEsB,EAAO,IAAK,CAACA,EAAE,CAAChL,gBAAgB,IAAI,EAAE,EAAEsN,QAAQ,EAAE,CAACC,IAAI,EAAE,CAAC,CAC/DC,MAAM,CAAEC,CAAS,IAAKA,CAAC,CAAC,CAAC,CAAC;MAC7BzB,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCb,GAAG,CAACc,WAAW,CAAC,EAAE,CAAC;MACnBd,GAAG,CAAC3E,IAAI,CAAC,aAAa8F,SAAS,CAACO,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAEtB,SAAS,GAAG,CAAC,EAAEQ,CAAC,EAAE;QAAEM,KAAK,EAAE;MAAQ,CAAE,CAAC;MAC1FN,CAAC,IAAI,EAAE;MAEP;MACA,MAAM9M,YAAY,GAAG8K,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAAClL,YAAY,CAAC,EAAEA,YAAY;MAC3E,MAAM6N,aAAa,GAAG/C,KAAK,CAACO,IAAI,CAAEH,EAAO,IAAKA,EAAE,CAACjL,aAAa,CAAC,EAAEA,aAAa;MAC9EiM,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCb,GAAG,CAACc,WAAW,CAAC,CAAC,CAAC;MAClBd,GAAG,CAAC3E,IAAI,CACN,kBAAkBvH,YAAY,GAAG,IAAIkK,IAAI,CAAClK,YAAY,CAAC,CAAC8N,kBAAkB,EAAE,GAAG,EAAE,EAAE,EACnFpB,MAAM,CAACC,IAAI,EACXG,CAAC,CACF;MACDZ,GAAG,CAAC3E,IAAI,CACN,kBAAkBsG,aAAa,GAAG,IAAI3D,IAAI,CAAC2D,aAAa,CAAC,CAACC,kBAAkB,EAAE,GAAG,EAAE,EAAE,EACrFxB,SAAS,GAAGI,MAAM,CAACE,KAAK,EACxBE,CAAC,EACD;QAAEM,KAAK,EAAE;MAAO,CAAE,CACnB;MACDN,CAAC,IAAI,CAAC;MAEN,MAAMiB,IAAI,GAAGjD,KAAK,CAAClB,GAAG,CAAC,CAACsB,EAAE,EAAE8C,GAAG,KAAK,CAClC,CAACA,GAAG,GAAG,CAAC,EAAER,QAAQ,EAAE,EACpBtC,EAAE,CAACnL,eAAe,IAAI,EAAE,EACxB,CAACmL,EAAE,CAACY,cAAc,IAAI,EAAE,EAAE0B,QAAQ,EAAE,EACpC,CAACtC,EAAE,CAACc,UAAU,IAAI,EAAE,EAAEwB,QAAQ,EAAE,EAChCtC,EAAE,CAACrL,0BAA0B,IAAI,EAAE,CACpC,CAAC;MAEFvF,SAAS,CAAC4R,GAAG,EAAE;QACb+B,MAAM,EAAEnB,CAAC,GAAG,CAAC;QACboB,IAAI,EAAE,CAAC,CACL,GAAG,EACH,WAAW,EACX,iBAAiB,EACjB,cAAc,EACd,QAAQ,CACT,CAAC;QACFC,IAAI,EAAEJ,IAAI;QACVrB,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDwB,MAAM,EAAE;UACNC,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,CAAC;UACXC,WAAW,EAAE,CAAC;UACdC,MAAM,EAAE,KAAK;UACbC,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;SACZ;QACDC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE,GAAG;UAAEC,MAAM,EAAE,QAAQ;UAAER,QAAQ,EAAE;QAAC,CAAE;QACxF;QACA;QACAS,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEL,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAAI;UACjE,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAED,QAAQ,EAAE;UAAW,CAAE;UAAsB;UACjE,CAAC,EAAE;YAAEC,SAAS,EAAE,GAAG;YAAED,QAAQ,EAAE;UAAW,CAAE;UAAqB;UACjE,CAAC,EAAE;YAAEC,SAAS,EAAE,GAAG;YAAED,QAAQ,EAAE;UAAW,CAAE;UAAqB;UACjE,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAED,QAAQ,EAAE;UAAW,CAAE,CAAsB;SAClE;QACDO,KAAK,EAAE;OACR,CAAC;MAEF;MACA;MACAlC,CAAC,GAAIZ,GAAW,CAAC+C,aAAa,CAACC,MAAM,GAAG,EAAE;MAE1C;MACA,IAAIpC,CAAC,GAAGZ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC2C,SAAS,EAAE,GAAG,GAAG,EAAE;QAC/CjD,GAAG,CAACkD,OAAO,EAAE;QACbtC,CAAC,GAAGJ,MAAM,CAACG,GAAG;MAChB;IACF,CAAC;IAEDxD,MAAM,CAACC,IAAI,CAAC2C,OAAO,CAAC,CAACxB,OAAO,CAAC,CAACyC,QAAQ,EAAEc,GAAG,KAAI;MAC7Cf,WAAW,CAACC,QAAQ,EAAEjB,OAAO,CAACiB,QAAQ,CAAC,CAAC;IAC1C,CAAC,CAAC;IAEF;IACA,MAAMmC,WAAW,GAAG,+BAA+B;IACnD,MAAMC,iBAAiB,GAAGpD,GAAG,CAACqD,gBAAgB,EAAE;IAChD,MAAMC,iBAAiB,GAAGtD,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;IAC1D,MAAMgD,kBAAkB,GAAGvD,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC2C,SAAS,EAAE;IAC5D,MAAMO,WAAW,GAAG,IAAIxF,IAAI,EAAE,CAACyF,cAAc,EAAE;IAC/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIN,iBAAiB,EAAEM,CAAC,EAAE,EAAE;MAC3C1D,GAAG,CAAC2D,OAAO,CAACD,CAAC,CAAC;MACd1D,GAAG,CAAC4D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/B5D,GAAG,CAAC6D,IAAI,CAACrD,MAAM,CAACC,IAAI,EAAE8C,kBAAkB,GAAG,EAAE,EAAED,iBAAiB,GAAG9C,MAAM,CAACE,KAAK,EAAE6C,kBAAkB,GAAG,EAAE,CAAC;MACzGvD,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;MAClCb,GAAG,CAACc,WAAW,CAAC,CAAC,CAAC;MAClBd,GAAG,CAAC8D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;MAC/B9D,GAAG,CAAC3E,IAAI,CAAC,iBAAiBmI,WAAW,EAAE,EAAEhD,MAAM,CAACC,IAAI,EAAE8C,kBAAkB,GAAG,EAAE,CAAC;MAC9EvD,GAAG,CAAC3E,IAAI,CAAC8H,WAAW,EAAEG,iBAAiB,GAAG,CAAC,EAAEC,kBAAkB,GAAG,EAAE,EAAE;QAAErC,KAAK,EAAE;MAAQ,CAAE,CAAC;MAC1FlB,GAAG,CAAC3E,IAAI,CAAC,QAAQqI,CAAC,OAAON,iBAAiB,EAAE,EAAEE,iBAAiB,GAAG9C,MAAM,CAACE,KAAK,GAAG,EAAE,EAAE6C,kBAAkB,GAAG,EAAE,CAAC;IAC/G;IAEA,MAAMQ,QAAQ,GAAG,oBAAoB,IAAI,CAAC5S,MAAM,EAAE6H,YAAY,IAAI,EAAE,IAAI,IAAIgF,IAAI,EAAE,CAACgG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;IACpHjE,GAAG,CAACkE,IAAI,CAACH,QAAQ,CAAC;EACpB;EAEOI,uBAAuBA,CAACpM,EAAO;IACpC,IAAI,CAACjH,2BAA2B,GAAGiH,EAAE;IACrC,MAAMqM,GAAG,GAAG,IAAI,CAACpT,kBAAkB,CAACmO,IAAI,CAACkF,CAAC,IAAI3F,MAAM,CAAC2F,CAAC,CAACtM,EAAE,CAAC,KAAK2G,MAAM,CAAC3G,EAAE,CAAC,CAAC;IAC1E,IAAIqM,GAAG,EAAE;MACP,IAAI,CAAC7J,iBAAiB,GAAG6J,GAAG,CAAC1Q,KAAK;MAClC,IAAI,CAAC8G,mBAAmB,GAAG4J,GAAG,CAACxM,eAAe;IAChD;EACF;EAEO7G,sCAAsCA,CAAA;IAC3C,IAAI,CAAC,IAAI,CAACD,2BAA2B,EAAE;MAAE,OAAO,EAAE;IAAE;IACpD,OAAO,IAAI,CAACgH,8BAA8B,CAAC,IAAI,CAAChH,2BAA2B,CAAC;EAC9E;EAEOgH,8BAA8BA,CAACqG,WAAgB;IACpD,MAAMV,OAAO,GAAG,IAAI,CAAChD,eAAe,CAAC+G,MAAM,CAAC7D,CAAC,IAAIe,MAAM,CAACf,CAAC,CAACQ,WAAW,CAAC,KAAKO,MAAM,CAACP,WAAW,CAAC,CAAC;IAE/F;IACA,OAAOV,OAAO,CAAC4B,IAAI,CAAC,CAACJ,CAAC,EAAEC,CAAC,KAAI;MAC3B;MACA,IAAID,CAAC,CAACjI,WAAW,KAAKkI,CAAC,CAAClI,WAAW,EAAE;QACnC,OAAO,CAAC;MACV;MAEA;MACA,IAAI,CAACiI,CAAC,CAACjI,WAAW,IAAIkI,CAAC,CAAClI,WAAW,EAAE;QACnC,OAAO,CAAC,CAAC;MACX;MAEA;MACA,IAAIiI,CAAC,CAACjI,WAAW,IAAI,CAACkI,CAAC,CAAClI,WAAW,EAAE;QACnC,OAAO,CAAC;MACV;MAEA,OAAO,CAAC;IACV,CAAC,CAAC,CAACsN,OAAO,EAAE,CAAC,CAAC;EAChB;EAEO9M,wBAAwBA,CAACtE,KAAa;IAC3C,IAAI,IAAI,CAAC4H,kBAAkB,CAACyJ,GAAG,CAACrR,KAAK,CAAC,EAAE;MACtC,IAAI,CAAC4H,kBAAkB,CAAC0J,MAAM,CAACtR,KAAK,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAAC4H,kBAAkB,CAAC2J,GAAG,CAACvR,KAAK,CAAC;IACpC;EACF;EAEOwE,mBAAmBA,CAACxE,KAAa;IACtC,OAAO,IAAI,CAAC4H,kBAAkB,CAACyJ,GAAG,CAACrR,KAAK,CAAC;EAC3C;EAEOjC,wBAAwBA,CAAA;IAC7B,OAAO,IAAI,CAACD,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACZ,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC0K,kBAAkB,CAAC4J,IAAI,KAAK,IAAI,CAAC1T,kBAAkB,CAACZ,MAAM;EACzI;EAEOK,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACO,kBAAkB,IAAI,IAAI,CAACA,kBAAkB,CAACZ,MAAM,KAAK,CAAC,EAAE;MACpE;IACF;IACA,IAAI,IAAI,CAACa,wBAAwB,EAAE,EAAE;MACnC,IAAI,CAAC6J,kBAAkB,CAAC6J,KAAK,EAAE;IACjC,CAAC,MAAM;MACL,IAAI,CAAC7J,kBAAkB,GAAG,IAAIC,GAAG,CAAC,IAAI,CAAC/J,kBAAkB,CAAC0M,GAAG,CAAC,CAACkH,CAAC,EAAE9C,GAAG,KAAKA,GAAG,CAAC,CAAC;IACjF;IACA,IAAI,CAAC7H,GAAG,CAACsD,YAAY,EAAE;EACzB;EAEO/G,qBAAqBA,CAACqO,QAAgB;IAC3C,IAAI,IAAI,CAAC7J,eAAe,CAACuJ,GAAG,CAACM,QAAQ,CAAC,EAAE;MACtC,IAAI,CAAC7J,eAAe,CAACwJ,MAAM,CAACK,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAAC7J,eAAe,CAACyJ,GAAG,CAACI,QAAQ,CAAC;MAClC;MACA,IAAI,CAAC,IAAI,CAAC5J,kBAAkB,CAAC4J,QAAQ,CAAC,EAAE;QACtC,IAAI,CAAC5J,kBAAkB,CAAC4J,QAAQ,CAAC,GAAG,aAAa;MACnD;IACF;EACF;EAEO/N,gBAAgBA,CAAC+N,QAAgB;IACtC,OAAO,IAAI,CAAC7J,eAAe,CAACuJ,GAAG,CAACM,QAAQ,CAAC;EAC3C;EAEOC,aAAaA,CAACD,QAAgB,EAAEE,GAAW,EAAEpW,MAAW;IAC7DA,MAAM,CAAC2E,eAAe,EAAE;IACxB,IAAI,CAAC2H,kBAAkB,CAAC4J,QAAQ,CAAC,GAAGE,GAAG;IACvC,IAAI,CAAC9K,GAAG,CAACsD,YAAY,EAAE;EACzB;EAEOyH,oBAAoBA,CAACrF,MAAW;IACrC,IAAI,CAACzP,SAAS,GAAG,IAAI;IACrB,MAAM+U,QAAQ,GAAG;MACf7P,oBAAoB,EAAEuK,MAAM,CAACvK,oBAAoB;MACjDC,kBAAkB,EAAEsK,MAAM,CAACtK,kBAAkB;MAC7CiF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvB7D,UAAU,EAAEkJ,MAAM,CAAClJ,UAAU;MAC7ByO,cAAc,EAAE,IAAI,CAACvK,SAAS,CAACwK;KAChC;IAED,IAAI,CAAC9K,cAAc,CAAC+K,oBAAoB,CAACH,QAAQ,CAAC,CAACnJ,SAAS,CAAC;MAC3DgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7M,SAAS,GAAG,KAAK;QACtB,IAAI6M,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI,CAAC5C,wBAAwB,CAACiL,WAAW,CAACtI,GAAG,CAACE,YAAY,CAACqI,OAAO,EAAE,EAAE,CAAC;UACvE;UACA,IAAI,CAAChR,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACL,IAAI,CAAC8F,wBAAwB,CAACmL,SAAS,CAACxI,GAAG,CAACO,YAAY,IAAE,wBAAwB,EAAE,EAAE,CAAC;UACvF;QACF;QACA,IAAI,CAACrD,GAAG,CAACsD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACrP,SAAS,GAAG,KAAK;QACtB,IAAI,CAACkK,wBAAwB,CAACmL,SAAS,CAAC,kCAAkC,EAAE,EAAE,CAAC;QAC/E;QACAtJ,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAACtF,GAAG,CAACsD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOiI,mBAAmBA,CAACrH,WAAgB;IACzC,OAAOO,MAAM,CAAC,IAAI,CAAC5N,2BAA2B,CAAC,KAAK4N,MAAM,CAACP,WAAW,CAAC;EACzE;EAEOhL,WAAWA,CAACD,KAAa;IAC9B,IAAI,CAACM,kBAAkB,GAAGN,KAAK;IAC/B,IAAI,CAACqH,iBAAiB,GAAG,IAAI,CAACpK,YAAY,CAAC,IAAI,CAACqD,kBAAkB,CAAC,CAACE,KAAK;IACzE,IAAI,CAAC8G,mBAAmB,GACtB,IAAI,CAACrK,YAAY,CAAC,IAAI,CAACqD,kBAAkB,CAAC,CAACoE,eAAe;EAC9D;EAEO6N,0BAA0BA,CAAA;IAC/B,IACE,IAAI,CAACjS,kBAAkB,KAAK,IAAI,IAChC,IAAI,CAACA,kBAAkB,IAAI,IAAI,CAACrD,YAAY,CAACC,MAAM,EACnD;MACA,OAAO,EAAE;IACX;IAEA,OAAO,IAAI,CAACD,YAAY,CAAC,IAAI,CAACqD,kBAAkB,CAAC,CAACiK,OAAO,IAAI,EAAE;EACjE;EAIOnF,MAAMA,CAAA;IACX2D,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAACX,YAAY,EAAE,YAAY,EAAE,IAAI,CAACC,SAAS,CAAC;IAC7F,IAAI,IAAI,CAACD,YAAY,KAAK,SAAS,IAAI,IAAI,CAACC,SAAS,EAAE;MACrD;MACAS,OAAO,CAACC,GAAG,CAAC,oDAAoD,CAAC;MACjE,IAAI,CAACnC,MAAM,CAAC2L,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAClK,SAAS,CAAC,EAAE;QACvDK,WAAW,EAAE;UAAE8J,SAAS,EAAE;QAAS;OACpC,CAAC;IACJ,CAAC,MAAM;MACL;MACA1J,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,IAAI,CAACnC,MAAM,CAAC2L,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;IACzC;EACF;EAEO7U,UAAUA,CAAA;IACf+U,MAAM,CAACC,IAAI,CACT,GAAG,IAAI,CAAC1U,MAAM,CAAC2U,cAAc,GAAG,IAAI,CAAC3U,MAAM,CAACC,cAAc,EAAE,EAC5D,QAAQ,CACT;EACH;EAEO2U,iBAAiBA,CAACpG,MAAW;IAClC,MAAMqG,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAAC6L,IAAI,CAAC/X,2BAA2B,EAAE;MACnE4W,IAAI,EAAE;KACP,CAAC;IACFsB,QAAQ,CAACC,iBAAiB,CAACtG,MAAM,GAAGA,MAAM;IAC1CqG,QAAQ,CAACC,iBAAiB,CAAC3L,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnD0L,QAAQ,CAACC,iBAAiB,CAACC,aAAa,GAAG,IAAI,CAAC/U,MAAM;IAEtD;IACA6U,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAC7R,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAAC+R,KAAK,CAAEhJ,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEO7K,cAAcA,CAAC6F,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,MAAMoP,UAAU,GAAGpP,MAAM,CAACqP,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAChFvK,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEhF,MAAM,EAAE,aAAa,EAAEoP,UAAU,CAAC;IAC5E;IACA,MAAMG,QAAQ,GAA4B;MACxC,WAAW,EAAE,WAAW;MACxB,oBAAoB,EAAE,oBAAoB;MAC1C,uBAAuB,EAAE,uBAAuB;MAChD,aAAa,EAAE,aAAa;MAC5B,WAAW,EAAE,WAAW;MACxB,UAAU,EAAE,UAAU;MACtB,SAAS,EAAE,SAAS;MACpB,UAAU,EAAE,UAAU;MACtB,UAAU,EAAE,UAAU;MACtB,cAAc,EAAE,cAAc;MAC9B,mBAAmB,EAAE,mBAAmB;MACxC,uBAAuB,EAAE,uBAAuB;MAChD,cAAc,EAAE,cAAc;MAC9B,cAAc,EAAE,cAAc;MAC9B,mBAAmB,EAAE,mBAAmB;MACxC,mBAAmB,EAAE,mBAAmB;MACxC,iBAAiB,EAAE;KACpB;IACD,MAAMC,QAAQ,GAAGD,QAAQ,CAACH,UAAU,CAAC,IAAIA,UAAU;IACnD,MAAMK,UAAU,GAAG,SAAS,GAAGD,QAAQ;IACvCzK,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEyK,UAAU,CAAC;IAC9C1K,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,CAClD,gBAAgB,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,iBAAiB,EAC7E,iBAAiB,EAAE,iBAAiB,EAAE,qBAAqB,EAAE,0BAA0B,EACvF,8BAA8B,EAAE,qBAAqB,EAAE,qBAAqB,EAC5E,kBAAkB,EAAE,0BAA0B,CAC/C,CAAC;IACF,OAAOyK,UAAU;EACnB;EAEO/S,cAAcA,CAACsD,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;IACtB,MAAMoP,UAAU,GAAGpP,MAAM,CAACqP,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IAEhF,MAAMI,QAAQ,GAAyB;MACrC,mBAAmB,EAAE;QAAEC,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAClG,mBAAmB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAClG,iBAAiB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAChG,uBAAuB,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACtG,cAAc,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC7F,cAAc,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC7F,WAAW,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC1F,SAAS,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACxF,UAAU,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MACzF,WAAW,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB,CAAE;MAC1F,UAAU,EAAE;QAAEF,eAAe,EAAE,SAAS;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAmB;KACxF;IAED,OAAOH,QAAQ,CAACN,UAAU,CAAC,IAAI,EAAE;EACnC;EAEArX,OAAOA,CAAC8V,GAAQ,EAAEpW,MAAW;IAC3B,IAAIoW,GAAG,KAAK,UAAU,IAAI,CAAC,IAAI,CAAC5L,uBAAuB,EAAE,EAAE;MACzD;IACF;IACA,IAAI,CAAC7J,WAAW,GAAGyV,GAAG;IACtB,IAAI,CAAC9K,GAAG,CAACsD,YAAY,EAAE;EACzB;EAEOpE,uBAAuBA,CAAA;IAC5B,MAAM6N,IAAI,GAAG,CAAC,IAAI,CAAC/N,gBAAgB,IAAI,IAAI,CAAC9H,MAAM,EAAE8H,gBAAgB,IAAI,EAAE,EAAEqI,QAAQ,EAAE,CAACiF,WAAW,EAAE;IACpG;IACA,OAAOS,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM;EAC/C;EAEO5N,uBAAuBA,CAAA;IAC5B,MAAM4N,IAAI,GAAG,CAAC,IAAI,CAAC/N,gBAAgB,IAAI,IAAI,CAAC9H,MAAM,EAAE8H,gBAAgB,IAAI,EAAE,EAAEqI,QAAQ,EAAE,CAACiF,WAAW,EAAE;IACpG;IACA,OAAOS,IAAI,KAAK,UAAU,IAAIA,IAAI,KAAK,MAAM;EAC/C;EAEA/W,QAAQA,CAAA;IACN,MAAMgX,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAAC6L,IAAI,CACrC7X,8BAA8B,EAC9BiZ,eAAe,CAChB;IAED;IACAjB,QAAQ,CAACC,iBAAiB,CAAC3L,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnD0L,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAACvK,SAAS,CAACwK,MAAM,CAAC,CAAC;IACnEa,QAAQ,CAACC,iBAAiB,CAACjN,YAAY,GAAG,IAAI,CAAC7H,MAAM,EAAE6H,YAAY,IAAI,EAAE;IAEzE;IACAgN,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAC5J,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAAC8J,KAAK,CAAEhJ,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEA7I,kBAAkBA,CAACgU,WAAmB;IACpC,IAAIA,WAAW,GAAG,CAAC,IAAIA,WAAW,IAAI,IAAI,CAAClX,YAAY,CAACC,MAAM,EAAE;MAC9D;IACF;IAEA,MAAM6W,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MACVwC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE,KAAK;MACfC,UAAU,EAAE;KACb;IAED,MAAMpB,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAAC6L,IAAI,CACrC7X,8BAA8B,EAC9BiZ,eAAe,CAChB;IAED;IACAjB,QAAQ,CAACC,iBAAiB,CAAC3L,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnD0L,QAAQ,CAACC,iBAAiB,CAACqB,UAAU,GAAG,IAAI,CAACnX,YAAY,CAACkX,WAAW,CAAC;IACtErB,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAACvK,SAAS,CAACwK,MAAM,CAAC,CAAC;IACnEa,QAAQ,CAACC,iBAAiB,CAACjN,YAAY,GAAG,IAAI,CAAC7H,MAAM,EAAE6H,YAAY,IAAI,EAAE;IAEzE;IACAgN,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,EAAE;QACxB;QACA,IAAI,CAAC5J,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAAC8J,KAAK,CAAEhJ,KAAK,IAAI;MACjBpB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEAqL,SAASA,CAAA;IACP,MAAMN,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAAC6L,IAAI,CACrC7X,8BAA8B,EAC9BiZ,eAAe,CAChB;EACH;EACAtX,UAAUA,CAAA;IACR,MAAMsX,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAAC6L,IAAI,CACrC5X,oBAAoB,EACpBgZ,eAAe,CAChB;IACD;IACAjB,QAAQ,CAACC,iBAAiB,CAAClO,EAAE,GAAG,IAAI,CAACuC,QAAQ;IAC7C0L,QAAQ,CAACC,iBAAiB,CAACuB,0BAA0B,GAAG,KAAK;IAE7D;IACAxB,QAAQ,CAACC,iBAAiB,CAACwB,SAAS,CAAC3L,SAAS,CAAE4L,KAAc,IAAI;MAChE,IAAIA,KAAK,EAAE;QACT,IAAI,CAACpL,kBAAkB,EAAE;MAC3B;IACF,CAAC,CAAC;EACJ;EAEA3L,WAAWA,CAAC+S,CAAM;IAChB,IAAI,CAACxT,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC2K,YAAY,GAAG6I,CAAC,IAAI,KAAK;IAC9B,IAAI,CAACrJ,cAAc,CAAC1J,WAAW,CAAC;MAAE2J,QAAQ,EAAE,IAAI,CAACA,QAAQ;MAAEO,YAAY,EAAE,IAAI,CAACA,YAAY;MAAE8M,SAAS,EAAE;IAAI,CAAE,CAAC,CAAC7L,SAAS,CAAC;MACvHgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7M,SAAS,GAAG,KAAK;QACtB+L,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEa,GAAG,CAAC;QAClCd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAE,OAAOa,GAAG,CAAC;QACzCd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEiB,MAAM,CAACC,IAAI,CAACL,GAAG,IAAI,EAAE,CAAC,CAAC;QACrDd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,GAAG,EAAE6K,OAAO,CAAC;QAC9C3L,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEa,GAAG,EAAEuI,OAAO,CAAC;QAC9CrJ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEa,GAAG,EAAEE,YAAY,CAAC;QAExD;QACA,IAAIA,YAAY,GAAGF,GAAG;QAEtB;QACA,IAAIA,GAAG,EAAEE,YAAY,EAAE;UACrBA,YAAY,GAAGF,GAAG,CAACE,YAAY;QACjC,CAAC,MAAM,IAAIF,GAAG,EAAEkF,IAAI,EAAEhF,YAAY,EAAE;UAClCA,YAAY,GAAGF,GAAG,CAACkF,IAAI,CAAChF,YAAY;QACtC,CAAC,MAAM,IAAIF,GAAG,EAAEkF,IAAI,EAAE;UACpBhF,YAAY,GAAGF,GAAG,CAACkF,IAAI;QACzB;QAEAhG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEe,YAAY,CAAC;QAChDhB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEe,YAAY,EAAE2K,OAAO,CAAC;QACpD3L,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEe,YAAY,EAAEqI,OAAO,CAAC;QAEpD,IAAIrI,YAAY,EAAED,OAAO,EAAE;UACzB;UACA,IAAI,CAAC5C,wBAAwB,CAACmL,SAAS,CAACtI,YAAY,CAACK,YAAY,EAAE,EAAE,CAAC;QACxE,CAAC,MAAM,IAAIL,YAAY,EAAE2K,OAAO,KAAK,KAAK,EAAE;UAC1C;UACA,IAAI3K,YAAY,CAACqI,OAAO,KAAK,oCAAoC,EAAE;YACzC,IAAI,CAAClL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAItI,YAAY,CAACqI,OAAO,KAAK,mCAAmC,EAAE;YAC/C,IAAI,CAAClL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAACnL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAItI,YAAY,EAAE2K,OAAO,KAAK,IAAI,IAAI3K,YAAY,EAAEC,IAAI,EAAE;UAC/D,IAAI,CAAC9C,wBAAwB,CAACiL,WAAW,CAAC,4BAA4B,EAAE,EAAE,CAAC;UAC3E;UACA,IAAI,CAAC/I,kBAAkB,EAAE;UACzB,IAAI,CAAChI,oBAAoB,EAAE;QAC7B,CAAC,MAAM;UACL;UACA2H,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;UAClE;UACA,IAAI,CAAC9B,wBAAwB,CAACiL,WAAW,CAAC,4BAA4B,EAAE,EAAE,CAAC;UAE3E,IAAI,CAAC/I,kBAAkB,EAAE;UACzB,IAAI,CAAChI,oBAAoB,EAAE;QAC7B;QACA,IAAI,CAAC2F,GAAG,CAACsD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACrP,SAAS,GAAG,KAAK;QACtB;QACA+L,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEqD,GAAG,CAAC;QACnCtD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,OAAOqD,GAAG,CAAC;QACtCtD,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEiB,MAAM,CAACC,IAAI,CAACmC,GAAG,IAAI,EAAE,CAAC,CAAC;QAClDtD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEqD,GAAG,EAAErI,MAAM,CAAC;QACzC+E,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqD,GAAG,EAAE+F,OAAO,CAAC;QAC3CrJ,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEqD,GAAG,EAAElC,KAAK,CAAC;QAEvC;QACA;QACA,IAAIkC,GAAG,EAAEqI,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAIrI,GAAG,CAAC+F,OAAO,KAAK,oCAAoC,EAAE;YAChC,IAAI,CAAClL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAIhG,GAAG,CAAC+F,OAAO,KAAK,mCAAmC,EAAE;YACtC,IAAI,CAAClL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAACnL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAIhG,GAAG,EAAElC,KAAK,EAAEiI,OAAO,EAAE;UAC9B,IAAI/F,GAAG,CAAClC,KAAK,CAACiI,OAAO,KAAK,oCAAoC,EAAE;YACtC,IAAI,CAAClL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM,IAAIhG,GAAG,CAAClC,KAAK,CAACiI,OAAO,KAAK,mCAAmC,EAAE;YAC5C,IAAI,CAAClL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF,CAAC,MAAM;YACmB,IAAI,CAACnL,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;YAE3J;UACF;QACF,CAAC,MAAM,IAAIhG,GAAG,EAAErI,MAAM,KAAK,GAAG,EAAE;UACN,IAAI,CAACkD,wBAAwB,CAACmL,SAAS,CAAC,sFAAsF,EAAE,EAAE,CAAC;UAE3J;UACA;QACF,CAAC,MAAM;UACL;QAAA;QAEFtJ,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAACtF,GAAG,CAACsD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEAsK,kBAAkBA,CAAClI,MAAU;IAC3B,MAAMsH,eAAe,GAKjB;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMpB,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAAC6L,IAAI,CACrC3X,2BAA2B,EAC3B+Y,eAAe,CAChB;IAEDhL,OAAO,CAACC,GAAG,CAAC,aAAa,EAAEyD,MAAM,CAAC;IAClC;IACAqG,QAAQ,CAACC,iBAAiB,CAAC3L,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnD0L,QAAQ,CAACC,iBAAiB,CAACqB,UAAU,GAAG3H,MAAM;IAC9CqG,QAAQ,CAACC,iBAAiB,CAACC,aAAa,GAAG,IAAI,CAAC/U,MAAM;IACtD6U,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAACvK,SAAS,CAACwK,MAAM,CAAC,CAAC;IAEnE;IACAa,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAED,MAAM,IAAI;MAC9B,IAAIA,MAAM,KAAK,SAAS,IAAIA,MAAM,KAAK,SAAS,EAAE;QAChD;QACA,IAAI,CAAC7R,oBAAoB,EAAE;MAC7B;IACF,CAAC,CAAC,CAAC+R,KAAK,CAAEhJ,KAAK,IAAI;MACjB;MACApB,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAChC,CAAC,CAAC;EACJ;EAEOlH,iBAAiBA,CAAC8S,UAAe,EAAEnI,MAAW;IACnD;IACA,MAAMqG,QAAQ,GAAG,IAAI,CAAChM,YAAY,CAAC6L,IAAI,CAAC9X,sBAAsB,EAAE;MAC9D2W,IAAI,EAAE,IAAI;MACVwC,QAAQ,EAAE,QAAQ;MAClBC,QAAQ,EAAE;KACX,CAAC;IAEF;IACAnB,QAAQ,CAACC,iBAAiB,CAAC6B,UAAU,GAAGA,UAAU;IAClD9B,QAAQ,CAACC,iBAAiB,CAACtG,MAAM,GAAGA,MAAM;IAC1CqG,QAAQ,CAACC,iBAAiB,CAAC3L,QAAQ,GAAG,IAAI,CAACA,QAAQ;IACnD0L,QAAQ,CAACC,iBAAiB,CAACf,cAAc,GAAG,IAAI,CAACvK,SAAS,CAACwK,MAAM;IACjEa,QAAQ,CAACC,iBAAiB,CAACrL,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACAoL,QAAQ,CAACC,iBAAiB,CAAC8B,iBAAiB,CAACjM,SAAS,CAAEmJ,QAAa,IAAI;MACvE,IAAI,CAAC+C,cAAc,CAAC/C,QAAQ,EAAEe,QAAQ,CAAC;IACzC,CAAC,CAAC;IAEF;IACAA,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACnM,SAAS,CAAE8L,OAAgB,IAAI;MAC1E,IAAI,CAACA,OAAO,EAAE;QACZ;QACA5B,QAAQ,CAACC,iBAAiB,CAAC/V,SAAS,GAAG,KAAK;MAC9C;IACF,CAAC,CAAC;IAEF8V,QAAQ,CAACG,MAAM,CAACC,IAAI,CAAC,MAAK;MACxB;IAAA,CACD,CAAC,CAACC,KAAK,CAAC,MAAK;MACZ;IAAA,CACD,CAAC;EACJ;EAEO2B,cAAcA,CAAC/C,QAAa,EAAEe,QAAc;IACjD,IAAI,CAACf,QAAQ,EAAE;MACb;IACF;IAEA,IAAI,CAAC/U,SAAS,GAAG,IAAI;IAErB,IAAI,CAACmK,cAAc,CAAC+K,oBAAoB,CAACH,QAAQ,CAAC,CAACnJ,SAAS,CAAC;MAC3DgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7M,SAAS,GAAG,KAAK;QACtB,IAAI6M,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UACF,IAAI,CAAC5C,wBAAwB,CAACiL,WAAW,CAACtI,GAAG,CAACE,YAAY,CAACqI,OAAO,EAAE,EAAE,CAAC;UAE/F;UACA,IAAI,CAAChR,oBAAoB,EAAE,CAAC,CAAC;UAE7B;UACA,IAAI0R,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;YAC1CD,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACC,IAAI,CAAC,IAAI,CAAC;UACzD;UAEA;UACA,IAAIlC,QAAQ,EAAE;YACZA,QAAQ,CAACmC,KAAK,EAAE;UAClB;QACF,CAAC,MAAM;UACL;UACwB,IAAI,CAAC/N,wBAAwB,CAACmL,SAAS,CAACxI,GAAG,CAACO,YAAY,IAAE,2BAA2B,EAAE,EAAE,CAAC;UAElH;UACA,IAAI0I,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;YAC1CD,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACC,IAAI,CAAC,KAAK,CAAC;UAC1D;QACF;QACA,IAAI,CAACjO,GAAG,CAACsD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACrP,SAAS,GAAG,KAAK;QACtB;QACA+L,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QACM,IAAI,CAACnF,wBAAwB,CAACiL,WAAW,CAAC,2BAA2B,EAAE,EAAE,CAAC;QAElG;QACA,IAAIW,QAAQ,IAAIA,QAAQ,CAACC,iBAAiB,EAAE;UAC1CD,QAAQ,CAACC,iBAAiB,CAACgC,iBAAiB,CAACC,IAAI,CAAC,KAAK,CAAC;QAC1D;QAEA,IAAI,CAACjO,GAAG,CAACsD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEO3I,iBAAiBA,CAAC+K,MAAW;IAClC,IAAI,CAACA,MAAM,EAAE;MACX;IACF;IAEA,IAAI;MACF,MAAM3G,YAAY,GAAG,IAAI,CAAC7H,MAAM,EAAE6H,YAAY,IAAI,EAAE;MACpD,MAAM7B,QAAQ,GAAG,CAACwI,MAAM,EAAE9B,UAAU,IAAI8B,MAAM,EAAEyI,oBAAoB,IAAIzI,MAAM,EAAExI,QAAQ,IAAI,EAAE,EAAEmK,QAAQ,EAAE;MAC1G,MAAM+G,YAAY,GAAG,CAAC1I,MAAM,EAAEzJ,QAAQ,KAAKyJ,MAAM,EAAE0I,YAAY,IAAI,EAAE,CAAC,EAAE/G,QAAQ,EAAE;MAClF,MAAMgH,aAAa,GAAG,CAAC3I,MAAM,EAAEvK,oBAAoB,IAAI,EAAE,EAAEkM,QAAQ,EAAE;MAErE;MACA,MAAMiH,cAAc,GAAG,IAAI,CAACvX,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAACZ,MAAM,GAAG,CAAC;MACnF,MAAMoY,KAAK,GAAGD,cAAc,GAAG,CAAC,GAAGA,cAAc,CAACjH,QAAQ,EAAE,GAAG,CAAC3B,MAAM,EAAE6I,KAAK,IAAI7I,MAAM,EAAE8I,KAAK,IAAI,EAAE,EAAEnH,QAAQ,EAAE;MAEhH,MAAMpK,MAAM,GAAG,CAACyI,MAAM,EAAE+I,UAAU,IAAI/I,MAAM,EAAEgJ,aAAa,IAAIhJ,MAAM,EAAEzI,MAAM,IAAI,EAAE,EAAEoK,QAAQ,EAAE;MAC/F,MAAMsH,WAAW,GAAIjJ,MAAM,EAAE5B,aAAa,IAAI4B,MAAM,EAAE5L,aAAa,IAAI4L,MAAM,EAAE1B,OAAO,IAAI0B,MAAM,EAAEP,WAAW,GACzG,IAAIpB,IAAI,CAAC2B,MAAM,EAAE5B,aAAa,IAAI4B,MAAM,EAAE5L,aAAa,IAAI4L,MAAM,EAAE1B,OAAO,IAAI0B,MAAM,EAAEP,WAAW,CAAC,CAACwC,kBAAkB,EAAE,GACvH,IAAI5D,IAAI,EAAE,CAAC4D,kBAAkB,EAAE;MAEnC,MAAM5B,GAAG,GAAG,IAAI7R,KAAK,CAAC;QAAE8R,WAAW,EAAE,UAAU;QAAEC,IAAI,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAI,CAAE,CAAC;MAE5E,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;MAClD,MAAMsI,UAAU,GAAG7I,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC2C,SAAS,EAAE;MACpD,MAAMzC,MAAM,GAAG;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,GAAG,EAAE,EAAE;QAAEmI,MAAM,EAAE;MAAE,CAAE;MAE3D;MACA9I,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCb,GAAG,CAACc,WAAW,CAAC,EAAE,CAAC;MACnBd,GAAG,CAAC3E,IAAI,CAAC,aAAarC,YAAY,IAAI,EAAE,EAAE,EAAEwH,MAAM,CAACC,IAAI,EAAED,MAAM,CAACG,GAAG,CAAC;MAEpE,MAAMoB,MAAM,GAAGvB,MAAM,CAACG,GAAG,GAAG,EAAE;MAE9B;MACA,MAAMoI,OAAO,GAAG,CACd,aAAa,EACb,eAAe,EACf,gCAAgC,EAChC,OAAO,EACP,QAAQ,EACR,MAAM,CACP;MAED;MACA,MAAMC,cAAc,GAAQ,CAACrJ,MAAM,IAAKA,MAAc,CAACzB,WAAW,KAAMyB,MAAc,CAACxJ,WAAW,IAAI,EAAE;MACxG,MAAM8S,gBAAgB,GAAG7H,KAAK,CAAC8H,OAAO,CAACF,cAAc,CAAC,GAAGA,cAAc,GAAIA,cAAc,GAAG,CAACA,cAAc,CAAC,GAAG,EAAG;MAClH,MAAMG,QAAQ,GAAUF,gBAAgB,CAAC7Y,MAAM,GAAG,CAAC,GAC/C6Y,gBAAgB,CAACvL,GAAG,CAAE0L,CAAM,IAAK,CAC/BjS,QAAQ,IAAI,EAAE,EACdiS,CAAC,EAAElT,QAAQ,IAAImS,YAAY,IAAI,EAAE,EACjCe,CAAC,EAAEjU,QAAQ,IAAIiU,CAAC,EAAEhU,oBAAoB,IAAIkT,aAAa,IAAI,EAAE,EAC7DE,KAAK,IAAI,EAAE,EACXtR,MAAM,IAAI,EAAE,EACZ,CAACkS,CAAC,EAAErT,YAAY,GAAG,IAAIiI,IAAI,CAACoL,CAAC,CAACrT,YAAY,CAAC,CAAC6L,kBAAkB,EAAE,GAAGgH,WAAW,KAAK,EAAE,CACtF,CAAC,GACF,CAAC,CACCzR,QAAQ,IAAI,EAAE,EACdkR,YAAY,IAAI,EAAE,EAClBC,aAAa,IAAI,EAAE,EACnBE,KAAK,IAAI,EAAE,EACXtR,MAAM,IAAI,EAAE,EACZ0R,WAAW,IAAI,EAAE,CAClB,CAAC;MAENxa,SAAS,CAAC4R,GAAG,EAAE;QACb+B,MAAM;QACNC,IAAI,EAAE,CAAC+G,OAAO,CAAC;QACf9G,IAAI,EAAEkH,QAAQ;QACd3I,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDwB,MAAM,EAAE;UACNC,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,CAAC;UACXC,WAAW,EAAE,CAAC;UACdE,QAAQ,EAAE,WAAW;UACrBC,SAAS,EAAE;SACZ;QACDC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;UAAE0G,SAAS,EAAE,MAAM;UAAEjH,QAAQ,EAAE;QAAC,CAAE;QAChGS,YAAY,EAAE;UACZ,CAAC,EAAE;YAAEL,SAAS,EAAE,EAAE;YAAED,QAAQ,EAAE;UAAW,CAAE;UAC3C,CAAC,EAAE;YAAEC,SAAS,EAAE,CAACpC,SAAS,GAAGI,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,IAAI,IAAI;YAAE6B,QAAQ,EAAE;UAAW,CAAE;UACxF,CAAC,EAAE;YAAEC,SAAS,EAAE,CAACpC,SAAS,GAAGI,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,KAAK,IAAI,IAAI;YAAE6B,QAAQ,EAAE;UAAW,CAAE;UACxF,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAC7D,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAC7D,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW;SAC5D;QACD+G,YAAY,EAAGpM,IAAS,IAAI;UAC1B;UACA,IAAIA,IAAI,CAACqM,OAAO,KAAK,MAAM,IAAIrM,IAAI,CAACsM,MAAM,CAACtW,KAAK,KAAK,CAAC,EAAE;YACtDgK,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACS,SAAS,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC;UAC1C;UAEA;UACA,IAAIzF,IAAI,CAACqM,OAAO,KAAK,MAAM,IAAIrM,IAAI,CAACsM,MAAM,CAACtW,KAAK,KAAK,CAAC,EAAE;YACtD,MAAMoI,KAAK,GAAGoD,MAAM,CAACxB,IAAI,CAACuM,IAAI,CAACC,GAAG,IAAI,EAAE,CAAC;YACzC,MAAMC,UAAU,GAAGrO,KAAK,CAACiL,WAAW,EAAE,KAAK,UAAU;YACrD,MAAMqD,EAAE,GAAGD,UAAU,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;YACrD,MAAMhH,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YAEjCzF,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACQ,SAAS,GAAGkH,EAAE;YAC/B1M,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACS,SAAS,GAAGA,SAAS;YACtCzF,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACmH,SAAS,GAAG,MAAM;YACnCnM,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACU,MAAM,GAAG,QAAQ;YAClC1F,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACI,MAAM,GAAG,QAAQ;UACpC;QACF,CAAC;QACDQ,KAAK,EAAE;OACR,CAAC;MAEF;MACA,MAAM+G,SAAS,GAAG7J,GAAG,CAACqD,gBAAgB,EAAE;MACxC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImG,SAAS,EAAEnG,CAAC,EAAE,EAAE;QACnC1D,GAAG,CAAC2D,OAAO,CAACD,CAAC,CAAC;QACd1D,GAAG,CAAC4D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/B5D,GAAG,CAAC6D,IAAI,CAACrD,MAAM,CAACC,IAAI,EAAEoI,UAAU,GAAG,EAAE,EAAEzI,SAAS,GAAGI,MAAM,CAACE,KAAK,EAAEmI,UAAU,GAAG,EAAE,CAAC;QACjF7I,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCb,GAAG,CAACc,WAAW,CAAC,CAAC,CAAC;QAClBd,GAAG,CAAC8D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/B9D,GAAG,CAAC3E,IAAI,CAAC,iBAAiB,IAAI2C,IAAI,EAAE,CAACyF,cAAc,EAAE,EAAE,EAAEjD,MAAM,CAACC,IAAI,EAAEoI,UAAU,GAAG,EAAE,CAAC;QACtF7I,GAAG,CAAC3E,IAAI,CAAC,QAAQqI,CAAC,OAAOmG,SAAS,EAAE,EAAEzJ,SAAS,GAAGI,MAAM,CAACE,KAAK,GAAG,EAAE,EAAEmI,UAAU,GAAG,EAAE,CAAC;QACrF;QACA7I,GAAG,CAAC3E,IAAI,CAAC,+BAA+B,EAAE+E,SAAS,GAAG,CAAC,EAAEyI,UAAU,GAAG,EAAE,EAAE;UAAE3H,KAAK,EAAE;QAAQ,CAAE,CAAC;MAChG;MAEA,MAAM6C,QAAQ,GAAG,UAAU/K,YAAY,GAAGA,YAAY,GAAG,GAAG,GAAG,EAAE,GAAG,IAAIgF,IAAI,EAAE,CAACgG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MAChHjE,GAAG,CAACkE,IAAI,CAACH,QAAQ,CAAC;IAEpB,CAAC,CAAC,OAAO1G,KAAK,EAAE;MACdpB,OAAO,CAACoB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MACrB,IAAI,CAACjD,wBAAwB,CAACiL,WAAW,CAAC,yCAAyC,EAAE,EAAE,CAAC;MAEhH;IACF;EACF;EAEO9U,0BAA0BA,CAAA;IAC/B;IACA,IAAI,CAAC,IAAI,CAACO,2BAA2B,EAAE;MAAE;IAAQ;IACjD,MAAM2M,OAAO,GAAG,IAAI,CAAC1M,sCAAsC,EAAE;IAC7D,IAAI,CAAC0M,OAAO,IAAIA,OAAO,CAACrN,MAAM,KAAK,CAAC,EAAE;MAAE;IAAQ;IAEhD,IAAI;MACF,MAAM4I,YAAY,GAAG,IAAI,CAAC7H,MAAM,EAAE6H,YAAY,IAAI,EAAE;MACpD,MAAMgH,GAAG,GAAG,IAAI7R,KAAK,CAAC;QAAE8R,WAAW,EAAE,UAAU;QAAEC,IAAI,EAAE,IAAI;QAAEC,MAAM,EAAE;MAAI,CAAE,CAAC;MAE5E,MAAMC,SAAS,GAAGJ,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;MAClD,MAAMC,MAAM,GAAG;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,GAAG,EAAE;MAAE,CAAE;MAE/C;MACAX,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC;MAChCb,GAAG,CAACc,WAAW,CAAC,EAAE,CAAC;MACnBd,GAAG,CAAC3E,IAAI,CAAC,aAAarC,YAAY,EAAE,EAAEwH,MAAM,CAACC,IAAI,EAAED,MAAM,CAACG,GAAG,CAAC;MAE9D,MAAMoI,OAAO,GAAG,CACd,YAAY,EACZ,UAAU,EACV,QAAQ,EACR,SAAS,EACT,YAAY,EACZ,qBAAqB,EACrB,oBAAoB,CACrB;MAED,MAAM9G,IAAI,GAAGxE,OAAO,CAACC,GAAG,CAAEC,CAAM,IAAK,CACnCA,CAAC,EAAE1G,IAAI,IAAI,EAAE,EACb,CAAC0G,CAAC,EAAEE,UAAU,IAAIF,CAAC,EAAEyK,oBAAoB,IAAIzK,CAAC,EAAExG,QAAQ,IAAI,EAAE,EAAEmK,QAAQ,EAAE,EAC1E,CAAC3D,CAAC,EAAE+K,UAAU,IAAI/K,CAAC,EAAEgL,aAAa,IAAIhL,CAAC,EAAEzG,MAAM,IAAI,EAAE,EAAEoK,QAAQ,EAAE,EACjE,CAAC,IAAI,CAACtQ,kBAAkB,EAAEZ,MAAM,IAAIuN,CAAC,EAAE6K,KAAK,IAAI7K,CAAC,EAAE8K,KAAK,IAAI,EAAE,EAAEnH,QAAQ,EAAE,EACzE3D,CAAC,EAAEI,aAAa,IAAIJ,CAAC,EAAE5J,aAAa,IAAI4J,CAAC,EAAEM,OAAO,IAAIN,CAAC,EAAEyB,WAAW,GACjE,IAAIpB,IAAI,CAACL,CAAC,EAAEI,aAAa,IAAIJ,CAAC,EAAE5J,aAAa,IAAI4J,CAAC,EAAEM,OAAO,IAAIN,CAAC,EAAEyB,WAAW,CAAC,CAACwC,kBAAkB,EAAE,GACnG,EAAE,EACLjE,CAAC,EAAEmM,gBAAgB,GAAG,KAAK,GAAG,IAAI,EAClCnM,CAAC,EAAEoM,eAAe,GAAG,KAAK,GAAIpM,CAAC,EAAEvI,oBAAoB,GAAG,IAAI,GAAG,KAAM,CACvE,CAAC;MAEFhH,SAAS,CAAC4R,GAAG,EAAE;QACb+B,MAAM,EAAEvB,MAAM,CAACG,GAAG,GAAG,EAAE;QACvBqB,IAAI,EAAE,CAAC+G,OAAO,CAAC;QACf9G,IAAI;QACJzB,MAAM,EAAE;UAAEC,IAAI,EAAED,MAAM,CAACC,IAAI;UAAEC,KAAK,EAAEF,MAAM,CAACE;QAAK,CAAE;QAClDwB,MAAM,EAAE;UACNC,IAAI,EAAE,WAAW;UACjBC,QAAQ,EAAE,CAAC;UACXC,WAAW,EAAE,CAAC;UACdE,QAAQ,EAAE,WAAW;UACrBD,MAAM,EAAE,KAAK;UACbE,SAAS,EAAE;SACZ;QACDC,UAAU,EAAE;UAAEC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC;UAAEC,SAAS,EAAE,GAAG;UAAEC,MAAM,EAAE,QAAQ;UAAER,QAAQ,EAAE,CAAC;UAAEC,WAAW,EAAE;QAAC,CAAE;QACxGQ,YAAY,EAAE;UACZ;UACA,CAAC,EAAE;YAAEL,SAAS,EAAE,EAAE;YAAED,QAAQ,EAAE;UAAW,CAAE;UAC3C,CAAC,EAAE;YAAEC,SAAS,EAAE,GAAG;YAAED,QAAQ,EAAE;UAAW,CAAE;UAC5C,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAC7D;UACA,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAC7D,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAC7D,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW,CAAE;UAC7D,CAAC,EAAE;YAAEC,SAAS,EAAE,EAAE;YAAEI,MAAM,EAAE,QAAQ;YAAEL,QAAQ,EAAE;UAAW;SAC5D;QACD+G,YAAY,EAAGpM,IAAS,IAAI;UAC1B;UACA,IAAIA,IAAI,CAACqM,OAAO,KAAK,MAAM,IAAIrM,IAAI,CAACsM,MAAM,CAACtW,KAAK,KAAK,CAAC,EAAE;YACtD,MAAMoI,KAAK,GAAGoD,MAAM,CAACxB,IAAI,CAACuM,IAAI,CAACC,GAAG,IAAI,EAAE,CAAC;YACzC,MAAMC,UAAU,GAAGrO,KAAK,CAACiL,WAAW,EAAE,KAAK,UAAU;YACrD,MAAMqD,EAAE,GAAGD,UAAU,GAAG,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAC;YACrD,MAAMhH,SAAS,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;YAEjCzF,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACQ,SAAS,GAAGkH,EAAE;YAC/B1M,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACS,SAAS,GAAGA,SAAS;YACtCzF,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACmH,SAAS,GAAG,MAAM;YACnCnM,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACU,MAAM,GAAG,QAAQ;YAClC1F,IAAI,CAACuM,IAAI,CAACvH,MAAM,CAACI,MAAM,GAAG,QAAQ;UACpC;QACF,CAAC;QACDQ,KAAK,EAAE;OACR,CAAC;MAEF;MACA,MAAMK,WAAW,GAAG,+BAA+B;MACnD,MAAM0G,SAAS,GAAG7J,GAAG,CAACqD,gBAAgB,EAAE;MACxC,MAAM2G,eAAe,GAAGhK,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAACC,QAAQ,EAAE;MACxD,MAAM0J,gBAAgB,GAAGjK,GAAG,CAACK,QAAQ,CAACC,QAAQ,CAAC2C,SAAS,EAAE;MAC1D,MAAMiH,iBAAiB,GAAG,IAAIlM,IAAI,EAAE,CAACyF,cAAc,EAAE;MACrD,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAImG,SAAS,EAAEnG,CAAC,EAAE,EAAE;QACnC1D,GAAG,CAAC2D,OAAO,CAACD,CAAC,CAAC;QACd1D,GAAG,CAAC4D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/B5D,GAAG,CAAC6D,IAAI,CAACrD,MAAM,CAACC,IAAI,EAAEwJ,gBAAgB,GAAG,EAAE,EAAED,eAAe,GAAGxJ,MAAM,CAACE,KAAK,EAAEuJ,gBAAgB,GAAG,EAAE,CAAC;QACnGjK,GAAG,CAACa,OAAO,CAAC,WAAW,EAAE,QAAQ,CAAC;QAClCb,GAAG,CAACc,WAAW,CAAC,CAAC,CAAC;QAClBd,GAAG,CAAC8D,YAAY,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;QAC/B9D,GAAG,CAAC3E,IAAI,CAAC,iBAAiB6O,iBAAiB,EAAE,EAAE1J,MAAM,CAACC,IAAI,EAAEwJ,gBAAgB,GAAG,EAAE,CAAC;QAClFjK,GAAG,CAAC3E,IAAI,CAAC8H,WAAW,EAAE6G,eAAe,GAAG,CAAC,EAAEC,gBAAgB,GAAG,EAAE,EAAE;UAAE/I,KAAK,EAAE;QAAQ,CAAE,CAAC;QACtFlB,GAAG,CAAC3E,IAAI,CAAC,QAAQqI,CAAC,OAAOmG,SAAS,EAAE,EAAEG,eAAe,GAAGxJ,MAAM,CAACE,KAAK,GAAG,EAAE,EAAEuJ,gBAAgB,GAAG,EAAE,CAAC;MACnG;MAEA,MAAMlG,QAAQ,GAAG,oBAAoB/K,YAAY,IAAI,EAAE,IAAI,IAAIgF,IAAI,EAAE,CAACgG,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,MAAM;MACvGjE,GAAG,CAACkE,IAAI,CAACH,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAOoG,CAAC,EAAE;MACVlO,OAAO,CAACoB,KAAK,CAAC,uCAAuC,EAAE8M,CAAC,CAAC;IAC3D;EACF;EAEOvO,YAAYA,CAAA;IACjB;IACA;IACA,OAAO,IAAI,CAACjB,SAAS,IAAI,IAAI,CAACA,SAAS,CAACyP,MAAM,KAAK,CAAC;EACtD;EAEOpU,4BAA4BA,CAAC8R,UAAe;IACjD;IACA;IACA;IACA,IAAI,IAAI,CAAClN,OAAO,EAAE;MAChB,OAAO,IAAI;IACb;IAEA;IACA,OAAOkN,UAAU,CAACuC,YAAY,KAAK,KAAK;EAC1C;EACAzY,MAAMA,CAAC0Y,QAAa;IACrB,MAAMrD,eAAe,GAKd;MACFvC,IAAI,EAAE,IAAI;MAAE;MACZwC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IACD,IAAI,CAACpN,YAAY,CAAC6L,IAAI,CAACyE,QAAQ,EAACrD,eAAe,CAAC;IAEhD;IACA,IAAI,CAACtN,SAAS,CAAC4Q,UAAU,CAAC;MACxBzX,WAAW,EAAC,IAAI,CAAC3B,MAAM,CAAC2B,WAAW;MACnCF,eAAe,EAAC,IAAI,CAACzB,MAAM,CAACyB,eAAe;MAC3CC,aAAa,EAAC,IAAI,CAAC1B,MAAM,CAAC0B,aAAa;MACvCvB,oBAAoB,EAAC,IAAI,CAACH,MAAM,CAACG;MACjC;KACD,CAAC;EAEJ;EAEAiI,SAASA,CAAA;IACP,IAAI,CAACS,YAAY,CAACwQ,UAAU,EAAE;EAChC;EAGO9Q,mBAAmBA,CAAA;IACxB,IAAI,CAACxJ,SAAS,GAAG,IAAI;IACrB,MAAM+U,QAAQ,GAAG;MAChB3K,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACtBxH,WAAW,EAAE,IAAI,CAAC6G,SAAS,CAAC2B,KAAK,CAACxI,WAAW;MAC7CD,aAAa,EAAE,IAAI,CAAC8G,SAAS,CAAC2B,KAAK,CAACzI,aAAa;MACjDD,eAAe,EAAE,IAAI,CAAC+G,SAAS,CAAC2B,KAAK,CAAC1I,eAAe;MACrDtB,oBAAoB,EAAE,IAAI,CAACqI,SAAS,CAAC2B,KAAK,CAAChK;KAE5C;IAED,IAAI,CAAC+I,cAAc,CAACoQ,mBAAmB,CAACxF,QAAQ,CAAC,CAACnJ,SAAS,CAAC;MAC1DgB,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACxD,SAAS,EAAE;QAChB,IAAI,CAACrJ,SAAS,GAAG,KAAK;QACtB,IAAI6M,GAAG,EAAEC,OAAO,KAAK,KAAK,EAAE;UAC1B;UACA,IAAI,CAAC5C,wBAAwB,CAACiL,WAAW,CAACtI,GAAG,CAACE,YAAY,CAACqI,OAAO,EAAE,EAAE,CAAC;UACvE;UACA,IAAI,CAAChJ,kBAAkB,EAAE;QAC3B,CAAC,MAAM;UACL,IAAI,CAAClC,wBAAwB,CAACmL,SAAS,CAACxI,GAAG,CAACO,YAAY,IAAE,qCAAqC,EAAE,EAAE,CAAC;UACpG;QACF;QACA,IAAI,CAACrD,GAAG,CAACsD,YAAY,EAAE;MACzB,CAAC;MACDF,KAAK,EAAGkC,GAAQ,IAAI;QAClB,IAAI,CAACrP,SAAS,GAAG,KAAK;QACtB,IAAI,CAACkK,wBAAwB,CAACmL,SAAS,CAAC,qCAAqC,EAAE,EAAE,CAAC;QAClF;QACAtJ,OAAO,CAACoB,KAAK,CAACkC,GAAG,CAAC;QAClB,IAAI,CAACtF,GAAG,CAACsD,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;;qCA91CW1D,mBAAmB,EAAAxL,EAAA,CAAAqc,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAvc,EAAA,CAAAqc,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAxc,EAAA,CAAAqc,iBAAA,CAAAI,EAAA,CAAAC,QAAA,GAAA1c,EAAA,CAAAqc,iBAAA,CAAArc,EAAA,CAAA2c,iBAAA,GAAA3c,EAAA,CAAAqc,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7c,EAAA,CAAAqc,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAA/c,EAAA,CAAAqc,iBAAA,CAAAW,EAAA,CAAAC,wBAAA,GAAAjd,EAAA,CAAAqc,iBAAA,CAAAa,EAAA,CAAAC,cAAA;EAAA;;UAAnB3R,mBAAmB;IAAA4R,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAtB,QAAA,WAAAuB,6BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrBhCzd,EAAA,CAAAwD,UAAA,IAAAma,kCAAA,iBAA0D;QAS1D3d,EAAA,CAAAC,cAAA,aAAmC;QAEjCD,EAAA,CAAAwD,UAAA,IAAAoa,kCAAA,mBAAqD;QAuevD5d,EAAA,CAAAG,YAAA,EAAM;QAGNH,EAAA,CAAAwD,UAAA,IAAAqa,0CAAA,iCAAA7d,EAAA,CAAA8d,sBAAA,CAAuD;;;QArfjD9d,EAAA,CAAAc,UAAA,SAAA4c,GAAA,CAAA7b,SAAA,CAAe;QAWoB7B,EAAA,CAAAa,SAAA,GAAY;QAAZb,EAAA,CAAAc,UAAA,SAAA4c,GAAA,CAAA5a,MAAA,CAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
<ng-container>
  <div class="menu-item px-3">
    <div class="menu-content d-flex align-items-center px-3">
      <div class="symbol symbol-50px me-5 d-flex align-items-center justify-content-center">
        <ng-container *ngIf="hasImage; else initialsTpl">
          <img alt="User" [src]="userImage" (error)="onImageError($event)" />
        </ng-container>
        <ng-template #initialsTpl>
          <div class="symbol-label bg-success text-white fw-bold d-flex align-items-center justify-content-center w-50px h-50px">
            {{ initials }}
          </div>
        </ng-template>
      </div>

      <div class="d-flex flex-column">
        <div class="fw-bolder d-flex align-items-center fs-5">
          {{ userFullName }}

        </div>
        <a class="fw-bold text-muted text-hover-primary fs-7 cursor-pointer">
          {{ userEmail }}
        </a>
      </div>
    </div>
  </div>

  <div class="separator my-2"></div>

  <div class="menu-item px-5">
    <a routerLink="/user/profile" class="menu-link px-5">
      My Profile
    </a>
  </div>


  <div class="menu-item px-5 my-1">
    <a (click)="changePassword()" class="menu-link px-5 py-2" >
      Change Password
    </a>
  </div>

  <!-- <div class="menu-item px-5">
    <a class="menu-link px-5 cursor-pointer" placement="top-start" ngbTooltip="Coming soon">
      <span class="menu-text">My Projects</span>
      <span class="menu-badge">
        <span class="badge badge-light-danger badge-circle fw-bolder fs-7">3</span>
      </span>
    </a>
  </div> -->



  <div class="separator my-2"></div>



  <div class="menu-item px-5">
    <a (click)="appService.logout()" class="menu-link px-5 cursor-pointer"> Sign Out </a>
  </div>
</ng-container>

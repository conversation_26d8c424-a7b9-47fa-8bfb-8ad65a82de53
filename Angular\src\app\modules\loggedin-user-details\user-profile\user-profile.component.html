<div class="modal-content h-auto" style="position: relative;">
  <div class="modal-body">
    <ng-container>
      <!-- Loading overlay inside modal -->
      <div *ngIf="isLoading" class="fullscreen-loading-overlay">
        <div class="loading-content">
          <div class="custom-colored-spinner" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
          <div class="mt-4 fs-5">Please wait...</div>
        </div>
      </div>
      <div class="row">
        <div class="col-xl-12">
          <div class="d-flex">
            <ul
              class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap"
            >
              <li class="nav-item">
                <a
                  class="nav-link text-active-primary me-6 cursor-pointer"
                  data-toggle="tab"
                  [ngClass]="{ active: selectedTab === 'basic' }"
                  
                >
                  Profile
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>

      <form class="form form-label-right" [formGroup]="userForm">
        <!-- BASIC TAB -->
        <div class="card-body response-list" *ngIf="selectedTab === 'basic'">
          <div class="row mt-4">
            <!-- First Name -->
            <div class="col-xl-6">
              <div class="form-group">
                <label class="fw-bold form-label mb-2">First Name<sup class="text-danger">*</sup></label>
                <input
                  type="text"
                  class="form-control form-control-sm"
                  name="firstname"
                  formControlName="firstname"
                  placeholder="Type Here"
                />
              </div>
            </div>
            <!-- Last Name -->
            <div class="col-xl-6">
              <div class="form-group">
                <label class="fw-bold form-label mb-2">Last Name<sup class="text-danger">*</sup></label>
                <input
                  type="text"
                  class="form-control form-control-sm"
                  name="lastname"
                  formControlName="lastname"
                  placeholder="Type Here"
                />
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <!-- Email -->
            <div class="col-xl-6">
              <div class="form-group">
                <label class="fw-bold form-label mb-2">Email<sup class="text-danger">*</sup></label>
                <input
                  type="text"
                  class="form-control form-control-sm"
                  name="email"
                  formControlName="email"
                  placeholder="Type Here"
                  readonly
                />
              </div>
            </div>
            <!-- Phone -->
            <div class="col-xl-6">
              <div class="form-group">
                <label class="fw-bold form-label mb-2">Phone</label>
                <igx-input-group>
                  <input
                    class="form-control form-control-sm"
                    name="phone"
                    id="tel"
                    igxInput
                    type="text"
                    autocomplete="off"
                    formControlName="phone"
                    [igxMask]="'(************* Ext. 9999'"
                  />
                </igx-input-group>
              </div>
            </div>
          </div>

          <div class="row mt-4">
            <!-- Title -->
            <div class="col-xl-6">
              <div class="form-group">
                <label class="fw-bold form-label mb-2">Title</label>
                <input
                  type="text"
                  class="form-control form-control-sm"
                  name="title"
                  formControlName="title"
                  placeholder="Type Here"
                />
              </div>
            </div>
          </div>
        </div>
</form>
    </ng-container>
  </div>

  <!-- FOOTER -->
  <div class="modal-footer justify-content-end">
  <button
    type="button"
    class="btn btn-cancel btn-elevate btn-sm me-2"
    (click)="cancelToDashboard()"
  >
    Cancel
  </button>
  <button
    type="button"
    class="btn btn-primary btn-elevate btn-sm"
    [disabled]="isLoading"
    (click)="updateProfile()"
  >
    Update
  </button>
</div>
</div>

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { ProjectsComponent } from './projects.component';
import { ProjectListComponent } from './project-list/project-list.component';
import { ProjectViewComponent } from './project-view/project-view.component';



const routes: Routes = [
    {
        path: '',
        component: ProjectsComponent,
        children: [
            {
                path: 'list',
                component: ProjectListComponent,
            },
            {
                path: 'view/:id',
                component: ProjectViewComponent,
            },
            {
                path: '',
                redirectTo: 'list',
                pathMatch: 'full'
            },
            { path: '**', redirectTo: 'list', pathMatch: 'full' },
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class ProjectRoutingModule { }

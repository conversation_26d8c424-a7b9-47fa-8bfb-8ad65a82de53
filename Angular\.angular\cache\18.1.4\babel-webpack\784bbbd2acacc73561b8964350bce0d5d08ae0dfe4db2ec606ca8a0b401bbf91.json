{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/app.service\";\nimport * as i4 from \"../../services/http-utils.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../../modules/services/projects.service\";\nimport * as i7 from \"../../services/user.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Project\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Project - \", ctx_r0.projectName, \"\");\n  }\n}\nfunction ProjectPopupComponent_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 20)(1, \"div\", 21)(2, \"div\", 22)(3, \"span\", 23);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 24);\n    i0.ɵɵtext(6, \"Initializing form...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectPopupComponent_form_20_ng_container_1_span_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_20_ng_container_1_span_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_20_ng_container_1_span_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_20_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 27)(2, \"div\", 9)(3, \"div\", 28)(4, \"label\", 29);\n    i0.ɵɵtext(5, \"Project Name\");\n    i0.ɵɵelementStart(6, \"sup\", 30);\n    i0.ɵɵtext(7, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(8, \"input\", 31);\n    i0.ɵɵtemplate(9, ProjectPopupComponent_form_20_ng_container_1_span_9_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 27)(11, \"div\", 33)(12, \"div\", 28)(13, \"label\", 29);\n    i0.ɵɵtext(14, \"Internal Project # \");\n    i0.ɵɵelementStart(15, \"sup\", 30);\n    i0.ɵɵtext(16, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(17, \"input\", 34);\n    i0.ɵɵtemplate(18, ProjectPopupComponent_form_20_ng_container_1_span_18_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 35)(20, \"label\", 29);\n    i0.ɵɵtext(21, \"Start Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(22, \"input\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 35)(24, \"label\", 29);\n    i0.ɵɵtext(25, \"End Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(26, \"input\", 37);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"div\", 9)(28, \"label\", 29);\n    i0.ɵɵtext(29, \"Location\");\n    i0.ɵɵelementStart(30, \"sup\", 30);\n    i0.ɵɵtext(31, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(32, \"input\", 38);\n    i0.ɵɵtemplate(33, ProjectPopupComponent_form_20_ng_container_1_span_33_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"div\", 27)(35, \"div\", 9)(36, \"label\", 29);\n    i0.ɵɵtext(37, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(38, \"textarea\", 39);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"projectName\"));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"internalProjectNo\"));\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"location\"));\n  }\n}\nfunction ProjectPopupComponent_form_20_div_2_span_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 40);\n    i0.ɵɵtext(1, \"Required Field\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_form_20_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"div\", 33)(2, \"label\", 29);\n    i0.ɵɵtext(3, \"Internal Project Manager \");\n    i0.ɵɵelementStart(4, \"sup\", 30);\n    i0.ɵɵtext(5, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"ng-select\", 41);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_20_div_2_Template_ng_select_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeInternalManager($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(7, ProjectPopupComponent_form_20_div_2_span_7_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 42)(9, \"label\", 29);\n    i0.ɵɵtext(10, \"External Project Manager (multiple)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"ng-select\", 43);\n    i0.ɵɵlistener(\"change\", function ProjectPopupComponent_form_20_div_2_Template_ng_select_change_11_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.changeexternalPM($event));\n    });\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.managers)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.controlHasError(\"required\", \"manager\"));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"items\", ctx_r0.externalPMs)(\"clearable\", true)(\"multiple\", true);\n  }\n}\nfunction ProjectPopupComponent_form_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"form\", 25);\n    i0.ɵɵtemplate(1, ProjectPopupComponent_form_20_ng_container_1_Template, 39, 3, \"ng-container\", 3)(2, ProjectPopupComponent_form_20_div_2_Template, 12, 7, \"div\", 26);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"formGroup\", ctx_r0.projectForm);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab == \"basic\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.selectedTab != \"basic\");\n  }\n}\nfunction ProjectPopupComponent_button_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_23_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectPopupComponent_button_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_28_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.projectForm == null ? null : ctx_r0.projectForm.invalid);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.id ? \"Update\" : \"Save\", \" \");\n  }\n}\nfunction ProjectPopupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ProjectPopupComponent_button_29_Template_button_click_0_listener($event) {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.showTab(\"role\", $event));\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class ProjectPopupComponent {\n  modal;\n  cdr;\n  fb;\n  appService;\n  httpUtilService;\n  customLayoutUtilsService;\n  projectsService;\n  usersService;\n  id = 0; // 0 = Add, otherwise Edit\n  project; // incoming project data (for edit)\n  passEntry = new EventEmitter();\n  projectForm = null;\n  loginUser = {};\n  managers = [];\n  externalPMs = [];\n  isLoading = false;\n  selectedTab = 'basic'; //store navigation tab\n  projectName;\n  loadingTimeout;\n  constructor(modal, cdr, fb, appService, httpUtilService, customLayoutUtilsService, projectsService,\n  // adjust path\n  usersService) {\n    this.modal = modal;\n    this.cdr = cdr;\n    this.fb = fb;\n    this.appService = appService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.projectsService = projectsService;\n    this.usersService = usersService;\n    // Subscribe to loading state\n    this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading;\n    });\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadForm();\n    // Set loading state immediately for edit mode\n    if (this.id !== 0) {\n      this.isLoading = true;\n      this.httpUtilService.loadingSubject.next(true);\n      // Wait for both manager lists to load before patching form\n      this.loadManagersAndPatchForm();\n    } else {\n      // For add mode, load managers and ensure loading state is properly managed\n      this.loadManagersForAddMode();\n    }\n  }\n  // New method to handle loading managers for add mode with proper loading state management\n  loadManagersForAddMode() {\n    // Set loading state for add mode\n    this.httpUtilService.loadingSubject.next(true);\n    // Safety timeout to prevent loader from getting stuck\n    this.loadingTimeout = setTimeout(() => {\n      console.warn('Loading timeout reached for manager lists, resetting loading state');\n      this.httpUtilService.loadingSubject.next(false);\n      this.customLayoutUtilsService.showError('Loading timeout. Please try again.', '');\n    }, 15000); // 15 seconds timeout\n    // Use Promise.all to wait for both API calls to complete\n    const internalPMsPromise = this.usersService.getUserlistForDropdown({\n      roleName: 'Internal PM'\n    }).toPromise();\n    const externalPMsPromise = this.usersService.getUserlistForDropdown({\n      roleName: 'External PM'\n    }).toPromise();\n    Promise.all([internalPMsPromise, externalPMsPromise]).then(([internalResponse, externalResponse]) => {\n      // Clear the safety timeout since we got a response\n      if (this.loadingTimeout) {\n        clearTimeout(this.loadingTimeout);\n      }\n      // Set the manager lists\n      this.managers = Array.isArray(internalResponse?.responseData?.users) ? internalResponse.responseData.users : [];\n      this.externalPMs = Array.isArray(externalResponse?.responseData?.users) ? externalResponse.responseData.users : [];\n      // Clear loading state\n      this.httpUtilService.loadingSubject.next(false);\n    }).catch(error => {\n      // Clear the safety timeout since we got an error\n      if (this.loadingTimeout) {\n        clearTimeout(this.loadingTimeout);\n      }\n      console.error('Error loading manager lists for add mode:', error);\n      this.managers = [];\n      this.externalPMs = [];\n      this.customLayoutUtilsService.showError('Failed to load manager lists', '');\n      // Clear loading state even on error\n      this.httpUtilService.loadingSubject.next(false);\n    });\n  }\n  loadForm() {\n    this.projectForm = this.fb.group({\n      projectName: ['', Validators.required],\n      internalProjectNo: ['', Validators.required],\n      startDate: [''],\n      endDate: [''],\n      // Make end date optional\n      location: ['', Validators.required],\n      projectDescription: [''],\n      manager: [null, Validators.required],\n      externalPM: [[]] // Remove required validator for external PM as it's optional\n    });\n    // Trigger change detection to update the view\n    this.cdr.detectChanges();\n  }\n  // Load MedicalCenters\n  loadInternalManager() {\n    this.usersService.getUserlistForDropdown({\n      roleName: 'Internal PM'\n    }).subscribe({\n      next: internalPMSResponse => {\n        const users = internalPMSResponse?.responseData?.users;\n        this.managers = Array.isArray(users) ? users : [];\n      },\n      error: error => {\n        console.error('Error loading internal managers:', error);\n        this.managers = [];\n        this.customLayoutUtilsService.showError('Failed to load internal managers', '');\n      }\n    });\n  }\n  // Load roles for advanced filters\n  loadExternalManagers() {\n    this.usersService.getUserlistForDropdown({\n      roleName: 'External PM'\n    }).subscribe({\n      next: externalPMSResponse => {\n        const users = externalPMSResponse?.responseData?.users;\n        this.externalPMs = Array.isArray(users) ? users : [];\n      },\n      error: error => {\n        console.error('Error loading external managers:', error);\n        this.externalPMs = [];\n        this.customLayoutUtilsService.showError('Failed to load external managers', '');\n      }\n    });\n  }\n  // Load both manager lists and then patch the form\n  loadManagersAndPatchForm() {\n    // Use Promise.all to wait for both API calls to complete\n    const internalPMsPromise = this.usersService.getUserlistForDropdown({\n      roleName: 'Internal PM'\n    }).toPromise();\n    const externalPMsPromise = this.usersService.getUserlistForDropdown({\n      roleName: 'External PM'\n    }).toPromise();\n    Promise.all([internalPMsPromise, externalPMsPromise]).then(([internalResponse, externalResponse]) => {\n      // Set the manager lists\n      this.managers = Array.isArray(internalResponse?.responseData?.users) ? internalResponse.responseData.users : [];\n      this.externalPMs = Array.isArray(externalResponse?.responseData?.users) ? externalResponse.responseData.users : [];\n      // Now patch the form with the loaded data\n      this.patchForm();\n    }).catch(error => {\n      console.error('Error loading manager lists:', error);\n      // Still try to patch form even if manager lists fail\n      this.patchForm();\n    });\n  }\n  patchForm() {\n    // Loading state is already set in ngOnInit for edit mode\n    this.projectsService.getProject({\n      projectId: this.id,\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: projectResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!projectResponse.isFault) {\n          let projectData = projectResponse.responseData.Project;\n          // this.projectId = projectData.projectId\n          console.log('Project data received:', projectData);\n          console.log('Available managers:', this.managers);\n          console.log('Available external PMs:', this.externalPMs);\n          this.projectName = projectData.projectName;\n          // Format dates for HTML date inputs (YYYY-MM-DD format)\n          const formatDateForInput = dateValue => {\n            if (!dateValue) return '';\n            const date = new Date(dateValue);\n            return date.toISOString().split('T')[0];\n          };\n          // The manager is already stored as userId in the database\n          const getManagerId = managerId => {\n            if (!managerId) return null;\n            return parseInt(managerId);\n          };\n          // Parse external PM names string to array of userIds\n          const parseExternalPMs = externalPMNamesString => {\n            if (!externalPMNamesString) return [];\n            // Split comma-separated names and find matching userIds\n            const pmNames = externalPMNamesString.split(',').map(name => name.trim()).filter(name => name !== '');\n            return pmNames.map(name => {\n              const pm = this.externalPMs.find(epm => epm.userFullName === name);\n              return pm ? pm.userId : null;\n            }).filter(id => id !== null);\n          };\n          const formData = {\n            projectName: projectData.projectName,\n            internalProjectNo: projectData.internalProjectNumber,\n            startDate: formatDateForInput(projectData.projectStartDate),\n            endDate: formatDateForInput(projectData.projectEndDate),\n            location: projectData.projectLocation,\n            projectDescription: projectData.projectDescription,\n            manager: getManagerId(projectData.internalProjectManager),\n            externalPM: parseExternalPMs(projectData.externalPMNames)\n          };\n          console.log('Form data to patch:', formData);\n          if (this.projectForm) {\n            this.projectForm.patchValue(formData);\n          }\n        } else {\n          console.warn('User response has isFault = true', projectResponse.responseData);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n  }\n  save() {\n    if (!this.projectForm) {\n      this.customLayoutUtilsService.showError('Form is not initialized. Please try again.', '');\n      return;\n    }\n    let controls = this.projectForm.controls;\n    if (this.projectForm.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.customLayoutUtilsService.showError('Please fill all required fields', '');\n      return;\n    }\n    let projectData = this.prepareProjectData();\n    console.log('Line: 203', projectData);\n    if (this.id === 0) {\n      this.create(projectData);\n    } else {\n      this.edit(projectData);\n    }\n  }\n  prepareProjectData() {\n    if (!this.projectForm) {\n      throw new Error('Form is not initialized');\n    }\n    const formData = this.projectForm.value;\n    let projectRequestData = {};\n    projectRequestData.projectName = formData.projectName;\n    projectRequestData.internalProjectNumber = formData.internalProjectNo;\n    projectRequestData.projectStartDate = formData.startDate;\n    projectRequestData.projectEndDate = formData.endDate;\n    projectRequestData.projectLocation = formData.location;\n    projectRequestData.projectDescription = formData.projectDescription;\n    // Send manager userId directly to backend\n    projectRequestData.internalProjectManager = formData.manager;\n    // Convert external PM userIds to comma-separated string for backend\n    const getExternalPMIds = userIds => {\n      if (!userIds || !Array.isArray(userIds)) return '';\n      return userIds.filter(id => id !== null && id !== undefined).join(',');\n    };\n    projectRequestData.externalPM = getExternalPMIds(formData.externalPM);\n    projectRequestData.loggedInUserId = this.loginUser.userId;\n    console.log('Prepared project data for backend:', projectRequestData);\n    return projectRequestData;\n  }\n  // API to update the user details based on the userid\n  edit(projectData) {\n    projectData.projectId = this.id;\n    this.httpUtilService.loadingSubject.next(true);\n    this.projectsService.updateProject(projectData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          this.passEntry.emit(true);\n          this.modal.close();\n        } else {\n          this.customLayoutUtilsService.showError(res.responseData.message, '');\n          this.passEntry.emit(false);\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error updating project:', error);\n        this.customLayoutUtilsService.showError('Failed to update project. Please try again.', '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  // API to save new user details\n  create(projectData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.projectsService.createProject(projectData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n          this.passEntry.emit(true);\n          this.modal.close();\n        } else {\n          this.customLayoutUtilsService.showError(res.responseData.message, '');\n          this.passEntry.emit(false);\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error creating project:', error);\n        this.customLayoutUtilsService.showError('Failed to create project. Please try again.', '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  changeInternalManager(event) {}\n  changeexternalPM(event) {}\n  controlHasError(validation, controlName) {\n    if (!this.projectForm) {\n      return false;\n    }\n    const control = this.projectForm.controls[controlName];\n    if (!control) {\n      return false;\n    }\n    let result = control.hasError(validation) && (control.dirty || control.touched);\n    return result;\n  }\n  goToPreviousTab() {\n    // if (this.selectedTab === 'notes') {\n    //   this.selectedTab = 'details';\n    // } else if (this.selectedTab === 'details') {\n    // }\n    this.selectedTab = 'basic';\n    this.cdr.markForCheck();\n  }\n  ngOnDestroy() {\n    // Clear any pending timeouts to prevent memory leaks\n    if (this.loadingTimeout) {\n      clearTimeout(this.loadingTimeout);\n    }\n  }\n  static ɵfac = function ProjectPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectPopupComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.AppService), i0.ɵɵdirectiveInject(i4.HttpUtilsService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.ProjectsService), i0.ɵɵdirectiveInject(i7.UserService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectPopupComponent,\n    selectors: [[\"app-project-popup\"]],\n    inputs: {\n      id: \"id\",\n      project: \"project\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 30,\n    vars: 13,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [\"class\", \"d-flex justify-content-center align-items-center h-100\", 4, \"ngIf\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [\"class\", \"form form-label-right\", 3, \"formGroup\", 4, \"ngIf\"], [1, \"modal-footer\", \"d-flex\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"h-100\"], [1, \"text-center\"], [\"role\", \"status\", 1, \"custom-colored-spinner\", \"mb-3\"], [1, \"visually-hidden\"], [1, \"text-muted\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [\"class\", \"row mt-4\", 4, \"ngIf\"], [1, \"row\", \"mt-4\"], [1, \"form-group\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"type\", \"text\", \"formControlName\", \"projectName\", \"placeholder\", \"Project Name\", 1, \"form-control\", \"form-control-sm\"], [\"class\", \"custom-error-css\", 4, \"ngIf\"], [1, \"col-xl-6\"], [\"type\", \"text\", \"formControlName\", \"internalProjectNo\", \"placeholder\", \"Internal Project\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-3\"], [\"type\", \"date\", \"formControlName\", \"startDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"endDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", \"placeholder\", \"Location\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"projectDescription\", 1, \"form-control\", \"form-control-sm\"], [1, \"custom-error-css\"], [\"bindLabel\", \"userFullName\", \"name\", \"manager\", \"formControlName\", \"manager\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-12\", \"mt-4\"], [\"bindLabel\", \"userFullName\", \"name\", \"externalPM\", \"formControlName\", \"externalPM\", \"bindValue\", \"userId\", \"placeholder\", \"Select an option\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"]],\n    template: function ProjectPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, ProjectPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, ProjectPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_i_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6);\n        i0.ɵɵtemplate(9, ProjectPopupComponent_div_9_Template, 7, 0, \"div\", 7);\n        i0.ɵɵelementStart(10, \"div\", 8)(11, \"div\", 9)(12, \"div\", 10)(13, \"ul\", 11)(14, \"li\", 12)(15, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_15_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(16, \" Project Details \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(17, \"li\", 12)(18, \"a\", 13);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_a_click_18_listener($event) {\n          return ctx.showTab(\"role\", $event);\n        });\n        i0.ɵɵtext(19, \" Project Manager \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵtemplate(20, ProjectPopupComponent_form_20_Template, 3, 3, \"form\", 14);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(21, \"div\", 15)(22, \"div\");\n        i0.ɵɵtemplate(23, ProjectPopupComponent_button_23_Template, 2, 0, \"button\", 16);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"div\")(25, \"button\", 17);\n        i0.ɵɵlistener(\"click\", function ProjectPopupComponent_Template_button_click_25_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵtext(26, \" Cancel \");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(27, \" \\u00A0 \");\n        i0.ɵɵtemplate(28, ProjectPopupComponent_button_28_Template, 2, 2, \"button\", 18)(29, ProjectPopupComponent_button_29_Template, 2, 0, \"button\", 19);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", !ctx.projectForm && !ctx.isLoading);\n        i0.ɵɵadvance(6);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(9, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(11, _c0, ctx.selectedTab === \"role\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.projectForm);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab != \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i9.NgSelectComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "projectName", "ɵɵelementContainerStart", "ɵɵelement", "ɵɵtemplate", "ProjectPopupComponent_form_20_ng_container_1_span_9_Template", "ProjectPopupComponent_form_20_ng_container_1_span_18_Template", "ProjectPopupComponent_form_20_ng_container_1_span_33_Template", "ɵɵproperty", "controlHasError", "ɵɵlistener", "ProjectPopupComponent_form_20_div_2_Template_ng_select_change_6_listener", "$event", "ɵɵrestoreView", "_r2", "ɵɵnextContext", "ɵɵresetView", "changeInternalManager", "ProjectPopupComponent_form_20_div_2_span_7_Template", "ProjectPopupComponent_form_20_div_2_Template_ng_select_change_11_listener", "changeexternalPM", "managers", "externalPMs", "ProjectPopupComponent_form_20_ng_container_1_Template", "ProjectPopupComponent_form_20_div_2_Template", "projectForm", "selectedTab", "ProjectPopupComponent_button_23_Template_button_click_0_listener", "_r3", "goToPreviousTab", "ProjectPopupComponent_button_28_Template_button_click_0_listener", "_r4", "save", "invalid", "id", "ProjectPopupComponent_button_29_Template_button_click_0_listener", "_r5", "showTab", "ProjectPopupComponent", "modal", "cdr", "fb", "appService", "httpUtilService", "customLayoutUtilsService", "projectsService", "usersService", "project", "passEntry", "loginUser", "isLoading", "loadingTimeout", "constructor", "loadingSubject", "subscribe", "loading", "ngOnInit", "getLoggedInUser", "loadForm", "next", "loadManagersAndPatchForm", "loadManagersForAddMode", "setTimeout", "console", "warn", "showError", "internalPMsPromise", "getUserlistForDropdown", "<PERSON><PERSON><PERSON>", "to<PERSON>romise", "externalPMsPromise", "Promise", "all", "then", "internalResponse", "externalResponse", "clearTimeout", "Array", "isArray", "responseData", "users", "catch", "error", "group", "required", "internalProjectNo", "startDate", "endDate", "location", "projectDescription", "manager", "externalPM", "detectChanges", "loadInternalManager", "internalPMSResponse", "loadExternalManagers", "externalPMSResponse", "patchForm", "getProject", "projectId", "loggedInUserId", "userId", "projectResponse", "<PERSON><PERSON><PERSON>", "projectData", "Project", "log", "formatDateForInput", "dateValue", "date", "Date", "toISOString", "split", "getManagerId", "managerId", "parseInt", "parseExternalPMs", "externalPMNamesString", "pmNames", "map", "name", "trim", "filter", "pm", "find", "epm", "userFullName", "formData", "internalProjectNumber", "projectStartDate", "projectEndDate", "projectLocation", "internalProjectManager", "externalPMNames", "patchValue", "err", "controls", "Object", "keys", "for<PERSON>ach", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "prepareProjectData", "create", "edit", "Error", "value", "projectRequestData", "getExternalPMIds", "userIds", "undefined", "join", "updateProject", "res", "showSuccess", "message", "emit", "close", "createProject", "tab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "validation", "control", "result", "<PERSON><PERSON><PERSON><PERSON>", "dirty", "touched", "ngOnDestroy", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "ChangeDetectorRef", "i2", "FormBuilder", "i3", "AppService", "i4", "HttpUtilsService", "i5", "CustomLayoutUtilsService", "i6", "ProjectsService", "i7", "UserService", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "ProjectPopupComponent_Template", "rf", "ctx", "ProjectPopupComponent_div_4_Template", "ProjectPopupComponent_div_5_Template", "ProjectPopupComponent_Template_i_click_7_listener", "dismiss", "ProjectPopupComponent_div_9_Template", "ProjectPopupComponent_Template_a_click_15_listener", "ProjectPopupComponent_Template_a_click_18_listener", "ProjectPopupComponent_form_20_Template", "ProjectPopupComponent_button_23_Template", "ProjectPopupComponent_Template_button_click_25_listener", "ProjectPopupComponent_button_28_Template", "ProjectPopupComponent_button_29_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-popup\\project-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-popup\\project-popup.component.html"], "sourcesContent": ["import {\r\n  ChangeDetectorRef,\r\n  Component,\r\n  EventEmitter,\r\n  Input,\r\n  Output,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ProjectsService } from '../../../modules/services/projects.service'; // adjust path\r\nimport { AppService } from '../../services/app.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { UserService } from '../../services/user.service';\r\n\r\n@Component({\r\n  selector: 'app-project-popup',\r\n  templateUrl: './project-popup.component.html',\r\n})\r\nexport class ProjectPopupComponent implements OnDestroy {\r\n  @Input() id: number = 0; // 0 = Add, otherwise Edit\r\n  @Input() project: any; // incoming project data (for edit)\r\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\r\n  projectForm: FormGroup | null = null;\r\n  loginUser: any = {};\r\n  managers: any = [];\r\n  externalPMs: any = [];\r\n  isLoading: boolean = false;\r\n  selectedTab: string = 'basic'; //store navigation tab\r\n  projectName: any;\r\n  private loadingTimeout: any;\r\n  constructor(\r\n    public modal: NgbActiveModal,\r\n    private cdr: ChangeDetectorRef,\r\n        \r\n    private fb: FormBuilder,\r\n    private appService: AppService,\r\n    private httpUtilService: HttpUtilsService,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private projectsService: ProjectsService, // adjust path\r\n    private usersService: UserService\r\n  ) {\r\n    // Subscribe to loading state\r\n    this.httpUtilService.loadingSubject.subscribe((loading) => {\r\n      this.isLoading = loading;\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n    this.loadForm();\r\n\r\n    // Set loading state immediately for edit mode\r\n    if (this.id !== 0) {\r\n      this.isLoading = true;\r\n      this.httpUtilService.loadingSubject.next(true);\r\n      // Wait for both manager lists to load before patching form\r\n      this.loadManagersAndPatchForm();\r\n    } else {\r\n      // For add mode, load managers and ensure loading state is properly managed\r\n      this.loadManagersForAddMode();\r\n    }\r\n  }\r\n\r\n  // New method to handle loading managers for add mode with proper loading state management\r\n  loadManagersForAddMode(): void {\r\n    // Set loading state for add mode\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Safety timeout to prevent loader from getting stuck\r\n    this.loadingTimeout = setTimeout(() => {\r\n      console.warn('Loading timeout reached for manager lists, resetting loading state');\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      this.customLayoutUtilsService.showError('Loading timeout. Please try again.', '');\r\n    }, 15000); // 15 seconds timeout\r\n\r\n    // Use Promise.all to wait for both API calls to complete\r\n    const internalPMsPromise = this.usersService\r\n      .getUserlistForDropdown({ roleName: 'Internal PM' })\r\n      .toPromise();\r\n\r\n    const externalPMsPromise = this.usersService\r\n      .getUserlistForDropdown({ roleName: 'External PM' })\r\n      .toPromise();\r\n\r\n    Promise.all([internalPMsPromise, externalPMsPromise])\r\n      .then(([internalResponse, externalResponse]) => {\r\n        // Clear the safety timeout since we got a response\r\n        if (this.loadingTimeout) {\r\n          clearTimeout(this.loadingTimeout);\r\n        }\r\n\r\n        // Set the manager lists\r\n        this.managers = Array.isArray(internalResponse?.responseData?.users)\r\n          ? internalResponse.responseData.users : [];\r\n        this.externalPMs = Array.isArray(externalResponse?.responseData?.users)\r\n          ? externalResponse.responseData.users : [];\r\n\r\n        // Clear loading state\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      })\r\n      .catch(error => {\r\n        // Clear the safety timeout since we got an error\r\n        if (this.loadingTimeout) {\r\n          clearTimeout(this.loadingTimeout);\r\n        }\r\n\r\n        console.error('Error loading manager lists for add mode:', error);\r\n        this.managers = [];\r\n        this.externalPMs = [];\r\n        this.customLayoutUtilsService.showError('Failed to load manager lists', '');\r\n        // Clear loading state even on error\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      });\r\n  }\r\n\r\n  loadForm() {\r\n    this.projectForm = this.fb.group({\r\n      projectName: ['', Validators.required],\r\n      internalProjectNo: ['', Validators.required],\r\n      startDate: [''],\r\n      endDate: [''], // Make end date optional\r\n      location: ['', Validators.required],\r\n      projectDescription: [''],\r\n      manager: [null, Validators.required],\r\n      externalPM: [[]], // Remove required validator for external PM as it's optional\r\n    });\r\n\r\n    // Trigger change detection to update the view\r\n    this.cdr.detectChanges();\r\n  }\r\n  // Load MedicalCenters\r\n  loadInternalManager(): void {\r\n    this.usersService\r\n      .getUserlistForDropdown({ roleName: 'Internal PM' })\r\n      .subscribe({\r\n        next: (internalPMSResponse: any) => {\r\n          const users = internalPMSResponse?.responseData?.users;\r\n          this.managers = Array.isArray(users) ? users : [];\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading internal managers:', error);\r\n          this.managers = [];\r\n          this.customLayoutUtilsService.showError('Failed to load internal managers', '');\r\n        }\r\n      });\r\n  }\r\n  // Load roles for advanced filters\r\n  loadExternalManagers(): void {\r\n    this.usersService\r\n      .getUserlistForDropdown({ roleName: 'External PM' })\r\n      .subscribe({\r\n        next: (externalPMSResponse: any) => {\r\n          const users = externalPMSResponse?.responseData?.users;\r\n          this.externalPMs = Array.isArray(users) ? users : [];\r\n        },\r\n        error: (error: any) => {\r\n          console.error('Error loading external managers:', error);\r\n          this.externalPMs = [];\r\n          this.customLayoutUtilsService.showError('Failed to load external managers', '');\r\n        }\r\n      });\r\n  }\r\n\r\n  // Load both manager lists and then patch the form\r\n  loadManagersAndPatchForm(): void {\r\n    // Use Promise.all to wait for both API calls to complete\r\n    const internalPMsPromise = this.usersService\r\n      .getUserlistForDropdown({ roleName: 'Internal PM' })\r\n      .toPromise();\r\n\r\n    const externalPMsPromise = this.usersService\r\n      .getUserlistForDropdown({ roleName: 'External PM' })\r\n      .toPromise();\r\n\r\n    Promise.all([internalPMsPromise, externalPMsPromise])\r\n      .then(([internalResponse, externalResponse]) => {\r\n        // Set the manager lists\r\n        this.managers = Array.isArray(internalResponse?.responseData?.users)\r\n          ? internalResponse.responseData.users : [];\r\n        this.externalPMs = Array.isArray(externalResponse?.responseData?.users)\r\n          ? externalResponse.responseData.users : [];\r\n\r\n        // Now patch the form with the loaded data\r\n        this.patchForm();\r\n      })\r\n      .catch(error => {\r\n        console.error('Error loading manager lists:', error);\r\n        // Still try to patch form even if manager lists fail\r\n        this.patchForm();\r\n      });\r\n  }\r\n\r\n  patchForm() {\r\n    // Loading state is already set in ngOnInit for edit mode\r\n    this.projectsService\r\n      .getProject({ projectId: this.id, loggedInUserId: this.loginUser.userId })\r\n      .subscribe({\r\n        next: (projectResponse: any) => {\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          if (!projectResponse.isFault) {\r\n            let projectData = projectResponse.responseData.Project;\r\n            // this.projectId = projectData.projectId\r\n            console.log('Project data received:', projectData);\r\n            console.log('Available managers:', this.managers);\r\n            console.log('Available external PMs:', this.externalPMs);\r\n            this.projectName = projectData.projectName;\r\n\r\n            // Format dates for HTML date inputs (YYYY-MM-DD format)\r\n            const formatDateForInput = (dateValue: any) => {\r\n              if (!dateValue) return '';\r\n              const date = new Date(dateValue);\r\n              return date.toISOString().split('T')[0];\r\n            };\r\n\r\n            // The manager is already stored as userId in the database\r\n            const getManagerId = (managerId: any) => {\r\n              if (!managerId) return null;\r\n              return parseInt(managerId);\r\n            };\r\n\r\n            // Parse external PM names string to array of userIds\r\n            const parseExternalPMs = (externalPMNamesString: string) => {\r\n              if (!externalPMNamesString) return [];\r\n              // Split comma-separated names and find matching userIds\r\n              const pmNames = externalPMNamesString.split(',').map(name => name.trim()).filter(name => name !== '');\r\n              return pmNames.map(name => {\r\n                const pm = this.externalPMs.find((epm: any) => epm.userFullName === name);\r\n                return pm ? pm.userId : null;\r\n              }).filter(id => id !== null);\r\n            };\r\n\r\n            const formData = {\r\n              projectName: projectData.projectName,\r\n              internalProjectNo: projectData.internalProjectNumber,\r\n              startDate: formatDateForInput(projectData.projectStartDate),\r\n              endDate: formatDateForInput(projectData.projectEndDate),\r\n              location: projectData.projectLocation,\r\n              projectDescription: projectData.projectDescription,\r\n              manager: getManagerId(projectData.internalProjectManager),\r\n              externalPM: parseExternalPMs(projectData.externalPMNames),\r\n            };\r\n\r\n            console.log('Form data to patch:', formData);\r\n            if (this.projectForm) {\r\n              this.projectForm.patchValue(formData);\r\n            }\r\n          } else {\r\n            console.warn(\r\n              'User response has isFault = true',\r\n              projectResponse.responseData\r\n            );\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          console.error('API call failed', err);\r\n        },\r\n      });\r\n  }\r\n  save() {\r\n    if (!this.projectForm) {\r\n      this.customLayoutUtilsService.showError(\r\n        'Form is not initialized. Please try again.',\r\n        ''\r\n      );\r\n      return;\r\n    }\r\n\r\n    let controls = this.projectForm!.controls;\r\n    if (this.projectForm!.invalid) {\r\n      Object.keys(controls).forEach((controlName) =>\r\n        controls[controlName].markAsTouched()\r\n      );\r\n      this.customLayoutUtilsService.showError(\r\n        'Please fill all required fields',\r\n        ''\r\n      );\r\n      return;\r\n    }\r\n    let projectData: any = this.prepareProjectData();\r\n    console.log('Line: 203', projectData);\r\n    if (this.id === 0) {\r\n      this.create(projectData);\r\n    } else {\r\n      this.edit(projectData);\r\n    }\r\n  }\r\n\r\n  prepareProjectData() {\r\n    if (!this.projectForm) {\r\n      throw new Error('Form is not initialized');\r\n    }\r\n\r\n    const formData = this.projectForm!.value;\r\n    let projectRequestData: any = {};\r\n    projectRequestData.projectName = formData.projectName;\r\n    projectRequestData.internalProjectNumber = formData.internalProjectNo;\r\n    projectRequestData.projectStartDate = formData.startDate;\r\n    projectRequestData.projectEndDate = formData.endDate;\r\n    projectRequestData.projectLocation = formData.location;\r\n    projectRequestData.projectDescription = formData.projectDescription;\r\n\r\n    // Send manager userId directly to backend\r\n    projectRequestData.internalProjectManager = formData.manager;\r\n\r\n    // Convert external PM userIds to comma-separated string for backend\r\n    const getExternalPMIds = (userIds: number[]) => {\r\n      if (!userIds || !Array.isArray(userIds)) return '';\r\n      return userIds.filter(id => id !== null && id !== undefined).join(',');\r\n    };\r\n\r\n    projectRequestData.externalPM = getExternalPMIds(formData.externalPM);\r\n    projectRequestData.loggedInUserId = this.loginUser.userId;\r\n\r\n    console.log('Prepared project data for backend:', projectRequestData);\r\n    return projectRequestData;\r\n  }\r\n  // API to update the user details based on the userid\r\n  edit(projectData: any) {\r\n    projectData.projectId = this.id\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.projectsService.updateProject(projectData).subscribe({\r\n      next: (res) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (!res.isFault) {\r\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n          this.passEntry.emit(true);\r\n          this.modal.close();\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.responseData.message, '');\r\n          this.passEntry.emit(false);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error updating project:', error);\r\n        this.customLayoutUtilsService.showError('Failed to update project. Please try again.', '');\r\n        this.passEntry.emit(false);\r\n      }\r\n    });\r\n  }\r\n  // API to save new user details\r\n  create(projectData: any) {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.projectsService.createProject(projectData).subscribe({\r\n      next: (res: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (!res.isFault) {\r\n          this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n          this.passEntry.emit(true);\r\n          this.modal.close();\r\n        } else {\r\n          this.customLayoutUtilsService.showError(res.responseData.message, '');\r\n          this.passEntry.emit(false);\r\n        }\r\n      },\r\n      error: (error) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error creating project:', error);\r\n        this.customLayoutUtilsService.showError('Failed to create project. Please try again.', '');\r\n        this.passEntry.emit(false);\r\n      }\r\n    });\r\n  }\r\n  showTab(tab: any, $event: any) {\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n  changeInternalManager(event: any) {}\r\n  changeexternalPM(event: any) {}\r\n  controlHasError(validation: any, controlName: string | number): boolean {\r\n    if (!this.projectForm) {\r\n      return false;\r\n    }\r\n\r\n    const control = this.projectForm!.controls[controlName];\r\n    if (!control) {\r\n      return false;\r\n    }\r\n    let result =\r\n      control.hasError(validation) && (control.dirty || control.touched);\r\n    return result;\r\n  }\r\n    goToPreviousTab() {\r\n    // if (this.selectedTab === 'notes') {\r\n    //   this.selectedTab = 'details';\r\n    // } else if (this.selectedTab === 'details') {\r\n    // }\r\n    this.selectedTab = 'basic';\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  ngOnDestroy() {\r\n    // Clear any pending timeouts to prevent memory leaks\r\n    if (this.loadingTimeout) {\r\n      clearTimeout(this.loadingTimeout);\r\n    }\r\n  }\r\n}\r\n", "<div class=\"modal-content h-auto\">\r\n  <div class=\"modal-header bg-light-primary\">\r\n    <div class=\"modal-title h5 fs-3\">\r\n      <ng-container>\r\n        <div *ngIf=\"id === 0\">Add Project</div>\r\n        <div *ngIf=\"id !== 0\">Edit Project - {{ projectName }}</div>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n      <i\r\n        class=\"fa-solid fs-2 fa-xmark text-white\"\r\n        (click)=\"modal.dismiss()\"\r\n      ></i>\r\n    </div>\r\n  </div>\r\n\r\n  <div\r\n    class=\"modal-body\"\r\n    \r\n  >\r\n    <!--   max-height: calc(100vh - 250px);\r\n      overflow-y: auto;\r\n      position: relative; -->\r\n    <!-- Loading overlay removed; global loader handles this -->\r\n\r\n    <!-- Initial loading state for form -->\r\n    <div *ngIf=\"!projectForm && !isLoading\" class=\"d-flex justify-content-center align-items-center h-100\">\r\n      <div class=\"text-center\">\r\n        <div class=\"custom-colored-spinner mb-3\" role=\"status\">\r\n          <span class=\"visually-hidden\">Loading...</span>\r\n        </div>\r\n        <div class=\"text-muted\">Initializing form...</div>\r\n      </div>\r\n    </div>\r\n\r\n    <div class=\"row\">\r\n      <div class=\"col-xl-12\">\r\n        <div class=\"d-flex\">\r\n          <ul\r\n            class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\"\r\n          >\r\n            <li class=\"nav-item\">\r\n              <a\r\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\r\n                data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'basic' }\"\r\n                (click)=\"showTab('basic', $event)\"\r\n              >\r\n                Project Details\r\n              </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a\r\n                class=\"nav-link text-active-primary me-6 cursor-pointer\"\r\n                data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'role' }\"\r\n                (click)=\"showTab('role', $event)\"\r\n              >\r\n                Project Manager\r\n              </a>\r\n            </li>\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <form class=\"form form-label-right\" [formGroup]=\"projectForm\" *ngIf=\"projectForm\">\r\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\r\n        <div class=\"row mt-4\">\r\n          <!-- Project Name -->\r\n\r\n          <div class=\"col-xl-12\">\r\n            <div class=\"form-group\">\r\n              <label class=\"fw-bold form-label mb-2\">Project Name<sup class=\"text-danger\">*</sup></label>\r\n              <input\r\n                type=\"text\"\r\n                class=\"form-control form-control-sm\"\r\n                formControlName=\"projectName\"\r\n                placeholder=\"Project Name\"\r\n              />\r\n               <span\r\n                class=\"custom-error-css\"\r\n                *ngIf=\"controlHasError('required', 'projectName')\"\r\n                >Required Field</span\r\n              >\r\n            </div>\r\n          </div>\r\n\r\n          <!-- Internal Project Number -->\r\n\r\n        </div>\r\n        \r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-6\">\r\n            <div class=\"form-group\">\r\n              <label class=\"fw-bold form-label mb-2\"\r\n                >Internal Project # <sup class=\"text-danger\">*</sup></label\r\n              >\r\n              <input\r\n                type=\"text\"\r\n                class=\"form-control form-control-sm\"\r\n                formControlName=\"internalProjectNo\"\r\n                placeholder=\"Internal Project\"\r\n              />\r\n              <span\r\n                class=\"custom-error-css\"\r\n                *ngIf=\"controlHasError('required', 'internalProjectNo')\"\r\n                >Required Field</span\r\n              >\r\n            </div>\r\n          </div>\r\n          <!-- Start Date -->\r\n          <div class=\"col-xl-3\">\r\n            <label class=\"fw-bold form-label mb-2\">Start Date</label>\r\n            <input\r\n              type=\"date\"\r\n              class=\"form-control form-control-sm\"\r\n              formControlName=\"startDate\"\r\n            />\r\n          </div>\r\n\r\n          <!-- End Date -->\r\n          <div class=\"col-xl-3\">\r\n            <label class=\"fw-bold form-label mb-2\">End Date</label>\r\n            <input\r\n              type=\"date\"\r\n              class=\"form-control form-control-sm\"\r\n              formControlName=\"endDate\"\r\n            />\r\n          </div>\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Location<sup class=\"text-danger\">*</sup></label>\r\n            <input\r\n              type=\"text\"\r\n              class=\"form-control form-control-sm\"\r\n              formControlName=\"location\"\r\n              placeholder=\"Location\"\r\n            />\r\n             <span\r\n                class=\"custom-error-css\"\r\n                *ngIf=\"controlHasError('required', 'location')\"\r\n                >Required Field</span\r\n              >\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Description</label>\r\n            <textarea\r\n              class=\"form-control form-control-sm\"\r\n              rows=\"3\"\r\n              formControlName=\"projectDescription\"\r\n            ></textarea>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n\r\n      <div class=\"row mt-4\" *ngIf=\"selectedTab != 'basic'\">\r\n        <!-- Location -->\r\n\r\n        <!-- Manager -->\r\n        <div class=\"col-xl-6\">\r\n          <label class=\"fw-bold form-label mb-2\"\r\n            >Internal Project Manager <sup class=\"text-danger\">*</sup></label\r\n          >\r\n          <ng-select\r\n            [items]=\"managers\"\r\n            [clearable]=\"false\"\r\n            [multiple]=\"false\"\r\n            bindLabel=\"userFullName\"\r\n            name=\"manager\"\r\n            formControlName=\"manager\"\r\n            bindValue=\"userId\"\r\n            (change)=\"changeInternalManager($event)\"\r\n            placeholder=\"Select an option\"\r\n          >\r\n          </ng-select>\r\n          <span\r\n            class=\"custom-error-css\"\r\n            *ngIf=\"controlHasError('required', 'manager')\"\r\n            >Required Field</span\r\n          >\r\n        </div>\r\n\r\n        <!-- External PM -->\r\n        <div class=\"col-xl-12 mt-4\">\r\n          <label class=\"fw-bold form-label mb-2\"\r\n            >External Project Manager (multiple)</label\r\n          >\r\n          <ng-select\r\n            [items]=\"externalPMs\"\r\n            [clearable]=\"true\"\r\n            [multiple]=\"true\"\r\n            bindLabel=\"userFullName\"\r\n            name=\"externalPM\"\r\n            formControlName=\"externalPM\"\r\n            bindValue=\"userId\"\r\n            (change)=\"changeexternalPM($event)\"\r\n            placeholder=\"Select an option\"\r\n          >\r\n          </ng-select>\r\n        </div>\r\n      </div>\r\n    </form>\r\n  </div>\r\n\r\n<div class=\"modal-footer d-flex justify-content-between\">\r\n  <!-- Left Side -->\r\n  <div>\r\n    <button\r\n      *ngIf=\"selectedTab != 'basic'\"\r\n      type=\"button\"\r\n      class=\"btn btn-secondary btn-sm btn-elevate\"\r\n      (click)=\"goToPreviousTab()\"\r\n    >\r\n      Previous\r\n    </button>\r\n  </div>\r\n\r\n  <!-- Right Side -->\r\n  <div>\r\n    <button\r\n      class=\"btn btn-danger btn-sm btn-elevate mr-2\"\r\n      (click)=\"modal.dismiss()\"\r\n    >\r\n      Cancel\r\n    </button>\r\n    &nbsp;\r\n    <button\r\n      *ngIf=\"selectedTab != 'basic'\"\r\n      type=\"button\"\r\n      class=\"btn btn-primary btn-sm\"\r\n      [disabled]=\"projectForm?.invalid\"\r\n      (click)=\"save()\"\r\n    >\r\n      {{ id ? \"Update\" : \"Save\" }}\r\n    </button>\r\n    <button\r\n      *ngIf=\"selectedTab == 'basic'\"\r\n      type=\"button\"\r\n      class=\"btn btn-primary btn-sm\"\r\n      (click)=\"showTab('role', $event)\"\r\n    >\r\n      Next\r\n    </button>\r\n  </div>\r\n</div>\r\n\r\n</div>\r\n"], "mappings": "AAAA,SAGEA,YAAY,QAGP,eAAe;AACtB,SAAiCC,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;ICH3DC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACvCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,oBAAAC,MAAA,CAAAC,WAAA,KAAgC;;;;;IAwBpDP,EAHN,CAAAC,cAAA,cAAuG,cAC5E,cACgC,eACvB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAEhDF,EAFgD,CAAAG,YAAA,EAAM,EAC9C,EACF;;;;;IA+CKH,EAAA,CAAAC,cAAA,eAGE;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IAqBDH,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IA8BFH,EAAA,CAAAC,cAAA,eAGI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;IA5ETH,EAAA,CAAAQ,uBAAA,GAA6C;IAMrCR,EALN,CAAAC,cAAA,cAAsB,aAGG,cACG,gBACiB;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IAC3FH,EAAA,CAAAS,SAAA,gBAKE;IACDT,EAAA,CAAAU,UAAA,IAAAC,4DAAA,mBAGE;IAOTX,EALI,CAAAG,YAAA,EAAM,EACF,EAIF;IAMAH,EAHN,CAAAC,cAAA,eAAsB,eACE,eACI,iBAEnB;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EACrD;IACDH,EAAA,CAAAS,SAAA,iBAKE;IACFT,EAAA,CAAAU,UAAA,KAAAE,6DAAA,mBAGG;IAGPZ,EADE,CAAAG,YAAA,EAAM,EACF;IAGJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAS,SAAA,iBAIE;IACJT,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvDH,EAAA,CAAAS,SAAA,iBAIE;IACJT,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAuB,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAAQ;IACvFH,EAAA,CAAAS,SAAA,iBAKE;IACDT,EAAA,CAAAU,UAAA,KAAAG,6DAAA,mBAGI;IAGTb,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAS,SAAA,oBAIY;IAEhBT,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAzEGH,EAAA,CAAAI,SAAA,GAAgD;IAAhDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,4BAAgD;IAyBhDf,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,kCAAsD;IAkCtDf,EAAA,CAAAI,SAAA,IAA6C;IAA7CJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,yBAA6C;;;;;IAqCpDf,EAAA,CAAAC,cAAA,eAGG;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAChB;;;;;;IAnBDH,EALJ,CAAAC,cAAA,cAAqD,cAI7B,gBAEjB;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAC,cAAA,cAAyB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAMF,EAAN,CAAAG,YAAA,EAAM,EAC3D;IACDH,EAAA,CAAAC,cAAA,oBAUC;IAFCD,EAAA,CAAAgB,UAAA,oBAAAC,yEAAAC,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAUhB,MAAA,CAAAiB,qBAAA,CAAAL,MAAA,CAA6B;IAAA,EAAC;IAG1ClB,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAU,UAAA,IAAAc,mDAAA,mBAGG;IAELxB,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,cAA4B,gBAEvB;IAAAD,EAAA,CAAAE,MAAA,2CAAmC;IAAAF,EAAA,CAAAG,YAAA,EACrC;IACDH,EAAA,CAAAC,cAAA,qBAUC;IAFCD,EAAA,CAAAgB,UAAA,oBAAAS,0EAAAP,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAC,GAAA;MAAA,MAAAd,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAUhB,MAAA,CAAAoB,gBAAA,CAAAR,MAAA,CAAwB;IAAA,EAAC;IAKzClB,EAFI,CAAAG,YAAA,EAAY,EACR,EACF;;;;IApCAH,EAAA,CAAAI,SAAA,GAAkB;IAElBJ,EAFA,CAAAc,UAAA,UAAAR,MAAA,CAAAqB,QAAA,CAAkB,oBACC,mBACD;IAWjB3B,EAAA,CAAAI,SAAA,EAA4C;IAA5CJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAAS,eAAA,wBAA4C;IAW7Cf,EAAA,CAAAI,SAAA,GAAqB;IAErBJ,EAFA,CAAAc,UAAA,UAAAR,MAAA,CAAAsB,WAAA,CAAqB,mBACH,kBACD;;;;;IA/HzB5B,EAAA,CAAAC,cAAA,eAAkF;IA4FhFD,EA3FA,CAAAU,UAAA,IAAAmB,qDAAA,2BAA6C,IAAAC,4CAAA,mBA2FQ;IA8CvD9B,EAAA,CAAAG,YAAA,EAAO;;;;IA1I6BH,EAAA,CAAAc,UAAA,cAAAR,MAAA,CAAAyB,WAAA,CAAyB;IAC5C/B,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA0B,WAAA,YAA4B;IA2FpBhC,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAc,UAAA,SAAAR,MAAA,CAAA0B,WAAA,YAA4B;;;;;;IAoDrDhC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAgB,UAAA,mBAAAiB,iEAAA;MAAAjC,EAAA,CAAAmB,aAAA,CAAAe,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAA6B,eAAA,EAAiB;IAAA,EAAC;IAE3BnC,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAYTH,EAAA,CAAAC,cAAA,iBAMC;IADCD,EAAA,CAAAgB,UAAA,mBAAAoB,iEAAA;MAAApC,EAAA,CAAAmB,aAAA,CAAAkB,GAAA;MAAA,MAAA/B,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAAgC,IAAA,EAAM;IAAA,EAAC;IAEhBtC,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAJPH,EAAA,CAAAc,UAAA,aAAAR,MAAA,CAAAyB,WAAA,kBAAAzB,MAAA,CAAAyB,WAAA,CAAAQ,OAAA,CAAiC;IAGjCvC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAkC,EAAA,0BACF;;;;;;IACAxC,EAAA,CAAAC,cAAA,iBAKC;IADCD,EAAA,CAAAgB,UAAA,mBAAAyB,iEAAAvB,MAAA;MAAAlB,EAAA,CAAAmB,aAAA,CAAAuB,GAAA;MAAA,MAAApC,MAAA,GAAAN,EAAA,CAAAqB,aAAA;MAAA,OAAArB,EAAA,CAAAsB,WAAA,CAAShB,MAAA,CAAAqC,OAAA,CAAQ,MAAM,EAAAzB,MAAA,CAAS;IAAA,EAAC;IAEjClB,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADlOb,OAAM,MAAOyC,qBAAqB;EAavBC,KAAA;EACCC,GAAA;EAEAC,EAAA;EACAC,UAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,eAAA;EACAC,YAAA;EApBDZ,EAAE,GAAW,CAAC,CAAC,CAAC;EAChBa,OAAO,CAAM,CAAC;EACbC,SAAS,GAAsB,IAAIxD,YAAY,EAAE;EAC3DiC,WAAW,GAAqB,IAAI;EACpCwB,SAAS,GAAQ,EAAE;EACnB5B,QAAQ,GAAQ,EAAE;EAClBC,WAAW,GAAQ,EAAE;EACrB4B,SAAS,GAAY,KAAK;EAC1BxB,WAAW,GAAW,OAAO,CAAC,CAAC;EAC/BzB,WAAW;EACHkD,cAAc;EACtBC,YACSb,KAAqB,EACpBC,GAAsB,EAEtBC,EAAe,EACfC,UAAsB,EACtBC,eAAiC,EACjCC,wBAAkD,EAClDC,eAAgC;EAAE;EAClCC,YAAyB;IAR1B,KAAAP,KAAK,GAALA,KAAK;IACJ,KAAAC,GAAG,GAAHA,GAAG;IAEH,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,YAAY,GAAZA,YAAY;IAEpB;IACA,IAAI,CAACH,eAAe,CAACU,cAAc,CAACC,SAAS,CAAEC,OAAO,IAAI;MACxD,IAAI,CAACL,SAAS,GAAGK,OAAO;IAC1B,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACP,SAAS,GAAG,IAAI,CAACP,UAAU,CAACe,eAAe,EAAE;IAClD,IAAI,CAACC,QAAQ,EAAE;IAEf;IACA,IAAI,IAAI,CAACxB,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACgB,SAAS,GAAG,IAAI;MACrB,IAAI,CAACP,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,IAAI,CAAC;MAC9C;MACA,IAAI,CAACC,wBAAwB,EAAE;IACjC,CAAC,MAAM;MACL;MACA,IAAI,CAACC,sBAAsB,EAAE;IAC/B;EACF;EAEA;EACAA,sBAAsBA,CAAA;IACpB;IACA,IAAI,CAAClB,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACR,cAAc,GAAGW,UAAU,CAAC,MAAK;MACpCC,OAAO,CAACC,IAAI,CAAC,oEAAoE,CAAC;MAClF,IAAI,CAACrB,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACf,wBAAwB,CAACqB,SAAS,CAAC,oCAAoC,EAAE,EAAE,CAAC;IACnF,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;IAEX;IACA,MAAMC,kBAAkB,GAAG,IAAI,CAACpB,YAAY,CACzCqB,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDC,SAAS,EAAE;IAEd,MAAMC,kBAAkB,GAAG,IAAI,CAACxB,YAAY,CACzCqB,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDC,SAAS,EAAE;IAEdE,OAAO,CAACC,GAAG,CAAC,CAACN,kBAAkB,EAAEI,kBAAkB,CAAC,CAAC,CAClDG,IAAI,CAAC,CAAC,CAACC,gBAAgB,EAAEC,gBAAgB,CAAC,KAAI;MAC7C;MACA,IAAI,IAAI,CAACxB,cAAc,EAAE;QACvByB,YAAY,CAAC,IAAI,CAACzB,cAAc,CAAC;MACnC;MAEA;MACA,IAAI,CAAC9B,QAAQ,GAAGwD,KAAK,CAACC,OAAO,CAACJ,gBAAgB,EAAEK,YAAY,EAAEC,KAAK,CAAC,GAChEN,gBAAgB,CAACK,YAAY,CAACC,KAAK,GAAG,EAAE;MAC5C,IAAI,CAAC1D,WAAW,GAAGuD,KAAK,CAACC,OAAO,CAACH,gBAAgB,EAAEI,YAAY,EAAEC,KAAK,CAAC,GACnEL,gBAAgB,CAACI,YAAY,CAACC,KAAK,GAAG,EAAE;MAE5C;MACA,IAAI,CAACrC,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;IACjD,CAAC,CAAC,CACDsB,KAAK,CAACC,KAAK,IAAG;MACb;MACA,IAAI,IAAI,CAAC/B,cAAc,EAAE;QACvByB,YAAY,CAAC,IAAI,CAACzB,cAAc,CAAC;MACnC;MAEAY,OAAO,CAACmB,KAAK,CAAC,2CAA2C,EAAEA,KAAK,CAAC;MACjE,IAAI,CAAC7D,QAAQ,GAAG,EAAE;MAClB,IAAI,CAACC,WAAW,GAAG,EAAE;MACrB,IAAI,CAACsB,wBAAwB,CAACqB,SAAS,CAAC,8BAA8B,EAAE,EAAE,CAAC;MAC3E;MACA,IAAI,CAACtB,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;IACjD,CAAC,CAAC;EACN;EAEAD,QAAQA,CAAA;IACN,IAAI,CAACjC,WAAW,GAAG,IAAI,CAACgB,EAAE,CAAC0C,KAAK,CAAC;MAC/BlF,WAAW,EAAE,CAAC,EAAE,EAAER,UAAU,CAAC2F,QAAQ,CAAC;MACtCC,iBAAiB,EAAE,CAAC,EAAE,EAAE5F,UAAU,CAAC2F,QAAQ,CAAC;MAC5CE,SAAS,EAAE,CAAC,EAAE,CAAC;MACfC,OAAO,EAAE,CAAC,EAAE,CAAC;MAAE;MACfC,QAAQ,EAAE,CAAC,EAAE,EAAE/F,UAAU,CAAC2F,QAAQ,CAAC;MACnCK,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,OAAO,EAAE,CAAC,IAAI,EAAEjG,UAAU,CAAC2F,QAAQ,CAAC;MACpCO,UAAU,EAAE,CAAC,EAAE,CAAC,CAAE;KACnB,CAAC;IAEF;IACA,IAAI,CAACnD,GAAG,CAACoD,aAAa,EAAE;EAC1B;EACA;EACAC,mBAAmBA,CAAA;IACjB,IAAI,CAAC/C,YAAY,CACdqB,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDd,SAAS,CAAC;MACTK,IAAI,EAAGmC,mBAAwB,IAAI;QACjC,MAAMd,KAAK,GAAGc,mBAAmB,EAAEf,YAAY,EAAEC,KAAK;QACtD,IAAI,CAAC3D,QAAQ,GAAGwD,KAAK,CAACC,OAAO,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;MACnD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpBnB,OAAO,CAACmB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAAC7D,QAAQ,GAAG,EAAE;QAClB,IAAI,CAACuB,wBAAwB,CAACqB,SAAS,CAAC,kCAAkC,EAAE,EAAE,CAAC;MACjF;KACD,CAAC;EACN;EACA;EACA8B,oBAAoBA,CAAA;IAClB,IAAI,CAACjD,YAAY,CACdqB,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDd,SAAS,CAAC;MACTK,IAAI,EAAGqC,mBAAwB,IAAI;QACjC,MAAMhB,KAAK,GAAGgB,mBAAmB,EAAEjB,YAAY,EAAEC,KAAK;QACtD,IAAI,CAAC1D,WAAW,GAAGuD,KAAK,CAACC,OAAO,CAACE,KAAK,CAAC,GAAGA,KAAK,GAAG,EAAE;MACtD,CAAC;MACDE,KAAK,EAAGA,KAAU,IAAI;QACpBnB,OAAO,CAACmB,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAAC5D,WAAW,GAAG,EAAE;QACrB,IAAI,CAACsB,wBAAwB,CAACqB,SAAS,CAAC,kCAAkC,EAAE,EAAE,CAAC;MACjF;KACD,CAAC;EACN;EAEA;EACAL,wBAAwBA,CAAA;IACtB;IACA,MAAMM,kBAAkB,GAAG,IAAI,CAACpB,YAAY,CACzCqB,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDC,SAAS,EAAE;IAEd,MAAMC,kBAAkB,GAAG,IAAI,CAACxB,YAAY,CACzCqB,sBAAsB,CAAC;MAAEC,QAAQ,EAAE;IAAa,CAAE,CAAC,CACnDC,SAAS,EAAE;IAEdE,OAAO,CAACC,GAAG,CAAC,CAACN,kBAAkB,EAAEI,kBAAkB,CAAC,CAAC,CAClDG,IAAI,CAAC,CAAC,CAACC,gBAAgB,EAAEC,gBAAgB,CAAC,KAAI;MAC7C;MACA,IAAI,CAACtD,QAAQ,GAAGwD,KAAK,CAACC,OAAO,CAACJ,gBAAgB,EAAEK,YAAY,EAAEC,KAAK,CAAC,GAChEN,gBAAgB,CAACK,YAAY,CAACC,KAAK,GAAG,EAAE;MAC5C,IAAI,CAAC1D,WAAW,GAAGuD,KAAK,CAACC,OAAO,CAACH,gBAAgB,EAAEI,YAAY,EAAEC,KAAK,CAAC,GACnEL,gBAAgB,CAACI,YAAY,CAACC,KAAK,GAAG,EAAE;MAE5C;MACA,IAAI,CAACiB,SAAS,EAAE;IAClB,CAAC,CAAC,CACDhB,KAAK,CAACC,KAAK,IAAG;MACbnB,OAAO,CAACmB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD;MACA,IAAI,CAACe,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEAA,SAASA,CAAA;IACP;IACA,IAAI,CAACpD,eAAe,CACjBqD,UAAU,CAAC;MAAEC,SAAS,EAAE,IAAI,CAACjE,EAAE;MAAEkE,cAAc,EAAE,IAAI,CAACnD,SAAS,CAACoD;IAAM,CAAE,CAAC,CACzE/C,SAAS,CAAC;MACTK,IAAI,EAAG2C,eAAoB,IAAI;QAC7B,IAAI,CAAC3D,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC2C,eAAe,CAACC,OAAO,EAAE;UAC5B,IAAIC,WAAW,GAAGF,eAAe,CAACvB,YAAY,CAAC0B,OAAO;UACtD;UACA1C,OAAO,CAAC2C,GAAG,CAAC,wBAAwB,EAAEF,WAAW,CAAC;UAClDzC,OAAO,CAAC2C,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAACrF,QAAQ,CAAC;UACjD0C,OAAO,CAAC2C,GAAG,CAAC,yBAAyB,EAAE,IAAI,CAACpF,WAAW,CAAC;UACxD,IAAI,CAACrB,WAAW,GAAGuG,WAAW,CAACvG,WAAW;UAE1C;UACA,MAAM0G,kBAAkB,GAAIC,SAAc,IAAI;YAC5C,IAAI,CAACA,SAAS,EAAE,OAAO,EAAE;YACzB,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,SAAS,CAAC;YAChC,OAAOC,IAAI,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;UACzC,CAAC;UAED;UACA,MAAMC,YAAY,GAAIC,SAAc,IAAI;YACtC,IAAI,CAACA,SAAS,EAAE,OAAO,IAAI;YAC3B,OAAOC,QAAQ,CAACD,SAAS,CAAC;UAC5B,CAAC;UAED;UACA,MAAME,gBAAgB,GAAIC,qBAA6B,IAAI;YACzD,IAAI,CAACA,qBAAqB,EAAE,OAAO,EAAE;YACrC;YACA,MAAMC,OAAO,GAAGD,qBAAqB,CAACL,KAAK,CAAC,GAAG,CAAC,CAACO,GAAG,CAACC,IAAI,IAAIA,IAAI,CAACC,IAAI,EAAE,CAAC,CAACC,MAAM,CAACF,IAAI,IAAIA,IAAI,KAAK,EAAE,CAAC;YACrG,OAAOF,OAAO,CAACC,GAAG,CAACC,IAAI,IAAG;cACxB,MAAMG,EAAE,GAAG,IAAI,CAACrG,WAAW,CAACsG,IAAI,CAAEC,GAAQ,IAAKA,GAAG,CAACC,YAAY,KAAKN,IAAI,CAAC;cACzE,OAAOG,EAAE,GAAGA,EAAE,CAACtB,MAAM,GAAG,IAAI;YAC9B,CAAC,CAAC,CAACqB,MAAM,CAACxF,EAAE,IAAIA,EAAE,KAAK,IAAI,CAAC;UAC9B,CAAC;UAED,MAAM6F,QAAQ,GAAG;YACf9H,WAAW,EAAEuG,WAAW,CAACvG,WAAW;YACpCoF,iBAAiB,EAAEmB,WAAW,CAACwB,qBAAqB;YACpD1C,SAAS,EAAEqB,kBAAkB,CAACH,WAAW,CAACyB,gBAAgB,CAAC;YAC3D1C,OAAO,EAAEoB,kBAAkB,CAACH,WAAW,CAAC0B,cAAc,CAAC;YACvD1C,QAAQ,EAAEgB,WAAW,CAAC2B,eAAe;YACrC1C,kBAAkB,EAAEe,WAAW,CAACf,kBAAkB;YAClDC,OAAO,EAAEuB,YAAY,CAACT,WAAW,CAAC4B,sBAAsB,CAAC;YACzDzC,UAAU,EAAEyB,gBAAgB,CAACZ,WAAW,CAAC6B,eAAe;WACzD;UAEDtE,OAAO,CAAC2C,GAAG,CAAC,qBAAqB,EAAEqB,QAAQ,CAAC;UAC5C,IAAI,IAAI,CAACtG,WAAW,EAAE;YACpB,IAAI,CAACA,WAAW,CAAC6G,UAAU,CAACP,QAAQ,CAAC;UACvC;QACF,CAAC,MAAM;UACLhE,OAAO,CAACC,IAAI,CACV,kCAAkC,EAClCsC,eAAe,CAACvB,YAAY,CAC7B;QACH;MACF,CAAC;MACDG,KAAK,EAAGqD,GAAG,IAAI;QACb,IAAI,CAAC5F,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;QAC/CI,OAAO,CAACmB,KAAK,CAAC,iBAAiB,EAAEqD,GAAG,CAAC;MACvC;KACD,CAAC;EACN;EACAvG,IAAIA,CAAA;IACF,IAAI,CAAC,IAAI,CAACP,WAAW,EAAE;MACrB,IAAI,CAACmB,wBAAwB,CAACqB,SAAS,CACrC,4CAA4C,EAC5C,EAAE,CACH;MACD;IACF;IAEA,IAAIuE,QAAQ,GAAG,IAAI,CAAC/G,WAAY,CAAC+G,QAAQ;IACzC,IAAI,IAAI,CAAC/G,WAAY,CAACQ,OAAO,EAAE;MAC7BwG,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,OAAO,CAAEC,WAAW,IACxCJ,QAAQ,CAACI,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAACjG,wBAAwB,CAACqB,SAAS,CACrC,iCAAiC,EACjC,EAAE,CACH;MACD;IACF;IACA,IAAIuC,WAAW,GAAQ,IAAI,CAACsC,kBAAkB,EAAE;IAChD/E,OAAO,CAAC2C,GAAG,CAAC,WAAW,EAAEF,WAAW,CAAC;IACrC,IAAI,IAAI,CAACtE,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC6G,MAAM,CAACvC,WAAW,CAAC;IAC1B,CAAC,MAAM;MACL,IAAI,CAACwC,IAAI,CAACxC,WAAW,CAAC;IACxB;EACF;EAEAsC,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACrH,WAAW,EAAE;MACrB,MAAM,IAAIwH,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMlB,QAAQ,GAAG,IAAI,CAACtG,WAAY,CAACyH,KAAK;IACxC,IAAIC,kBAAkB,GAAQ,EAAE;IAChCA,kBAAkB,CAAClJ,WAAW,GAAG8H,QAAQ,CAAC9H,WAAW;IACrDkJ,kBAAkB,CAACnB,qBAAqB,GAAGD,QAAQ,CAAC1C,iBAAiB;IACrE8D,kBAAkB,CAAClB,gBAAgB,GAAGF,QAAQ,CAACzC,SAAS;IACxD6D,kBAAkB,CAACjB,cAAc,GAAGH,QAAQ,CAACxC,OAAO;IACpD4D,kBAAkB,CAAChB,eAAe,GAAGJ,QAAQ,CAACvC,QAAQ;IACtD2D,kBAAkB,CAAC1D,kBAAkB,GAAGsC,QAAQ,CAACtC,kBAAkB;IAEnE;IACA0D,kBAAkB,CAACf,sBAAsB,GAAGL,QAAQ,CAACrC,OAAO;IAE5D;IACA,MAAM0D,gBAAgB,GAAIC,OAAiB,IAAI;MAC7C,IAAI,CAACA,OAAO,IAAI,CAACxE,KAAK,CAACC,OAAO,CAACuE,OAAO,CAAC,EAAE,OAAO,EAAE;MAClD,OAAOA,OAAO,CAAC3B,MAAM,CAACxF,EAAE,IAAIA,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAKoH,SAAS,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC;IACxE,CAAC;IAEDJ,kBAAkB,CAACxD,UAAU,GAAGyD,gBAAgB,CAACrB,QAAQ,CAACpC,UAAU,CAAC;IACrEwD,kBAAkB,CAAC/C,cAAc,GAAG,IAAI,CAACnD,SAAS,CAACoD,MAAM;IAEzDtC,OAAO,CAAC2C,GAAG,CAAC,oCAAoC,EAAEyC,kBAAkB,CAAC;IACrE,OAAOA,kBAAkB;EAC3B;EACA;EACAH,IAAIA,CAACxC,WAAgB;IACnBA,WAAW,CAACL,SAAS,GAAG,IAAI,CAACjE,EAAE;IAC/B,IAAI,CAACS,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACd,eAAe,CAAC2G,aAAa,CAAChD,WAAW,CAAC,CAAClD,SAAS,CAAC;MACxDK,IAAI,EAAG8F,GAAG,IAAI;QACZ,IAAI,CAAC9G,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC8F,GAAG,CAAClD,OAAO,EAAE;UAChB,IAAI,CAAC3D,wBAAwB,CAAC8G,WAAW,CAACD,GAAG,CAAC1E,YAAY,CAAC4E,OAAO,EAAE,EAAE,CAAC;UACvE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,IAAI,CAAC;UACzB,IAAI,CAACrH,KAAK,CAACsH,KAAK,EAAE;QACpB,CAAC,MAAM;UACL,IAAI,CAACjH,wBAAwB,CAACqB,SAAS,CAACwF,GAAG,CAAC1E,YAAY,CAAC4E,OAAO,EAAE,EAAE,CAAC;UACrE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,KAAK,CAAC;QAC5B;MACF,CAAC;MACD1E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvC,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;QAC/CI,OAAO,CAACmB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACtC,wBAAwB,CAACqB,SAAS,CAAC,6CAA6C,EAAE,EAAE,CAAC;QAC1F,IAAI,CAACjB,SAAS,CAAC4G,IAAI,CAAC,KAAK,CAAC;MAC5B;KACD,CAAC;EACJ;EACA;EACAb,MAAMA,CAACvC,WAAgB;IACrB,IAAI,CAAC7D,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACd,eAAe,CAACiH,aAAa,CAACtD,WAAW,CAAC,CAAClD,SAAS,CAAC;MACxDK,IAAI,EAAG8F,GAAQ,IAAI;QACjB,IAAI,CAAC9G,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAAC8F,GAAG,CAAClD,OAAO,EAAE;UAChB,IAAI,CAAC3D,wBAAwB,CAAC8G,WAAW,CAACD,GAAG,CAAC1E,YAAY,CAAC4E,OAAO,EAAE,EAAE,CAAC;UACvE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,IAAI,CAAC;UACzB,IAAI,CAACrH,KAAK,CAACsH,KAAK,EAAE;QACpB,CAAC,MAAM;UACL,IAAI,CAACjH,wBAAwB,CAACqB,SAAS,CAACwF,GAAG,CAAC1E,YAAY,CAAC4E,OAAO,EAAE,EAAE,CAAC;UACrE,IAAI,CAAC3G,SAAS,CAAC4G,IAAI,CAAC,KAAK,CAAC;QAC5B;MACF,CAAC;MACD1E,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACvC,eAAe,CAACU,cAAc,CAACM,IAAI,CAAC,KAAK,CAAC;QAC/CI,OAAO,CAACmB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACtC,wBAAwB,CAACqB,SAAS,CAAC,6CAA6C,EAAE,EAAE,CAAC;QAC1F,IAAI,CAACjB,SAAS,CAAC4G,IAAI,CAAC,KAAK,CAAC;MAC5B;KACD,CAAC;EACJ;EACAvH,OAAOA,CAAC0H,GAAQ,EAAEnJ,MAAW;IAC3B,IAAI,CAACc,WAAW,GAAGqI,GAAG;IACtB,IAAI,CAACvH,GAAG,CAACwH,YAAY,EAAE;EACzB;EACA/I,qBAAqBA,CAACgJ,KAAU,GAAG;EACnC7I,gBAAgBA,CAAC6I,KAAU,GAAG;EAC9BxJ,eAAeA,CAACyJ,UAAe,EAAEtB,WAA4B;IAC3D,IAAI,CAAC,IAAI,CAACnH,WAAW,EAAE;MACrB,OAAO,KAAK;IACd;IAEA,MAAM0I,OAAO,GAAG,IAAI,CAAC1I,WAAY,CAAC+G,QAAQ,CAACI,WAAW,CAAC;IACvD,IAAI,CAACuB,OAAO,EAAE;MACZ,OAAO,KAAK;IACd;IACA,IAAIC,MAAM,GACRD,OAAO,CAACE,QAAQ,CAACH,UAAU,CAAC,KAAKC,OAAO,CAACG,KAAK,IAAIH,OAAO,CAACI,OAAO,CAAC;IACpE,OAAOH,MAAM;EACf;EACEvI,eAAeA,CAAA;IACf;IACA;IACA;IACA;IACA,IAAI,CAACH,WAAW,GAAG,OAAO;IAC1B,IAAI,CAACc,GAAG,CAACwH,YAAY,EAAE;EACzB;EAEAQ,WAAWA,CAAA;IACT;IACA,IAAI,IAAI,CAACrH,cAAc,EAAE;MACvByB,YAAY,CAAC,IAAI,CAACzB,cAAc,CAAC;IACnC;EACF;;qCA3XWb,qBAAqB,EAAA5C,EAAA,CAAA+K,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjL,EAAA,CAAA+K,iBAAA,CAAA/K,EAAA,CAAAkL,iBAAA,GAAAlL,EAAA,CAAA+K,iBAAA,CAAAI,EAAA,CAAAC,WAAA,GAAApL,EAAA,CAAA+K,iBAAA,CAAAM,EAAA,CAAAC,UAAA,GAAAtL,EAAA,CAAA+K,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA,GAAAxL,EAAA,CAAA+K,iBAAA,CAAAU,EAAA,CAAAC,wBAAA,GAAA1L,EAAA,CAAA+K,iBAAA,CAAAY,EAAA,CAAAC,eAAA,GAAA5L,EAAA,CAAA+K,iBAAA,CAAAc,EAAA,CAAAC,WAAA;EAAA;;UAArBlJ,qBAAqB;IAAAmJ,SAAA;IAAAC,MAAA;MAAAxJ,EAAA;MAAAa,OAAA;IAAA;IAAA4I,OAAA;MAAA3I,SAAA;IAAA;IAAA4I,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCjB9BvM,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAAQ,uBAAA,GAAc;QAEZR,EADA,CAAAU,UAAA,IAAA+L,oCAAA,iBAAsB,IAAAC,oCAAA,iBACA;;QAE1B1M,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WAItB;QADCD,EAAA,CAAAgB,UAAA,mBAAA2L,kDAAA;UAAA,OAASH,GAAA,CAAA3J,KAAA,CAAA+J,OAAA,EAAe;QAAA,EAAC;QAG/B5M,EAFK,CAAAG,YAAA,EAAI,EACD,EACF;QAENH,EAAA,CAAAC,cAAA,aAGC;QAOCD,EAAA,CAAAU,UAAA,IAAAmM,oCAAA,iBAAuG;QAgB7F7M,EAPV,CAAAC,cAAA,cAAiB,cACQ,eACD,cAGjB,cACsB,aAMlB;QADCD,EAAA,CAAAgB,UAAA,mBAAA8L,mDAAA5L,MAAA;UAAA,OAASsL,GAAA,CAAA7J,OAAA,CAAQ,OAAO,EAAAzB,MAAA,CAAS;QAAA,EAAC;QAElClB,EAAA,CAAAE,MAAA,yBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAMlB;QADCD,EAAA,CAAAgB,UAAA,mBAAA+L,mDAAA7L,MAAA;UAAA,OAASsL,GAAA,CAAA7J,OAAA,CAAQ,MAAM,EAAAzB,MAAA,CAAS;QAAA,EAAC;QAEjClB,EAAA,CAAAE,MAAA,yBACF;QAKVF,EALU,CAAAG,YAAA,EAAI,EACD,EACF,EACD,EACF,EACF;QAENH,EAAA,CAAAU,UAAA,KAAAsM,sCAAA,mBAAkF;QA2IpFhN,EAAA,CAAAG,YAAA,EAAM;QAINH,EAFF,CAAAC,cAAA,eAAyD,WAElD;QACHD,EAAA,CAAAU,UAAA,KAAAuM,wCAAA,qBAKC;QAGHjN,EAAA,CAAAG,YAAA,EAAM;QAIJH,EADF,CAAAC,cAAA,WAAK,kBAIF;QADCD,EAAA,CAAAgB,UAAA,mBAAAkM,wDAAA;UAAA,OAASV,GAAA,CAAA3J,KAAA,CAAA+J,OAAA,EAAe;QAAA,EAAC;QAEzB5M,EAAA,CAAAE,MAAA,gBACF;QAAAF,EAAA,CAAAG,YAAA,EAAS;QACTH,EAAA,CAAAE,MAAA,gBACA;QASAF,EATA,CAAAU,UAAA,KAAAyM,wCAAA,qBAMC,KAAAC,wCAAA,qBAQA;QAMLpN,EAHE,CAAAG,YAAA,EAAM,EACF,EAEA;;;QArPQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAc,UAAA,SAAA0L,GAAA,CAAAhK,EAAA,OAAc;QACdxC,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAc,UAAA,SAAA0L,GAAA,CAAAhK,EAAA,OAAc;QAqBlBxC,EAAA,CAAAI,SAAA,GAAgC;QAAhCJ,EAAA,CAAAc,UAAA,UAAA0L,GAAA,CAAAzK,WAAA,KAAAyK,GAAA,CAAAhJ,SAAA,CAAgC;QAmB1BxD,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAqN,eAAA,IAAAC,GAAA,EAAAd,GAAA,CAAAxK,WAAA,cAA+C;QAU/ChC,EAAA,CAAAI,SAAA,GAA8C;QAA9CJ,EAAA,CAAAc,UAAA,YAAAd,EAAA,CAAAqN,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAAxK,WAAA,aAA8C;QAWKhC,EAAA,CAAAI,SAAA,GAAiB;QAAjBJ,EAAA,CAAAc,UAAA,SAAA0L,GAAA,CAAAzK,WAAA,CAAiB;QAiJ7E/B,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA0L,GAAA,CAAAxK,WAAA,YAA4B;QAmB5BhC,EAAA,CAAAI,SAAA,GAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA0L,GAAA,CAAAxK,WAAA,YAA4B;QAS5BhC,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAc,UAAA,SAAA0L,GAAA,CAAAxK,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
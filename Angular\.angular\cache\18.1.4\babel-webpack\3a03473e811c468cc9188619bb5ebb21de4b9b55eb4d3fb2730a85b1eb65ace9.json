{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29)(3, \"div\", 30)(4, \"label\");\n    i0.ɵɵtext(5, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 31);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 32)(9, \"label\");\n    i0.ɵɵtext(10, \"Project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 31);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 32)(14, \"label\");\n    i0.ɵɵtext(15, \"External project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 31);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 30)(19, \"label\");\n    i0.ɵɵtext(20, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 31);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"label\");\n    i0.ɵɵtext(25, \"Start date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 31);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 32)(30, \"label\");\n    i0.ɵɵtext(31, \"End date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 31);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectLocation || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.externalPMNames || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectDescription || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStartDate ? i0.ɵɵpipeBind2(28, 6, ctx_r1.project.projectStartDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectEndDate ? i0.ɵɵpipeBind2(34, 9, ctx_r1.project.projectEndDate, \"MM/dd/yyyy\") : \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_a_click_2_listener() {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r5.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"select\", 44);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_select_change_10_listener($event) {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r5, $event.target.value));\n    });\n    i0.ɵɵelementStart(11, \"option\", 45);\n    i0.ɵɵtext(12, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 46);\n    i0.ɵɵtext(14, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 47);\n    i0.ɵɵtext(16, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 48);\n    i0.ɵɵtext(18, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 49);\n    i0.ɵɵtext(20, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 50);\n    i0.ɵɵtext(22, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 51);\n    i0.ɵɵtext(24, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 52);\n    i0.ɵɵtext(26, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"td\")(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"td\", 53)(32, \"span\", 54);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitName || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitReviewType || \"\", \"\\n\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r5.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r5.permitAppliedDate ? i0.ɵɵpipeBind2(30, 8, permit_r5.permitAppliedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(permit_r5.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"table\", 39)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Permit #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Submitted Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 40);\n    i0.ɵɵtext(13, \"Ball in Court\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template, 34, 11, \"tr\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectViewComponent_div_2_ng_container_25_div_1_Template, 5, 0, \"div\", 33)(2, ProjectViewComponent_div_2_ng_container_25_div_2_Template, 16, 1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"ul\", 19)(15, \"li\", 20)(16, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(17, \" Project details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 20)(19, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"permits\", $event));\n    });\n    i0.ɵɵtext(20, \" Permits list \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 22);\n    i0.ɵɵtemplate(22, ProjectViewComponent_div_2_button_22_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 24);\n    i0.ɵɵtemplate(24, ProjectViewComponent_div_2_ng_container_24_Template, 35, 12, \"ng-container\", 25)(25, ProjectViewComponent_div_2_ng_container_25_Template, 3, 2, \"ng-container\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"Project # \", ctx_r1.project.internalProjectNumber || \"\", \" - \", ctx_r1.project.projectName || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r1.selectedTab === \"permits\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"details\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.project);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"permits\");\n  }\n}\nexport class ProjectViewComponent {\n  route;\n  router;\n  cdr;\n  appService;\n  projectsService;\n  permitsService;\n  modalService;\n  projectId = null;\n  project = null;\n  isLoading = false;\n  selectedTab = 'details';\n  projectPermits = [];\n  loginUser = {};\n  routeSubscription = new Subscription();\n  constructor(route, router, cdr, appService, projectsService, permitsService, modalService) {\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.modalService = modalService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n      console.log('Project view - received params:', {\n        projectId: this.projectId,\n        queryParams\n      });\n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n  fetchProjectDetails() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    this.projectsService.getProject({\n      projectId: this.projectId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchProjectPermits() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [{\n          field: 'projectId',\n          operator: 'eq',\n          value: this.projectId\n        }]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter(p => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    if (!this.projectId) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    // Subscribe to the modal event when it closes\n    modalRef.result.then(result => {\n      // Handle successful edit\n      if (result) {\n        console.log('Project edited successfully:', result);\n        // Refresh project details\n        this.fetchProjectDetails();\n      }\n    }, reason => {\n      // Handle modal dismissal\n      console.log('Modal dismissed:', reason);\n    });\n  }\n  viewPermit(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'project',\n        projectId: this.projectId\n      }\n    });\n  }\n  onStatusChange(permit, newStatus) {\n    if (!permit?.permitId || !newStatus) {\n      return;\n    }\n    const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n    if (!allowed.includes(newStatus)) {\n      return;\n    }\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.isLoading = true;\n    this.cdr.markForCheck();\n    this.permitsService.updatePermitInternalReviewStatus({\n      permitId: permit.permitId,\n      internalReviewStatus: newStatus\n    }).subscribe({\n      next: res => {\n        const isFault = res?.isFault || res?.responseData?.isFault;\n        if (isFault) {\n          permit.internalReviewStatus = previous;\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        }\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        permit.internalReviewStatus = previous;\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", \"status-active\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"mb-2\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", 2, \"margin-right\", \"16px\"], [\"type\", \"button\", \"class\", \"btn btn-link p-0\", \"title\", \"Edit Project\", 3, \"click\", 4, \"ngIf\"], [1, \"card-body\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", 2, \"font-size\", \"1.1rem\"], [1, \"project-details-content\"], [1, \"project-details-grid\"], [1, \"project-detail-item\", \"span-2\"], [1, \"project-value\"], [1, \"project-detail-item\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\"], [1, \"ball-in-court-col\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"badge\", \"badge-green-light\", \"ms-1\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"], [1, \"ball-in-court-cell\"], [1, \"wrap-text\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 26, 12, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.NgSelectOption, i7.ɵNgSelectMultipleOption, i6.DatePipe],\n    styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 903:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/@angular-devkit/build-angular/node_modules/sass-loader/dist/cjs.js):\\\\nunmatched \\\\\\\"}\\\\\\\".\\\\n    \\u2577\\\\n237 \\u2502 }\\\\r\\\\n    \\u2502 ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\modules\\\\\\\\projects\\\\\\\\project-view\\\\\\\\project-view.component.scss 237:1  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t})[_ngcontent-%COMP%];\\n\\n\\n\\n \\t\\n\\n \\t//[_ngcontent-%COMP%]   startup\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   Load[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   and[_ngcontent-%COMP%]   return[_ngcontent-%COMP%]   exports\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   This[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   doesn't[_ngcontent-%COMP%]   tell[_ngcontent-%COMP%]   about[_ngcontent-%COMP%]   it's[_ngcontent-%COMP%]   top-level[_ngcontent-%COMP%]   declarations[_ngcontent-%COMP%]   so[_ngcontent-%COMP%]   it[_ngcontent-%COMP%]   can't[_ngcontent-%COMP%]   be[_ngcontent-%COMP%]   inlined\\n\\n[_ngcontent-%COMP%]   var[_ngcontent-%COMP%]   __webpack_exports__[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] {};\\n\\n \\t__webpack_modules__[903]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "combineLatest", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProjectViewComponent_div_2_button_22_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "editProject", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵadvance", "ɵɵtextInterpolate", "project", "projectLocation", "internalProjectManagerName", "internalProjectManager", "externalPMNames", "projectDescription", "projectStartDate", "ɵɵpipeBind2", "projectEndDate", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_a_click_2_listener", "permit_r5", "_r4", "$implicit", "viewPermit", "permitId", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_select_change_10_listener", "$event", "onStatusChange", "target", "value", "ɵɵtextInterpolate1", "permitName", "permitReviewType", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "permitAppliedDate", "attentionReason", "ɵɵtemplate", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template", "projectPermits", "ProjectViewComponent_div_2_ng_container_25_div_1_Template", "ProjectViewComponent_div_2_ng_container_25_div_2_Template", "length", "ProjectViewComponent_div_2_Template_button_click_10_listener", "_r1", "goBack", "ProjectViewComponent_div_2_Template_a_click_16_listener", "showTab", "ProjectViewComponent_div_2_Template_a_click_19_listener", "ProjectViewComponent_div_2_button_22_Template", "ProjectViewComponent_div_2_ng_container_24_Template", "ProjectViewComponent_div_2_ng_container_25_Template", "ɵɵtextInterpolate2", "internalProjectNumber", "projectName", "projectStatus", "ɵɵpureFunction1", "_c0", "selectedTab", "ProjectViewComponent", "route", "router", "cdr", "appService", "projectsService", "permitsService", "modalService", "projectId", "loginUser", "routeSubscription", "constructor", "ngOnInit", "getLoggedInUser", "paramMap", "queryParams", "subscribe", "idParam", "get", "Number", "console", "log", "activeTab", "fetchProjectDetails", "fetchProjectPermits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "getProject", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "Project", "data", "Object", "keys", "error", "faultMessage", "err", "getPermitsForKendoGrid", "take", "skip", "sort", "filter", "logic", "filters", "field", "operator", "search", "loggedInUserId", "userId", "rawPermits", "p", "permitProjectId", "projectID", "project_id", "ProjectId", "ProjectID", "navigate", "NgbModalOptions", "size", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "id", "result", "then", "reason", "from", "permit", "newStatus", "allowed", "includes", "previous", "updatePermitInternalReviewStatus", "getStatusClass", "status", "toLowerCase", "replace", "tab", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "ChangeDetectorRef", "i2", "AppService", "i3", "ProjectsService", "i4", "PermitsService", "i5", "NgbModal", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription, combineLatest } from 'rxjs';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\r\n\r\n@Component({\r\n  selector: 'app-project-view',\r\n  templateUrl: './project-view.component.html',\r\n  styleUrls: ['./project-view.component.scss']\r\n})\r\nexport class ProjectViewComponent implements OnInit, OnDestroy {\r\n  public projectId: number | null = null;\r\n  public project: any = null;\r\n  public isLoading: boolean = false;\r\n  public selectedTab: string = 'details';\r\n  public projectPermits: any[] = [];\r\n  public loginUser: any = {};\r\n  private routeSubscription: Subscription = new Subscription();\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private appService: AppService,\r\n    private projectsService: ProjectsService,\r\n    private permitsService: PermitsService,\r\n    private modalService: NgbModal\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n\r\n    // Combine route params and query params to handle both together\r\n    this.routeSubscription = combineLatest([\r\n      this.route.paramMap,\r\n      this.route.queryParams\r\n    ]).subscribe(([paramMap, queryParams]) => {\r\n      const idParam = paramMap.get('id');\r\n      this.projectId = idParam ? Number(idParam) : null;\r\n\r\n      console.log('Project view - received params:', { projectId: this.projectId, queryParams });\r\n      \r\n      // Handle active tab from query params\r\n      const activeTab = queryParams['activeTab'];\r\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\r\n        this.selectedTab = activeTab;\r\n        console.log('Setting selectedTab from query params:', activeTab);\r\n      } else {\r\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\r\n      }\r\n\r\n      if (this.projectId) {\r\n        this.fetchProjectDetails();\r\n        this.fetchProjectPermits();\r\n      }\r\n      \r\n      this.cdr.markForCheck();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.routeSubscription) {\r\n      this.routeSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  public fetchProjectDetails(): void {\r\n    if (!this.projectId) { return; }\r\n    this.isLoading = true;\r\n    this.projectsService.getProject({ projectId: this.projectId }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Project API Response:', res);\r\n        if (!res?.isFault) {\r\n          // Try different response structures\r\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\r\n          console.log('Project data assigned:', this.project);\r\n          console.log('Project fields available:', Object.keys(this.project || {}));\r\n          // Don't override selectedTab here - let query params handle it\r\n        } else {\r\n          console.error('API returned fault:', res.faultMessage);\r\n          this.project = null;\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        this.isLoading = false;\r\n        console.error('Error fetching project details:', err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchProjectPermits(): void {\r\n    if (!this.projectId) { return; }\r\n    this.isLoading = true;\r\n    \r\n    // Get permits for this specific project\r\n    this.permitsService.getPermitsForKendoGrid({\r\n      take: 100,\r\n      skip: 0,\r\n      sort: [],\r\n      filter: {\r\n        logic: 'and',\r\n        filters: [\r\n          {\r\n            field: 'projectId',\r\n            operator: 'eq',\r\n            value: this.projectId\r\n          }\r\n        ]\r\n      },\r\n      search: '',\r\n      loggedInUserId: this.loginUser.userId\r\n    }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Project permits API response:', res);\r\n        if (res?.isFault) {\r\n          console.error('Failed to load project permits:', res.faultMessage);\r\n          this.projectPermits = [];\r\n        } else {\r\n          const rawPermits = res.responseData?.data || res.data || [];\r\n          // Client-side guard: ensure only permits for this project are shown\r\n          this.projectPermits = (rawPermits || []).filter((p: any) => {\r\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\r\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\r\n          });\r\n          console.log('Project permits assigned (filtered):', this.projectPermits);\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        console.error('Error loading project permits:', err);\r\n        this.projectPermits = [];\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public goBack(): void {\r\n    this.router.navigate(['/projects/list']);\r\n  }\r\n\r\n  public editProject(): void {\r\n    if (!this.projectId) { return; }\r\n    \r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      ProjectPopupComponent,\r\n      NgbModalOptions\r\n    );\r\n    \r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = this.projectId;\r\n    modalRef.componentInstance.project = this.project;\r\n    \r\n    // Subscribe to the modal event when it closes\r\n    modalRef.result.then(\r\n      (result) => {\r\n        // Handle successful edit\r\n        if (result) {\r\n          console.log('Project edited successfully:', result);\r\n          // Refresh project details\r\n          this.fetchProjectDetails();\r\n        }\r\n      },\r\n      (reason) => {\r\n        // Handle modal dismissal\r\n        console.log('Modal dismissed:', reason);\r\n      }\r\n    );\r\n  }\r\n\r\n  public viewPermit(permitId: number): void {\r\n    this.router.navigate(['/permits/view', permitId], { \r\n      queryParams: { from: 'project', projectId: this.projectId } \r\n    });\r\n  }\r\n\r\n  public onStatusChange(permit: any, newStatus: string): void {\r\n    if (!permit?.permitId || !newStatus) { return; }\r\n    const allowed = [\r\n      'Approved',\r\n      'Pacifica Verification',\r\n      'Dis-Approved',\r\n      'Pending',\r\n      'Not Required',\r\n      'In Review',\r\n      '1 Cycle Completed'\r\n    ];\r\n    if (!allowed.includes(newStatus)) { return; }\r\n\r\n    const previous = permit.internalReviewStatus;\r\n    permit.internalReviewStatus = newStatus;\r\n    this.isLoading = true;\r\n    this.cdr.markForCheck();\r\n\r\n    this.permitsService\r\n      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          const isFault = res?.isFault || res?.responseData?.isFault;\r\n          if (isFault) {\r\n            permit.internalReviewStatus = previous;\r\n            this.isLoading = false;\r\n            this.cdr.markForCheck();\r\n          }\r\n          this.isLoading = false;\r\n          this.cdr.markForCheck();\r\n        },\r\n        error: () => {\r\n          permit.internalReviewStatus = previous;\r\n          this.isLoading = false;\r\n          this.cdr.markForCheck();\r\n        }\r\n      });\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'status-n-a';\r\n    return (\r\n      'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-')\r\n    );\r\n  }\r\n\r\n  showTab(tab: string, $event: any) {\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"project-view-container\">\r\n  <!-- Project Details Card -->\r\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"project\">\r\n    <!-- Project Details Header -->\r\n    <div class=\"project-details-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-wrap\">\r\n          <div class=\"title-line\">\r\n            <span class=\"project-title\">Project # {{ project.internalProjectNumber || \"\" }} - {{ project.projectName || \"\" }}</span>\r\n            <span class=\"status-text status-active\">{{ project.projectStatus || \"Active\" }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"button-group\">\r\n          <!-- <button type=\"button\" class=\"btn portal-button\" (click)=\"editProject()\">\r\n            <i class=\"fa fa-pencil\"></i>Edit\r\n          </button> -->\r\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center mb-2\" (click)=\"goBack()\">\r\n            <i class=\"fas fa-arrow-left me-2\"></i>\r\n            Back\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n    <!-- Card Header with Tabs -->\r\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\r\n      <!-- Tabs -->\r\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\r\n            (click)=\"showTab('details', $event)\">\r\n            Project details\r\n          </a>\r\n        </li>\r\n        <li class=\"nav-item\">\r\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'permits' }\"\r\n            (click)=\"showTab('permits', $event)\">\r\n            Permits list\r\n          </a>\r\n        </li>\r\n      </ul>\r\n       <div class=\"d-flex align-items-center gap-2\" style=\"margin-right: 16px;\">\r\n        <!-- Edit icon - only show when permit details tab is active -->\r\n        <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"editProject()\" *ngIf=\"selectedTab === 'details'\" title=\"Edit Project\">\r\n          <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\r\n        </button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Card Body with Tab Content -->\r\n    <div class=\"card-body\">\r\n      <!-- Project Details Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'details' && project\">\r\n        <div class=\"project-details-content\">\r\n          <div class=\"project-details-grid\">\r\n            <div class=\"project-detail-item span-2\">\r\n           <label >Location</label>\r\n              <span class=\"project-value\">{{ project.projectLocation || \"\" }}</span>\r\n            </div>\r\n             <div class=\"project-detail-item\">\r\n               <label>Project manager</label>\r\n               <span class=\"project-value\">{{ project.internalProjectManagerName || project.internalProjectManager || \"\" }}</span>\r\n             </div>\r\n            <div class=\"project-detail-item\">\r\n              <label>External project manager</label>\r\n              <span class=\"project-value\">{{ project.externalPMNames || \"\" }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item span-2\">\r\n              <label>Description</label>\r\n              <span class=\"project-value\">{{ project.projectDescription || \"\" }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item\">\r\n              <label>Start date</label>\r\n              <span class=\"project-value\">{{\r\n                project.projectStartDate\r\n                ? (project.projectStartDate | date : \"MM/dd/yyyy\")\r\n                : \"\"\r\n              }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item\">\r\n              <label>End date</label>\r\n              <span class=\"project-value\">{{\r\n                project.projectEndDate\r\n                ? (project.projectEndDate | date : \"MM/dd/yyyy\")\r\n                : \"\"\r\n              }}</span>\r\n            </div>\r\n           </div>\r\n         </div>\r\n       </ng-container>\r\n\r\n      <!-- Permits List Tab Content -->\r\n      <ng-container *ngIf=\"selectedTab == 'permits'\">\r\n        <!-- Empty State for Permits -->\r\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"projectPermits.length === 0\">\r\n          <div class=\"text-center\">\r\n            <i class=\"fas fa-file-alt fa-3x mb-3\"></i>\r\n            <p>No permits found for this project.</p>\r\n          </div>\r\n        </div>\r\n\r\n        <!-- Permits Table -->\r\n        <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\r\n          <table class=\"table\">\r\n            <thead>\r\n              <tr>\r\n                <th>Permit Name</th>\r\n                <th>Permit #</th>\r\n                <th>Status</th>\r\n                <th>Submitted Date</th>\r\n                <th class=\"ball-in-court-col\">Ball in Court</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr *ngFor=\"let permit of projectPermits\">\r\n                <td>\r\n                  <a \r\n                    class=\"fw-bold\" \r\n                    (click)=\"viewPermit(permit.permitId)\" \r\n                    title=\"View Permit\"\r\n                    aria-label=\"View Permit\"\r\n                  >\r\n                    {{ permit.permitName || \"\" }}  \r\n                  </a>\r\n              <span class=\"badge badge-green-light ms-1\">\r\n  {{ permit.permitReviewType || \"\" }}\r\n</span>\r\n                </td>\r\n                <td>\r\n                  <span>{{ permit.permitNumber || \"\" }}</span>\r\n                </td>\r\n                <td>\r\n                  <select class=\"form-select form-select-sm w-auto\"\r\n                          [value]=\"permit.internalReviewStatus || ''\"\r\n                          (change)=\"onStatusChange(permit, $any($event.target).value)\"\r\n                          [disabled]=\"isLoading\">\r\n                    <option [value]=\"''\" disabled>Select status</option>\r\n                    <option value=\"Approved\">Approved</option>\r\n                    <option value=\"Pacifica Verification\">Pacifica Verification</option>\r\n                    <option value=\"Dis-Approved\">Dis-Approved</option>\r\n                    <option value=\"Pending\">Pending</option>\r\n                    <option value=\"Not Required\">Not Required</option>\r\n                    <option value=\"In Review\">In Review</option>\r\n                    <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\r\n                  </select>\r\n                </td>\r\n                <td>\r\n                  <span>{{ permit.permitAppliedDate ? (permit.permitAppliedDate | date : 'MM/dd/yyyy') : '' }}</span>\r\n                </td>\r\n                <td class=\"ball-in-court-cell\">\r\n                  <span class=\"wrap-text\">{{ permit.attentionReason || '' }}</span>\r\n                </td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </ng-container>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,aAAa,QAAQ,MAAM;AAKlD,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;;;;ICH1EC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA4CEH,EAAA,CAAAC,cAAA,iBAA8H;IAA/ED,EAAA,CAAAI,UAAA,mBAAAC,sEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpEX,EAAA,CAAAY,SAAA,YAAmE;IACrEZ,EAAA,CAAAG,YAAA,EAAS;;;;;IAOXH,EAAA,CAAAa,uBAAA,GAA0D;IAIrDb,EAHH,CAAAC,cAAA,cAAqC,cACD,cACQ,YACjC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEHH,EADF,CAAAC,cAAA,cAAiC,YACxB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgF;IAC9GF,EAD8G,CAAAG,YAAA,EAAO,EAC/G;IAELH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAwC,aAC/B;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IAGPF,EAHO,CAAAG,YAAA,EAAO,EACL,EACD,EACF;;;;;IA/B2BH,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAC,eAAA,OAAmC;IAIlCjB,EAAA,CAAAc,SAAA,GAAgF;IAAhFd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAE,0BAAA,IAAAV,MAAA,CAAAQ,OAAA,CAAAG,sBAAA,OAAgF;IAIjFnB,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAI,eAAA,OAAmC;IAInCpB,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAK,kBAAA,OAAsC;IAItCrB,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAM,gBAAA,GAAAtB,EAAA,CAAAuB,WAAA,QAAAf,MAAA,CAAAQ,OAAA,CAAAM,gBAAA,qBAI1B;IAI0BtB,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAQ,cAAA,GAAAxB,EAAA,CAAAuB,WAAA,QAAAf,MAAA,CAAAQ,OAAA,CAAAQ,cAAA,qBAI1B;;;;;IAUNxB,EADF,CAAAC,cAAA,cAAkH,cACvF;IACvBD,EAAA,CAAAY,SAAA,YAA0C;IAC1CZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACrC,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAI,UAAA,mBAAAqB,mFAAA;MAAA,MAAAC,SAAA,GAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqB,UAAA,CAAAH,SAAA,CAAAI,QAAA,CAA2B;IAAA,EAAC;IAIrC9B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACRH,EAAA,CAAAC,cAAA,eAA2C;IACvDD,EAAA,CAAAE,MAAA,GACF;IACgBF,EADhB,CAAAG,YAAA,EAAO,EACc;IAEHH,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACzC;IAEHH,EADF,CAAAC,cAAA,SAAI,kBAI6B;IADvBD,EAAA,CAAAI,UAAA,oBAAA2B,0FAAAC,MAAA;MAAA,MAAAN,SAAA,GAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAyB,cAAA,CAAAP,SAAA,EAAAM,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElEnC,EAAA,CAAAC,cAAA,kBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAAsF;;IAC9FF,EAD8F,CAAAG,YAAA,EAAO,EAChG;IAEHH,EADF,CAAAC,cAAA,cAA+B,gBACL;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE9DF,EAF8D,CAAAG,YAAA,EAAO,EAC9D,EACF;;;;;IA9BCH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAoC,kBAAA,MAAAV,SAAA,CAAAW,UAAA,YACF;IAEhBrC,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAoC,kBAAA,MAAAV,SAAA,CAAAY,gBAAA,aACF;IAGwBtC,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAa,YAAA,OAA+B;IAI7BvC,EAAA,CAAAc,SAAA,GAA2C;IAE3Cd,EAFA,CAAAwC,UAAA,UAAAd,SAAA,CAAAe,oBAAA,OAA2C,aAAAjC,MAAA,CAAAkC,SAAA,CAErB;IACpB1C,EAAA,CAAAc,SAAA,EAAY;IAAZd,EAAA,CAAAwC,UAAA,aAAY;IAWhBxC,EAAA,CAAAc,SAAA,IAAsF;IAAtFd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAiB,iBAAA,GAAA3C,EAAA,CAAAuB,WAAA,QAAAG,SAAA,CAAAiB,iBAAA,qBAAsF;IAGpE3C,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAkB,eAAA,OAAkC;;;;;IA5C5D5C,EAJR,CAAAC,cAAA,cAAgE,gBACzC,YACZ,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAK,EAC7C,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA6C,UAAA,KAAAC,+DAAA,mBAA0C;IAyChD9C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAzCuBH,EAAA,CAAAc,SAAA,IAAiB;IAAjBd,EAAA,CAAAwC,UAAA,YAAAhC,MAAA,CAAAuC,cAAA,CAAiB;;;;;IAtBhD/C,EAAA,CAAAa,uBAAA,GAA+C;IAU7Cb,EARA,CAAA6C,UAAA,IAAAG,yDAAA,kBAAkH,IAAAC,yDAAA,mBAQlD;;;;;IARejD,EAAA,CAAAc,SAAA,EAAiC;IAAjCd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAAuC,cAAA,CAAAG,MAAA,OAAiC;IAQjFlD,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAAuC,cAAA,CAAAG,MAAA,KAA+B;;;;;;IA7F1DlD,EANV,CAAAC,cAAA,aAAsD,aAEhB,cACN,cACF,cACE,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxHH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAEnFF,EAFmF,CAAAG,YAAA,EAAO,EAClF,EACF;IAKJH,EAJF,CAAAC,cAAA,cAA0B,kBAIqF;IAAnBD,EAAA,CAAAI,UAAA,mBAAA+C,6DAAA;MAAAnD,EAAA,CAAAM,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6C,MAAA,EAAQ;IAAA,EAAC;IAC1GrD,EAAA,CAAAY,SAAA,aAAsC;IACtCZ,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAkD,wDAAAtB,MAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+C,OAAA,CAAQ,SAAS,EAAAvB,MAAA,CAAS;IAAA,EAAC;IACpChC,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,cAAqB,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAoD,wDAAAxB,MAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+C,OAAA,CAAQ,SAAS,EAAAvB,MAAA,CAAS;IAAA,EAAC;IACpChC,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACD,EACF;IACJH,EAAA,CAAAC,cAAA,eAAyE;IAExED,EAAA,CAAA6C,UAAA,KAAAY,6CAAA,qBAA8H;IAIlIzD,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IA0CrBD,EAxCA,CAAA6C,UAAA,KAAAa,mDAAA,6BAA0D,KAAAC,mDAAA,2BAwCX;IAkEnD3D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IArJgCH,EAAA,CAAAc,SAAA,GAAqF;IAArFd,EAAA,CAAA4D,kBAAA,eAAApD,MAAA,CAAAQ,OAAA,CAAA6C,qBAAA,eAAArD,MAAA,CAAAQ,OAAA,CAAA8C,WAAA,WAAqF;IACzE9D,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAA+C,aAAA,aAAuC;IAmBrB/D,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAAgE,eAAA,IAAAC,GAAA,EAAAzD,MAAA,CAAA0D,WAAA,gBAAiD;IAMjDlE,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAAgE,eAAA,KAAAC,GAAA,EAAAzD,MAAA,CAAA0D,WAAA,gBAAiD;IAQvClE,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAA0D,WAAA,eAA+B;IAS1FlE,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAA0D,WAAA,iBAAA1D,MAAA,CAAAQ,OAAA,CAAyC;IAwCzChB,EAAA,CAAAc,SAAA,EAA8B;IAA9Bd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAA0D,WAAA,cAA8B;;;ADvFnD,OAAM,MAAOC,oBAAoB;EAUrBC,KAAA;EACAC,MAAA;EACAC,GAAA;EACAC,UAAA;EACAC,eAAA;EACAC,cAAA;EACAC,YAAA;EAfHC,SAAS,GAAkB,IAAI;EAC/B3D,OAAO,GAAQ,IAAI;EACnB0B,SAAS,GAAY,KAAK;EAC1BwB,WAAW,GAAW,SAAS;EAC/BnB,cAAc,GAAU,EAAE;EAC1B6B,SAAS,GAAQ,EAAE;EAClBC,iBAAiB,GAAiB,IAAIhF,YAAY,EAAE;EAE5DiF,YACUV,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,cAA8B,EAC9BC,YAAsB;IANtB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;EACnB;EAEHK,QAAQA,CAAA;IACN,IAAI,CAACH,SAAS,GAAG,IAAI,CAACL,UAAU,CAACS,eAAe,EAAE;IAElD;IACA,IAAI,CAACH,iBAAiB,GAAG/E,aAAa,CAAC,CACrC,IAAI,CAACsE,KAAK,CAACa,QAAQ,EACnB,IAAI,CAACb,KAAK,CAACc,WAAW,CACvB,CAAC,CAACC,SAAS,CAAC,CAAC,CAACF,QAAQ,EAAEC,WAAW,CAAC,KAAI;MACvC,MAAME,OAAO,GAAGH,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAACV,SAAS,GAAGS,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI;MAEjDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAAEb,SAAS,EAAE,IAAI,CAACA,SAAS;QAAEO;MAAW,CAAE,CAAC;MAE1F;MACA,MAAMO,SAAS,GAAGP,WAAW,CAAC,WAAW,CAAC;MAC1C,IAAIO,SAAS,KAAKA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,SAAS,CAAC,EAAE;QACrE,IAAI,CAACvB,WAAW,GAAGuB,SAAS;QAC5BF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,SAAS,CAAC;MAClE,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACtB,WAAW,CAAC;MAC7E;MAEA,IAAI,IAAI,CAACS,SAAS,EAAE;QAClB,IAAI,CAACe,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;MAEA,IAAI,CAACrB,GAAG,CAACsB,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACiB,WAAW,EAAE;IACtC;EACF;EAEOJ,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACjC,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC8B,eAAe,CAACuB,UAAU,CAAC;MAAEpB,SAAS,EAAE,IAAI,CAACA;IAAS,CAAE,CAAC,CAACQ,SAAS,CAAC;MACvEa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACvD,SAAS,GAAG,KAAK;QACtB6C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,GAAG,CAAC;QACzC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB;UACA,IAAI,CAAClF,OAAO,GAAGiF,GAAG,CAACE,YAAY,EAAEC,OAAO,IAAIH,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACE,YAAY,IAAI,IAAI;UAC9FZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAACxE,OAAO,CAAC;UACnDuE,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEc,MAAM,CAACC,IAAI,CAAC,IAAI,CAACvF,OAAO,IAAI,EAAE,CAAC,CAAC;UACzE;QACF,CAAC,MAAM;UACLuE,OAAO,CAACiB,KAAK,CAAC,qBAAqB,EAAEP,GAAG,CAACQ,YAAY,CAAC;UACtD,IAAI,CAACzF,OAAO,GAAG,IAAI;QACrB;QACA,IAAI,CAACsD,GAAG,CAACsB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAG,IAAI;QACb,IAAI,CAAChE,SAAS,GAAG,KAAK;QACtB6C,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEE,GAAG,CAAC;QACrD,IAAI,CAACpC,GAAG,CAACsB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOD,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACjC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAAC+B,cAAc,CAACkC,sBAAsB,CAAC;MACzCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;QACNC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,IAAI;UACdhF,KAAK,EAAE,IAAI,CAACwC;SACb;OAEJ;MACDyC,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,IAAI,CAACzC,SAAS,CAAC0C;KAChC,CAAC,CAACnC,SAAS,CAAC;MACXa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACvD,SAAS,GAAG,KAAK;QACtB6C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,GAAG,CAAC;QACjD,IAAIA,GAAG,EAAEC,OAAO,EAAE;UAChBX,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEP,GAAG,CAACQ,YAAY,CAAC;UAClE,IAAI,CAAC1D,cAAc,GAAG,EAAE;QAC1B,CAAC,MAAM;UACL,MAAMwE,UAAU,GAAGtB,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACI,IAAI,IAAI,EAAE;UAC3D;UACA,IAAI,CAACtD,cAAc,GAAG,CAACwE,UAAU,IAAI,EAAE,EAAER,MAAM,CAAES,CAAM,IAAI;YACzD,MAAMC,eAAe,GAAGD,CAAC,EAAE7C,SAAS,IAAI6C,CAAC,EAAEE,SAAS,IAAIF,CAAC,EAAEG,UAAU,IAAIH,CAAC,EAAEI,SAAS,IAAIJ,CAAC,EAAEK,SAAS;YACrG,OAAO,IAAI,CAAClD,SAAS,IAAI,IAAI,GAAGW,MAAM,CAACmC,eAAe,CAAC,KAAKnC,MAAM,CAAC,IAAI,CAACX,SAAS,CAAC,GAAG,IAAI;UAC3F,CAAC,CAAC;UACFY,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACzC,cAAc,CAAC;QAC1E;QACA,IAAI,CAACuB,GAAG,CAACsB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAQ,IAAI;QAClB,IAAI,CAAChE,SAAS,GAAG,KAAK;QACtB6C,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEE,GAAG,CAAC;QACpD,IAAI,CAAC3D,cAAc,GAAG,EAAE;QACxB,IAAI,CAACuB,GAAG,CAACsB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOvC,MAAMA,CAAA;IACX,IAAI,CAACgB,MAAM,CAACyD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEOnH,WAAWA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACgE,SAAS,EAAE;MAAE;IAAQ;IAE/B,MAAMoD,eAAe,GAKjB;MACFC,IAAI,EAAE,IAAI;MAAE;MACZC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC1D,YAAY,CAAC2D,IAAI,CACrCtI,qBAAqB,EACrBgI,eAAe,CAChB;IAED;IACAK,QAAQ,CAACE,iBAAiB,CAACC,EAAE,GAAG,IAAI,CAAC5D,SAAS;IAC9CyD,QAAQ,CAACE,iBAAiB,CAACtH,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACAoH,QAAQ,CAACI,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAIA,MAAM,EAAE;QACVjD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgD,MAAM,CAAC;QACnD;QACA,IAAI,CAAC9C,mBAAmB,EAAE;MAC5B;IACF,CAAC,EACAgD,MAAM,IAAI;MACT;MACAnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,MAAM,CAAC;IACzC,CAAC,CACF;EACH;EAEO7G,UAAUA,CAACC,QAAgB;IAChC,IAAI,CAACuC,MAAM,CAACyD,QAAQ,CAAC,CAAC,eAAe,EAAEhG,QAAQ,CAAC,EAAE;MAChDoD,WAAW,EAAE;QAAEyD,IAAI,EAAE,SAAS;QAAEhE,SAAS,EAAE,IAAI,CAACA;MAAS;KAC1D,CAAC;EACJ;EAEO1C,cAAcA,CAAC2G,MAAW,EAAEC,SAAiB;IAClD,IAAI,CAACD,MAAM,EAAE9G,QAAQ,IAAI,CAAC+G,SAAS,EAAE;MAAE;IAAQ;IAC/C,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,uBAAuB,EACvB,cAAc,EACd,SAAS,EACT,cAAc,EACd,WAAW,EACX,mBAAmB,CACpB;IACD,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAAE;IAAQ;IAE5C,MAAMG,QAAQ,GAAGJ,MAAM,CAACnG,oBAAoB;IAC5CmG,MAAM,CAACnG,oBAAoB,GAAGoG,SAAS;IACvC,IAAI,CAACnG,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC4B,GAAG,CAACsB,YAAY,EAAE;IAEvB,IAAI,CAACnB,cAAc,CAChBwE,gCAAgC,CAAC;MAAEnH,QAAQ,EAAE8G,MAAM,CAAC9G,QAAQ;MAAEW,oBAAoB,EAAEoG;IAAS,CAAE,CAAC,CAChG1D,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAMC,OAAO,GAAGD,GAAG,EAAEC,OAAO,IAAID,GAAG,EAAEE,YAAY,EAAED,OAAO;QAC1D,IAAIA,OAAO,EAAE;UACX0C,MAAM,CAACnG,oBAAoB,GAAGuG,QAAQ;UACtC,IAAI,CAACtG,SAAS,GAAG,KAAK;UACtB,IAAI,CAAC4B,GAAG,CAACsB,YAAY,EAAE;QACzB;QACA,IAAI,CAAClD,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC4B,GAAG,CAACsB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAEA,CAAA,KAAK;QACVoC,MAAM,CAACnG,oBAAoB,GAAGuG,QAAQ;QACtC,IAAI,CAACtG,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC4B,GAAG,CAACsB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEOsD,cAAcA,CAACC,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,OACE,SAAS,GAAGA,MAAM,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE7E;EAEA9F,OAAOA,CAAC+F,GAAW,EAAEtH,MAAW;IAC9B,IAAI,CAACkC,WAAW,GAAGoF,GAAG;IACtB,IAAI,CAAChF,GAAG,CAACsB,YAAY,EAAE;EACzB;;qCAxOWzB,oBAAoB,EAAAnE,EAAA,CAAAuJ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzJ,EAAA,CAAAuJ,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1J,EAAA,CAAAuJ,iBAAA,CAAAvJ,EAAA,CAAA2J,iBAAA,GAAA3J,EAAA,CAAAuJ,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA7J,EAAA,CAAAuJ,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA/J,EAAA,CAAAuJ,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAAjK,EAAA,CAAAuJ,iBAAA,CAAAW,EAAA,CAAAC,QAAA;EAAA;;UAApBhG,oBAAoB;IAAAiG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbjC1K,EAAA,CAAA6C,UAAA,IAAA+H,mCAAA,iBAA0D;QAS1D5K,EAAA,CAAAC,cAAA,aAAoC;QAElCD,EAAA,CAAA6C,UAAA,IAAAgI,mCAAA,mBAAsD;QA4JxD7K,EAAA,CAAAG,YAAA,EAAM;;;QAvKAH,EAAA,CAAAwC,UAAA,SAAAmI,GAAA,CAAAjI,SAAA,CAAe;QAWoB1C,EAAA,CAAAc,SAAA,GAAa;QAAbd,EAAA,CAAAwC,UAAA,SAAAmI,GAAA,CAAA3J,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
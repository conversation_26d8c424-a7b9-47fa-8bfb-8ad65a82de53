import { ChangeDetectorRef, Component, Input } from '@angular/core';
import { FormGroup, FormBuilder } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { AppService } from '../../services/app.service';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
@Component({
  selector: 'app-review-details-modal',
  templateUrl: './review-details-modal.component.html',
  styleUrls: ['./review-details-modal.component.scss']
})
export class ReviewDetailsModalComponent {
  @Input() review: any;
  @Input() permitId: number | null = null;
  @Input() permitDetails: any = {};
  selectedTab:any ='corrections';
  reviewForm!: FormGroup;
  isLoading: boolean = false;
  loginUser:any
  constructor(public modal: NgbActiveModal,private customLayoutUtilsService: CustomLayoutUtilsService,public appService: AppService, private fb: FormBuilder, private cdr:ChangeDetectorRef, private permitsService: PermitsService) {}
  ngOnInit(): void {
    this.loginUser =this.appService.getLoggedInUser()
    this.reviewForm = this.fb.group({
      // cityComments: [this.reviewData?.comments || ''],
      EORAOROwner_Response: [this.review?.EORAOROwner_Response || ''],
      commentResponsedBy: [this.review?.commentResponsedBy || '']
    });
  }
  showTab(tab: any, $event: any) {
    this.selectedTab = tab;
    this.cdr.markForCheck();
  }

  onSubmit(): void {
    this.isLoading = true;
    const formData = {
      // cityComments:this.reviewForm.controls.cityComments.value,
      EORAOROwner_Response:this.reviewForm.controls.EORAOROwner_Response.value,
      commentResponsedBy:this.reviewForm.controls.commentResponsedBy.value,
      permitId: this.permitId,
      commentsId:this.review.commentsId,
      loggedInUserId: this.loginUser.userId
    };
      this.permitsService.updateExternalReview(formData).subscribe({
        next: (res: any) => {
          this.isLoading = false;
          if (res?.isFault === false) {
            //alert(res.responseData.message);
                                    this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');

            this.modal.close('updated');

          } else {
                      this.customLayoutUtilsService.showError(res.responseData.message ||'Failed to update external review', '');

            //alert(res.responseData.message || 'Failed to update external review');
          }
        },
        error: (err: any) => {
          this.isLoading = false;
                    this.customLayoutUtilsService.showError('Error updating external review', '');

          //alert('Error updating external review');
          console.error(err);
        }
      });


}
getPdf(){
  const permitNumber = this.permitDetails?.permitNumber || '';
  const projectName = this.permitDetails?.projectName || '';
  const applicantName = this.permitDetails?.applicantName || '';
  const reviewer = (this.review?.AssignedTo || this.review?.municipalityReviewer || '').toString();
  const cityComments = (this.review?.Comments || (this.review?.cityComments ?? '')).toString();
  const ownerResponse = (this.reviewForm?.value?.EORAOROwner_Response ?? this.review?.EORAOROwner_Response ?? '').toString();
  const respondedBy = (this.reviewForm?.value?.commentResponsedBy ?? this.review?.commentResponsedBy ?? '').toString();
  const cycle = (this.review?.cycle || this.review?.Cycle || '').toString();
  const status = (this.review?.StatusName || this.review?.commentstatus || this.review?.status || '').toString();
  const reviewCategory = (this.review?.TypeName || this.review?.reviewCategory || '').toString();
  const dueDate = this.review?.DueDate ? new Date(this.review.DueDate).toLocaleDateString() : '';
  const completedDate = this.review?.CompletedDate ? new Date(this.review.CompletedDate).toLocaleDateString() : '';
  const createdDate = this.review?.createdDate ? new Date(this.review.createdDate).toLocaleDateString() : '';

  const doc = new jsPDF({ orientation: 'portrait', unit: 'pt', format: 'a4' });

  // Page metrics and margins
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();
  const margin = { left: 50, right: 50, top: 60, bottom: 60 };

  let currentY = margin.top;

  // Professional Header with Company Branding
  const drawHeader = () => {
    // Company Logo/Title Area
    doc.setFillColor(41, 128, 185); // Professional blue
    doc.rect(0, 0, pageWidth, 50, 'F');
    
    doc.setTextColor(255, 255, 255);
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(18);
    doc.text('PERMIT TRACKER SYSTEM', margin.left, 25);
    
    doc.setFontSize(12);
    doc.text('Review Report', pageWidth - margin.right - 80, 25);
    
    // Reset text color
    doc.setTextColor(0, 0, 0);
    
    // Permit Information Header
    currentY = 70;
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(14);
    doc.text('REVIEW DETAILS', margin.left, currentY);
    
    currentY += 20;
  };

  drawHeader();

  // Permit Information Section
  const drawPermitInfo = () => {
    const infoData = [
      ['Permit #:', permitNumber || ''],
      ['Project Name:', projectName || ''],
      ['Applicant Name:', applicantName || ''],
      ['Review Category:', reviewCategory || ''],
      ['Reviewer:', reviewer || ''],
      ['Status:', status || ''],
      ['Due Date:', dueDate || ''],
      ['Completed Date:', completedDate || ''],
      ['Created Date:', createdDate || '']
    ];

    autoTable(doc, {
      startY: currentY,
      body: infoData,
      margin: { left: margin.left, right: margin.right },
      styles: {
        font: 'helvetica',
        fontSize: 10,
        cellPadding: 8,
        overflow: 'linebreak',
        cellWidth: 'wrap'
      },
      columnStyles: {
        0: { cellWidth: 120, fontStyle: 'bold', fillColor: [240, 240, 240], overflow: 'linebreak' },
        1: { cellWidth: 200, overflow: 'linebreak' }
      },
      theme: 'grid'
    });

    currentY = (doc as any).lastAutoTable.finalY + 20;
  };

  drawPermitInfo();

  // City Comments Section
  if (cityComments) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.setTextColor(192, 0, 0); // Red color for city comments
    doc.text('CITY COMMENTS', margin.left, currentY);
    currentY += 15;

    doc.setTextColor(0, 0, 0);
    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    
    // Split long text into multiple lines
    const splitText = doc.splitTextToSize(cityComments, pageWidth - margin.left - margin.right);
    doc.text(splitText, margin.left, currentY);
    currentY += splitText.length * 12 + 20;
  }

  // Owner Response Section
  if (ownerResponse) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.text('EOR/AOR/OWNER RESPONSE', margin.left, currentY);
    currentY += 15;

    doc.setFont('helvetica', 'normal');
    doc.setFontSize(10);
    
    const splitText = doc.splitTextToSize(ownerResponse, pageWidth - margin.left - margin.right);
    doc.text(splitText, margin.left, currentY);
    currentY += splitText.length * 12 + 20;

    // Response details
    if (respondedBy) {
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.text(`Responded By: ${respondedBy}`, margin.left, currentY);
      currentY += 15;
    }
  }

  // Corrections Section
  if (this.review?.Corrections && this.review.Corrections.length > 0) {
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(12);
    doc.text('CORRECTIONS & DETAILS', margin.left, currentY);
    currentY += 15;

    this.review.Corrections.forEach((correction: any, index: number) => {
      // Check if we need a new page
      if (currentY > pageHeight - 200) {
        doc.addPage();
        drawHeader();
        currentY = margin.top + 20;
      }

      // Correction header
      doc.setFont('helvetica', 'bold');
      doc.setFontSize(10);
      doc.text(`Correction ${index + 1}`, margin.left, currentY);
      currentY += 15;

      // Correction details table
      const correctionData = [
        ['Correction ID:', correction.CorrectionID || ''],
        ['Type:', correction.CorrectionTypeName || ''],
        ['Category:', correction.CorrectionCategoryName || ''],
        ['Comments:', correction.Comments || ''],
        ['Corrective Action:', correction.CorrectiveAction || ''],
        ['Response:', correction.Response || ''],
        ['Owner Response:', correction.EORAOROwner_Response || ''],
        ['Responded By:', correction.commentResponsedBy || ''],
        ['Resolved Date:', correction.ResolvedDate ? new Date(correction.ResolvedDate).toLocaleDateString() : ''],
        ['Is Resolved:', correction.IsResolvedText || '']
      ];

      autoTable(doc, {
        startY: currentY,
        body: correctionData,
        margin: { left: margin.left, right: margin.right },
        styles: {
          font: 'helvetica',
          fontSize: 9,
          cellPadding: 6,
          overflow: 'linebreak',
          cellWidth: 'wrap'
        },
        columnStyles: {
          0: { cellWidth: 100, fontStyle: 'bold', fillColor: [245, 245, 245], overflow: 'linebreak' },
          1: { cellWidth: 200, overflow: 'linebreak' }
        },
        theme: 'grid'
      });

      currentY = (doc as any).lastAutoTable.finalY + 15;
    });
  }

  // Footer
  const drawFooter = () => {
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      
      // Footer line
      doc.setDrawColor(200, 200, 200);
      doc.line(margin.left, pageHeight - 30, pageWidth - margin.right, pageHeight - 30);
      
      // Footer text
      doc.setFont('helvetica', 'normal');
      doc.setFontSize(8);
      doc.setTextColor(100, 100, 100);
      doc.text(`Generated on: ${new Date().toLocaleString()}`, margin.left, pageHeight - 15);
      doc.text(`Page ${i} of ${pageCount}`, pageWidth - margin.right - 50, pageHeight - 15);
    }
  };

  drawFooter();

  // Save the PDF
  const fileName = `Review_${permitNumber ? permitNumber + '_' : ''}${reviewCategory ? reviewCategory.replace(/[^a-zA-Z0-9]/g, '_') + '_' : ''}${new Date().toISOString().split('T')[0]}.pdf`;
  doc.save(fileName);
}
}



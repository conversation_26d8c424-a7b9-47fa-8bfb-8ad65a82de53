{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, catchError, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/custom-layout.utils.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Permit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Permit - \", ctx_r0.permitNumber, \"\");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_16_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"div\", 39)(2, \"span\", 40);\n    i0.ɵɵtext(3, \"Validating...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_26_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_26_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberValidationError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_26_div_1_Template, 2, 0, \"div\", 3)(2, PermitPopupComponent_ng_container_20_div_26_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"permitNumberExists\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberValidationError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_ng_container_29_div_6_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_ng_container_29_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_ng_container_29_div_6_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_ng_container_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"label\", 20);\n    i0.ɵɵtext(2, \"Internal Review Status \");\n    i0.ɵɵelementStart(3, \"span\", 21);\n    i0.ɵɵtext(4, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(5, \"ng-select\", 41);\n    i0.ɵɵtemplate(6, PermitPopupComponent_ng_container_20_ng_container_29_div_6_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_5_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"items\", ctx_r0.internalStatusArray)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"internalReviewStatus\")) == null ? null : tmp_5_0.invalid));\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_36_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_36_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_44_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_44_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_span_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_50_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_50_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_58_div_1_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_ng_container_20_div_58_div_1_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r0.syncPermitDetails());\n    });\n    i0.ɵɵelement(1, \"i\", 47);\n    i0.ɵɵtext(2, \" Sync \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_58_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 43)(1, \"div\", 44)(2, \"div\", 45);\n    i0.ɵɵtext(3, \" The syncing of permit details may take up to 3 minutes. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" \\u00A0 \");\n    i0.ɵɵelementStart(5, \"div\");\n    i0.ɵɵtemplate(6, PermitPopupComponent_ng_container_20_div_58_div_1_button_6_Template, 3, 0, \"button\", 18);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.id === 0);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_58_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_58_div_1_Template, 7, 1, \"div\", 42);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.getSyncButtonDisableStatus());\n  }\n}\nfunction PermitPopupComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 8)(3, \"label\", 20);\n    i0.ɵɵtext(4, \"Project \");\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"ng-select\", 22);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_20_Template_ng_select_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onProjectChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_20_div_8_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"div\", 8)(11, \"label\", 20);\n    i0.ɵɵtext(12, \"Permit / Sub Project Name \");\n    i0.ɵɵelementStart(13, \"span\", 21);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"input\", 24);\n    i0.ɵɵtemplate(16, PermitPopupComponent_ng_container_20_div_16_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 19)(18, \"div\", 25)(19, \"label\", 20);\n    i0.ɵɵtext(20, \"Permit # \");\n    i0.ɵɵelementStart(21, \"span\", 21);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 26)(24, \"input\", 27);\n    i0.ɵɵlistener(\"blur\", function PermitPopupComponent_ng_container_20_Template_input_blur_24_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.triggerPermitNumberValidation());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(25, PermitPopupComponent_ng_container_20_div_25_Template, 4, 0, \"div\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, PermitPopupComponent_ng_container_20_div_26_Template, 3, 2, \"div\", 23)(27, PermitPopupComponent_ng_container_20_div_27_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 25);\n    i0.ɵɵtemplate(29, PermitPopupComponent_ng_container_20_ng_container_29_Template, 7, 4, \"ng-container\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 25)(31, \"label\", 20);\n    i0.ɵɵtext(32, \"Permit Category \");\n    i0.ɵɵelementStart(33, \"span\", 21);\n    i0.ɵɵtext(34, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(35, \"ng-select\", 29);\n    i0.ɵɵtemplate(36, PermitPopupComponent_ng_container_20_div_36_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(37, \"div\", 19)(38, \"div\", 30)(39, \"label\", 20);\n    i0.ɵɵtext(40, \"Permit Review Type\");\n    i0.ɵɵelementStart(41, \"span\", 21);\n    i0.ɵɵtext(42, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"ng-select\", 31);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_20_Template_ng_select_change_43_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitReviewTypeChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(44, PermitPopupComponent_ng_container_20_div_44_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"div\", 30)(46, \"label\", 20);\n    i0.ɵɵtext(47, \"Permit Municipality (External Review)\");\n    i0.ɵɵtemplate(48, PermitPopupComponent_ng_container_20_span_48_Template, 2, 0, \"span\", 32);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"ng-select\", 33);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_20_Template_ng_select_change_49_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitMunicipalityChange($event));\n    })(\"clear\", function PermitPopupComponent_ng_container_20_Template_ng_select_clear_49_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitMunicipalityClear());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(50, PermitPopupComponent_ng_container_20_div_50_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"div\", 34)(52, \"div\", 8)(53, \"div\", 35);\n    i0.ɵɵelement(54, \"i\", 36);\n    i0.ɵɵelementStart(55, \"span\");\n    i0.ɵɵtext(56, \"Permit # and Permit Municipality are required to sync permit details from the portal.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(57, \"br\");\n    i0.ɵɵtemplate(58, PermitPopupComponent_ng_container_20_div_58_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_8_0;\n    let tmp_9_0;\n    let tmp_14_0;\n    let tmp_16_0;\n    let tmp_21_0;\n    let tmp_22_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.projects)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_6_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_6_0.invalid) && ((tmp_6_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_6_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPermitNumberValidating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_8_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_8_0.touched) && ((tmp_8_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_8_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.permitNumberValidationError && !((tmp_9_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_9_0.touched));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isHideInternalReviewStatus);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.categories)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_14_0.touched) && ((tmp_14_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_14_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.reviewTypeArray);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_16_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_16_0.touched) && ((tmp_16_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_16_0.invalid));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPermitMunicipalRequired);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"items\", ctx_r0.muncipalities)(\"clearable\", true)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_21_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_21_0.touched) && ((tmp_21_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_21_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_22_0.value) === \"External\");\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_19_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_27_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_38_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_38_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_59_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_59_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_59_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 25)(3, \"label\", 20);\n    i0.ɵɵtext(4, \"Review Responsible Party\");\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"input\", 48);\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_21_div_8_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 25)(10, \"label\", 20);\n    i0.ɵɵtext(11, \"Primary Contact\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 49);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 25)(14, \"label\", 20);\n    i0.ɵɵtext(15, \"Permit Status \");\n    i0.ɵɵelementStart(16, \"span\", 21);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(18, \"ng-select\", 50);\n    i0.ɵɵtemplate(19, PermitPopupComponent_ng_container_21_div_19_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 19)(21, \"div\", 25)(22, \"label\", 20);\n    i0.ɵɵtext(23, \"Permit Type \");\n    i0.ɵɵelementStart(24, \"span\", 21);\n    i0.ɵɵtext(25, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(26, \"ng-select\", 51);\n    i0.ɵɵtemplate(27, PermitPopupComponent_ng_container_21_div_27_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 25)(29, \"label\", 20);\n    i0.ɵɵtext(30, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 52);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 25)(33, \"label\", 20);\n    i0.ɵɵtext(34, \"Applied Date \");\n    i0.ɵɵelementStart(35, \"span\", 21);\n    i0.ɵɵtext(36, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(37, \"input\", 53);\n    i0.ɵɵtemplate(38, PermitPopupComponent_ng_container_21_div_38_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 25)(41, \"label\", 20);\n    i0.ɵɵtext(42, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 25)(45, \"label\", 20);\n    i0.ɵɵtext(46, \"Completed Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 55);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 25)(49, \"label\", 20);\n    i0.ɵɵtext(50, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"input\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 19)(53, \"div\", 8)(54, \"label\", 20);\n    i0.ɵɵtext(55, \"Location\");\n    i0.ɵɵelementStart(56, \"span\", 21);\n    i0.ɵɵtext(57, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(58, \"input\", 57);\n    i0.ɵɵtemplate(59, PermitPopupComponent_ng_container_21_div_59_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(60, \"div\", 19)(61, \"div\", 8)(62, \"label\", 20);\n    i0.ɵɵtext(63, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(64, \"textarea\", 58);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_5_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_11_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_1_0.touched) && ((tmp_1_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_1_0.invalid));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", ctx_r0.statuses)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.permitTypes)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_9_0.invalid));\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_10_0.touched) && ((tmp_10_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_10_0.invalid));\n    i0.ɵɵadvance(21);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_11_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_11_0.touched) && ((tmp_11_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_11_0.invalid));\n  }\n}\nfunction PermitPopupComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 60);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.permitForm.invalid || ctx_r0.isPermitNumberValidating);\n  }\n}\nfunction PermitPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToNextTab());\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PermitPopupComponent {\n  modal;\n  fb;\n  projectsService;\n  permitsService;\n  appService;\n  httpUtilService;\n  customLayoutUtilsService;\n  cdr;\n  id = 0; // 0 = Add, otherwise Edit\n  isHideInternalReviewStatus = true; // 0 = Add, otherwise Edit\n  permit; // incoming permit data (for edit)\n  passEntry = new EventEmitter();\n  permitForm;\n  projects = [];\n  reviewTypeArray = ['Internal', 'External', 'Both'];\n  loginUser = {};\n  isLoading = false;\n  muncipalities = [];\n  selectedTab = 'basic';\n  // dropdown options\n  permitTypes = ['Access Control System - Commercial', 'Addition - Commercial', 'Addition - Residential', 'Backflow - Commercial', 'Building Miscellaneous - Commercial', 'Building Move Permit - Residential', 'Building Revisions - Commercial Revision', 'Certificate of Completion', 'Certificate of Occupancy - Commercial', 'Commercial - LV Data Voice Cable Sub-Permit', 'Demolition - Commercial', 'Document Submittal - Commercial Building', 'Electrical Sub-Permit - Commercial', 'Engineering Construction Traffic & Parking Management Plan', 'Fence - Commercial', 'Fire Alarm - Fire', 'Fire Sprinkler/Fire Suppression - Fire', 'Foundation Only - Commercial', 'Gas Sub-Permit - Commercial', 'General Electrical - Commercial', 'General Paving - Paving', 'General Sign Permit', 'Generator - Commercial', 'Interceptor - Commercial', 'Interior (<5000 sq ft) - Commercial', 'Irrigation - Commercial', 'Landscape Non-Residential and Multi-Family', 'Low Voltage - Commercial', 'Mechanical Sub-Permit - Commercial', 'Monument - Sign', 'Mural - Sign', 'New Building - Commercial', 'Plumbing Sub-Permit - Commercial', 'Pool Plumbing Commercial (Sub-Permit)', 'Public Art Permit Application', 'Remodel - Commercial', 'Right-of-Way | ENG A - General', 'Sewer Cap for Demo - Commercial', 'Windows and Doors - Commercial'];\n  categories = ['Primary', 'Sub Permit', 'Industrial', 'Municipal', 'Environmental'];\n  statuses = ['Canceled', 'Complete', 'Expired', 'Fees Due', 'In Review', 'Issued', 'On Hold', 'Requires Resubmit', 'Requires Resubmit for Prescreen', 'Submitted - Online', 'Void'];\n  internalStatusArray = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n  permitNumber;\n  isPermitMunicipalRequired = false;\n  iscityreviewLinkMunicipalRequired = false;\n  cityReviewLink = '';\n  syncedPermitData = {};\n  permitNumberValidationError = '';\n  isPermitNumberValidating = false;\n  constructor(modal, fb, projectsService, permitsService, appService, httpUtilService, customLayoutUtilsService, cdr) {\n    this.modal = modal;\n    this.fb = fb;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.appService = appService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.cdr = cdr;\n    // Subscribe to loading state\n    this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading;\n    });\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadMunicipalities();\n    this.loadForm();\n    this.loadProjects();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n  }\n  ngOnDestroy() {\n    // Reset global loading state when component is destroyed\n    this.httpUtilService.loadingSubject.next(false);\n  }\n  loadForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      permitCategory: ['', Validators.required],\n      permitType: ['', Validators.required],\n      description: [''],\n      permitReviewType: ['', Validators.required],\n      location: ['', Validators.required],\n      primaryContact: [''],\n      permitAppliedDate: ['', Validators.required],\n      permitExpirationDate: [''],\n      permitIssueDate: [''],\n      permitFinalDate: [''],\n      permitCompleteDate: [''],\n      permitStatus: ['', Validators.required],\n      internalReviewStatus: ['', Validators.required],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      reviewResponsibleParty: ['', Validators.required],\n      permitMunicipality: ['']\n      // cityReviewLink: [''],\n    });\n  }\n  setupPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    if (permitNumberControl) {\n      // Only clear server-side validation while typing; don't call API here\n      permitNumberControl.valueChanges.pipe(distinctUntilChanged()).subscribe(() => {\n        this.permitNumberValidationError = '';\n        if (permitNumberControl.hasError('permitNumberExists')) {\n          const errors = {\n            ...permitNumberControl.errors\n          };\n          delete errors['permitNumberExists'];\n          permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n        }\n      });\n    }\n  }\n  triggerPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    const projectIdControl = this.permitForm.get('projectId');\n    const permitNumber = permitNumberControl?.value;\n    const projectId = projectIdControl?.value;\n    const enteredNormalized = (permitNumber || '').toString().trim().toLowerCase();\n    const originalNormalized = (this.permitNumber || '').toString().trim().toLowerCase();\n    // If empty, or in edit mode and value hasn't changed, just clear any prior error and skip API\n    if (!enteredNormalized || this.id !== 0 && enteredNormalized === originalNormalized) {\n      this.isPermitNumberValidating = false;\n      this.permitNumberValidationError = '';\n      if (permitNumberControl?.hasError('permitNumberExists')) {\n        const errors = {\n          ...permitNumberControl.errors\n        };\n        delete errors['permitNumberExists'];\n        permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n      }\n      return;\n    }\n    if (enteredNormalized && projectId) {\n      this.isPermitNumberValidating = true;\n      this.validatePermitNumber(permitNumber, projectId).pipe(map(res => res?.responseData ?? res)).subscribe(result => {\n        this.isPermitNumberValidating = false;\n        if (result && result.exists) {\n          this.permitNumberValidationError = result.message;\n          permitNumberControl?.setErrors({\n            'permitNumberExists': true\n          });\n        } else {\n          this.permitNumberValidationError = '';\n          if (permitNumberControl?.hasError('permitNumberExists')) {\n            const errors = {\n              ...permitNumberControl.errors\n            };\n            delete errors['permitNumberExists'];\n            permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n          }\n        }\n      });\n    }\n  }\n  validatePermitNumber(permitNumber, projectId) {\n    this.isPermitNumberValidating = true;\n    this.permitNumberValidationError = '';\n    const params = {\n      permitNumber: permitNumber,\n      projectId: projectId,\n      permitId: this.id !== 0 ? this.id : null // Include permitId for edit mode\n    };\n    return this.permitsService.validatePermitNumber(params).pipe(catchError(() => {\n      this.isPermitNumberValidating = false;\n      return of({\n        exists: false,\n        message: ''\n      });\n    }));\n  }\n  loadProjects() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = {\n      paginate: false\n    };\n    this.projectsService.getAllProjectsData(params).subscribe({\n      next: response => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.projects = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.projects = [];\n      }\n    });\n  }\n  loadMunicipalities() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = {\n      loggedinUser: this.loginUser.userId\n    };\n    this.permitsService.getAllMunicipalities(params).subscribe({\n      next: response => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.muncipalities = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.muncipalities = [];\n      }\n    });\n  }\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getPermit({\n      permitId: this.id,\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.data;\n          this.permitNumber = permitData.permitNumber;\n          this.permitForm.patchValue({\n            projectId: permitData.projectId,\n            permitNumber: permitData.permitNumber,\n            permitReviewType: permitData.permitReviewType,\n            permitCategory: permitData.permitCategory,\n            permitType: permitData.permitType,\n            description: permitData.description,\n            permitName: permitData.permitName,\n            location: permitData.location,\n            internalReviewStatus: permitData.internalReviewStatus,\n            primaryContact: permitData.primaryContact,\n            permitAppliedDate: permitData.permitAppliedDate ? this.formatDateForInput(permitData.permitAppliedDate) : '',\n            permitExpirationDate: permitData.permitExpirationDate ? this.formatDateForInput(permitData.permitExpirationDate) : '',\n            permitIssueDate: permitData.permitIssueDate ? this.formatDateForInput(permitData.permitIssueDate) : '',\n            permitFinalDate: permitData.permitFinalDate ? this.formatDateForInput(permitData.permitFinalDate) : '',\n            permitCompleteDate: permitData.permitCompleteDate ? this.formatDateForInput(permitData.permitCompleteDate) : '',\n            permitStatus: permitData.permitStatus,\n            attentionReason: permitData.attentionReason,\n            internalNotes: permitData.internalNotes,\n            actionTaken: permitData.actionTaken,\n            reviewResponsibleParty: permitData.reviewResponsibleParty,\n            permitMunicipality: permitData.permitMunicipality\n            // cityReviewLink: permitData.cityReviewLink,\n          });\n          this.onPermitReviewTypeChange(permitData.permitReviewType);\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n  }\n  formatDateForInput(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    // Use local timezone to avoid date shifting\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  preparePermitData() {\n    const formData = this.permitForm.value;\n    let permitRequestData = {};\n    permitRequestData.projectId = formData.projectId;\n    permitRequestData.permitNumber = formData.permitNumber;\n    permitRequestData.permitReviewType = formData.permitReviewType;\n    permitRequestData.permitCategory = formData.permitCategory;\n    permitRequestData.permitType = formData.permitType;\n    permitRequestData.description = formData.description;\n    permitRequestData.location = formData.location;\n    permitRequestData.primaryContact = formData.primaryContact;\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\n    permitRequestData.permitExpirationDate = formData.permitExpirationDate || null;\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\n    permitRequestData.permitFinalDate = formData.permitFinalDate || null;\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\n    permitRequestData.permitStatus = formData.permitStatus;\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\n    permitRequestData.permitName = formData.permitName;\n    permitRequestData.attentionReason = formData.attentionReason;\n    permitRequestData.internalNotes = formData.internalNotes;\n    permitRequestData.actionTaken = formData.actionTaken;\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\n    permitRequestData.cityReviewLink = this.cityReviewLink;\n    permitRequestData.loggedInUserId = this.loginUser.userId;\n    permitRequestData.syncedPermitData = this.syncedPermitData;\n    const caseId = this.syncedPermitData.EntityId || this.syncedPermitData.permitId || this.syncedPermitData.Id || null;\n    permitRequestData.permitEntityID = caseId;\n    if (this.id !== 0) {\n      permitRequestData.permitId = this.id;\n    }\n    return permitRequestData;\n  }\n  save() {\n    let controls = this.permitForm.controls;\n    console.log('Permit Data:', this.permitForm.value);\n    if (this.permitForm.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.customLayoutUtilsService.showError('Please fill all required fields', '');\n      return;\n    }\n    let permitData = this.preparePermitData();\n    console.log('Permit Data:', permitData);\n    if (this.id === 0) {\n      this.create(permitData);\n    } else {\n      this.edit(permitData);\n    }\n  }\n  create(permitData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.createPermit(permitData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  edit(permitData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.updatePermit(permitData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'notes';\n    }\n    this.cdr.markForCheck();\n  }\n  goToPreviousTab() {\n    if (this.selectedTab === 'notes') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n    this.cdr.markForCheck();\n  }\n  onProjectChange(event) {\n    this.permitForm.patchValue({\n      location: event.projectLocation\n    });\n    // console.log(\"project loacation\",event)\n    // Auto-fill Permit / Sub Project Name for Add mode when selecting a project\n    if (this.id === 0) {\n      const permitNameControl = this.permitForm.get('permitName');\n      const currentValue = (permitNameControl?.value || '').toString().trim();\n      const projectName = event?.projectName || event?.project?.projectName || '';\n      if (permitNameControl && projectName && (currentValue === '' || permitNameControl.pristine)) {\n        permitNameControl.setValue(projectName);\n        permitNameControl.markAsDirty();\n      }\n    }\n  }\n  onPermitReviewTypeChange(event) {\n    const permitControl = this.permitForm.get('permitMunicipality');\n    // const cityReviewControl = this.permitForm.get('cityReviewLink');\n    if (event === 'External') {\n      this.isPermitMunicipalRequired = true;\n      // this.iscityreviewLinkMunicipalRequired = true;\n      permitControl?.setValidators([Validators.required]);\n      // cityReviewControl?.setValidators([Validators.required]);\n    } else {\n      this.isPermitMunicipalRequired = false;\n      // this.iscityreviewLinkMunicipalRequired = false;\n      permitControl?.clearValidators();\n      // cityReviewControl?.clearValidators();\n    }\n    permitControl?.updateValueAndValidity();\n    // cityReviewControl?.updateValueAndValidity();\n  }\n  onPermitMunicipalityChange(event) {\n    console.log('event 0 ', event);\n    this.cityReviewLink = event.cityWebsiteLink + '#/permit/';\n    this.getSyncButtonDisableStatus();\n  }\n  onPermitMunicipalityClear() {\n    this.permitForm.patchValue({\n      permitMunicipality: null\n    });\n    this.cityReviewLink = '';\n    this.getSyncButtonDisableStatus();\n  }\n  syncPermitDetails() {\n    const formData = this.permitForm.value;\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getPermitDetails({\n      permitNumber: formData.permitNumber,\n      municipalityId: formData.permitMunicipality\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.permit;\n          this.syncedPermitData = permitData;\n          this.permitForm.patchValue({\n            permitType: permitData.permitType,\n            description: permitData.description,\n            location: permitData.address,\n            permitAppliedDate: permitData.applyDate ? this.formatDateForInput(permitData.applyDate) : '',\n            permitExpirationDate: permitData.expireDate ? this.formatDateForInput(permitData.expireDate) : '',\n            permitIssueDate: permitData.issueDate ? this.formatDateForInput(permitData.issueDate) : '',\n            permitFinalDate: permitData.finalDate ? this.formatDateForInput(permitData.finalDate) : '',\n            permitCompleteDate: permitData.completeDate ? this.formatDateForInput(permitData.completeDate) : '',\n            permitStatus: permitData.permitStatus\n          });\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData.message);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n  }\n  getSyncButtonDisableStatus() {\n    const reviewType = this.permitForm.get('permitReviewType')?.value;\n    const permitNumber = this.permitForm.get('permitNumber')?.value;\n    const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\n    const isExternal = reviewType === 'External';\n    const hasPermitNumber = !!permitNumber;\n    const hasPermitMunicipality = !!permitMunicipality;\n    console.log('isExternal ', isExternal);\n    console.log('hasPermitNumber ', hasPermitNumber);\n    console.log('hasPermitMunicipality ', hasPermitMunicipality);\n    // Disable if any of the conditions are not satisfied\n    return !(isExternal && hasPermitNumber && hasPermitMunicipality);\n  }\n  static ɵfac = function PermitPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitPopupComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitPopupComponent,\n    selectors: [[\"app-permit-popup\"]],\n    inputs: {\n      id: \"id\",\n      isHideInternalReviewStatus: \"isHideInternalReviewStatus\",\n      permit: \"permit\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 31,\n    vars: 14,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"modal-footer\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"mt-4\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"bindLabel\", \"projectName\", \"formControlName\", \"projectId\", \"bindValue\", \"projectId\", \"placeholder\", \"Select Project\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"class\", \"text-danger mt-1 small\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"permitName\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-4\"], [1, \"position-relative\"], [\"type\", \"text\", \"formControlName\", \"permitNumber\", 1, \"form-control\", \"form-control-sm\", 3, \"blur\"], [\"class\", \"position-absolute top-50 end-0 translate-middle-y me-2\", 4, \"ngIf\"], [\"formControlName\", \"permitCategory\", \"placeholder\", \"Select Category\", 3, \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-6\"], [\"formControlName\", \"permitReviewType\", 3, \"change\", \"items\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"bindLabel\", \"cityName\", \"formControlName\", \"permitMunicipality\", \"bindValue\", \"municipalityId\", \"placeholder\", \"Select Project\", 3, \"change\", \"clear\", \"items\", \"clearable\", \"multiple\"], [1, \"row\", \"mt-3\"], [1, \"text-muted\", \"small\", \"d-flex\", \"align-items-center\", 2, \"white-space\", \"normal\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [1, \"text-danger\", \"mt-1\", \"small\"], [1, \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\"], [1, \"visually-hidden\"], [\"formControlName\", \"internalReviewStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"class\", \"row mt-1\", 4, \"ngIf\"], [1, \"row\", \"mt-1\"], [1, \"col-xl-12\", \"d-flex\", \"align-items-center\"], [1, \"small\", 2, \"white-space\", \"normal\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [\"type\", \"text\", \"formControlName\", \"reviewResponsibleParty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"primaryContact\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"permitStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"formControlName\", \"permitType\", \"placeholder\", \"Select Type\", 3, \"items\", \"clearable\", \"multiple\"], [\"type\", \"date\", \"formControlName\", \"permitIssueDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitAppliedDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitExpirationDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitCompleteDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitFinalDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"description\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"]],\n    template: function PermitPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, PermitPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, PermitPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_i_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"ul\", 10)(13, \"li\", 11)(14, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_14_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(15, \" Basic Info \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 11)(17, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_17_listener($event) {\n          return ctx.showTab(\"details\", $event);\n        });\n        i0.ɵɵtext(18, \" Permit Details \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(19, \"form\", 13);\n        i0.ɵɵtemplate(20, PermitPopupComponent_ng_container_20_Template, 59, 23, \"ng-container\", 3)(21, PermitPopupComponent_ng_container_21_Template, 65, 11, \"ng-container\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 14)(23, \"div\");\n        i0.ɵɵtemplate(24, PermitPopupComponent_button_24_Template, 2, 0, \"button\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\")(26, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_button_click_26_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵtext(27, \" Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(28, \"\\u00A0 \");\n        i0.ɵɵtemplate(29, PermitPopupComponent_button_29_Template, 2, 1, \"button\", 17)(30, PermitPopupComponent_button_30_Template, 2, 0, \"button\", 18);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.selectedTab === \"details\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.permitForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i9.NgSelectComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "distinctUntilChanged", "catchError", "map", "of", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "permitNumber", "ɵɵtemplate", "PermitPopupComponent_ng_container_20_div_8_div_1_Template", "ɵɵproperty", "tmp_2_0", "permitForm", "get", "errors", "PermitPopupComponent_ng_container_20_div_16_div_1_Template", "permitNumberValidationError", "PermitPopupComponent_ng_container_20_div_26_div_1_Template", "PermitPopupComponent_ng_container_20_div_26_div_2_Template", "tmp_3_0", "PermitPopupComponent_ng_container_20_ng_container_29_div_6_div_1_Template", "ɵɵelementContainerStart", "ɵɵelement", "PermitPopupComponent_ng_container_20_ng_container_29_div_6_Template", "internalStatusArray", "tmp_5_0", "touched", "invalid", "PermitPopupComponent_ng_container_20_div_36_div_1_Template", "PermitPopupComponent_ng_container_20_div_44_div_1_Template", "PermitPopupComponent_ng_container_20_div_50_div_1_Template", "ɵɵlistener", "PermitPopupComponent_ng_container_20_div_58_div_1_button_6_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "syncPermitDetails", "PermitPopupComponent_ng_container_20_div_58_div_1_button_6_Template", "id", "PermitPopupComponent_ng_container_20_div_58_div_1_Template", "getSyncButtonDisableStatus", "PermitPopupComponent_ng_container_20_Template_ng_select_change_7_listener", "$event", "_r2", "onProjectChange", "PermitPopupComponent_ng_container_20_div_8_Template", "PermitPopupComponent_ng_container_20_div_16_Template", "PermitPopupComponent_ng_container_20_Template_input_blur_24_listener", "triggerPermitNumberValidation", "PermitPopupComponent_ng_container_20_div_25_Template", "PermitPopupComponent_ng_container_20_div_26_Template", "PermitPopupComponent_ng_container_20_div_27_Template", "PermitPopupComponent_ng_container_20_ng_container_29_Template", "PermitPopupComponent_ng_container_20_div_36_Template", "PermitPopupComponent_ng_container_20_Template_ng_select_change_43_listener", "onPermitReviewTypeChange", "PermitPopupComponent_ng_container_20_div_44_Template", "PermitPopupComponent_ng_container_20_span_48_Template", "PermitPopupComponent_ng_container_20_Template_ng_select_change_49_listener", "onPermitMunicipalityChange", "PermitPopupComponent_ng_container_20_Template_ng_select_clear_49_listener", "onPermitMunicipalityClear", "PermitPopupComponent_ng_container_20_div_50_Template", "PermitPopupComponent_ng_container_20_div_58_Template", "projects", "tmp_4_0", "ɵɵclassProp", "tmp_6_0", "isPermitNumberValidating", "tmp_8_0", "tmp_9_0", "isHideInternalReviewStatus", "categories", "tmp_14_0", "reviewTypeArray", "tmp_16_0", "isPermitMunicipalRequired", "muncipalities", "tmp_21_0", "tmp_22_0", "value", "PermitPopupComponent_ng_container_21_div_8_div_1_Template", "PermitPopupComponent_ng_container_21_div_19_div_1_Template", "PermitPopupComponent_ng_container_21_div_27_div_1_Template", "PermitPopupComponent_ng_container_21_div_38_div_1_Template", "PermitPopupComponent_ng_container_21_div_59_div_1_Template", "PermitPopupComponent_ng_container_21_div_8_Template", "PermitPopupComponent_ng_container_21_div_19_Template", "PermitPopupComponent_ng_container_21_div_27_Template", "PermitPopupComponent_ng_container_21_div_38_Template", "PermitPopupComponent_ng_container_21_div_59_Template", "tmp_1_0", "statuses", "permitTypes", "tmp_10_0", "tmp_11_0", "PermitPopupComponent_button_24_Template_button_click_0_listener", "_r4", "goToPreviousTab", "PermitPopupComponent_button_29_Template_button_click_0_listener", "_r5", "save", "PermitPopupComponent_button_30_Template_button_click_0_listener", "_r6", "goToNextTab", "PermitPopupComponent", "modal", "fb", "projectsService", "permitsService", "appService", "httpUtilService", "customLayoutUtilsService", "cdr", "permit", "passEntry", "loginUser", "isLoading", "selectedTab", "iscityreviewLinkMunicipalRequired", "cityReviewLink", "syncedPermitData", "constructor", "loadingSubject", "subscribe", "loading", "ngOnInit", "getLoggedInUser", "loadMunicipalities", "loadForm", "loadProjects", "setupPermitNumberValidation", "patchForm", "ngOnDestroy", "next", "group", "projectId", "required", "permitName", "permitCategory", "permitType", "description", "permitReviewType", "location", "primaryContact", "permitAppliedDate", "permitExpirationDate", "permitIssueDate", "permitFinalDate", "permitCompleteDate", "permitStatus", "internalReviewStatus", "attentionReason", "internalNotes", "actionTaken", "reviewResponsibleParty", "permitMunicipality", "permitNumberControl", "valueChanges", "pipe", "<PERSON><PERSON><PERSON><PERSON>", "setErrors", "Object", "keys", "length", "projectIdControl", "enteredNormalized", "toString", "trim", "toLowerCase", "originalNormalized", "validatePermitNumber", "res", "responseData", "result", "exists", "message", "params", "permitId", "paginate", "getAllProjectsData", "response", "data", "error", "console", "loggedinUser", "userId", "getAllMunicipalities", "get<PERSON><PERSON><PERSON>", "loggedInUserId", "permitResponse", "<PERSON><PERSON><PERSON>", "permitData", "patchValue", "formatDateForInput", "warn", "err", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "preparePermitData", "formData", "permitRequestData", "caseId", "EntityId", "Id", "permitEntityID", "controls", "log", "for<PERSON>ach", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "showError", "create", "edit", "createPermit", "showSuccess", "emit", "close", "updatePermit", "showTab", "tab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "projectLocation", "permitNameControl", "currentValue", "projectName", "project", "pristine", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permitControl", "setValidators", "clearValidators", "updateValueAndValidity", "cityWebsiteLink", "getPermitDetails", "municipalityId", "address", "applyDate", "expireDate", "issueDate", "finalDate", "completeDate", "reviewType", "isExternal", "hasPermitNumber", "hasPermitMunicipality", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "FormBuilder", "i3", "ProjectsService", "i4", "PermitsService", "i5", "AppService", "i6", "HttpUtilsService", "i7", "CustomLayoutUtilsService", "ChangeDetectorRef", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "PermitPopupComponent_Template", "rf", "ctx", "PermitPopupComponent_div_4_Template", "PermitPopupComponent_div_5_Template", "PermitPopupComponent_Template_i_click_7_listener", "dismiss", "PermitPopupComponent_Template_a_click_14_listener", "PermitPopupComponent_Template_a_click_17_listener", "PermitPopupComponent_ng_container_20_Template", "PermitPopupComponent_ng_container_21_Template", "PermitPopupComponent_button_24_Template", "PermitPopupComponent_Template_button_click_26_listener", "PermitPopupComponent_button_29_Template", "PermitPopupComponent_button_30_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.html"], "sourcesContent": ["import {\r\n  Component,\r\n  Input,\r\n  Output,\r\n  EventEmitter,\r\n  ChangeDetectorRef,\r\n  OnInit,\r\n  OnDestroy,\r\n} from '@angular/core';\r\nimport { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\r\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { even } from '@rxweb/reactive-form-validators';\r\nimport { debounceTime, distinctUntilChanged, switchMap, catchError, map } from 'rxjs/operators';\r\nimport { of } from 'rxjs';\r\n\r\n@Component({\r\n  selector: 'app-permit-popup',\r\n  templateUrl: './permit-popup.component.html',\r\n})\r\nexport class PermitPopupComponent implements OnDestroy {\r\n  @Input() id: number = 0; // 0 = Add, otherwise Edit\r\n  @Input() isHideInternalReviewStatus: Boolean = true; // 0 = Add, otherwise Edit\r\n  @Input() permit: any; // incoming permit data (for edit)\r\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\r\n\r\n  permitForm: FormGroup;\r\n  projects: any[] = [];\r\n  reviewTypeArray: any[] = ['Internal', 'External', 'Both'];\r\n  loginUser: any = {};\r\n  isLoading: boolean = false;\r\n  muncipalities: any = [];\r\n  selectedTab: any = 'basic';\r\n  // dropdown options\r\n  permitTypes = [\r\n    'Access Control System - Commercial',\r\n    'Addition - Commercial',\r\n    'Addition - Residential',\r\n    'Backflow - Commercial',\r\n    'Building Miscellaneous - Commercial',\r\n    'Building Move Permit - Residential',\r\n    'Building Revisions - Commercial Revision',\r\n    'Certificate of Completion',\r\n    'Certificate of Occupancy - Commercial',\r\n    'Commercial - LV Data Voice Cable Sub-Permit',\r\n    'Demolition - Commercial',\r\n    'Document Submittal - Commercial Building',\r\n    'Electrical Sub-Permit - Commercial',\r\n    'Engineering Construction Traffic & Parking Management Plan',\r\n    'Fence - Commercial',\r\n    'Fire Alarm - Fire',\r\n    'Fire Sprinkler/Fire Suppression - Fire',\r\n    'Foundation Only - Commercial',\r\n    'Gas Sub-Permit - Commercial',\r\n    'General Electrical - Commercial',\r\n    'General Paving - Paving',\r\n    'General Sign Permit',\r\n    'Generator - Commercial',\r\n    'Interceptor - Commercial',\r\n    'Interior (<5000 sq ft) - Commercial',\r\n    'Irrigation - Commercial',\r\n    'Landscape Non-Residential and Multi-Family',\r\n    'Low Voltage - Commercial',\r\n    'Mechanical Sub-Permit - Commercial',\r\n    'Monument - Sign',\r\n    'Mural - Sign',\r\n    'New Building - Commercial',\r\n    'Plumbing Sub-Permit - Commercial',\r\n    'Pool Plumbing Commercial (Sub-Permit)',\r\n    'Public Art Permit Application',\r\n    'Remodel - Commercial',\r\n    'Right-of-Way | ENG A - General',\r\n    'Sewer Cap for Demo - Commercial',\r\n    'Windows and Doors - Commercial',\r\n  ];\r\n  categories = [\r\n    'Primary',\r\n    'Sub Permit',\r\n    'Industrial',\r\n    'Municipal',\r\n    'Environmental',\r\n  ];\r\n  statuses = [\r\n    'Canceled',\r\n    'Complete',\r\n    'Expired',\r\n    'Fees Due',\r\n    'In Review',\r\n    'Issued',\r\n    'On Hold',\r\n    'Requires Resubmit',\r\n    'Requires Resubmit for Prescreen',\r\n    'Submitted - Online',\r\n    'Void',\r\n  ];\r\n  internalStatusArray =['Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed']\r\n  permitNumber: any;\r\n  isPermitMunicipalRequired: boolean = false;\r\n  iscityreviewLinkMunicipalRequired: boolean=false;\r\n  cityReviewLink:any ='';\r\n  syncedPermitData:any ={};\r\n  permitNumberValidationError: string = '';\r\n  isPermitNumberValidating: boolean = false;\r\n  constructor(\r\n    public modal: NgbActiveModal,\r\n    private fb: FormBuilder,\r\n    private projectsService: ProjectsService,\r\n    private permitsService: PermitsService,\r\n    private appService: AppService,\r\n    private httpUtilService: HttpUtilsService,\r\n    private customLayoutUtilsService: CustomLayoutUtilsService,\r\n    private cdr: ChangeDetectorRef\r\n  ) {\r\n    // Subscribe to loading state\r\n    this.httpUtilService.loadingSubject.subscribe((loading) => {\r\n      this.isLoading = loading;\r\n    });\r\n  }\r\n\r\n  ngOnInit() {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n    this.loadMunicipalities();\r\n    this.loadForm();\r\n    this.loadProjects();\r\n    this.setupPermitNumberValidation();\r\n    if (this.id !== 0) {\r\n      this.patchForm();\r\n    }\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    // Reset global loading state when component is destroyed\r\n    this.httpUtilService.loadingSubject.next(false);\r\n  }\r\n\r\n  loadForm() {\r\n    this.permitForm = this.fb.group({\r\n      projectId: ['', Validators.required],\r\n      permitName: ['', Validators.required],\r\n      permitNumber: ['', Validators.required],\r\n      permitCategory: ['', Validators.required],\r\n      permitType: ['', Validators.required],\r\n      description: [''],\r\n      permitReviewType: ['', Validators.required],\r\n      location: ['',Validators.required],\r\n      primaryContact: [''],\r\n      permitAppliedDate: ['', Validators.required],\r\n      permitExpirationDate: [''],\r\n      permitIssueDate: [''],\r\n      permitFinalDate: [''],\r\n      permitCompleteDate: [''],\r\n      permitStatus: ['', Validators.required],\r\n      internalReviewStatus: ['', Validators.required],\r\n      attentionReason: [''],\r\n      internalNotes: [''],\r\n      actionTaken: [''],\r\n      reviewResponsibleParty: ['', Validators.required],\r\n      permitMunicipality: [''],\r\n      // cityReviewLink: [''],\r\n    });\r\n  }\r\n\r\n  setupPermitNumberValidation() {\r\n    const permitNumberControl = this.permitForm.get('permitNumber');\r\n    if (permitNumberControl) {\r\n      // Only clear server-side validation while typing; don't call API here\r\n      permitNumberControl.valueChanges\r\n        .pipe(distinctUntilChanged())\r\n        .subscribe(() => {\r\n          this.permitNumberValidationError = '';\r\n          if (permitNumberControl.hasError('permitNumberExists')) {\r\n            const errors: any = { ...permitNumberControl.errors };\r\n            delete errors['permitNumberExists'];\r\n            permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  triggerPermitNumberValidation() {\r\n    const permitNumberControl = this.permitForm.get('permitNumber');\r\n    const projectIdControl = this.permitForm.get('projectId');\r\n    const permitNumber = permitNumberControl?.value;\r\n    const projectId = projectIdControl?.value;\r\n\r\n    const enteredNormalized = (permitNumber || '').toString().trim().toLowerCase();\r\n    const originalNormalized = (this.permitNumber || '').toString().trim().toLowerCase();\r\n\r\n    // If empty, or in edit mode and value hasn't changed, just clear any prior error and skip API\r\n    if (!enteredNormalized || (this.id !== 0 && enteredNormalized === originalNormalized)) {\r\n      this.isPermitNumberValidating = false;\r\n      this.permitNumberValidationError = '';\r\n      if (permitNumberControl?.hasError('permitNumberExists')) {\r\n        const errors: any = { ...permitNumberControl.errors };\r\n        delete errors['permitNumberExists'];\r\n        permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\r\n      }\r\n      return;\r\n    }\r\n\r\n    if (enteredNormalized && projectId) {\r\n      this.isPermitNumberValidating = true;\r\n      this.validatePermitNumber(permitNumber, projectId)\r\n        .pipe(map((res: any) => res?.responseData ?? res))\r\n        .subscribe((result: any) => {\r\n          this.isPermitNumberValidating = false;\r\n          if (result && result.exists) {\r\n            this.permitNumberValidationError = result.message;\r\n            permitNumberControl?.setErrors({ 'permitNumberExists': true });\r\n          } else {\r\n            this.permitNumberValidationError = '';\r\n            if (permitNumberControl?.hasError('permitNumberExists')) {\r\n              const errors: any = { ...permitNumberControl.errors };\r\n              delete errors['permitNumberExists'];\r\n              permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\r\n            }\r\n          }\r\n        });\r\n    }\r\n  }\r\n\r\n  validatePermitNumber(permitNumber: string, projectId: number) {\r\n    this.isPermitNumberValidating = true;\r\n    this.permitNumberValidationError = '';\r\n\r\n    const params = {\r\n      permitNumber: permitNumber,\r\n      projectId: projectId,\r\n      permitId: this.id !== 0 ? this.id : null // Include permitId for edit mode\r\n    };\r\n\r\n    return this.permitsService.validatePermitNumber(params).pipe(\r\n      catchError(() => {\r\n        this.isPermitNumberValidating = false;\r\n        return of({ exists: false, message: '' });\r\n      })\r\n    );\r\n  }\r\n\r\n  loadProjects() {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    const params = { paginate: false };\r\n    this.projectsService.getAllProjectsData(params).subscribe({\r\n      next: (response: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (response && response.responseData) {\r\n          this.projects = response.responseData.data;\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error loading projects:', error);\r\n        this.projects = [];\r\n      },\r\n    });\r\n  }\r\n\r\n  loadMunicipalities() {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    const params = { loggedinUser: this.loginUser.userId };\r\n    this.permitsService.getAllMunicipalities(params).subscribe({\r\n      next: (response: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (response && response.responseData) {\r\n          this.muncipalities = response.responseData.data;\r\n        }\r\n      },\r\n      error: (error: any) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error loading projects:', error);\r\n        this.muncipalities = [];\r\n      },\r\n    });\r\n  }\r\n\r\n  patchForm() {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.permitsService\r\n      .getPermit({ permitId: this.id, loggedInUserId: this.loginUser.userId })\r\n      .subscribe({\r\n        next: (permitResponse: any) => {\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          if (!permitResponse.isFault) {\r\n            let permitData = permitResponse.responseData.data;\r\n            this.permitNumber = permitData.permitNumber;\r\n            this.permitForm.patchValue({\r\n              projectId: permitData.projectId,\r\n              permitNumber: permitData.permitNumber,\r\n              permitReviewType: permitData.permitReviewType,\r\n              permitCategory: permitData.permitCategory,\r\n              permitType: permitData.permitType,\r\n              description: permitData.description,\r\n              permitName: permitData.permitName,\r\n              location: permitData.location,\r\n              internalReviewStatus:permitData.internalReviewStatus,\r\n              primaryContact: permitData.primaryContact,\r\n              permitAppliedDate: permitData.permitAppliedDate\r\n                ? this.formatDateForInput(permitData.permitAppliedDate)\r\n                : '',\r\n              permitExpirationDate: permitData.permitExpirationDate\r\n                ? this.formatDateForInput(permitData.permitExpirationDate)\r\n                : '',\r\n              permitIssueDate: permitData.permitIssueDate\r\n                ? this.formatDateForInput(permitData.permitIssueDate)\r\n                : '',\r\n                  permitFinalDate: permitData.permitFinalDate\r\n                ? this.formatDateForInput(permitData.permitFinalDate)\r\n                : '',\r\n              permitCompleteDate: permitData.permitCompleteDate\r\n                ? this.formatDateForInput(permitData.permitCompleteDate)\r\n                : '',\r\n              permitStatus: permitData.permitStatus,\r\n              attentionReason: permitData.attentionReason,\r\n              internalNotes: permitData.internalNotes,\r\n              actionTaken: permitData.actionTaken,\r\n              reviewResponsibleParty: permitData.reviewResponsibleParty,\r\n              permitMunicipality: permitData.permitMunicipality,\r\n              // cityReviewLink: permitData.cityReviewLink,\r\n            });\r\n            this.onPermitReviewTypeChange(permitData.permitReviewType)\r\n          } else {\r\n            console.warn(\r\n              'Permit response has isFault = true',\r\n              permitResponse.responseData\r\n            );\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          console.error('API call failed', err);\r\n        },\r\n      });\r\n  }\r\n\r\n  formatDateForInput(dateString: string): string {\r\n    if (!dateString) return '';\r\n\r\n    const date = new Date(dateString);\r\n    // Use local timezone to avoid date shifting\r\n    const year = date.getFullYear();\r\n    const month = String(date.getMonth() + 1).padStart(2, '0');\r\n    const day = String(date.getDate()).padStart(2, '0');\r\n\r\n    return `${year}-${month}-${day}`;\r\n  }\r\n\r\n  preparePermitData() {\r\n    const formData = this.permitForm.value;\r\n\r\n    let permitRequestData: any = {};\r\n    permitRequestData.projectId = formData.projectId;\r\n    permitRequestData.permitNumber = formData.permitNumber;\r\n    permitRequestData.permitReviewType = formData.permitReviewType;\r\n    permitRequestData.permitCategory = formData.permitCategory;\r\n    permitRequestData.permitType = formData.permitType;\r\n    permitRequestData.description = formData.description;\r\n    permitRequestData.location = formData.location;\r\n    permitRequestData.primaryContact = formData.primaryContact;\r\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\r\n    permitRequestData.permitExpirationDate =\r\n      formData.permitExpirationDate || null;\r\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\r\n    permitRequestData.permitFinalDate =\r\n      formData.permitFinalDate || null;\r\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\r\n    permitRequestData.permitStatus = formData.permitStatus;\r\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\r\n    permitRequestData.permitName = formData.permitName;\r\n    permitRequestData.attentionReason = formData.attentionReason;\r\n    permitRequestData.internalNotes = formData.internalNotes;\r\n    permitRequestData.actionTaken = formData.actionTaken;\r\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\r\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\r\n    permitRequestData.cityReviewLink = this.cityReviewLink;\r\n    permitRequestData.loggedInUserId = this.loginUser.userId;\r\n    permitRequestData.syncedPermitData = this.syncedPermitData;\r\n    const caseId =  this.syncedPermitData.EntityId ||  this.syncedPermitData.permitId ||  this.syncedPermitData.Id || null;\r\n    permitRequestData.permitEntityID = caseId\r\n    if (this.id !== 0) {\r\n      permitRequestData.permitId = this.id;\r\n    }\r\n\r\n    return permitRequestData;\r\n  }\r\n\r\n  save() {\r\n    let controls = this.permitForm.controls;\r\n    console.log('Permit Data:', this.permitForm.value);\r\n    if (this.permitForm.invalid) {\r\n      Object.keys(controls).forEach((controlName) =>\r\n        controls[controlName].markAsTouched()\r\n      );\r\n      this.customLayoutUtilsService.showError(\r\n        'Please fill all required fields',\r\n        ''\r\n      );\r\n      return;\r\n    }\r\n    let permitData: any = this.preparePermitData();\r\n    console.log('Permit Data:', permitData);\r\n    if (this.id === 0) {\r\n      this.create(permitData);\r\n    } else {\r\n      this.edit(permitData);\r\n    }\r\n  }\r\n\r\n  create(permitData: any) {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.permitsService.createPermit(permitData).subscribe((res: any) => {\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      if (!res.isFault) {\r\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n        this.passEntry.emit(true);\r\n        this.modal.close();\r\n      } else {\r\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\r\n        this.passEntry.emit(false);\r\n      }\r\n    });\r\n  }\r\n\r\n  edit(permitData: any) {\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.permitsService.updatePermit(permitData).subscribe((res) => {\r\n      this.httpUtilService.loadingSubject.next(false);\r\n      if (!res.isFault) {\r\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\r\n        this.passEntry.emit(true);\r\n        this.modal.close();\r\n      } else {\r\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\r\n        this.passEntry.emit(false);\r\n      }\r\n    });\r\n  }\r\n  showTab(tab: any, $event: any) {\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  goToNextTab() {\r\n    if (this.selectedTab === 'basic') {\r\n      this.selectedTab = 'details';\r\n    } else if (this.selectedTab === 'details') {\r\n      this.selectedTab = 'notes';\r\n    }\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  goToPreviousTab() {\r\n    if (this.selectedTab === 'notes') {\r\n      this.selectedTab = 'details';\r\n    } else if (this.selectedTab === 'details') {\r\n      this.selectedTab = 'basic';\r\n    }\r\n    this.cdr.markForCheck();\r\n  }\r\n\r\n  onProjectChange(event:any){\r\n     this.permitForm.patchValue({\r\n              location: event.projectLocation })\r\n    // console.log(\"project loacation\",event)\r\n    // Auto-fill Permit / Sub Project Name for Add mode when selecting a project\r\n    if (this.id === 0) {\r\n      const permitNameControl = this.permitForm.get('permitName');\r\n      const currentValue = (permitNameControl?.value || '').toString().trim();\r\n      const projectName = event?.projectName || event?.project?.projectName || '';\r\n      if (permitNameControl && projectName && (currentValue === '' || permitNameControl.pristine)) {\r\n        permitNameControl.setValue(projectName);\r\n        permitNameControl.markAsDirty();\r\n      }\r\n    }\r\n\r\n  }\r\n\r\nonPermitReviewTypeChange(event: any) {\r\n  const permitControl = this.permitForm.get('permitMunicipality');\r\n  // const cityReviewControl = this.permitForm.get('cityReviewLink');\r\n\r\n  if (event === 'External') {\r\n    this.isPermitMunicipalRequired = true;\r\n    // this.iscityreviewLinkMunicipalRequired = true;\r\n\r\n    permitControl?.setValidators([Validators.required]);\r\n    // cityReviewControl?.setValidators([Validators.required]);\r\n  } else {\r\n    this.isPermitMunicipalRequired = false;\r\n    // this.iscityreviewLinkMunicipalRequired = false;\r\n\r\n    permitControl?.clearValidators();\r\n    // cityReviewControl?.clearValidators();\r\n  }\r\n\r\n  permitControl?.updateValueAndValidity();\r\n  // cityReviewControl?.updateValueAndValidity();\r\n}\r\n\r\nonPermitMunicipalityChange(event:any){\r\n  console.log('event 0 ',event);\r\n  this.cityReviewLink= event.cityWebsiteLink+'#/permit/';\r\n  this.getSyncButtonDisableStatus()\r\n}\r\n\r\nonPermitMunicipalityClear(){\r\n  this.permitForm.patchValue({ permitMunicipality: null });\r\n  this.cityReviewLink = '';\r\n  this.getSyncButtonDisableStatus();\r\n}\r\n\r\nsyncPermitDetails(){\r\n   const formData = this.permitForm.value;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.permitsService\r\n      .getPermitDetails({ permitNumber: formData.permitNumber, municipalityId: formData.permitMunicipality })\r\n      .subscribe({\r\n        next: (permitResponse: any) => {\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          if (!permitResponse.isFault) {\r\n            let permitData = permitResponse.responseData.permit;\r\n            this.syncedPermitData = permitData\r\n            this.permitForm.patchValue({\r\n              permitType: permitData.permitType,\r\n              description: permitData.description,\r\n              location: permitData.address,\r\n              permitAppliedDate: permitData.applyDate\r\n                ? this.formatDateForInput(permitData.applyDate)\r\n                : '',\r\n              permitExpirationDate: permitData.expireDate\r\n                ? this.formatDateForInput(permitData.expireDate)\r\n                : '',\r\n              permitIssueDate: permitData.issueDate\r\n                ? this.formatDateForInput(permitData.issueDate)\r\n                : '',\r\n              permitFinalDate: permitData.finalDate\r\n                ? this.formatDateForInput(permitData.finalDate)\r\n                : '',\r\n              permitCompleteDate: permitData.completeDate\r\n                ? this.formatDateForInput(permitData.completeDate)\r\n                : '',\r\n              permitStatus: permitData.permitStatus,\r\n\r\n            });\r\n          } else {\r\n            console.warn(\r\n              'Permit response has isFault = true',\r\n              permitResponse.responseData.message\r\n            );\r\n          }\r\n        },\r\n        error: (err) => {\r\n          this.httpUtilService.loadingSubject.next(false);\r\n          console.error('API call failed', err);\r\n        },\r\n      });\r\n}\r\n\r\ngetSyncButtonDisableStatus(): boolean {\r\n  const reviewType = this.permitForm.get('permitReviewType')?.value;\r\n  const permitNumber = this.permitForm.get('permitNumber')?.value;\r\n  const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\r\n\r\n  const isExternal = reviewType === 'External';\r\n  const hasPermitNumber = !!permitNumber;\r\n  const hasPermitMunicipality = !!permitMunicipality;\r\n\r\n  console.log('isExternal ', isExternal)\r\n  console.log('hasPermitNumber ', hasPermitNumber)\r\n  console.log('hasPermitMunicipality ', hasPermitMunicipality)\r\n  // Disable if any of the conditions are not satisfied\r\n  return !(isExternal && hasPermitNumber && hasPermitMunicipality);\r\n}\r\n}\r\n", "<div class=\"modal-content h-auto\">\r\n  <div class=\"modal-header bg-light-primary\">\r\n    <div class=\"modal-title h5 fs-3\">\r\n      <ng-container> \r\n        <div *ngIf=\"id === 0\">Add Permit</div>\r\n        <div *ngIf=\"id !== 0\">Edit Permit - {{ permitNumber }}</div>\r\n      </ng-container>\r\n    </div>\r\n    <div class=\"float-right\">\r\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"modal.dismiss()\"></i>\r\n    </div>\r\n  </div>\r\n\r\n  <div class=\"modal-body\">\r\n    <!-- Loading overlay removed; global loader handles this -->\r\n\r\n    <div class=\"row\">\r\n      <div class=\"col-xl-12\">\r\n        <div class=\"d-flex\">\r\n          <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\">\r\n            <li class=\"nav-item\">\r\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'basic' }\" (click)=\"showTab('basic', $event)\">\r\n                Basic Info\r\n              </a>\r\n            </li>\r\n            <li class=\"nav-item\">\r\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'details' }\" (click)=\"showTab('details', $event)\">\r\n                Permit Details\r\n              </a>\r\n            </li>\r\n            <!-- <li class=\"nav-item\">\r\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\r\n                [ngClass]=\"{ active: selectedTab === 'notes' }\" (click)=\"showTab('notes', $event)\">\r\n                Notes/Actions\r\n              </a>\r\n            </li> -->\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <form class=\"form form-label-right\" [formGroup]=\"permitForm\">\r\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Project <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"projects\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"projectName\"\r\n              formControlName=\"projectId\" bindValue=\"projectId\" placeholder=\"Select Project\"\r\n              (change)=\"onProjectChange($event)\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('projectId')?.touched && permitForm.get('projectId')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('projectId')?.errors?.['required']\">\r\n                Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit / Sub Project Name <span class=\"text-danger\">*</span></label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitName\" />\r\n            <div *ngIf=\"permitForm.get('permitName')?.touched && permitForm.get('permitName')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitName')?.errors?.['required']\">\r\n                Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit # <span class=\"text-danger\">*</span></label>\r\n            <div class=\"position-relative\">\r\n              <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitNumber\" \r\n                     (blur)=\"triggerPermitNumberValidation()\"\r\n                     [class.is-invalid]=\"permitForm.get('permitNumber')?.invalid && permitForm.get('permitNumber')?.touched\" />\r\n              <div *ngIf=\"isPermitNumberValidating\" class=\"position-absolute top-50 end-0 translate-middle-y me-2\">\r\n                <div class=\"spinner-border spinner-border-sm text-primary\" role=\"status\">\r\n                  <span class=\"visually-hidden\">Validating...</span>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            <!-- Validation error messages -->\r\n            <div *ngIf=\"permitForm.get('permitNumber')?.touched && permitForm.get('permitNumber')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['required']\">\r\n                Required Field\r\n              </div>\r\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['permitNumberExists']\">\r\n                {{ permitNumberValidationError }}\r\n              </div>\r\n            </div>\r\n            <!-- Show validation error even if field is not touched but has error (e.g., after blur) -->\r\n            <div *ngIf=\"permitNumberValidationError && !permitForm.get('permitNumber')?.touched\"\r\n              class=\"text-danger mt-1 small\">\r\n              {{ permitNumberValidationError }}\r\n            </div>\r\n          </div>\r\n          <div  class=\"col-xl-4\">\r\n            <ng-container *ngIf=\"isHideInternalReviewStatus\">\r\n\r\n            <label class=\"fw-bold form-label mb-2\">Internal Review Status <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"internalStatusArray\" [clearable]=\"false\" [multiple]=\"false\"\r\n              formControlName=\"internalReviewStatus\" placeholder=\"Select Status\">\r\n            </ng-select>\r\n            <div\r\n              *ngIf=\"permitForm.get('internalReviewStatus')?.touched && permitForm.get('internalReviewStatus')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('internalReviewStatus')?.errors?.['required']\">\r\n                 Required Field\r\n              </div>\r\n            </div>\r\n            </ng-container>\r\n\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Category <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"categories\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitCategory\"\r\n              placeholder=\"Select Category\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitCategory')?.touched && permitForm.get('permitCategory')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitCategory')?.errors?.['required']\">\r\n                 Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-6\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Review Type<span\r\n                class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"reviewTypeArray\" formControlName=\"permitReviewType\"\r\n              (change)=\"onPermitReviewTypeChange($event)\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitReviewType')?.touched && permitForm.get('permitReviewType')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitReviewType')?.errors?.['required']\">\r\n                 Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-6\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Municipality (External Review)<span\r\n                *ngIf=\"isPermitMunicipalRequired\" class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"muncipalities\" [clearable]=\"true\" [multiple]=\"false\" bindLabel=\"cityName\"\r\n              formControlName=\"permitMunicipality\" bindValue=\"municipalityId\" placeholder=\"Select Project\"\r\n              (change)=\"onPermitMunicipalityChange($event)\" (clear)=\"onPermitMunicipalityClear()\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitMunicipality')?.touched && permitForm.get('permitMunicipality')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitMunicipality')?.errors?.['required']\">\r\n                 Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <!-- <i class=\"fas fa-sync-alt action-icon edit-icon\" [disabled]=\"getSyncButtonDisableStatus()\"  (click)=\"syncPermitDetails()\"\r\n                    title=\"Sync Review\"></i> -->\r\n\r\n        <div class=\"row mt-3\">\r\n          <div class=\"col-xl-12\">\r\n            <div class=\"text-muted small d-flex align-items-center\" style=\"white-space: normal;\">\r\n              <i class=\"fas fa-info-circle me-2\"></i>\r\n              <span>Permit # and Permit Municipality are required to sync permit details from the portal.</span>\r\n            </div>\r\n          </div>\r\n        </div><br>\r\n\r\n        <div *ngIf=\"permitForm.get('permitReviewType')?.value === 'External'\">\r\n          <div class=\"row mt-1\" *ngIf=\"!getSyncButtonDisableStatus()\">\r\n            <div class=\"col-xl-12 d-flex align-items-center\">\r\n              <div class=\"small\" style=\"white-space: normal;\">\r\n                The syncing of permit details may take up to 3 minutes.\r\n              </div> &nbsp;\r\n              <div>\r\n                <button type=\"button\" class=\"btn btn-primary btn-sm\"\r\n                        *ngIf=\"id === 0\"\r\n                        (click)=\"syncPermitDetails()\">\r\n                  <i class=\"fas fa-sync-alt\"></i> Sync\r\n                </button>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n      <ng-container *ngIf=\"selectedTab == 'details'\">\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Review Responsible Party<span class=\"text-danger\">*</span></label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"reviewResponsibleParty\" />\r\n            <div\r\n              *ngIf=\"permitForm.get('reviewResponsibleParty')?.touched && permitForm.get('reviewResponsibleParty')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('reviewResponsibleParty')?.errors?.['required']\">\r\n                Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Primary Contact</label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"primaryContact\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Status <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"statuses\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitStatus\"\r\n              placeholder=\"Select Status\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitStatus')?.touched && permitForm.get('permitStatus')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitStatus')?.errors?.['required']\">\r\n                 Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit Type <span class=\"text-danger\">*</span></label>\r\n            <ng-select [items]=\"permitTypes\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitType\"\r\n              placeholder=\"Select Type\">\r\n            </ng-select>\r\n            <div *ngIf=\"permitForm.get('permitType')?.touched && permitForm.get('permitType')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitType')?.errors?.['required']\">\r\n                Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Issue Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitIssueDate\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Applied Date <span class=\"text-danger\">*</span></label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitAppliedDate\" />\r\n            <div *ngIf=\"permitForm.get('permitAppliedDate')?.touched && permitForm.get('permitAppliedDate')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('permitAppliedDate')?.errors?.['required']\">\r\n                 Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n\r\n\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Expiration Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitExpirationDate\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Completed Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitCompleteDate\" />\r\n          </div>\r\n          <div class=\"col-xl-4\">\r\n            <label class=\"fw-bold form-label mb-2\">Final Date</label>\r\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitFinalDate\" />\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Location<span class=\"text-danger\">*</span></label>\r\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"location\" />\r\n            <div *ngIf=\"permitForm.get('location')?.touched && permitForm.get('location')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('location')?.errors?.['required']\">\r\n                 Required Field\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Description</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"description\"></textarea>\r\n          </div>\r\n        </div>\r\n      </ng-container>\r\n      <!-- <ng-container *ngIf=\"selectedTab == 'role'\">\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Permit City Review Link<span *ngIf=\"isPermitMunicipalRequired\"\r\n                class=\"text-danger\">*</span></label>\r\n            <input type=\"url\" class=\"form-control form-control-sm\" formControlName=\"cityReviewLink\" />\r\n            <div *ngIf=\"permitForm.get('cityReviewLink')?.touched && permitForm.get('cityReviewLink')?.invalid\"\r\n              class=\"text-danger mt-1 small\">\r\n              <div *ngIf=\"permitForm.get('cityReviewLink')?.errors?.['required']\">\r\n                Permit City Review Link is required\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n\r\n        </div>\r\n\r\n\r\n      </ng-container> -->\r\n      <!-- <ng-container *ngIf=\"selectedTab == 'notes'\">\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Attention Reason</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"attentionReason\"></textarea>\r\n          </div>\r\n        </div>\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Internal Notes</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"internalNotes\"></textarea>\r\n          </div>\r\n        </div>\r\n\r\n        <div class=\"row mt-4\">\r\n          <div class=\"col-xl-12\">\r\n            <label class=\"fw-bold form-label mb-2\">Action Taken</label>\r\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"actionTaken\"></textarea>\r\n          </div>\r\n        </div>\r\n      </ng-container> -->\r\n    </form>\r\n  </div>\r\n\r\n  <div class=\"modal-footer justify-content-between\">\r\n    <div>\r\n      <button *ngIf=\"selectedTab == 'details'\" type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate\"\r\n        (click)=\"goToPreviousTab()\">\r\n        Previous\r\n      </button>\r\n    </div>\r\n    <div>\r\n      <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate mr-2\" (click)=\"modal.dismiss()\">\r\n        Cancel</button>&nbsp;\r\n      <!-- *ngIf=\"selectedTab == 'notes'\"  -->\r\n      <button type=\"button\" class=\"btn btn-primary btn-sm\" *ngIf=\"selectedTab == 'details'\"\r\n        [disabled]=\"permitForm.invalid || isPermitNumberValidating\" (click)=\"save()\">\r\n        Save\r\n      </button>\r\n      <button *ngIf=\"selectedTab == 'basic'\" type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"goToNextTab()\">\r\n        Next\r\n      </button>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAAA,SAIEA,YAAY,QAIP,eAAe;AACtB,SAAiCC,UAAU,QAAyB,gBAAgB;AAQpF,SAAuBC,oBAAoB,EAAaC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAC/F,SAASC,EAAE,QAAQ,MAAM;;;;;;;;;;;;;;;;ICdjBC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACtCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,YAAA,KAAgC;;;;;IAiDhDP,EAAA,CAAAC,cAAA,UAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAC,yDAAA,iBAA+D;IAGjET,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAuD;;;;;IAa7Dd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAO,0DAAA,iBAAgE;IAGlEf,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAe1Dd,EAFJ,CAAAC,cAAA,cAAqG,cAC1B,eACzC;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;;;;;IAKNH,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAU,2BAAA,MACF;;;;;IAPFhB,EAAA,CAAAC,cAAA,cACiC;IAI/BD,EAHA,CAAAQ,UAAA,IAAAS,0DAAA,iBAAkE,IAAAC,0DAAA,iBAGU;IAG9ElB,EAAA,CAAAG,YAAA,EAAM;;;;;;IANEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;IAG1Dd,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAS,OAAA,GAAAb,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAM,OAAA,CAAAL,MAAA,kBAAAK,OAAA,CAAAL,MAAA,uBAAoE;;;;;IAK5Ed,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAU,2BAAA,MACF;;;;;IAYEhB,EAAA,CAAAC,cAAA,UAA0E;IACvED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAY,yEAAA,iBAA0E;IAG5EpB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAkE;IAAlEJ,EAAA,CAAAU,UAAA,UAAAS,OAAA,GAAAb,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAAM,OAAA,CAAAL,MAAA,kBAAAK,OAAA,CAAAL,MAAA,aAAkE;;;;;IAT1Ed,EAAA,CAAAqB,uBAAA,GAAiD;IAEjDrB,EAAA,CAAAC,cAAA,gBAAuC;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACxGH,EAAA,CAAAsB,SAAA,oBAEY;IACZtB,EAAA,CAAAQ,UAAA,IAAAe,mEAAA,kBAEiC;;;;;;IALtBvB,EAAA,CAAAI,SAAA,GAA6B;IAAqBJ,EAAlD,CAAAU,UAAA,UAAAJ,MAAA,CAAAkB,mBAAA,CAA6B,oBAAoB,mBAAmB;IAI5ExB,EAAA,CAAAI,SAAA,EAAwG;IAAxGJ,EAAA,CAAAU,UAAA,WAAAe,OAAA,GAAAnB,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAAY,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAnB,MAAA,CAAAM,UAAA,CAAAC,GAAA,2CAAAY,OAAA,CAAAE,OAAA,EAAwG;;;;;IAgBzG3B,EAAA,CAAAC,cAAA,UAAoE;IACjED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAoB,0DAAA,iBAAoE;IAGtE5B,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA4D;IAA5DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA4D;;;;;IAgBlEd,EAAA,CAAAC,cAAA,UAAsE;IACnED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAqB,0DAAA,iBAAsE;IAGxE7B,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA8D;;;;;IAMMd,EAAA,CAAAC,cAAA,eAClB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOhEH,EAAA,CAAAC,cAAA,UAAwE;IACrED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAsB,0DAAA,iBAAwE;IAG1E9B,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAgE;IAAhEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAgE;;;;;;IAyBpEd,EAAA,CAAAC,cAAA,iBAEsC;IAA9BD,EAAA,CAAA+B,UAAA,mBAAAC,4FAAA;MAAAhC,EAAA,CAAAiC,aAAA,CAAAC,GAAA;MAAA,MAAA5B,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAS9B,MAAA,CAAA+B,iBAAA,EAAmB;IAAA,EAAC;IACnCrC,EAAA,CAAAsB,SAAA,YAA+B;IAACtB,EAAA,CAAAE,MAAA,aAClC;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IARXH,EAFJ,CAAAC,cAAA,cAA4D,cACT,cACC;IAC9CD,EAAA,CAAAE,MAAA,gEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAACH,EAAA,CAAAE,MAAA,eACP;IAAAF,EAAA,CAAAC,cAAA,UAAK;IACHD,EAAA,CAAAQ,UAAA,IAAA8B,mEAAA,qBAEsC;IAK5CtC,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;;IANSH,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAiC,EAAA,OAAc;;;;;IAR/BvC,EAAA,CAAAC,cAAA,UAAsE;IACpED,EAAA,CAAAQ,UAAA,IAAAgC,0DAAA,kBAA4D;IAc9DxC,EAAA,CAAAG,YAAA,EAAM;;;;IAdmBH,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAU,UAAA,UAAAJ,MAAA,CAAAmC,0BAAA,GAAmC;;;;;;IAnI9DzC,EAAA,CAAAqB,uBAAA,GAA6C;IAGvCrB,EAFJ,CAAAC,cAAA,cAAsB,aACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAC,cAAA,oBAEqC;IAAnCD,EAAA,CAAA+B,UAAA,oBAAAW,0EAAAC,MAAA;MAAA3C,EAAA,CAAAiC,aAAA,CAAAW,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAU9B,MAAA,CAAAuC,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IACpC3C,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,IAAAsC,mDAAA,kBACiC;IAMrC9C,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3GH,EAAA,CAAAsB,SAAA,iBAAuF;IACvFtB,EAAA,CAAAQ,UAAA,KAAAuC,oDAAA,kBACiC;IAMrC/C,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAExFH,EADF,CAAAC,cAAA,eAA+B,iBAGoF;IAD1GD,EAAA,CAAA+B,UAAA,kBAAAiB,qEAAA;MAAAhD,EAAA,CAAAiC,aAAA,CAAAW,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAQ9B,MAAA,CAAA2C,6BAAA,EAA+B;IAAA,EAAC;IAD/CjD,EAAA,CAAAG,YAAA,EAEiH;IACjHH,EAAA,CAAAQ,UAAA,KAAA0C,oDAAA,kBAAqG;IAKvGlD,EAAA,CAAAG,YAAA,EAAM;IAYNH,EAVA,CAAAQ,UAAA,KAAA2C,oDAAA,kBACiC,KAAAC,oDAAA,kBAUA;IAGnCpD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAQ,UAAA,KAAA6C,6DAAA,0BAAiD;IAenDrD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACjGH,EAAA,CAAAsB,SAAA,qBAEY;IACZtB,EAAA,CAAAQ,UAAA,KAAA8C,oDAAA,kBACiC;IAOrCtD,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAC,cAAA,gBACjC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACxCH,EAAA,CAAAC,cAAA,qBAC8C;IAA5CD,EAAA,CAAA+B,UAAA,oBAAAwB,2EAAAZ,MAAA;MAAA3C,EAAA,CAAAiC,aAAA,CAAAW,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAU9B,MAAA,CAAAkD,wBAAA,CAAAb,MAAA,CAAgC;IAAA,EAAC;IAC7C3C,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAAiD,oDAAA,kBACiC;IAKnCzD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAQ,UAAA,KAAAkD,qDAAA,mBAClB;IAAQ1D,EAAA,CAAAG,YAAA,EAAQ;IAC1EH,EAAA,CAAAC,cAAA,qBAEsF;IAAtCD,EAA9C,CAAA+B,UAAA,oBAAA4B,2EAAAhB,MAAA;MAAA3C,EAAA,CAAAiC,aAAA,CAAAW,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAU9B,MAAA,CAAAsD,0BAAA,CAAAjB,MAAA,CAAkC;IAAA,EAAC,mBAAAkB,0EAAA;MAAA7D,EAAA,CAAAiC,aAAA,CAAAW,GAAA;MAAA,MAAAtC,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAU9B,MAAA,CAAAwD,yBAAA,EAA2B;IAAA,EAAC;IACrF9D,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAAuD,oDAAA,kBACiC;IAMrC/D,EADE,CAAAG,YAAA,EAAM,EACF;IAMFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,eACgE;IACnFD,EAAA,CAAAsB,SAAA,aAAuC;IACvCtB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,6FAAqF;IAGjGF,EAHiG,CAAAG,YAAA,EAAO,EAC9F,EACF,EACF;IAAAH,EAAA,CAAAsB,SAAA,UAAI;IAEVtB,EAAA,CAAAQ,UAAA,KAAAwD,oDAAA,iBAAsE;;;;;;;;;;;;;;IA9HvDhE,EAAA,CAAAI,SAAA,GAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAA2D,QAAA,CAAkB,oBAAoB,mBAAmB;IAI9DjE,EAAA,CAAAI,SAAA,EAAkF;IAAlFJ,EAAA,CAAAU,UAAA,WAAAwD,OAAA,GAAA5D,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAqD,OAAA,CAAAxC,OAAA,OAAAwC,OAAA,GAAA5D,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAqD,OAAA,CAAAvC,OAAA,EAAkF;IAalF3B,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAAe,OAAA,GAAAnB,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAY,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAnB,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAY,OAAA,CAAAE,OAAA,EAAoF;IAcjF3B,EAAA,CAAAI,SAAA,GAAuG;IAAvGJ,EAAA,CAAAmE,WAAA,iBAAAC,OAAA,GAAA9D,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuD,OAAA,CAAAzC,OAAA,OAAAyC,OAAA,GAAA9D,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuD,OAAA,CAAA1C,OAAA,EAAuG;IACxG1B,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAA+D,wBAAA,CAA8B;IAOhCrE,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAA4D,OAAA,GAAAhE,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAyD,OAAA,CAAA5C,OAAA,OAAA4C,OAAA,GAAAhE,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAyD,OAAA,CAAA3C,OAAA,EAAwF;IAUxF3B,EAAA,CAAAI,SAAA,EAA6E;IAA7EJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAU,2BAAA,OAAAuD,OAAA,GAAAjE,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAA0D,OAAA,CAAA7C,OAAA,EAA6E;IAMpE1B,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAkE,0BAAA,CAAgC;IAkBpCxE,EAAA,CAAAI,SAAA,GAAoB;IAAqBJ,EAAzC,CAAAU,UAAA,UAAAJ,MAAA,CAAAmE,UAAA,CAAoB,oBAAoB,mBAAmB;IAGhEzE,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAAU,UAAA,WAAAgE,QAAA,GAAApE,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA6D,QAAA,CAAAhD,OAAA,OAAAgD,QAAA,GAAApE,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA6D,QAAA,CAAA/C,OAAA,EAA4F;IAavF3B,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAU,UAAA,UAAAJ,MAAA,CAAAqE,eAAA,CAAyB;IAG9B3E,EAAA,CAAAI,SAAA,EAAgG;IAAhGJ,EAAA,CAAAU,UAAA,WAAAkE,QAAA,GAAAtE,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA+D,QAAA,CAAAlD,OAAA,OAAAkD,QAAA,GAAAtE,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA+D,QAAA,CAAAjD,OAAA,EAAgG;IASjG3B,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAuE,yBAAA,CAA+B;IACzB7E,EAAA,CAAAI,SAAA,EAAuB;IAAoBJ,EAA3C,CAAAU,UAAA,UAAAJ,MAAA,CAAAwE,aAAA,CAAuB,mBAAmB,mBAAmB;IAIlE9E,EAAA,CAAAI,SAAA,EAAoG;IAApGJ,EAAA,CAAAU,UAAA,WAAAqE,QAAA,GAAAzE,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAkE,QAAA,CAAArD,OAAA,OAAAqD,QAAA,GAAAzE,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAkE,QAAA,CAAApD,OAAA,EAAoG;IAoBxG3B,EAAA,CAAAI,SAAA,GAA8D;IAA9DJ,EAAA,CAAAU,UAAA,WAAAsE,QAAA,GAAA1E,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAmE,QAAA,CAAAC,KAAA,iBAA8D;;;;;IAyB9DjF,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA0E,yDAAA,iBAA4E;IAG9ElF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAoE;;;;;IAgB1Ed,EAAA,CAAAC,cAAA,UAAkE;IAC/DD,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA2E,0DAAA,iBAAkE;IAGpEnF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;;;;;IAehEd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA4E,0DAAA,iBAAgE;IAGlEpF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAc9Dd,EAAA,CAAAC,cAAA,UAAuE;IACpED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA6E,0DAAA,iBAAuE;IAGzErF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA+D;IAA/DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA+D;;;;;IA4BrEd,EAAA,CAAAC,cAAA,UAA8D;IAC3DD,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA8E,0DAAA,iBAA8D;IAGhEtF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAsD;;;;;IAjFpEd,EAAA,CAAAqB,uBAAA,GAA+C;IAGzCrB,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzGH,EAAA,CAAAsB,SAAA,gBAAmG;IACnGtB,EAAA,CAAAQ,UAAA,IAAA+E,mDAAA,kBAEiC;IAKnCvF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAsB,SAAA,iBAA2F;IAC7FtB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAAsB,SAAA,qBAEY;IACZtB,EAAA,CAAAQ,UAAA,KAAAgF,oDAAA,kBACiC;IAOrCxF,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC7FH,EAAA,CAAAsB,SAAA,qBAEY;IACZtB,EAAA,CAAAQ,UAAA,KAAAiF,oDAAA,kBACiC;IAKnCzF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAsB,SAAA,iBAA4F;IAC9FtB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAsB,SAAA,iBAA8F;IAC9FtB,EAAA,CAAAQ,UAAA,KAAAkF,oDAAA,kBACiC;IAMrC1F,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAJJ,CAAAC,cAAA,eAAsB,eAGE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAsB,SAAA,iBAAiG;IACnGtB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAsB,SAAA,iBAA+F;IACjGtB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAsB,SAAA,iBAA4F;IAEhGtB,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAsB,SAAA,iBAAqF;IACrFtB,EAAA,CAAAQ,UAAA,KAAAmF,oDAAA,kBACiC;IAMrC3F,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAsB,SAAA,oBAAiG;IAErGtB,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;IAtFCH,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAU,UAAA,WAAAkF,OAAA,GAAAtF,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAA+E,OAAA,CAAAlE,OAAA,OAAAkE,OAAA,GAAAtF,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAA+E,OAAA,CAAAjE,OAAA,EAA4G;IAapG3B,EAAA,CAAAI,SAAA,IAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAAuF,QAAA,CAAkB,oBAAoB,mBAAmB;IAG9D7F,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAAe,OAAA,GAAAnB,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAY,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAnB,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAY,OAAA,CAAAE,OAAA,EAAwF;IAYnF3B,EAAA,CAAAI,SAAA,GAAqB;IAAqBJ,EAA1C,CAAAU,UAAA,UAAAJ,MAAA,CAAAwF,WAAA,CAAqB,oBAAoB,mBAAmB;IAGjE9F,EAAA,CAAAI,SAAA,EAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAA6D,OAAA,GAAAjE,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAA0D,OAAA,CAAA7C,OAAA,OAAA6C,OAAA,GAAAjE,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAA0D,OAAA,CAAA5C,OAAA,EAAoF;IAcpF3B,EAAA,CAAAI,SAAA,IAAkG;IAAlGJ,EAAA,CAAAU,UAAA,WAAAqF,QAAA,GAAAzF,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAkF,QAAA,CAAArE,OAAA,OAAAqE,QAAA,GAAAzF,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAkF,QAAA,CAAApE,OAAA,EAAkG;IA4BlG3B,EAAA,CAAAI,SAAA,IAAgF;IAAhFJ,EAAA,CAAAU,UAAA,WAAAsF,QAAA,GAAA1F,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAmF,QAAA,CAAAtE,OAAA,OAAAsE,QAAA,GAAA1F,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAmF,QAAA,CAAArE,OAAA,EAAgF;;;;;;IA+D5F3B,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAA+B,UAAA,mBAAAkE,gEAAA;MAAAjG,EAAA,CAAAiC,aAAA,CAAAiE,GAAA;MAAA,MAAA5F,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAS9B,MAAA,CAAA6F,eAAA,EAAiB;IAAA,EAAC;IAC3BnG,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBAC+E;IAAjBD,EAAA,CAAA+B,UAAA,mBAAAqE,gEAAA;MAAApG,EAAA,CAAAiC,aAAA,CAAAoE,GAAA;MAAA,MAAA/F,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAS9B,MAAA,CAAAgG,IAAA,EAAM;IAAA,EAAC;IAC5EtG,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAU,UAAA,aAAAJ,MAAA,CAAAM,UAAA,CAAAe,OAAA,IAAArB,MAAA,CAAA+D,wBAAA,CAA2D;;;;;;IAG7DrE,EAAA,CAAAC,cAAA,iBAA4G;IAAxBD,EAAA,CAAA+B,UAAA,mBAAAwE,gEAAA;MAAAvG,EAAA,CAAAiC,aAAA,CAAAuE,GAAA;MAAA,MAAAlG,MAAA,GAAAN,EAAA,CAAAmC,aAAA;MAAA,OAAAnC,EAAA,CAAAoC,WAAA,CAAS9B,MAAA,CAAAmG,WAAA,EAAa;IAAA,EAAC;IACzGzG,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADpUf,OAAM,MAAOuG,oBAAoB;EAoFtBC,KAAA;EACCC,EAAA;EACAC,eAAA;EACAC,cAAA;EACAC,UAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,GAAA;EA1FD3E,EAAE,GAAW,CAAC,CAAC,CAAC;EAChBiC,0BAA0B,GAAY,IAAI,CAAC,CAAC;EAC5C2C,MAAM,CAAM,CAAC;EACZC,SAAS,GAAsB,IAAI1H,YAAY,EAAE;EAE3DkB,UAAU;EACVqD,QAAQ,GAAU,EAAE;EACpBU,eAAe,GAAU,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;EACzD0C,SAAS,GAAQ,EAAE;EACnBC,SAAS,GAAY,KAAK;EAC1BxC,aAAa,GAAQ,EAAE;EACvByC,WAAW,GAAQ,OAAO;EAC1B;EACAzB,WAAW,GAAG,CACZ,oCAAoC,EACpC,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,qCAAqC,EACrC,oCAAoC,EACpC,0CAA0C,EAC1C,2BAA2B,EAC3B,uCAAuC,EACvC,6CAA6C,EAC7C,yBAAyB,EACzB,0CAA0C,EAC1C,oCAAoC,EACpC,4DAA4D,EAC5D,oBAAoB,EACpB,mBAAmB,EACnB,wCAAwC,EACxC,8BAA8B,EAC9B,6BAA6B,EAC7B,iCAAiC,EACjC,yBAAyB,EACzB,qBAAqB,EACrB,wBAAwB,EACxB,0BAA0B,EAC1B,qCAAqC,EACrC,yBAAyB,EACzB,4CAA4C,EAC5C,0BAA0B,EAC1B,oCAAoC,EACpC,iBAAiB,EACjB,cAAc,EACd,2BAA2B,EAC3B,kCAAkC,EAClC,uCAAuC,EACvC,+BAA+B,EAC/B,sBAAsB,EACtB,gCAAgC,EAChC,iCAAiC,EACjC,gCAAgC,CACjC;EACDrB,UAAU,GAAG,CACX,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,eAAe,CAChB;EACDoB,QAAQ,GAAG,CACT,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,QAAQ,EACR,SAAS,EACT,mBAAmB,EACnB,iCAAiC,EACjC,oBAAoB,EACpB,MAAM,CACP;EACDrE,mBAAmB,GAAE,CAAC,UAAU,EAAC,uBAAuB,EAAC,cAAc,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,mBAAmB,CAAC;EACjIjB,YAAY;EACZsE,yBAAyB,GAAY,KAAK;EAC1C2C,iCAAiC,GAAU,KAAK;EAChDC,cAAc,GAAM,EAAE;EACtBC,gBAAgB,GAAM,EAAE;EACxB1G,2BAA2B,GAAW,EAAE;EACxCqD,wBAAwB,GAAY,KAAK;EACzCsD,YACShB,KAAqB,EACpBC,EAAe,EACfC,eAAgC,EAChCC,cAA8B,EAC9BC,UAAsB,EACtBC,eAAiC,EACjCC,wBAAkD,EAClDC,GAAsB;IAPvB,KAAAP,KAAK,GAALA,KAAK;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,GAAG,GAAHA,GAAG;IAEX;IACA,IAAI,CAACF,eAAe,CAACY,cAAc,CAACC,SAAS,CAAEC,OAAO,IAAI;MACxD,IAAI,CAACR,SAAS,GAAGQ,OAAO;IAC1B,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACV,SAAS,GAAG,IAAI,CAACN,UAAU,CAACiB,eAAe,EAAE;IAClD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,2BAA2B,EAAE;IAClC,IAAI,IAAI,CAAC7F,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC8F,SAAS,EAAE;IAClB;EACF;EAEAC,WAAWA,CAAA;IACT;IACA,IAAI,CAACtB,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;EACjD;EAEAL,QAAQA,CAAA;IACN,IAAI,CAACtH,UAAU,GAAG,IAAI,CAACgG,EAAE,CAAC4B,KAAK,CAAC;MAC9BC,SAAS,EAAE,CAAC,EAAE,EAAE9I,UAAU,CAAC+I,QAAQ,CAAC;MACpCC,UAAU,EAAE,CAAC,EAAE,EAAEhJ,UAAU,CAAC+I,QAAQ,CAAC;MACrCnI,YAAY,EAAE,CAAC,EAAE,EAAEZ,UAAU,CAAC+I,QAAQ,CAAC;MACvCE,cAAc,EAAE,CAAC,EAAE,EAAEjJ,UAAU,CAAC+I,QAAQ,CAAC;MACzCG,UAAU,EAAE,CAAC,EAAE,EAAElJ,UAAU,CAAC+I,QAAQ,CAAC;MACrCI,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,gBAAgB,EAAE,CAAC,EAAE,EAAEpJ,UAAU,CAAC+I,QAAQ,CAAC;MAC3CM,QAAQ,EAAE,CAAC,EAAE,EAACrJ,UAAU,CAAC+I,QAAQ,CAAC;MAClCO,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,iBAAiB,EAAE,CAAC,EAAE,EAAEvJ,UAAU,CAAC+I,QAAQ,CAAC;MAC5CS,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,YAAY,EAAE,CAAC,EAAE,EAAE5J,UAAU,CAAC+I,QAAQ,CAAC;MACvCc,oBAAoB,EAAE,CAAC,EAAE,EAAE7J,UAAU,CAAC+I,QAAQ,CAAC;MAC/Ce,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,sBAAsB,EAAE,CAAC,EAAE,EAAEjK,UAAU,CAAC+I,QAAQ,CAAC;MACjDmB,kBAAkB,EAAE,CAAC,EAAE;MACvB;KACD,CAAC;EACJ;EAEAzB,2BAA2BA,CAAA;IACzB,MAAM0B,mBAAmB,GAAG,IAAI,CAAClJ,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC;IAC/D,IAAIiJ,mBAAmB,EAAE;MACvB;MACAA,mBAAmB,CAACC,YAAY,CAC7BC,IAAI,CAACpK,oBAAoB,EAAE,CAAC,CAC5BiI,SAAS,CAAC,MAAK;QACd,IAAI,CAAC7G,2BAA2B,GAAG,EAAE;QACrC,IAAI8I,mBAAmB,CAACG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;UACtD,MAAMnJ,MAAM,GAAQ;YAAE,GAAGgJ,mBAAmB,CAAChJ;UAAM,CAAE;UACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;UACnCgJ,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAACtJ,MAAM,CAAC,CAACuJ,MAAM,GAAG,CAAC,GAAGvJ,MAAM,GAAG,IAAI,CAAC;QAC/E;MACF,CAAC,CAAC;IACN;EACF;EAEAmC,6BAA6BA,CAAA;IAC3B,MAAM6G,mBAAmB,GAAG,IAAI,CAAClJ,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC;IAC/D,MAAMyJ,gBAAgB,GAAG,IAAI,CAAC1J,UAAU,CAACC,GAAG,CAAC,WAAW,CAAC;IACzD,MAAMN,YAAY,GAAGuJ,mBAAmB,EAAE7E,KAAK;IAC/C,MAAMwD,SAAS,GAAG6B,gBAAgB,EAAErF,KAAK;IAEzC,MAAMsF,iBAAiB,GAAG,CAAChK,YAAY,IAAI,EAAE,EAAEiK,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE;IAC9E,MAAMC,kBAAkB,GAAG,CAAC,IAAI,CAACpK,YAAY,IAAI,EAAE,EAAEiK,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE;IAEpF;IACA,IAAI,CAACH,iBAAiB,IAAK,IAAI,CAAChI,EAAE,KAAK,CAAC,IAAIgI,iBAAiB,KAAKI,kBAAmB,EAAE;MACrF,IAAI,CAACtG,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACrD,2BAA2B,GAAG,EAAE;MACrC,IAAI8I,mBAAmB,EAAEG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QACvD,MAAMnJ,MAAM,GAAQ;UAAE,GAAGgJ,mBAAmB,CAAChJ;QAAM,CAAE;QACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;QACnCgJ,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAACtJ,MAAM,CAAC,CAACuJ,MAAM,GAAG,CAAC,GAAGvJ,MAAM,GAAG,IAAI,CAAC;MAC/E;MACA;IACF;IAEA,IAAIyJ,iBAAiB,IAAI9B,SAAS,EAAE;MAClC,IAAI,CAACpE,wBAAwB,GAAG,IAAI;MACpC,IAAI,CAACuG,oBAAoB,CAACrK,YAAY,EAAEkI,SAAS,CAAC,CAC/CuB,IAAI,CAAClK,GAAG,CAAE+K,GAAQ,IAAKA,GAAG,EAAEC,YAAY,IAAID,GAAG,CAAC,CAAC,CACjDhD,SAAS,CAAEkD,MAAW,IAAI;QACzB,IAAI,CAAC1G,wBAAwB,GAAG,KAAK;QACrC,IAAI0G,MAAM,IAAIA,MAAM,CAACC,MAAM,EAAE;UAC3B,IAAI,CAAChK,2BAA2B,GAAG+J,MAAM,CAACE,OAAO;UACjDnB,mBAAmB,EAAEI,SAAS,CAAC;YAAE,oBAAoB,EAAE;UAAI,CAAE,CAAC;QAChE,CAAC,MAAM;UACL,IAAI,CAAClJ,2BAA2B,GAAG,EAAE;UACrC,IAAI8I,mBAAmB,EAAEG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;YACvD,MAAMnJ,MAAM,GAAQ;cAAE,GAAGgJ,mBAAmB,CAAChJ;YAAM,CAAE;YACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;YACnCgJ,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAACtJ,MAAM,CAAC,CAACuJ,MAAM,GAAG,CAAC,GAAGvJ,MAAM,GAAG,IAAI,CAAC;UAC/E;QACF;MACF,CAAC,CAAC;IACN;EACF;EAEA8J,oBAAoBA,CAACrK,YAAoB,EAAEkI,SAAiB;IAC1D,IAAI,CAACpE,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACrD,2BAA2B,GAAG,EAAE;IAErC,MAAMkK,MAAM,GAAG;MACb3K,YAAY,EAAEA,YAAY;MAC1BkI,SAAS,EAAEA,SAAS;MACpB0C,QAAQ,EAAE,IAAI,CAAC5I,EAAE,KAAK,CAAC,GAAG,IAAI,CAACA,EAAE,GAAG,IAAI,CAAC;KAC1C;IAED,OAAO,IAAI,CAACuE,cAAc,CAAC8D,oBAAoB,CAACM,MAAM,CAAC,CAAClB,IAAI,CAC1DnK,UAAU,CAAC,MAAK;MACd,IAAI,CAACwE,wBAAwB,GAAG,KAAK;MACrC,OAAOtE,EAAE,CAAC;QAAEiL,MAAM,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE,CAAC;IAC3C,CAAC,CAAC,CACH;EACH;EAEA9C,YAAYA,CAAA;IACV,IAAI,CAACnB,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAM2C,MAAM,GAAG;MAAEE,QAAQ,EAAE;IAAK,CAAE;IAClC,IAAI,CAACvE,eAAe,CAACwE,kBAAkB,CAACH,MAAM,CAAC,CAACrD,SAAS,CAAC;MACxDU,IAAI,EAAG+C,QAAa,IAAI;QACtB,IAAI,CAACtE,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI+C,QAAQ,IAAIA,QAAQ,CAACR,YAAY,EAAE;UACrC,IAAI,CAAC7G,QAAQ,GAAGqH,QAAQ,CAACR,YAAY,CAACS,IAAI;QAC5C;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACxE,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/CkD,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACvH,QAAQ,GAAG,EAAE;MACpB;KACD,CAAC;EACJ;EAEAgE,kBAAkBA,CAAA;IAChB,IAAI,CAACjB,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAM2C,MAAM,GAAG;MAAEQ,YAAY,EAAE,IAAI,CAACrE,SAAS,CAACsE;IAAM,CAAE;IACtD,IAAI,CAAC7E,cAAc,CAAC8E,oBAAoB,CAACV,MAAM,CAAC,CAACrD,SAAS,CAAC;MACzDU,IAAI,EAAG+C,QAAa,IAAI;QACtB,IAAI,CAACtE,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI+C,QAAQ,IAAIA,QAAQ,CAACR,YAAY,EAAE;UACrC,IAAI,CAAChG,aAAa,GAAGwG,QAAQ,CAACR,YAAY,CAACS,IAAI;QACjD;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACxE,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/CkD,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC1G,aAAa,GAAG,EAAE;MACzB;KACD,CAAC;EACJ;EAEAuD,SAASA,CAAA;IACP,IAAI,CAACrB,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzB,cAAc,CAChB+E,SAAS,CAAC;MAAEV,QAAQ,EAAE,IAAI,CAAC5I,EAAE;MAAEuJ,cAAc,EAAE,IAAI,CAACzE,SAAS,CAACsE;IAAM,CAAE,CAAC,CACvE9D,SAAS,CAAC;MACTU,IAAI,EAAGwD,cAAmB,IAAI;QAC5B,IAAI,CAAC/E,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACwD,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAACjB,YAAY,CAACS,IAAI;UACjD,IAAI,CAAChL,YAAY,GAAG0L,UAAU,CAAC1L,YAAY;UAC3C,IAAI,CAACK,UAAU,CAACsL,UAAU,CAAC;YACzBzD,SAAS,EAAEwD,UAAU,CAACxD,SAAS;YAC/BlI,YAAY,EAAE0L,UAAU,CAAC1L,YAAY;YACrCwI,gBAAgB,EAAEkD,UAAU,CAAClD,gBAAgB;YAC7CH,cAAc,EAAEqD,UAAU,CAACrD,cAAc;YACzCC,UAAU,EAAEoD,UAAU,CAACpD,UAAU;YACjCC,WAAW,EAAEmD,UAAU,CAACnD,WAAW;YACnCH,UAAU,EAAEsD,UAAU,CAACtD,UAAU;YACjCK,QAAQ,EAAEiD,UAAU,CAACjD,QAAQ;YAC7BQ,oBAAoB,EAACyC,UAAU,CAACzC,oBAAoB;YACpDP,cAAc,EAAEgD,UAAU,CAAChD,cAAc;YACzCC,iBAAiB,EAAE+C,UAAU,CAAC/C,iBAAiB,GAC3C,IAAI,CAACiD,kBAAkB,CAACF,UAAU,CAAC/C,iBAAiB,CAAC,GACrD,EAAE;YACNC,oBAAoB,EAAE8C,UAAU,CAAC9C,oBAAoB,GACjD,IAAI,CAACgD,kBAAkB,CAACF,UAAU,CAAC9C,oBAAoB,CAAC,GACxD,EAAE;YACNC,eAAe,EAAE6C,UAAU,CAAC7C,eAAe,GACvC,IAAI,CAAC+C,kBAAkB,CAACF,UAAU,CAAC7C,eAAe,CAAC,GACnD,EAAE;YACFC,eAAe,EAAE4C,UAAU,CAAC5C,eAAe,GAC3C,IAAI,CAAC8C,kBAAkB,CAACF,UAAU,CAAC5C,eAAe,CAAC,GACnD,EAAE;YACNC,kBAAkB,EAAE2C,UAAU,CAAC3C,kBAAkB,GAC7C,IAAI,CAAC6C,kBAAkB,CAACF,UAAU,CAAC3C,kBAAkB,CAAC,GACtD,EAAE;YACNC,YAAY,EAAE0C,UAAU,CAAC1C,YAAY;YACrCE,eAAe,EAAEwC,UAAU,CAACxC,eAAe;YAC3CC,aAAa,EAAEuC,UAAU,CAACvC,aAAa;YACvCC,WAAW,EAAEsC,UAAU,CAACtC,WAAW;YACnCC,sBAAsB,EAAEqC,UAAU,CAACrC,sBAAsB;YACzDC,kBAAkB,EAAEoC,UAAU,CAACpC;YAC/B;WACD,CAAC;UACF,IAAI,CAACrG,wBAAwB,CAACyI,UAAU,CAAClD,gBAAgB,CAAC;QAC5D,CAAC,MAAM;UACL0C,OAAO,CAACW,IAAI,CACV,oCAAoC,EACpCL,cAAc,CAACjB,YAAY,CAC5B;QACH;MACF,CAAC;MACDU,KAAK,EAAGa,GAAG,IAAI;QACb,IAAI,CAACrF,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/CkD,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEa,GAAG,CAAC;MACvC;KACD,CAAC;EACN;EAEAF,kBAAkBA,CAACG,UAAkB;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC;IACA,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEAE,iBAAiBA,CAAA;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACtM,UAAU,CAACqE,KAAK;IAEtC,IAAIkI,iBAAiB,GAAQ,EAAE;IAC/BA,iBAAiB,CAAC1E,SAAS,GAAGyE,QAAQ,CAACzE,SAAS;IAChD0E,iBAAiB,CAAC5M,YAAY,GAAG2M,QAAQ,CAAC3M,YAAY;IACtD4M,iBAAiB,CAACpE,gBAAgB,GAAGmE,QAAQ,CAACnE,gBAAgB;IAC9DoE,iBAAiB,CAACvE,cAAc,GAAGsE,QAAQ,CAACtE,cAAc;IAC1DuE,iBAAiB,CAACtE,UAAU,GAAGqE,QAAQ,CAACrE,UAAU;IAClDsE,iBAAiB,CAACrE,WAAW,GAAGoE,QAAQ,CAACpE,WAAW;IACpDqE,iBAAiB,CAACnE,QAAQ,GAAGkE,QAAQ,CAAClE,QAAQ;IAC9CmE,iBAAiB,CAAClE,cAAc,GAAGiE,QAAQ,CAACjE,cAAc;IAC1DkE,iBAAiB,CAACjE,iBAAiB,GAAGgE,QAAQ,CAAChE,iBAAiB,IAAI,IAAI;IACxEiE,iBAAiB,CAAChE,oBAAoB,GACpC+D,QAAQ,CAAC/D,oBAAoB,IAAI,IAAI;IACvCgE,iBAAiB,CAAC/D,eAAe,GAAG8D,QAAQ,CAAC9D,eAAe,IAAI,IAAI;IACpE+D,iBAAiB,CAAC9D,eAAe,GAC/B6D,QAAQ,CAAC7D,eAAe,IAAI,IAAI;IAClC8D,iBAAiB,CAAC7D,kBAAkB,GAAG4D,QAAQ,CAAC5D,kBAAkB,IAAI,IAAI;IAC1E6D,iBAAiB,CAAC5D,YAAY,GAAG2D,QAAQ,CAAC3D,YAAY;IACtD4D,iBAAiB,CAAC3D,oBAAoB,GAAG0D,QAAQ,CAAC1D,oBAAoB;IACtE2D,iBAAiB,CAACxE,UAAU,GAAGuE,QAAQ,CAACvE,UAAU;IAClDwE,iBAAiB,CAAC1D,eAAe,GAAGyD,QAAQ,CAACzD,eAAe;IAC5D0D,iBAAiB,CAACzD,aAAa,GAAGwD,QAAQ,CAACxD,aAAa;IACxDyD,iBAAiB,CAACxD,WAAW,GAAGuD,QAAQ,CAACvD,WAAW;IACpDwD,iBAAiB,CAACvD,sBAAsB,GAAGsD,QAAQ,CAACtD,sBAAsB;IAC1EuD,iBAAiB,CAACtD,kBAAkB,GAAGqD,QAAQ,CAACrD,kBAAkB;IAClEsD,iBAAiB,CAAC1F,cAAc,GAAG,IAAI,CAACA,cAAc;IACtD0F,iBAAiB,CAACrB,cAAc,GAAG,IAAI,CAACzE,SAAS,CAACsE,MAAM;IACxDwB,iBAAiB,CAACzF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC1D,MAAM0F,MAAM,GAAI,IAAI,CAAC1F,gBAAgB,CAAC2F,QAAQ,IAAK,IAAI,CAAC3F,gBAAgB,CAACyD,QAAQ,IAAK,IAAI,CAACzD,gBAAgB,CAAC4F,EAAE,IAAI,IAAI;IACtHH,iBAAiB,CAACI,cAAc,GAAGH,MAAM;IACzC,IAAI,IAAI,CAAC7K,EAAE,KAAK,CAAC,EAAE;MACjB4K,iBAAiB,CAAChC,QAAQ,GAAG,IAAI,CAAC5I,EAAE;IACtC;IAEA,OAAO4K,iBAAiB;EAC1B;EAEA7G,IAAIA,CAAA;IACF,IAAIkH,QAAQ,GAAG,IAAI,CAAC5M,UAAU,CAAC4M,QAAQ;IACvC/B,OAAO,CAACgC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC7M,UAAU,CAACqE,KAAK,CAAC;IAClD,IAAI,IAAI,CAACrE,UAAU,CAACe,OAAO,EAAE;MAC3BwI,MAAM,CAACC,IAAI,CAACoD,QAAQ,CAAC,CAACE,OAAO,CAAEC,WAAW,IACxCH,QAAQ,CAACG,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAAC3G,wBAAwB,CAAC4G,SAAS,CACrC,iCAAiC,EACjC,EAAE,CACH;MACD;IACF;IACA,IAAI5B,UAAU,GAAQ,IAAI,CAACgB,iBAAiB,EAAE;IAC9CxB,OAAO,CAACgC,GAAG,CAAC,cAAc,EAAExB,UAAU,CAAC;IACvC,IAAI,IAAI,CAAC1J,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACuL,MAAM,CAAC7B,UAAU,CAAC;IACzB,CAAC,MAAM;MACL,IAAI,CAAC8B,IAAI,CAAC9B,UAAU,CAAC;IACvB;EACF;EAEA6B,MAAMA,CAAC7B,UAAe;IACpB,IAAI,CAACjF,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzB,cAAc,CAACkH,YAAY,CAAC/B,UAAU,CAAC,CAACpE,SAAS,CAAEgD,GAAQ,IAAI;MAClE,IAAI,CAAC7D,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACsC,GAAG,CAACmB,OAAO,EAAE;QAChB,IAAI,CAAC/E,wBAAwB,CAACgH,WAAW,CAACpD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC7D,SAAS,CAAC8G,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACvH,KAAK,CAACwH,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAAClH,wBAAwB,CAAC4G,SAAS,CAAChD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC7D,SAAS,CAAC8G,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EAEAH,IAAIA,CAAC9B,UAAe;IAClB,IAAI,CAACjF,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzB,cAAc,CAACsH,YAAY,CAACnC,UAAU,CAAC,CAACpE,SAAS,CAAEgD,GAAG,IAAI;MAC7D,IAAI,CAAC7D,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACsC,GAAG,CAACmB,OAAO,EAAE;QAChB,IAAI,CAAC/E,wBAAwB,CAACgH,WAAW,CAACpD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC7D,SAAS,CAAC8G,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACvH,KAAK,CAACwH,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAAClH,wBAAwB,CAAC4G,SAAS,CAAChD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC7D,SAAS,CAAC8G,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EACAG,OAAOA,CAACC,GAAQ,EAAE3L,MAAW;IAC3B,IAAI,CAAC4E,WAAW,GAAG+G,GAAG;IACtB,IAAI,CAACpH,GAAG,CAACqH,YAAY,EAAE;EACzB;EAEA9H,WAAWA,CAAA;IACT,IAAI,IAAI,CAACc,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACL,GAAG,CAACqH,YAAY,EAAE;EACzB;EAEApI,eAAeA,CAAA;IACb,IAAI,IAAI,CAACoB,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACL,GAAG,CAACqH,YAAY,EAAE;EACzB;EAEA1L,eAAeA,CAAC2L,KAAS;IACtB,IAAI,CAAC5N,UAAU,CAACsL,UAAU,CAAC;MAClBlD,QAAQ,EAAEwF,KAAK,CAACC;KAAiB,CAAC;IAC5C;IACA;IACA,IAAI,IAAI,CAAClM,EAAE,KAAK,CAAC,EAAE;MACjB,MAAMmM,iBAAiB,GAAG,IAAI,CAAC9N,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3D,MAAM8N,YAAY,GAAG,CAACD,iBAAiB,EAAEzJ,KAAK,IAAI,EAAE,EAAEuF,QAAQ,EAAE,CAACC,IAAI,EAAE;MACvE,MAAMmE,WAAW,GAAGJ,KAAK,EAAEI,WAAW,IAAIJ,KAAK,EAAEK,OAAO,EAAED,WAAW,IAAI,EAAE;MAC3E,IAAIF,iBAAiB,IAAIE,WAAW,KAAKD,YAAY,KAAK,EAAE,IAAID,iBAAiB,CAACI,QAAQ,CAAC,EAAE;QAC3FJ,iBAAiB,CAACK,QAAQ,CAACH,WAAW,CAAC;QACvCF,iBAAiB,CAACM,WAAW,EAAE;MACjC;IACF;EAEF;EAEFxL,wBAAwBA,CAACgL,KAAU;IACjC,MAAMS,aAAa,GAAG,IAAI,CAACrO,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC/D;IAEA,IAAI2N,KAAK,KAAK,UAAU,EAAE;MACxB,IAAI,CAAC3J,yBAAyB,GAAG,IAAI;MACrC;MAEAoK,aAAa,EAAEC,aAAa,CAAC,CAACvP,UAAU,CAAC+I,QAAQ,CAAC,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAC7D,yBAAyB,GAAG,KAAK;MACtC;MAEAoK,aAAa,EAAEE,eAAe,EAAE;MAChC;IACF;IAEAF,aAAa,EAAEG,sBAAsB,EAAE;IACvC;EACF;EAEAxL,0BAA0BA,CAAC4K,KAAS;IAClC/C,OAAO,CAACgC,GAAG,CAAC,UAAU,EAACe,KAAK,CAAC;IAC7B,IAAI,CAAC/G,cAAc,GAAE+G,KAAK,CAACa,eAAe,GAAC,WAAW;IACtD,IAAI,CAAC5M,0BAA0B,EAAE;EACnC;EAEAqB,yBAAyBA,CAAA;IACvB,IAAI,CAAClD,UAAU,CAACsL,UAAU,CAAC;MAAErC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IACxD,IAAI,CAACpC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAChF,0BAA0B,EAAE;EACnC;EAEAJ,iBAAiBA,CAAA;IACd,MAAM6K,QAAQ,GAAG,IAAI,CAACtM,UAAU,CAACqE,KAAK;IACrC,IAAI,CAAC+B,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzB,cAAc,CAChBwI,gBAAgB,CAAC;MAAE/O,YAAY,EAAE2M,QAAQ,CAAC3M,YAAY;MAAEgP,cAAc,EAAErC,QAAQ,CAACrD;IAAkB,CAAE,CAAC,CACtGhC,SAAS,CAAC;MACTU,IAAI,EAAGwD,cAAmB,IAAI;QAC5B,IAAI,CAAC/E,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACwD,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAACjB,YAAY,CAAC3D,MAAM;UACnD,IAAI,CAACO,gBAAgB,GAAGuE,UAAU;UAClC,IAAI,CAACrL,UAAU,CAACsL,UAAU,CAAC;YACzBrD,UAAU,EAAEoD,UAAU,CAACpD,UAAU;YACjCC,WAAW,EAAEmD,UAAU,CAACnD,WAAW;YACnCE,QAAQ,EAAEiD,UAAU,CAACuD,OAAO;YAC5BtG,iBAAiB,EAAE+C,UAAU,CAACwD,SAAS,GACnC,IAAI,CAACtD,kBAAkB,CAACF,UAAU,CAACwD,SAAS,CAAC,GAC7C,EAAE;YACNtG,oBAAoB,EAAE8C,UAAU,CAACyD,UAAU,GACvC,IAAI,CAACvD,kBAAkB,CAACF,UAAU,CAACyD,UAAU,CAAC,GAC9C,EAAE;YACNtG,eAAe,EAAE6C,UAAU,CAAC0D,SAAS,GACjC,IAAI,CAACxD,kBAAkB,CAACF,UAAU,CAAC0D,SAAS,CAAC,GAC7C,EAAE;YACNtG,eAAe,EAAE4C,UAAU,CAAC2D,SAAS,GACjC,IAAI,CAACzD,kBAAkB,CAACF,UAAU,CAAC2D,SAAS,CAAC,GAC7C,EAAE;YACNtG,kBAAkB,EAAE2C,UAAU,CAAC4D,YAAY,GACvC,IAAI,CAAC1D,kBAAkB,CAACF,UAAU,CAAC4D,YAAY,CAAC,GAChD,EAAE;YACNtG,YAAY,EAAE0C,UAAU,CAAC1C;WAE1B,CAAC;QACJ,CAAC,MAAM;UACLkC,OAAO,CAACW,IAAI,CACV,oCAAoC,EACpCL,cAAc,CAACjB,YAAY,CAACG,OAAO,CACpC;QACH;MACF,CAAC;MACDO,KAAK,EAAGa,GAAG,IAAI;QACb,IAAI,CAACrF,eAAe,CAACY,cAAc,CAACW,IAAI,CAAC,KAAK,CAAC;QAC/CkD,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEa,GAAG,CAAC;MACvC;KACD,CAAC;EACR;EAEA5J,0BAA0BA,CAAA;IACxB,MAAMqN,UAAU,GAAG,IAAI,CAAClP,UAAU,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAEoE,KAAK;IACjE,MAAM1E,YAAY,GAAG,IAAI,CAACK,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAEoE,KAAK;IAC/D,MAAM4E,kBAAkB,GAAG,IAAI,CAACjJ,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAEoE,KAAK;IAE3E,MAAM8K,UAAU,GAAGD,UAAU,KAAK,UAAU;IAC5C,MAAME,eAAe,GAAG,CAAC,CAACzP,YAAY;IACtC,MAAM0P,qBAAqB,GAAG,CAAC,CAACpG,kBAAkB;IAElD4B,OAAO,CAACgC,GAAG,CAAC,aAAa,EAAEsC,UAAU,CAAC;IACtCtE,OAAO,CAACgC,GAAG,CAAC,kBAAkB,EAAEuC,eAAe,CAAC;IAChDvE,OAAO,CAACgC,GAAG,CAAC,wBAAwB,EAAEwC,qBAAqB,CAAC;IAC5D;IACA,OAAO,EAAEF,UAAU,IAAIC,eAAe,IAAIC,qBAAqB,CAAC;EAClE;;qCAviBavJ,oBAAoB,EAAA1G,EAAA,CAAAkQ,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApQ,EAAA,CAAAkQ,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtQ,EAAA,CAAAkQ,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAxQ,EAAA,CAAAkQ,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1Q,EAAA,CAAAkQ,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAA5Q,EAAA,CAAAkQ,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA9Q,EAAA,CAAAkQ,iBAAA,CAAAa,EAAA,CAAAC,wBAAA,GAAAhR,EAAA,CAAAkQ,iBAAA,CAAAlQ,EAAA,CAAAiR,iBAAA;EAAA;;UAApBvK,oBAAoB;IAAAwK,SAAA;IAAAC,MAAA;MAAA5O,EAAA;MAAAiC,0BAAA;MAAA2C,MAAA;IAAA;IAAAiK,OAAA;MAAAhK,SAAA;IAAA;IAAAiK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCtB7B1R,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAAqB,uBAAA,GAAc;QAEZrB,EADA,CAAAQ,UAAA,IAAAoR,mCAAA,iBAAsB,IAAAC,mCAAA,iBACA;;QAE1B7R,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WACgD;QAA1BD,EAAA,CAAA+B,UAAA,mBAAA+P,iDAAA;UAAA,OAASH,GAAA,CAAAhL,KAAA,CAAAoL,OAAA,EAAe;QAAA,EAAC;QAE1E/R,EAF2E,CAAAG,YAAA,EAAI,EACvE,EACF;QAUMH,EARZ,CAAAC,cAAA,aAAwB,aAGL,cACQ,cACD,cACqF,cAChF,aAEkE;QAAnCD,EAAA,CAAA+B,UAAA,mBAAAiQ,kDAAArP,MAAA;UAAA,OAASgP,GAAA,CAAAtD,OAAA,CAAQ,OAAO,EAAA1L,MAAA,CAAS;QAAA,EAAC;QAClF3C,EAAA,CAAAE,MAAA,oBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAEsE;QAArCD,EAAA,CAAA+B,UAAA,mBAAAkQ,kDAAAtP,MAAA;UAAA,OAASgP,GAAA,CAAAtD,OAAA,CAAQ,SAAS,EAAA1L,MAAA,CAAS;QAAA,EAAC;QACtF3C,EAAA,CAAAE,MAAA,wBACF;QAWVF,EAXU,CAAAG,YAAA,EAAI,EACD,EAOF,EACD,EACF,EACF;QAENH,EAAA,CAAAC,cAAA,gBAA6D;QAoJ3DD,EAnJA,CAAAQ,UAAA,KAAA0R,6CAAA,4BAA6C,KAAAC,6CAAA,4BAmJE;QA0InDnS,EADE,CAAAG,YAAA,EAAO,EACH;QAGJH,EADF,CAAAC,cAAA,eAAkD,WAC3C;QACHD,EAAA,CAAAQ,UAAA,KAAA4R,uCAAA,qBAC8B;QAGhCpS,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,WAAK,kBAC4F;QAA1BD,EAAA,CAAA+B,UAAA,mBAAAsQ,uDAAA;UAAA,OAASV,GAAA,CAAAhL,KAAA,CAAAoL,OAAA,EAAe;QAAA,EAAC;QAC5F/R,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAAAH,EAAA,CAAAE,MAAA,eACjB;QAKAF,EAJA,CAAAQ,UAAA,KAAA8R,uCAAA,qBAC+E,KAAAC,uCAAA,qBAG6B;QAKlHvS,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;QA3VQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAiR,GAAA,CAAApP,EAAA,OAAc;QACdvC,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAiR,GAAA,CAAApP,EAAA,OAAc;QAiBZvC,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAwS,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAApK,WAAA,cAA+C;QAM/CvH,EAAA,CAAAI,SAAA,GAAiD;QAAjDJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAwS,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAApK,WAAA,gBAAiD;QAezBvH,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAU,UAAA,cAAAiR,GAAA,CAAA/Q,UAAA,CAAwB;QAC3CZ,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAiR,GAAA,CAAApK,WAAA,YAA4B;QAmJ5BvH,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAiR,GAAA,CAAApK,WAAA,cAA8B;QA8IpCvH,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAiR,GAAA,CAAApK,WAAA,cAA8B;QASevH,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAiR,GAAA,CAAApK,WAAA,cAA8B;QAI3EvH,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAiR,GAAA,CAAApK,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
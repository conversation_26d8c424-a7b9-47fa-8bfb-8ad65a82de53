import {Injectable} from '@angular/core';
import * as CryptoJS from 'crypto-js';

@Injectable({
  providedIn: 'root',
})

export class AppEncryptDecryptService {
  constructor() {
  }

  set(keys: any, value: any){
    return CryptoJS.AES.encrypt(value, keys).toString();
  }

  get(keys: any, value: any){
    return CryptoJS.AES.decrypt(value, keys).toString(CryptoJS.enc.Utf8);
  }

  getPassword(keys: any, value: any){
    const decryptedValue = CryptoJS.AES.decrypt(value, keys).toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedValue);
  }
}

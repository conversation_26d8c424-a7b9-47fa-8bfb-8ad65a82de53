<div class="modal-header">
  <h5 class="modal-title">{{review?.name}}</h5>
  <button type="button" class="btn-close" aria-label="Close" (click)="modal.dismiss()"></button>
</div>

<div class="modal-body ">

  <!-- Due Date / Completed Date Section -->
  <div class="dates-section">
    <div class="date-row">
      <div class="date-item">
        <label>Reviwer</label>
        <span class="date-value">{{ review?.reviewer }}</span>
      </div>
      <div class="date-item">
        <label>Status</label>
        <span class="date-value">{{ review?.status }}</span>
      </div>
      <div class="date-item">
        <label>Due Date</label>
        <span class="date-value">{{ review?.dueDate | date:'MM/dd/yyyy' }}</span>
      </div>
      <div class="date-item">
        <label>Completed Date</label>
        <span class="date-value">{{ review?.completedDate | date:'MM/dd/yyyy' }}</span>
      </div>
    </div>
  </div>

  <div class="card shadow-sm rounded-3">
    <!-- Card Header with Tabs -->
    <div class="card-header border-0 py-2 d-flex justify-content-between align-items-center" style="min-height: 30px !important;
    padding: 0 1.25rem !important;">
      <!-- Tabs -->
      <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-6 fw-bold flex-nowrap">
        <li class="nav-item">
          <a class="nav-link text-active-primary me-6 cursor-pointer fs-6"
            [ngClass]="{ active: selectedTab === 'corrections' }" (click)="showTab('corrections', $event)">
            Corrections / Comments
          </a>
        </li>
        <li class="nav-item">
          <a class="nav-link text-active-primary me-6 cursor-pointer fs-6"
            [ngClass]="{ active: selectedTab === 'review' }" (click)="showTab('review', $event)">
            Reviews
          </a>
        </li>
      </ul>
    </div>
    <!-- Card Body with Single Section -->
    <div class="card-body p-0">
      <!-- Internal Tab Content -->
      <ng-container *ngIf="selectedTab == 'corrections'">
        <!-- Corrections Section -->
        <div class="corrections-section" *ngIf="review?.corrections && review.corrections.length > 0">
          <h6 class="section-title">Corrections ({{ review.corrections.length }})</h6>

          <div class="correction-item" *ngFor="let correction of review.corrections; let i = index">
            <div class="correction-header">
              <span class="correction-number">{{ i + 1 }}</span>
              <span class="correction-action">Correction Type: {{ correction.CorrectionTypeName || 'General' }}</span>
              <span class="correction-action">Category: {{ correction.CorrectionCategoryName || 'General Correction'
                }}</span>
            </div>

            <div class="correction-content">
              <div class="correction-field">
                <label>Corrective Action</label>
                <div class="correction-comment">{{ correction.CorrectiveAction || '' }}</div>
              </div>
              <div class="correction-field">
                <label>Comment</label>
                <div class="correction-comment">{{ correction.Comments || 'No comment provided' }}</div>
              </div>

              <div class="correction-field" *ngIf="correction.Response">
                <label>Response</label>
                <div class="correction-response">{{ correction.Response }}</div>
              </div>

              <div class="correction-field" *ngIf="correction.ResolvedDate">
                <label>Resolved Date</label>
                <div class="correction-resolved">{{ correction.ResolvedDate | date:'MM/dd/yyyy' }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Comments Section (fallback when no corrections) -->
        <div class="comments-section"
          *ngIf="(!review?.corrections || review.corrections.length === 0) && review?.comments">
          <h6 class="section-title">Comments</h6>
          <div class="comment-content">
            <div class="comment-text">{{ review.comments }}</div>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="selectedTab == 'review'">
        <form [formGroup]="reviewForm" class="px-6 mb-10" (ngSubmit)="onSubmit()" novalidate>
          <!-- EOR / AOR / Owner Response -->
          <div class="row mt-4">
            <div class="col-xl-12">
              <label class="fw-bold form-label mb-2">EOR / AOR / Owner Response</label>
              <textarea formControlName="EORAOROwner_Response" rows="3" class="form-control form-control-sm"
                placeholder="Type here" [disabled]="isLoading"></textarea>
            </div>
          </div>

          <!-- Comment Responded By -->
          <div class="row mt-4">
            <div class="col-xl-12">
              <label class="fw-bold form-label mb-2">Comment Responded By</label>
              <input type="text" formControlName="commentResponsedBy" class="form-control form-control-sm"
                placeholder="Who responded to comments" [disabled]="isLoading" />
            </div>
          </div>
        </form>
      </ng-container>
    </div>
  </div>


  <!-- No Data Message -->
  <div class="no-data-section" *ngIf="(!review?.corrections || review.corrections.length === 0) && !review?.comments">
    <div class="no-data-message">
      <i class="fas fa-info-circle"></i>
      <span> No corrections or comments available for this review.</span>
    </div>
  </div>
</div>

<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="modal.close()">Close</button>
  <button type="button" class="btn btn-primary btn-sm btn-elevate me-2" (click)="getPdf()" [disabled]="isLoading">
    Download PDF
  </button>
  <button type="submit" class="btn btn-success btn-sm" [disabled]="isLoading" (click)="onSubmit()">
    <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
    Update
  </button>
</div>

import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChange } from '@angular/core';

@Component({
  selector: 'app-password-strength',
  templateUrl: './password-strength.component.html',
  styleUrls: ['./password-strength.component.scss']
})
export class PasswordStrengthComponent implements OnChanges {
  @Input() public passwordToCheck: string; //getting password form field
  @Input() public minLength: number; //getting length of password
  @Output() passwordStrength = new EventEmitter<boolean>(); //sending password strength value field
  bar0: any;//apply color for bar 1 based on password strength
  bar1: any;//apply color for bar 2 based on password strength
  bar2: any;//apply color for bar 3 based on password strength
  bar3: any;//apply color for bar 4 based on password strength
  array:any=[];//storing bar color function
  msg = ''; //getting value from switch function
  private colors = ['darkred', 'orangered', 'orange', 'yellowgreen']; //getting color for bars
  minLengthData : number; //storing minimum length for password

 //function for checking strength of given password 
  private static checkStrength(p:any, minLength: any) {
    let force = 0;
    const regex = /[$-/:-?{-~!"^_@`\[\]]/g;

    const lowerLetters = /[a-z]+/.test(p);
    const upperLetters = /[A-Z]+/.test(p);
    const numbers = /[0-9]+/.test(p);
    const symbols = regex.test(p);

    const flags = [lowerLetters, upperLetters, numbers, symbols];

    let passedMatches = 0;
    for (const flag of flags) {
      passedMatches += flag === true ? 1 : 0;
    }

    force += 2 * p.length + ((p.length >= 10) ? 1 : 0);
    force += passedMatches * 10;

    // short password
    force = (p.length <= minLength) ? Math.min(force, 10) : force;

    // poor variety of characters
    force = (passedMatches === 1) ? Math.min(force, 10) : force;
    force = (passedMatches === 2) ? Math.min(force, 20) : force;
    force = (passedMatches === 3) ? Math.min(force, 30) : force;
    force = (passedMatches === 4) ? Math.min(force, 40) : force;
    return force;
  }
 //function for checking strength of password and getting color of bar
  ngOnChanges(changes: { [propName: string]: SimpleChange }): void {
    this.minLengthData = this.minLength;
    const password = changes.passwordToCheck.currentValue.trim();
    this.setBarColors(4, '#DDD');
    if (password) {
      const c = this.getColor(PasswordStrengthComponent.checkStrength(password, this.minLengthData));
      this.setBarColors(c.idx, c.col);

      const pwdStrength = PasswordStrengthComponent.checkStrength(password, this.minLengthData);
      pwdStrength === 40 ? this.passwordStrength.emit(true) : this.passwordStrength.emit(false);

      switch (c.idx) {
        case 1:
          this.msg = 'Poor';
          break;
        case 2:
          this.msg = 'Not Good';
          break;
        case 3:
          this.msg = 'Average';
          break;
        case 4:
          this.msg = 'Good';
          break;
      }
    } else {
      this.msg = '';
    }
  }

 //function for getting color value and stored in index
 //param: s- getting strength of new and confirm password value 
  private getColor(s: number) {
    let idx = 0;
    if (s <= 10) {
        idx = 0;
    } else if (s <= 20) {
        idx = 1;
    } else if (s <= 30) {
        idx = 2;
    } else if (s <= 40) {
        idx = 3;
    } else {
        idx = 4;
    }
    return {
        idx: idx + 1,
        col: this.colors[idx]
    };
  }
   //function for setting colour for bar based on password strength
   //param:count - bar number, col-colour of particular bar
  private setBarColors(count: number, col:string) {
    for (let n= 0; n < count; n++) {
      // let num: = n.toString();
      this.array['bar'+n] = col
    }
  }


}



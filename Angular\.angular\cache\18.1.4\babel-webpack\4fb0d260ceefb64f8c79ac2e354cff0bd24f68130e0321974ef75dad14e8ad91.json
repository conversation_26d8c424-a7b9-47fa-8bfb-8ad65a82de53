{"ast": null, "code": "import { catchError, from, mergeMap, of } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class PermitsService {\n  http;\n  constructor(http) {\n    this.http = http;\n  }\n  handleError(operation = 'operation', result) {\n    return error => {\n      // TODO: send the error to remote logging infrastructure\n      console.error(error); // log to console instead\n      // Let the app keep running by returning an empty result.\n      return from(result);\n    };\n  }\n  getAllPermits(queryParams) {\n    const requestBody = {\n      pageSize: queryParams.pageSize,\n      pageNumber: queryParams.pageNumber,\n      sortField: queryParams.sortField,\n      sortOrder: queryParams.sortOrder,\n      paginate: true,\n      search: queryParams.filter?.search || '',\n      columnFilter: queryParams.filter?.columnFilter || []\n    };\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllPermits`, requestBody).pipe(mergeMap(res => {\n      return of(res);\n    }), catchError(err => {\n      console.error('Error in getAllPermits:', err);\n      return of({\n        error: {\n          isFault: true\n        },\n        message: 'Error retrieving Permits'\n      });\n    }));\n  }\n  getPermitsForKendoGrid(state) {\n    const requestBody = {\n      take: state.take || 10,\n      skip: state.skip || 0,\n      sort: state.sort || [],\n      filter: state.filter || {\n        logic: 'and',\n        filters: []\n      },\n      search: state.search || '',\n      loggedInUserId: state.loggedInUserId\n    };\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsForKendoGrid`, requestBody).pipe(mergeMap(res => {\n      return of(res);\n    }), catchError(err => {\n      console.error('Error in getPermitsForKendoGrid:', err);\n      return of({\n        data: [],\n        total: 0,\n        errors: ['Error retrieving Permits']\n      });\n    }));\n  }\n  createPermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/createPermit', data);\n  }\n  updatePermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/editPermit', data);\n  }\n  updatePermitInternalReviewStatus(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/updatePermitInternalReviewStatus', data);\n  }\n  getPermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/getPermitById', data);\n  }\n  deletePermit(data) {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/deletePermit', data);\n  }\n  searchPermits(searchTerm) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/searchPermits`, {\n      search: searchTerm\n    });\n  }\n  exportPermits(exportType, selectedIds) {\n    const requestBody = {\n      exportType: exportType,\n      selectedIds: selectedIds || []\n    };\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/exportPermits`, requestBody);\n  }\n  getAllMunicipalities(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllMunicipalities`, params);\n  }\n  getAllReviews(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllReviews`, params);\n  }\n  syncPermits(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/syncPermits`, params);\n  }\n  // Internal Reviews Methods\n  addInternalReview(data) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/addInternalReview`, data);\n  }\n  updateInternalReview(data) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateInternalReview`, data);\n  }\n  getInternalReviews(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getInternalReviews`, params);\n  }\n  updateExternalReview(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateExternalReview`, params);\n  }\n  getPermitDetails(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitDetails`, params);\n  }\n  editNotesAndActions(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/editNotesAndActions`, params);\n  }\n  validatePermitNumber(params) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/validatePermitNumber`, params);\n  }\n  getPermitsByProjectId(projectId) {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsByProjectId`, {\n      projectId\n    });\n  }\n  static ɵfac = function PermitsService_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitsService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: PermitsService,\n    factory: PermitsService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "names": ["catchError", "from", "mergeMap", "of", "AppSettings", "PermitsService", "http", "constructor", "handleError", "operation", "result", "error", "console", "getAllPermits", "queryParams", "requestBody", "pageSize", "pageNumber", "sortField", "sortOrder", "paginate", "search", "filter", "columnFilter", "post", "REST_ENDPOINT", "pipe", "res", "err", "<PERSON><PERSON><PERSON>", "message", "getPermitsForKendoGrid", "state", "take", "skip", "sort", "logic", "filters", "loggedInUserId", "data", "total", "errors", "createPermit", "updatePermit", "updatePermitInternalReviewStatus", "get<PERSON><PERSON><PERSON>", "deletePermit", "searchPermits", "searchTerm", "exportPermits", "exportType", "selectedIds", "getAllMunicipalities", "params", "getAllReviews", "syncPermits", "addInternalReview", "updateInternalReview", "getInternalReviews", "updateExternalReview", "getPermitDetails", "editNotesAndActions", "validatePermitNumber", "getPermitsByProjectId", "projectId", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\services\\permits.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\nimport { Injectable } from '@angular/core';\nimport { Observable, catchError, from, mergeMap, of } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class PermitsService {\n\n  constructor(\n    private http: HttpClient\n  ) { }\n\n  private handleError<T>(operation = 'operation', result?: any) {\n    return (error: any): Observable<any> => {\n      // TODO: send the error to remote logging infrastructure\n      console.error(error); // log to console instead\n\n      // Let the app keep running by returning an empty result.\n      return from(result);\n    };\n  }\n\n  public getAllPermits(queryParams: any): Observable<any> {\n    const requestBody = {\n      pageSize: queryParams.pageSize,\n      pageNumber: queryParams.pageNumber,\n      sortField: queryParams.sortField,\n      sortOrder: queryParams.sortOrder,\n      paginate: true,\n      search: queryParams.filter?.search || '',\n      columnFilter: queryParams.filter?.columnFilter || []\n    };\n\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllPermits`, requestBody)\n      .pipe(\n        mergeMap((res: any) => {\n          return of(res);\n        }),\n        catchError((err: any) => {\n          console.error('Error in getAllPermits:', err);\n          return of({\n            error: { isFault: true },\n            message: 'Error retrieving Permits'\n          });\n        })\n      );\n  }\n\n  public getPermitsForKendoGrid(state: any): Observable<any> {\n    const requestBody = {\n      take: state.take || 10,\n      skip: state.skip || 0,\n      sort: state.sort || [],\n      filter: state.filter || { logic: 'and', filters: [] },\n      search: state.search || '',\n      loggedInUserId: state.loggedInUserId\n    };\n\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsForKendoGrid`, requestBody)\n      .pipe(\n        mergeMap((res: any) => {\n          return of(res);\n        }),\n        catchError((err: any) => {\n          console.error('Error in getPermitsForKendoGrid:', err);\n          return of({\n            data: [],\n            total: 0,\n            errors: ['Error retrieving Permits']\n          });\n        })\n      );\n  }\n\n  public createPermit(data: any): Observable<any> {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/createPermit', data);\n  }\n\n  public updatePermit(data: any): Observable<any> {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/editPermit', data);\n  }\n\n  public updatePermitInternalReviewStatus(data: { permitId: number; internalReviewStatus: string }): Observable<any> {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/updatePermitInternalReviewStatus', data);\n  }\n\n  public getPermit(data: any): Observable<any> {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/getPermitById', data);\n  }\n\n  public deletePermit(data: any): Observable<any> {\n    return this.http.post(AppSettings.REST_ENDPOINT + '/deletePermit', data);\n  }\n\n  public searchPermits(searchTerm: string): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/searchPermits`, { search: searchTerm });\n  }\n\n  public exportPermits(exportType: string, selectedIds?: number[]): Observable<any> {\n    const requestBody = {\n      exportType: exportType,\n      selectedIds: selectedIds || []\n    };\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/exportPermits`, requestBody);\n  }\n\n  public getAllMunicipalities(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllMunicipalities`, params);\n  }\n\n  public getAllReviews(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllReviews`, params);\n  }\n\n  public syncPermits(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/syncPermits`, params);\n  }\n\n  // Internal Reviews Methods\n  public addInternalReview(data: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/addInternalReview`, data);\n  }\n\n  public updateInternalReview(data: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateInternalReview`, data);\n  }\n\n  public getInternalReviews(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getInternalReviews`, params);\n  }\n  public updateExternalReview(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/updateExternalReview`, params);\n  }\n\n    public getPermitDetails(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitDetails`, params);\n  }\n\n   public editNotesAndActions(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/editNotesAndActions`, params);\n  }\n\n  public validatePermitNumber(params: any): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/validatePermitNumber`, params);\n  }\n\n  public getPermitsByProjectId(projectId: number): Observable<any> {\n    return this.http.post(`${AppSettings.REST_ENDPOINT}/getPermitsByProjectId`, { projectId });\n  }\n\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,EAAE,QAAQ,MAAM;AACjE,SAASC,WAAW,QAAQ,sBAAsB;;;AAKlD,OAAM,MAAOC,cAAc;EAGfC,IAAA;EADVC,YACUD,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;EACV;EAEIE,WAAWA,CAAIC,SAAS,GAAG,WAAW,EAAEC,MAAY;IAC1D,OAAQC,KAAU,IAAqB;MACrC;MACAC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC;MAEtB;MACA,OAAOV,IAAI,CAACS,MAAM,CAAC;IACrB,CAAC;EACH;EAEOG,aAAaA,CAACC,WAAgB;IACnC,MAAMC,WAAW,GAAG;MAClBC,QAAQ,EAAEF,WAAW,CAACE,QAAQ;MAC9BC,UAAU,EAAEH,WAAW,CAACG,UAAU;MAClCC,SAAS,EAAEJ,WAAW,CAACI,SAAS;MAChCC,SAAS,EAAEL,WAAW,CAACK,SAAS;MAChCC,QAAQ,EAAE,IAAI;MACdC,MAAM,EAAEP,WAAW,CAACQ,MAAM,EAAED,MAAM,IAAI,EAAE;MACxCE,YAAY,EAAET,WAAW,CAACQ,MAAM,EAAEC,YAAY,IAAI;KACnD;IAED,OAAO,IAAI,CAACjB,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAEV,WAAW,CAAC,CAC7EW,IAAI,CACHxB,QAAQ,CAAEyB,GAAQ,IAAI;MACpB,OAAOxB,EAAE,CAACwB,GAAG,CAAC;IAChB,CAAC,CAAC,EACF3B,UAAU,CAAE4B,GAAQ,IAAI;MACtBhB,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEiB,GAAG,CAAC;MAC7C,OAAOzB,EAAE,CAAC;QACRQ,KAAK,EAAE;UAAEkB,OAAO,EAAE;QAAI,CAAE;QACxBC,OAAO,EAAE;OACV,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEOC,sBAAsBA,CAACC,KAAU;IACtC,MAAMjB,WAAW,GAAG;MAClBkB,IAAI,EAAED,KAAK,CAACC,IAAI,IAAI,EAAE;MACtBC,IAAI,EAAEF,KAAK,CAACE,IAAI,IAAI,CAAC;MACrBC,IAAI,EAAEH,KAAK,CAACG,IAAI,IAAI,EAAE;MACtBb,MAAM,EAAEU,KAAK,CAACV,MAAM,IAAI;QAAEc,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MACrDhB,MAAM,EAAEW,KAAK,CAACX,MAAM,IAAI,EAAE;MAC1BiB,cAAc,EAAEN,KAAK,CAACM;KACvB;IAED,OAAO,IAAI,CAAChC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,yBAAyB,EAAEV,WAAW,CAAC,CACtFW,IAAI,CACHxB,QAAQ,CAAEyB,GAAQ,IAAI;MACpB,OAAOxB,EAAE,CAACwB,GAAG,CAAC;IAChB,CAAC,CAAC,EACF3B,UAAU,CAAE4B,GAAQ,IAAI;MACtBhB,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEiB,GAAG,CAAC;MACtD,OAAOzB,EAAE,CAAC;QACRoC,IAAI,EAAE,EAAE;QACRC,KAAK,EAAE,CAAC;QACRC,MAAM,EAAE,CAAC,0BAA0B;OACpC,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEOC,YAAYA,CAACH,IAAS;IAC3B,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,eAAe,EAAEc,IAAI,CAAC;EAC1E;EAEOI,YAAYA,CAACJ,IAAS;IAC3B,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,aAAa,EAAEc,IAAI,CAAC;EACxE;EAEOK,gCAAgCA,CAACL,IAAwD;IAC9F,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,mCAAmC,EAAEc,IAAI,CAAC;EAC9F;EAEOM,SAASA,CAACN,IAAS;IACxB,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,gBAAgB,EAAEc,IAAI,CAAC;EAC3E;EAEOO,YAAYA,CAACP,IAAS;IAC3B,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAACpB,WAAW,CAACqB,aAAa,GAAG,eAAe,EAAEc,IAAI,CAAC;EAC1E;EAEOQ,aAAaA,CAACC,UAAkB;IACrC,OAAO,IAAI,CAAC1C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAE;MAAEJ,MAAM,EAAE2B;IAAU,CAAE,CAAC;EAC7F;EAEOC,aAAaA,CAACC,UAAkB,EAAEC,WAAsB;IAC7D,MAAMpC,WAAW,GAAG;MAClBmC,UAAU,EAAEA,UAAU;MACtBC,WAAW,EAAEA,WAAW,IAAI;KAC7B;IACD,OAAO,IAAI,CAAC7C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAEV,WAAW,CAAC;EAClF;EAEOqC,oBAAoBA,CAACC,MAAW;IACrC,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,uBAAuB,EAAE4B,MAAM,CAAC;EACpF;EAEOC,aAAaA,CAACD,MAAW;IAC9B,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,gBAAgB,EAAE4B,MAAM,CAAC;EAC7E;EAEOE,WAAWA,CAACF,MAAW;IAC5B,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,cAAc,EAAE4B,MAAM,CAAC;EAC3E;EAEA;EACOG,iBAAiBA,CAACjB,IAAS;IAChC,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,oBAAoB,EAAEc,IAAI,CAAC;EAC/E;EAEOkB,oBAAoBA,CAAClB,IAAS;IACnC,OAAO,IAAI,CAACjC,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,uBAAuB,EAAEc,IAAI,CAAC;EAClF;EAEOmB,kBAAkBA,CAACL,MAAW;IACnC,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,qBAAqB,EAAE4B,MAAM,CAAC;EAClF;EACOM,oBAAoBA,CAACN,MAAW;IACrC,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,uBAAuB,EAAE4B,MAAM,CAAC;EACpF;EAESO,gBAAgBA,CAACP,MAAW;IACnC,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,mBAAmB,EAAE4B,MAAM,CAAC;EAChF;EAEQQ,mBAAmBA,CAACR,MAAW;IACrC,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,sBAAsB,EAAE4B,MAAM,CAAC;EACnF;EAEOS,oBAAoBA,CAACT,MAAW;IACrC,OAAO,IAAI,CAAC/C,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,uBAAuB,EAAE4B,MAAM,CAAC;EACpF;EAEOU,qBAAqBA,CAACC,SAAiB;IAC5C,OAAO,IAAI,CAAC1D,IAAI,CAACkB,IAAI,CAAC,GAAGpB,WAAW,CAACqB,aAAa,wBAAwB,EAAE;MAAEuC;IAAS,CAAE,CAAC;EAC5F;;qCA9IW3D,cAAc,EAAA4D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;;WAAd/D,cAAc;IAAAgE,OAAA,EAAdhE,cAAc,CAAAiE,IAAA;IAAAC,UAAA,EAFb;EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
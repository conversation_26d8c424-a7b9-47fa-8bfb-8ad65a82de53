<div class="modal-header">
  <h5 class="modal-title">Respond to Correction</h5>
  <button type="button" class="btn-close" (click)="closeModal()" aria-label="Close"></button>
</div>
<div class="modal-body ">
  <div class="row">
    <div class="col-12">
      <label class="fw-bold form-label mb-2">EOR / AOR / Owner Response</label>
      <textarea class="form-control" rows="4" 
        placeholder="Type here" 
        [(ngModel)]="responseForm.EORAOROwner_Response"
        [disabled]="responseForm.lockResponse"></textarea>
    </div>
  </div>
  <div class="row mt-3">
    <div class="col-12">
      <label class="fw-bold form-label mb-2">Comment Responded By</label>
      <input type="text" class="form-control"
        placeholder="Who is responding to this correction" 
        [(ngModel)]="responseForm.commentResponsedBy"
        [disabled]="responseForm.lockResponse" />
    </div>
  </div>
  <div class="row mt-3" *ngIf="isAdmin">
    <div class="col-12">
      <div class="form-check">
        <input class="form-check-input" type="checkbox" 
          id="lockResponse" 
          [(ngModel)]="responseForm.lockResponse"
          (ngModelChange)="onLockResponseChange()">
        <label class="form-check-label fw-bold" for="lockResponse">
          Lock Response
        </label>
        <small class="form-text text-muted d-block mt-1">
          When checked, this response cannot be edited by other users
        </small>
      </div>
    </div>
  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-danger" (click)="closeModal()">Cancel</button>
  <button type="button" class="btn btn-primary" 
    (click)="submitResponse()"
    [disabled]="isLoading">
    <span *ngIf="isLoading" class="spinner-border spinner-border-sm me-2"></span>
    Submit Response
  </button>
</div>

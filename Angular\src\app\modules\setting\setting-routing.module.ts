import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';

import { SettingComponent } from './setting.component';
import { SettingsViewComponent } from './settings-view/settings-view.component';
import { EmailTemplatesListComponent } from './email-templates-list/email-templates-list.component';

const routes: Routes = [
  {
    path: '',
    children: [
      {
        path: 'view',
        component: SettingsViewComponent,
      },
      {
        path: 'list',
        component: SettingComponent,
      },
      {
        path: 'email-list',
        component: EmailTemplatesListComponent,
      },
      {
        path: '',
        redirectTo: 'list',
        pathMatch: 'full',
      },
      { path: '**', redirectTo: 'list', pathMatch: 'full' },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingRoutingModule {}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { UserProfileComponent } from './user-profile/user-profile.component';

const routes: Routes = [
    {
        path: '',
        component: UserProfileComponent,
        children: [
            {
                path: 'profile',
                component: UserProfileComponent,
            },
            { path: '', redirectTo: 'auth/login', pathMatch: 'full' },
            { path: '**', redirectTo: 'auth', pathMatch: 'full' },

        ]
    }
];


@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class LoggedinUserDetailsRoutingModule { }

import { ChangeDetectorRef, Component } from '@angular/core';
import { Router } from '@angular/router';
import { UserService } from '../../services/user.service';
import { AppService } from '../../services/app.service';

@Component({
  selector: 'app-settings-view',
  standalone: true,
  imports: [],
  templateUrl: './settings-view.component.html',
  styleUrl: './settings-view.component.scss',
})
export class SettingsViewComponent {
  loginUser!: any;
  usersCount: Number = 0;
  rolesCount: Number = 0;
  municipalitiesCount: Number = 0;
  emailTemplatesCount: Number = 0;
  constructor(
    private router: Router,
    private userService: UserService,
    private AppService: AppService,
    private cd: ChangeDetectorRef
  ) {
    this.loginUser = this.AppService.getLoggedInUser();
    let data = {
      loggedInUserId: this.loginUser.userId,
    };

    this.getCounts(data);
    // console.log('Login user loaded:', this.loginUser);
  }

  ngOnInit() {}

  PermitNavigate() {
    this.router.navigate(['/permits/list']);
  }
  projectNavigate() {
    this.router.navigate(['/projects/list']);
  }
  userNavigate() {
    this.router.navigate(['/setting/list']);
  }
emailtemplateNavigate() {
    this.router.navigate(['/setting/email-list']);
  }

  getCounts(data: any) {
    console.log('data', data);
    this.userService.getAllCounts(data).subscribe((counts) => {
      if (counts && counts.isFault == false) {
        this.emailTemplatesCount =
          counts?.responseData?.emailTemplatesCount || 0;

        console.log(' this.emailTemplatesCount', counts.responseData.counts);
        this.municipalitiesCount =
          counts?.responseData?.municipalitiesCount || 0;
        this.rolesCount = counts?.responseData?.rolesCount || 0;
        this.usersCount = counts?.responseData?.usersCount || 0;
        this.cd.detectChanges();
        // emailTemplatesCount
        // municipalitiesCount
        // rolesCount
        // usersCount
        // console.log('counts', counts);
      }
    });
  }
}

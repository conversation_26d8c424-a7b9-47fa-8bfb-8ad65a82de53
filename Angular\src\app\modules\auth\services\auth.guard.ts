import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot } from '@angular/router';
import { AuthService } from './auth.service';
import { AppService } from '../../services/app.service';

@Injectable({ providedIn: 'root' })
export class AuthGuard  {
  constructor(private authService: AuthService, private appService:AppService,private router: Router) {}

  canActivate(route: ActivatedRouteSnapshot, state: RouterStateSnapshot) {
    const currentUser = this.appService.getLocalStorageItem('permitUser',true);
    const accessToken = this.appService.getLocalStorageItem('permitToken',true);
    if (accessToken) {
      return true;
    }
    else {
      this.router.navigate(['/auth/login']);

    }
    return (currentUser);
  }
}

.grid-container {
  padding: 20px;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

.fullscreen-grid {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: white;
  padding: 20px;
  overflow: auto;
}

/* Loading Overlay Styles handled globally in styles.scss */

.grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;

  h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 500;
  }

  .search-container {
    width: 300px;
  }
}
.search-section {
  .k-textbox {
    border-radius: 0.375rem;
    padding: 0.5rem 0.75rem; // reduced inner spacing for shorter height
    width: 80%; // full width (or set custom px)
    border: 2px solid #afc7dd; // highlight border color
    box-shadow: 0 0 6px rgba(59, 83, 135, 0.5); // glowing effect
  }

  .k-button {
    border-radius: 0.375rem;
    padding: 0.75rem 1.25rem; // bigger button
    min-width: 120px; // longer button
    background-color: #4c4e4f;
    color: white;
    font-weight: 500;
    transition: background 0.3s, transform 0.2s;

    &:hover {
      background-color: #4c4e4f;
      transform: scale(1.05);
    }
  }
}
.grid-toolbar {
  display: flex;
  gap: 10px;
  align-items: center;
  margin-bottom: 10px;
}

/* Toolbar button styles */
.k-grid-toolbar {
  .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    min-width: 40px;
    height: 40px;
    border: 1px solid transparent;
    background-color: transparent;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      background-color: rgba(0, 0, 0, 0.05);
    }

    /* Icon-only buttons */
    &.btn-icon {
      padding: 0.5rem;
      min-width: 40px;
      width: 40px;

      i, .svg-icon {
        font-size: 1.25rem;
      }
    }

    /* Buttons with text - ensure consistent spacing */
    &:not(.btn-icon) {
      padding: 0.375rem 0.75rem;
      gap: 0.5rem;
    }
  }

  .btn-success {
    background-color: #198754;
    border-color: #198754;
    color: white;

    &:hover {
      background-color: #157347;
      border-color: #146c43;
    }
  }

  .btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;

    &:hover {
      background-color: #ffca2c;
      border-color: #ffc720;
    }
  }

  .btn-info {
    background-color: #0dcaf0;
    border-color: #0dcaf0;
    color: #000;

    &:hover {
      background-color: #31d2f2;
      border-color: #25cff2;
    }
  }

  .btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;

    &:hover {
      background-color: #5c636a;
      border-color: #565e64;
    }
  }
}

/* Total count styling */
.total-count {
  display: flex;
  align-items: center;
  font-size: 0.875rem;

  .text-muted {
    color: #6c757d !important;
  }

  .fw-bold {
    font-weight: 600 !important;
    color: #495057;
  }
}

.k-grid {
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  font-size: 16px;
  color: #888;
  background-color: #f9f9f9;
  border-radius: 6px;
  margin-top: 20px;
}

.detail-container {
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  margin-bottom: 8px;

  .detail-label {
    width: 120px;
    font-weight: 500;
    color: #666;
  }
}

/* Status indicators */
.status-active {
  padding: 4px 8px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-inactive {
  padding: 4px 8px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* Advanced filters panel */
.advanced-filters-panel {
  border-top: 1px solid #dee2e6;
  margin-top: 10px;
}

/* Custom dropdown styling */
.custom-dropdown {
  .k-button {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;

    &:hover {
      background-color: #5c636a;
      border-color: #565e64;
    }
  }
}

/* Permission badges styling */
.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  margin: 0.125rem;
  border-radius: 0.25rem;
}

.badge-light-primary {
  background-color: #e3f2fd;
  color: #1976d2;
}

/* Responsive design */
@media (max-width: 768px) {
  .grid-container {
    padding: 10px;
  }

  .k-grid-toolbar {
    flex-wrap: wrap;
    gap: 5px;

    .btn {
      min-width: 35px;
      height: 35px;
      font-size: 0.75rem;
    }
  }
}

{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27)(4, \"div\", 28)(5, \"label\");\n    i0.ɵɵtext(6, \"Manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 29);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 28)(10, \"label\");\n    i0.ɵɵtext(11, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 30);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 28)(15, \"label\");\n    i0.ɵɵtext(16, \"Issues\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 29);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 28)(20, \"label\");\n    i0.ɵɵtext(21, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 29);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"div\", 31)(25, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_div_13_Template_button_click_25_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(26, \"i\", 33);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.issuesCount || \"0\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.comments || \"No comments\");\n  }\n}\nfunction ProjectViewComponent_div_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵelement(2, \"i\", 36);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_div_19_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 40);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_div_19_tr_15_Template_a_click_2_listener() {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r5.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"select\", 42);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_div_19_tr_15_Template_select_change_10_listener($event) {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r5, $event.target.value));\n    });\n    i0.ɵɵelementStart(11, \"option\", 43);\n    i0.ɵɵtext(12, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 44);\n    i0.ɵɵtext(14, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 45);\n    i0.ɵɵtext(16, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 46);\n    i0.ɵɵtext(18, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 47);\n    i0.ɵɵtext(20, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 48);\n    i0.ɵɵtext(22, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 49);\n    i0.ɵɵtext(24, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 50);\n    i0.ɵɵtext(26, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"td\")(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"td\")(31, \"span\", 51);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitName || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitReviewType || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r5.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r5.cycleDays || \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 37)(1, \"table\", 38)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Cycle (Days)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Comments\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_div_19_tr_15_Template, 33, 8, \"tr\", 39);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, ProjectViewComponent_div_2_div_13_Template, 27, 4, \"div\", 18);\n    i0.ɵɵelement(14, \"hr\", 19);\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"h5\", 21);\n    i0.ɵɵtext(17, \"Permits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProjectViewComponent_div_2_div_18_Template, 5, 0, \"div\", 22)(19, ProjectViewComponent_div_2_div_19_Template, 16, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectName || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nexport class ProjectViewComponent {\n  route;\n  router;\n  cdr;\n  appService;\n  projectsService;\n  permitsService;\n  modalService;\n  projectId = null;\n  project = null;\n  isLoading = false;\n  selectedTab = 'details';\n  projectPermits = [];\n  loginUser = {};\n  routeSubscription = new Subscription();\n  constructor(route, router, cdr, appService, projectsService, permitsService, modalService) {\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.modalService = modalService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n      console.log('Project view - received params:', {\n        projectId: this.projectId,\n        queryParams\n      });\n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n  fetchProjectDetails() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    this.projectsService.getProject({\n      projectId: this.projectId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchProjectPermits() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [{\n          field: 'projectId',\n          operator: 'eq',\n          value: this.projectId\n        }]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter(p => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    if (!this.projectId) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    // Subscribe to the modal event when it closes\n    modalRef.result.then(result => {\n      // Handle successful edit\n      if (result) {\n        console.log('Project edited successfully:', result);\n        // Refresh project details\n        this.fetchProjectDetails();\n      }\n    }, reason => {\n      // Handle modal dismissal\n      console.log('Modal dismissed:', reason);\n    });\n  }\n  viewPermit(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'project',\n        projectId: this.projectId\n      }\n    });\n  }\n  onStatusChange(permit, newStatus) {\n    if (!permit?.permitId || !newStatus) {\n      return;\n    }\n    const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n    if (!allowed.includes(newStatus)) {\n      return;\n    }\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.isLoading = true;\n    this.cdr.markForCheck();\n    this.permitsService.updatePermitInternalReviewStatus({\n      permitId: permit.permitId,\n      internalReviewStatus: newStatus\n    }).subscribe({\n      next: res => {\n        const isFault = res?.isFault || res?.responseData?.isFault;\n        if (isFault) {\n          permit.internalReviewStatus = previous;\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        }\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        permit.internalReviewStatus = previous;\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", \"status-active\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"mb-2\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [\"class\", \"project-details-section\", 4, \"ngIf\"], [1, \"project-divider\"], [1, \"permits-section\"], [1, \"permits-title\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"project-details-section\"], [1, \"project-details-content\"], [1, \"project-details-layout\"], [1, \"project-details-left\"], [1, \"project-detail-item\"], [1, \"project-value\"], [1, \"project-value\", \"status-bold\"], [1, \"project-details-right\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-link\", \"p-0\", \"edit-button\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"permits-table\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"badge\", \"badge-green-light\", \"ms-1\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"], [1, \"wrap-text\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 20, 5, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.NgSelectOption, i7.ɵNgSelectMultipleOption],\n    styles: [\".project-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.project-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem;\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1;\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.project-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n}\\n\\n.project-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.project-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  display: block; \\n\\n  margin-bottom: 0.15rem;\\n  font-size: 15px;\\n  display: block;\\n  line-height: 1.2;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%], \\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-label[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  display: block; \\n\\n  margin-bottom: 0.25rem; \\n\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .badge-green-light[_ngcontent-%COMP%] {\\n  background-color: #42c761; \\n\\n  color: #155724; \\n\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  margin: 0; \\n\\n  padding: 0.25rem 0; \\n\\n  border-bottom: none;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left;\\n  background: transparent;\\n  border: none;\\n  min-width: 0;\\n  border-radius: 0;\\n}\\n\\n.project-detail-item.span-2[_ngcontent-%COMP%] {\\n  grid-column: span 2;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n\\n.status-cancelled[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n\\n.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n  table-layout: fixed;\\n  width: 100%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #3f4254;\\n  border-bottom: 2px solid #e5eaee;\\n  padding: 1rem 0.75rem;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: none;\\n  border-bottom: 1px solid #e5eaee;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-number-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-description-col[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-type-col[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-status-col[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-number-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-description-cell[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-type-cell[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-status-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, \\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.ball-in-court-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.ball-in-court-cell[_ngcontent-%COMP%] {\\n  white-space: normal;\\n  word-break: break-word;\\n  overflow-wrap: anywhere;\\n}\\n\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%] {\\n  color: var(--bs-primary, #0d6efd);\\n  text-decoration: none;\\n  font-weight: 700;\\n  cursor: pointer;\\n  transition: color 0.15s ease, -webkit-text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease, -webkit-text-decoration 0.15s ease;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:hover {\\n  color: #0b5ed7;\\n  text-decoration: underline;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(24, 125, 228, 0.25);\\n  border-radius: 0.25rem;\\n}\\n\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-description-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-type-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-status-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n}\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.project-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.project-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: white;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n    gap: 1rem;\\n  }\\n  .project-detail-item.span-2[_ngcontent-%COMP%] {\\n    grid-column: auto;\\n  }\\n  .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.25rem 0.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 0.75rem;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: flex-start;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: flex-end;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "combineLatest", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProjectViewComponent_div_2_div_13_Template_button_click_25_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "editProject", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "project", "internalProjectManagerName", "internalProjectManager", "projectStatus", "issuesCount", "comments", "ProjectViewComponent_div_2_div_19_tr_15_Template_a_click_2_listener", "permit_r5", "_r4", "$implicit", "viewPermit", "permitId", "ProjectViewComponent_div_2_div_19_tr_15_Template_select_change_10_listener", "$event", "onStatusChange", "target", "value", "ɵɵtextInterpolate1", "permitName", "permitReviewType", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "cycleDays", "attentionReason", "ɵɵtemplate", "ProjectViewComponent_div_2_div_19_tr_15_Template", "projectPermits", "ProjectViewComponent_div_2_Template_button_click_10_listener", "_r1", "goBack", "ProjectViewComponent_div_2_div_13_Template", "ProjectViewComponent_div_2_div_18_Template", "ProjectViewComponent_div_2_div_19_Template", "projectName", "length", "ProjectViewComponent", "route", "router", "cdr", "appService", "projectsService", "permitsService", "modalService", "projectId", "selectedTab", "loginUser", "routeSubscription", "constructor", "ngOnInit", "getLoggedInUser", "paramMap", "queryParams", "subscribe", "idParam", "get", "Number", "console", "log", "activeTab", "fetchProjectDetails", "fetchProjectPermits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "getProject", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "Project", "data", "Object", "keys", "error", "faultMessage", "err", "getPermitsForKendoGrid", "take", "skip", "sort", "filter", "logic", "filters", "field", "operator", "search", "loggedInUserId", "userId", "rawPermits", "p", "permitProjectId", "projectID", "project_id", "ProjectId", "ProjectID", "navigate", "NgbModalOptions", "size", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "id", "result", "then", "reason", "from", "permit", "newStatus", "allowed", "includes", "previous", "updatePermitInternalReviewStatus", "getStatusClass", "status", "toLowerCase", "replace", "showTab", "tab", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "ChangeDetectorRef", "i2", "AppService", "i3", "ProjectsService", "i4", "PermitsService", "i5", "NgbModal", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { Subscription, combineLatest } from 'rxjs';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { PermitsService } from '../../services/permits.service';\r\nimport { AppService } from '../../services/app.service';\r\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\r\n\r\n@Component({\r\n  selector: 'app-project-view',\r\n  templateUrl: './project-view.component.html',\r\n  styleUrls: ['./project-view.component.scss']\r\n})\r\nexport class ProjectViewComponent implements OnInit, OnDestroy {\r\n  public projectId: number | null = null;\r\n  public project: any = null;\r\n  public isLoading: boolean = false;\r\n  public selectedTab: string = 'details';\r\n  public projectPermits: any[] = [];\r\n  public loginUser: any = {};\r\n  private routeSubscription: Subscription = new Subscription();\r\n\r\n  constructor(\r\n    private route: ActivatedRoute,\r\n    private router: Router,\r\n    private cdr: ChangeDetectorRef,\r\n    private appService: AppService,\r\n    private projectsService: ProjectsService,\r\n    private permitsService: PermitsService,\r\n    private modalService: NgbModal\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.appService.getLoggedInUser();\r\n\r\n    // Combine route params and query params to handle both together\r\n    this.routeSubscription = combineLatest([\r\n      this.route.paramMap,\r\n      this.route.queryParams\r\n    ]).subscribe(([paramMap, queryParams]) => {\r\n      const idParam = paramMap.get('id');\r\n      this.projectId = idParam ? Number(idParam) : null;\r\n\r\n      console.log('Project view - received params:', { projectId: this.projectId, queryParams });\r\n      \r\n      // Handle active tab from query params\r\n      const activeTab = queryParams['activeTab'];\r\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\r\n        this.selectedTab = activeTab;\r\n        console.log('Setting selectedTab from query params:', activeTab);\r\n      } else {\r\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\r\n      }\r\n\r\n      if (this.projectId) {\r\n        this.fetchProjectDetails();\r\n        this.fetchProjectPermits();\r\n      }\r\n      \r\n      this.cdr.markForCheck();\r\n    });\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.routeSubscription) {\r\n      this.routeSubscription.unsubscribe();\r\n    }\r\n  }\r\n\r\n  public fetchProjectDetails(): void {\r\n    if (!this.projectId) { return; }\r\n    this.isLoading = true;\r\n    this.projectsService.getProject({ projectId: this.projectId }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Project API Response:', res);\r\n        if (!res?.isFault) {\r\n          // Try different response structures\r\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\r\n          console.log('Project data assigned:', this.project);\r\n          console.log('Project fields available:', Object.keys(this.project || {}));\r\n          // Don't override selectedTab here - let query params handle it\r\n        } else {\r\n          console.error('API returned fault:', res.faultMessage);\r\n          this.project = null;\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err) => {\r\n        this.isLoading = false;\r\n        console.error('Error fetching project details:', err);\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public fetchProjectPermits(): void {\r\n    if (!this.projectId) { return; }\r\n    this.isLoading = true;\r\n    \r\n    // Get permits for this specific project\r\n    this.permitsService.getPermitsForKendoGrid({\r\n      take: 100,\r\n      skip: 0,\r\n      sort: [],\r\n      filter: {\r\n        logic: 'and',\r\n        filters: [\r\n          {\r\n            field: 'projectId',\r\n            operator: 'eq',\r\n            value: this.projectId\r\n          }\r\n        ]\r\n      },\r\n      search: '',\r\n      loggedInUserId: this.loginUser.userId\r\n    }).subscribe({\r\n      next: (res: any) => {\r\n        this.isLoading = false;\r\n        console.log('Project permits API response:', res);\r\n        if (res?.isFault) {\r\n          console.error('Failed to load project permits:', res.faultMessage);\r\n          this.projectPermits = [];\r\n        } else {\r\n          const rawPermits = res.responseData?.data || res.data || [];\r\n          // Client-side guard: ensure only permits for this project are shown\r\n          this.projectPermits = (rawPermits || []).filter((p: any) => {\r\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\r\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\r\n          });\r\n          console.log('Project permits assigned (filtered):', this.projectPermits);\r\n        }\r\n        this.cdr.markForCheck();\r\n      },\r\n      error: (err: any) => {\r\n        this.isLoading = false;\r\n        console.error('Error loading project permits:', err);\r\n        this.projectPermits = [];\r\n        this.cdr.markForCheck();\r\n      }\r\n    });\r\n  }\r\n\r\n  public goBack(): void {\r\n    this.router.navigate(['/projects/list']);\r\n  }\r\n\r\n  public editProject(): void {\r\n    if (!this.projectId) { return; }\r\n    \r\n    const NgbModalOptions: {\r\n      size: string;\r\n      backdrop: boolean | 'static';\r\n      keyboard: boolean;\r\n      scrollable: boolean;\r\n    } = {\r\n      size: 'lg', // Large modal size\r\n      backdrop: 'static', // Prevents closing when clicking outside\r\n      keyboard: false, // Disables closing with the Escape key\r\n      scrollable: true, // Allows scrolling inside the modal\r\n    };\r\n\r\n    // Open the modal and load the ProjectPopup\r\n    const modalRef = this.modalService.open(\r\n      ProjectPopupComponent,\r\n      NgbModalOptions\r\n    );\r\n    \r\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\r\n    modalRef.componentInstance.id = this.projectId;\r\n    modalRef.componentInstance.project = this.project;\r\n    \r\n    // Subscribe to the modal event when it closes\r\n    modalRef.result.then(\r\n      (result) => {\r\n        // Handle successful edit\r\n        if (result) {\r\n          console.log('Project edited successfully:', result);\r\n          // Refresh project details\r\n          this.fetchProjectDetails();\r\n        }\r\n      },\r\n      (reason) => {\r\n        // Handle modal dismissal\r\n        console.log('Modal dismissed:', reason);\r\n      }\r\n    );\r\n  }\r\n\r\n  public viewPermit(permitId: number): void {\r\n    this.router.navigate(['/permits/view', permitId], { \r\n      queryParams: { from: 'project', projectId: this.projectId } \r\n    });\r\n  }\r\n\r\n  public onStatusChange(permit: any, newStatus: string): void {\r\n    if (!permit?.permitId || !newStatus) { return; }\r\n    const allowed = [\r\n      'Approved',\r\n      'Pacifica Verification',\r\n      'Dis-Approved',\r\n      'Pending',\r\n      'Not Required',\r\n      'In Review',\r\n      '1 Cycle Completed'\r\n    ];\r\n    if (!allowed.includes(newStatus)) { return; }\r\n\r\n    const previous = permit.internalReviewStatus;\r\n    permit.internalReviewStatus = newStatus;\r\n    this.isLoading = true;\r\n    this.cdr.markForCheck();\r\n\r\n    this.permitsService\r\n      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          const isFault = res?.isFault || res?.responseData?.isFault;\r\n          if (isFault) {\r\n            permit.internalReviewStatus = previous;\r\n            this.isLoading = false;\r\n            this.cdr.markForCheck();\r\n          }\r\n          this.isLoading = false;\r\n          this.cdr.markForCheck();\r\n        },\r\n        error: () => {\r\n          permit.internalReviewStatus = previous;\r\n          this.isLoading = false;\r\n          this.cdr.markForCheck();\r\n        }\r\n      });\r\n  }\r\n\r\n  public getStatusClass(status: string): string {\r\n    if (!status) return 'status-n-a';\r\n    return (\r\n      'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-')\r\n    );\r\n  }\r\n\r\n  showTab(tab: string, $event: any) {\r\n    this.selectedTab = tab;\r\n    this.cdr.markForCheck();\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"project-view-container\">\r\n  <!-- Project Details Card -->\r\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"project\">\r\n    <!-- Project Details Header -->\r\n    <div class=\"project-details-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-wrap\">\r\n          <div class=\"title-line\">\r\n            <span class=\"project-title\">{{ project.projectName || \"\" }}</span>\r\n            <span class=\"status-text status-active\">{{ project.projectStatus || \"Active\" }}</span>\r\n          </div>\r\n        </div>\r\n        <div class=\"button-group\">\r\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center mb-2\" (click)=\"goBack()\">\r\n            <i class=\"fas fa-arrow-left me-2\"></i>\r\n            Back\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Project Details Section -->\r\n    <div class=\"project-details-section\" *ngIf=\"project\">\r\n      <div class=\"project-details-content\">\r\n        <div class=\"project-details-layout\">\r\n          <div class=\"project-details-left\">\r\n            <div class=\"project-detail-item\">\r\n              <label>Manager</label>\r\n              <span class=\"project-value\">{{ project.internalProjectManagerName || project.internalProjectManager || \"\" }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item\">\r\n              <label>Status</label>\r\n              <span class=\"project-value status-bold\">{{ project.projectStatus || \"Active\" }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item\">\r\n              <label>Issues</label>\r\n              <span class=\"project-value\">{{ project.issuesCount || \"0\" }}</span>\r\n            </div>\r\n            <div class=\"project-detail-item\">\r\n              <label>Comments</label>\r\n              <span class=\"project-value\">{{ project.comments || \"No comments\" }}</span>\r\n            </div>\r\n          </div>\r\n          <div class=\"project-details-right\">\r\n            <button type=\"button\" class=\"btn btn-link p-0 edit-button\" (click)=\"editProject()\" title=\"Edit Project\">\r\n              <i class=\"fas fa-edit text-primary\"></i>\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Divider -->\r\n    <hr class=\"project-divider\">\r\n\r\n    <!-- Permits Section -->\r\n    <div class=\"permits-section\">\r\n      <h5 class=\"permits-title\">Permits</h5>\r\n      \r\n      <!-- Empty State for Permits -->\r\n      <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"projectPermits.length === 0\">\r\n        <div class=\"text-center\">\r\n          <i class=\"fas fa-file-alt fa-3x mb-3\"></i>\r\n          <p>No permits found for this project.</p>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- Permits Table -->\r\n      <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\r\n        <table class=\"table permits-table\">\r\n          <thead>\r\n            <tr>\r\n              <th>Permit Type</th>\r\n              <th>Source</th>\r\n              <th>Status</th>\r\n              <th>Cycle (Days)</th>\r\n              <th>Comments</th>\r\n            </tr>\r\n          </thead>\r\n          <tbody>\r\n            <tr *ngFor=\"let permit of projectPermits\">\r\n              <td>\r\n                <a \r\n                  class=\"fw-bold\" \r\n                  (click)=\"viewPermit(permit.permitId)\" \r\n                  title=\"View Permit\"\r\n                  aria-label=\"View Permit\"\r\n                >\r\n                  {{ permit.permitName || \"\" }}  \r\n                </a>\r\n                <span class=\"badge badge-green-light ms-1\">\r\n                  {{ permit.permitReviewType || \"\" }}\r\n                </span>\r\n              </td>\r\n              <td>\r\n                <span>{{ permit.permitNumber || \"\" }}</span>\r\n              </td>\r\n              <td>\r\n                <select class=\"form-select form-select-sm w-auto\"\r\n                        [value]=\"permit.internalReviewStatus || ''\"\r\n                        (change)=\"onStatusChange(permit, $any($event.target).value)\"\r\n                        [disabled]=\"isLoading\">\r\n                  <option [value]=\"''\" disabled>Select status</option>\r\n                  <option value=\"Approved\">Approved</option>\r\n                  <option value=\"Pacifica Verification\">Pacifica Verification</option>\r\n                  <option value=\"Dis-Approved\">Dis-Approved</option>\r\n                  <option value=\"Pending\">Pending</option>\r\n                  <option value=\"Not Required\">Not Required</option>\r\n                  <option value=\"In Review\">In Review</option>\r\n                  <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\r\n                </select>\r\n              </td>\r\n              <td>\r\n                <span>{{ permit.cycleDays || \"\" }}</span>\r\n              </td>\r\n              <td>\r\n                <span class=\"wrap-text\">{{ permit.attentionReason || '' }}</span>\r\n              </td>\r\n            </tr>\r\n          </tbody>\r\n        </table>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,aAAa,QAAQ,MAAM;AAKlD,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;ICH1EC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA6BQH,EALV,CAAAC,cAAA,cAAqD,cACd,cACC,cACA,cACC,YACxB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtBH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAgF;IAC9GF,EAD8G,CAAAG,YAAA,EAAO,EAC/G;IAEJH,EADF,CAAAC,cAAA,cAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IACjFF,EADiF,CAAAG,YAAA,EAAO,EAClF;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC/D;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IAEvEF,EAFuE,CAAAG,YAAA,EAAO,EACtE,EACF;IAEJH,EADF,CAAAC,cAAA,eAAmC,kBACuE;IAA7CD,EAAA,CAAAI,UAAA,mBAAAC,oEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IAChFX,EAAA,CAAAY,SAAA,aAAwC;IAKlDZ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACF,EACF,EACF;;;;IAtBgCH,EAAA,CAAAa,SAAA,GAAgF;IAAhFb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAC,0BAAA,IAAAR,MAAA,CAAAO,OAAA,CAAAE,sBAAA,OAAgF;IAIpEjB,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAG,aAAA,aAAuC;IAInDlB,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAI,WAAA,QAAgC;IAIhCnB,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAK,QAAA,kBAAuC;;;;;IAqBzEpB,EADF,CAAAC,cAAA,cAAkH,cACvF;IACvBD,EAAA,CAAAY,SAAA,YAA0C;IAC1CZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACrC,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAI,UAAA,mBAAAiB,oEAAA;MAAA,MAAAC,SAAA,GAAAtB,EAAA,CAAAM,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiB,UAAA,CAAAH,SAAA,CAAAI,QAAA,CAA2B;IAAA,EAAC;IAIrC1B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACzC;IAEHH,EADF,CAAAC,cAAA,SAAI,kBAI6B;IADvBD,EAAA,CAAAI,UAAA,oBAAAuB,2EAAAC,MAAA;MAAA,MAAAN,SAAA,GAAAtB,EAAA,CAAAM,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAqB,cAAA,CAAAP,SAAA,EAAAM,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElE/B,EAAA,CAAAC,cAAA,kBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IACpCF,EADoC,CAAAG,YAAA,EAAO,EACtC;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACsB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE9DF,EAF8D,CAAAG,YAAA,EAAO,EAC9D,EACF;;;;;IA9BCH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAgC,kBAAA,MAAAV,SAAA,CAAAW,UAAA,YACF;IAEEjC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAgC,kBAAA,MAAAV,SAAA,CAAAY,gBAAA,YACF;IAGMlC,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,iBAAA,CAAAQ,SAAA,CAAAa,YAAA,OAA+B;IAI7BnC,EAAA,CAAAa,SAAA,GAA2C;IAE3Cb,EAFA,CAAAoC,UAAA,UAAAd,SAAA,CAAAe,oBAAA,OAA2C,aAAA7B,MAAA,CAAA8B,SAAA,CAErB;IACpBtC,EAAA,CAAAa,SAAA,EAAY;IAAZb,EAAA,CAAAoC,UAAA,aAAY;IAWhBpC,EAAA,CAAAa,SAAA,IAA4B;IAA5Bb,EAAA,CAAAc,iBAAA,CAAAQ,SAAA,CAAAiB,SAAA,OAA4B;IAGVvC,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAc,iBAAA,CAAAQ,SAAA,CAAAkB,eAAA,OAAkC;;;;;IA5C5DxC,EAJR,CAAAC,cAAA,cAAgE,gBAC3B,YAC1B,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAEhBF,EAFgB,CAAAG,YAAA,EAAK,EACd,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAyC,UAAA,KAAAC,gDAAA,kBAA0C;IAyChD1C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAzCuBH,EAAA,CAAAa,SAAA,IAAiB;IAAjBb,EAAA,CAAAoC,UAAA,YAAA5B,MAAA,CAAAmC,cAAA,CAAiB;;;;;;IAxExC3C,EANV,CAAAC,cAAA,aAAsD,aAEhB,cACN,cACF,cACE,eACM;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClEH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAEnFF,EAFmF,CAAAG,YAAA,EAAO,EAClF,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,kBACqF;IAAnBD,EAAA,CAAAI,UAAA,mBAAAwC,6DAAA;MAAA5C,EAAA,CAAAM,aAAA,CAAAuC,GAAA;MAAA,MAAArC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsC,MAAA,EAAQ;IAAA,EAAC;IAC1G9C,EAAA,CAAAY,SAAA,aAAsC;IACtCZ,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAGNH,EAAA,CAAAyC,UAAA,KAAAM,0CAAA,mBAAqD;IA+BrD/C,EAAA,CAAAY,SAAA,cAA4B;IAI1BZ,EADF,CAAAC,cAAA,eAA6B,cACD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAWtCH,EARA,CAAAyC,UAAA,KAAAO,0CAAA,kBAAkH,KAAAC,0CAAA,mBAQlD;IAuDpEjD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAnHgCH,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAmC,WAAA,OAA+B;IACnBlD,EAAA,CAAAa,SAAA,GAAuC;IAAvCb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAG,aAAA,aAAuC;IAajDlB,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAoC,UAAA,SAAA5B,MAAA,CAAAO,OAAA,CAAa;IAsC8Bf,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAoC,UAAA,SAAA5B,MAAA,CAAAmC,cAAA,CAAAQ,MAAA,OAAiC;IAQjFnD,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAoC,UAAA,SAAA5B,MAAA,CAAAmC,cAAA,CAAAQ,MAAA,KAA+B;;;ADhEpE,OAAM,MAAOC,oBAAoB;EAUrBC,KAAA;EACAC,MAAA;EACAC,GAAA;EACAC,UAAA;EACAC,eAAA;EACAC,cAAA;EACAC,YAAA;EAfHC,SAAS,GAAkB,IAAI;EAC/B7C,OAAO,GAAQ,IAAI;EACnBuB,SAAS,GAAY,KAAK;EAC1BuB,WAAW,GAAW,SAAS;EAC/BlB,cAAc,GAAU,EAAE;EAC1BmB,SAAS,GAAQ,EAAE;EAClBC,iBAAiB,GAAiB,IAAIlE,YAAY,EAAE;EAE5DmE,YACUX,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,cAA8B,EAC9BC,YAAsB;IANtB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;EACnB;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACH,SAAS,GAAG,IAAI,CAACN,UAAU,CAACU,eAAe,EAAE;IAElD;IACA,IAAI,CAACH,iBAAiB,GAAGjE,aAAa,CAAC,CACrC,IAAI,CAACuD,KAAK,CAACc,QAAQ,EACnB,IAAI,CAACd,KAAK,CAACe,WAAW,CACvB,CAAC,CAACC,SAAS,CAAC,CAAC,CAACF,QAAQ,EAAEC,WAAW,CAAC,KAAI;MACvC,MAAME,OAAO,GAAGH,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAACX,SAAS,GAAGU,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI;MAEjDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAAEd,SAAS,EAAE,IAAI,CAACA,SAAS;QAAEQ;MAAW,CAAE,CAAC;MAE1F;MACA,MAAMO,SAAS,GAAGP,WAAW,CAAC,WAAW,CAAC;MAC1C,IAAIO,SAAS,KAAKA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,SAAS,CAAC,EAAE;QACrE,IAAI,CAACd,WAAW,GAAGc,SAAS;QAC5BF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,SAAS,CAAC;MAClE,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACb,WAAW,CAAC;MAC7E;MAEA,IAAI,IAAI,CAACD,SAAS,EAAE;QAClB,IAAI,CAACgB,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;MAEA,IAAI,CAACtB,GAAG,CAACuB,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACiB,WAAW,EAAE;IACtC;EACF;EAEOJ,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACtB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACwB,UAAU,CAAC;MAAErB,SAAS,EAAE,IAAI,CAACA;IAAS,CAAE,CAAC,CAACS,SAAS,CAAC;MACvEa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7C,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,GAAG,CAAC;QACzC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB;UACA,IAAI,CAACrE,OAAO,GAAGoE,GAAG,CAACE,YAAY,EAAEC,OAAO,IAAIH,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACE,YAAY,IAAI,IAAI;UAC9FZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC3D,OAAO,CAAC;UACnD0D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEc,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1E,OAAO,IAAI,EAAE,CAAC,CAAC;UACzE;QACF,CAAC,MAAM;UACL0D,OAAO,CAACiB,KAAK,CAAC,qBAAqB,EAAEP,GAAG,CAACQ,YAAY,CAAC;UACtD,IAAI,CAAC5E,OAAO,GAAG,IAAI;QACrB;QACA,IAAI,CAACwC,GAAG,CAACuB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAG,IAAI;QACb,IAAI,CAACtD,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEE,GAAG,CAAC;QACrD,IAAI,CAACrC,GAAG,CAACuB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOD,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACtB,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACoB,cAAc,CAACmC,sBAAsB,CAAC;MACzCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;QACNC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,IAAI;UACdtE,KAAK,EAAE,IAAI,CAAC6B;SACb;OAEJ;MACD0C,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,IAAI,CAACzC,SAAS,CAAC0C;KAChC,CAAC,CAACnC,SAAS,CAAC;MACXa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7C,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,GAAG,CAAC;QACjD,IAAIA,GAAG,EAAEC,OAAO,EAAE;UAChBX,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEP,GAAG,CAACQ,YAAY,CAAC;UAClE,IAAI,CAAChD,cAAc,GAAG,EAAE;QAC1B,CAAC,MAAM;UACL,MAAM8D,UAAU,GAAGtB,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACI,IAAI,IAAI,EAAE;UAC3D;UACA,IAAI,CAAC5C,cAAc,GAAG,CAAC8D,UAAU,IAAI,EAAE,EAAER,MAAM,CAAES,CAAM,IAAI;YACzD,MAAMC,eAAe,GAAGD,CAAC,EAAE9C,SAAS,IAAI8C,CAAC,EAAEE,SAAS,IAAIF,CAAC,EAAEG,UAAU,IAAIH,CAAC,EAAEI,SAAS,IAAIJ,CAAC,EAAEK,SAAS;YACrG,OAAO,IAAI,CAACnD,SAAS,IAAI,IAAI,GAAGY,MAAM,CAACmC,eAAe,CAAC,KAAKnC,MAAM,CAAC,IAAI,CAACZ,SAAS,CAAC,GAAG,IAAI;UAC3F,CAAC,CAAC;UACFa,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC/B,cAAc,CAAC;QAC1E;QACA,IAAI,CAACY,GAAG,CAACuB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAQ,IAAI;QAClB,IAAI,CAACtD,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEE,GAAG,CAAC;QACpD,IAAI,CAACjD,cAAc,GAAG,EAAE;QACxB,IAAI,CAACY,GAAG,CAACuB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOhC,MAAMA,CAAA;IACX,IAAI,CAACQ,MAAM,CAAC0D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEOrG,WAAWA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACiD,SAAS,EAAE;MAAE;IAAQ;IAE/B,MAAMqD,eAAe,GAKjB;MACFC,IAAI,EAAE,IAAI;MAAE;MACZC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC3D,YAAY,CAAC4D,IAAI,CACrCxH,qBAAqB,EACrBkH,eAAe,CAChB;IAED;IACAK,QAAQ,CAACE,iBAAiB,CAACC,EAAE,GAAG,IAAI,CAAC7D,SAAS;IAC9C0D,QAAQ,CAACE,iBAAiB,CAACzG,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACAuG,QAAQ,CAACI,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAIA,MAAM,EAAE;QACVjD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgD,MAAM,CAAC;QACnD;QACA,IAAI,CAAC9C,mBAAmB,EAAE;MAC5B;IACF,CAAC,EACAgD,MAAM,IAAI;MACT;MACAnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,MAAM,CAAC;IACzC,CAAC,CACF;EACH;EAEOnG,UAAUA,CAACC,QAAgB;IAChC,IAAI,CAAC4B,MAAM,CAAC0D,QAAQ,CAAC,CAAC,eAAe,EAAEtF,QAAQ,CAAC,EAAE;MAChD0C,WAAW,EAAE;QAAEyD,IAAI,EAAE,SAAS;QAAEjE,SAAS,EAAE,IAAI,CAACA;MAAS;KAC1D,CAAC;EACJ;EAEO/B,cAAcA,CAACiG,MAAW,EAAEC,SAAiB;IAClD,IAAI,CAACD,MAAM,EAAEpG,QAAQ,IAAI,CAACqG,SAAS,EAAE;MAAE;IAAQ;IAC/C,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,uBAAuB,EACvB,cAAc,EACd,SAAS,EACT,cAAc,EACd,WAAW,EACX,mBAAmB,CACpB;IACD,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAAE;IAAQ;IAE5C,MAAMG,QAAQ,GAAGJ,MAAM,CAACzF,oBAAoB;IAC5CyF,MAAM,CAACzF,oBAAoB,GAAG0F,SAAS;IACvC,IAAI,CAACzF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;IAEvB,IAAI,CAACpB,cAAc,CAChByE,gCAAgC,CAAC;MAAEzG,QAAQ,EAAEoG,MAAM,CAACpG,QAAQ;MAAEW,oBAAoB,EAAE0F;IAAS,CAAE,CAAC,CAChG1D,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAMC,OAAO,GAAGD,GAAG,EAAEC,OAAO,IAAID,GAAG,EAAEE,YAAY,EAAED,OAAO;QAC1D,IAAIA,OAAO,EAAE;UACX0C,MAAM,CAACzF,oBAAoB,GAAG6F,QAAQ;UACtC,IAAI,CAAC5F,SAAS,GAAG,KAAK;UACtB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;QACzB;QACA,IAAI,CAACxC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAEA,CAAA,KAAK;QACVoC,MAAM,CAACzF,oBAAoB,GAAG6F,QAAQ;QACtC,IAAI,CAAC5F,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEOsD,cAAcA,CAACC,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,OACE,SAAS,GAAGA,MAAM,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE7E;EAEAC,OAAOA,CAACC,GAAW,EAAE7G,MAAW;IAC9B,IAAI,CAACiC,WAAW,GAAG4E,GAAG;IACtB,IAAI,CAAClF,GAAG,CAACuB,YAAY,EAAE;EACzB;;qCAxOW1B,oBAAoB,EAAApD,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5I,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7I,EAAA,CAAA0I,iBAAA,CAAA1I,EAAA,CAAA8I,iBAAA,GAAA9I,EAAA,CAAA0I,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAhJ,EAAA,CAAA0I,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAlJ,EAAA,CAAA0I,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAApJ,EAAA,CAAA0I,iBAAA,CAAAW,EAAA,CAAAC,QAAA;EAAA;;UAApBlG,oBAAoB;IAAAmG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbjC7J,EAAA,CAAAyC,UAAA,IAAAsH,mCAAA,iBAA0D;QAS1D/J,EAAA,CAAAC,cAAA,aAAoC;QAElCD,EAAA,CAAAyC,UAAA,IAAAuH,mCAAA,kBAAsD;QA0HxDhK,EAAA,CAAAG,YAAA,EAAM;;;QArIAH,EAAA,CAAAoC,UAAA,SAAA0H,GAAA,CAAAxH,SAAA,CAAe;QAWoBtC,EAAA,CAAAa,SAAA,GAAa;QAAbb,EAAA,CAAAoC,UAAA,SAAA0H,GAAA,CAAA/I,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
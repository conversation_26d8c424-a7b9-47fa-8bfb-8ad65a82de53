import {Component, Input, Host } from '@angular/core';
import { CheckboxGroupComponent } from './checkbox-group.component';

@Component({
    // eslint-disable-next-line @angular-eslint/component-selector
    selector: 'checkbox',
    template: `
    <div  (click)="toggleCheck()">
         <input type="checkbox" class="form-check-input" [checked]="isChecked()" />
    </div>`
})
// class="form-check form-check-custom form-check-solid d-inline-flex align-items-center"
export class CheckboxComponent {
    @Input() value: any;

    constructor(@Host() private checkboxGroup: CheckboxGroupComponent) {

    }

    toggleCheck() {
        this.checkboxGroup.addOrRemove(this.value);
    }

    isChecked() {
        return this.checkboxGroup.contains(this.value);
    }
}

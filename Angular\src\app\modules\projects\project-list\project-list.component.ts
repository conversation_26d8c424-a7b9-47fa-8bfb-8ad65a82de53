import { formatDate } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectorRef,
  Component,
  HostListener,
  OnDestroy,
  OnInit,
  ViewChild,
} from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';

import saveAs from 'file-saver';
import { add, each } from 'lodash';
import {
  Subject,
  Subscription,
  debounceTime,
  distinctUntilChanged,
  filter,
} from 'rxjs';
import { AppService } from '../../services/app.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';
import { KendoColumnService } from '../../services/kendo-column.service';
import {
  CompositeFilterDescriptor,
  SortDescriptor,
} from '@progress/kendo-data-query';
import { ProjectsService } from '../../services/projects.service';
import { ProjectPopupComponent } from '../project-popup/project-popup.component';
import { ExceljsService } from '../../services/exceljs.service';

@Component({
  selector: 'app-project-list',
  templateUrl: './project-list.component.html',
  styleUrl: './project-list.component.scss',
})
export class ProjectListComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('normalGrid') grid: any;

  // Data
  public serverSideRowData: any[] = [];
  public gridData: any = [];
  public IsListHasValue: boolean = false;

  public loading: boolean = false;
  public isLoading: boolean = false;

  loginUser: any = {};

  // Search
  public searchData: string = '';
  private searchTerms = new Subject<string>();
  private searchSubscription: Subscription;

  // Enhanced Filters for Kendo UI
  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public activeFilters: Array<{
    field: string;
    operator: string;
    value: any;
  }> = [];

  public filterOptions: Array<{ text: string; value: string | null }> = [
    { text: 'All', value: null },
    { text: 'Current', value: 'Current' },
    { text: 'Completed', value: 'Completed' },
    { text: 'Cancelled & Archived', value: 'Cancelled & Archived' },
    { text: 'Closed & Archived', value: 'Closed & Archived' },
  ];

  // Advanced filter options
  public advancedFilterOptions = {
    status: [
      { text: 'All', value: null },
      { text: 'Current', value: 'Current' },
      { text: 'Completed', value: 'Completed' },
      { text: 'Cancelled & Archived', value: 'Cancelled & Archived' },
      { text: 'Closed & Archived', value: 'Closed & Archived' },
    ] as Array<{ text: string; value: string | null }>,
    centers: [] as Array<{ text: string; value: string | null }>,
  };

  // Filter state
  public showAdvancedFilters = false;
  public appliedFilters: {
    status?: string | null;
    center?: string | null;
  } = {};

  // Column visibility system
  public kendoHide: any;
  public hiddenData: any = [];
  public kendoColOrder: any = [];
  public kendoInitColOrder: any = [];
  public hiddenFields: any = [];

  // Column configuration
  public gridColumns: string[] = [];
  public defaultColumns: string[] = [];
  public normalGrid: any;
  public expandedGrid: any;
  public isExpanded = false;

  // Custom reorderable configuration that prevents fixed column reordering
  public customReorderableConfig = false;

  // Enhanced Columns with Kendo UI features
  public gridColumnConfig: Array<{
    field: string;
    title: string;
    width: number;
    isFixed: boolean;
    type: string;
    filterable?: boolean;
    order: number;
  }> = [
    {
      field: 'action',
      title: 'Action',
      width: 100,
      isFixed: true,
      type: 'action',
      order: 1,
    },
    {
      field: 'projectName',
      title: 'Project Name',
      width: 200,
      isFixed: true,
      type: 'text',
      filterable: true,
      order: 2,
    },
    {
      field: 'internalProjectNumber',
      title: 'Internal Project #',
      width: 120,
      isFixed: true,
      type: 'text',
      filterable: true,
      order: 3,
    },
    {
      field: 'projectStartDate',
      title: 'Start Date',
      width: 120,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 4,
    },
    {
      field: 'projectEndDate',
      title: 'End Date',
      width: 120,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 5,
    },
    {
      field: 'projectLocation',
      title: 'Location',
      width: 150,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 6,
    },
    {
      field: 'internalProjectManagerName',
      title: 'Manager',
      width: 150,
      isFixed: false,
      type: 'text',
      filterable: true,
      order: 7,
    },
    {
      field: 'externalPMNames',
      title: 'External PM',
      width: 220,
      type: 'status',
      isFixed: false,
      filterable: true,
      order: 8,
    },
    {
      field: 'lastUpdatedDate',
      title: 'Updated Date',
      width: 120,
      isFixed: false,
      type: 'date',
      filterable: true,
      order: 9,
    },
  ];

  // State
  public sort: SortDescriptor[] = [{ field: 'lastUpdatedDate', dir: 'desc' }];

  public page: any = {
    size: 15,
    pageNumber: 0,
    totalElements: 0,
    totalPages: 0,
    orderBy: 'lastUpdatedDate',
    orderDir: 'desc',
  };

  public skip: number = 0;

  // Selection
  public selectedRows: any[] = [];
  public isAllSelected: boolean = false;

  // Export options
  public exportOptions = [
    { text: 'All', value: 'all' },
    { text: 'Page Results', value: 'selected' },
    // { text: 'Export Filtered', value: 'filtered' },
  ];

  // Custom dropdown state
  public isExcelDropdownOpen: boolean = false;
  public dropdownTop: number = 0;
  public dropdownLeft: number = 0;

  projectName: any;
  projectId: any;

  constructor(
    private router: Router,
    private execeljsservice: ExceljsService,
    private route: ActivatedRoute,
    private projectsService: ProjectsService,
    private httpUtilService: HttpUtilsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private kendoColumnService: KendoColumnService,
    private modalService: NgbModal,
    private cdr: ChangeDetectorRef,
    public appService: AppService
  ) {
    // Initialize search subscription with debounced search
    this.searchSubscription = this.searchTerms
      .pipe(debounceTime(500), distinctUntilChanged())
      .subscribe(() => {
        // Set loading state immediately for search
        this.loading = true;
        this.isLoading = true;
        this.httpUtilService.loadingSubject.next(true);
        
        this.loadTable();
      });
  }

  ngOnInit(): void {
    this.initializeComponent();
    this.loadTable();
  }

  ngAfterViewInit(): void {
    this.initializeGrid();
  }



  ngOnDestroy(): void {
    if (this.searchSubscription) {
      this.searchTerms.complete();
    }
  }

  private initializeComponent(): void {
    // Get login user info
    // this.loginUser = this.customLayoutUtils.getLoginUser();
    this.loginUser = this.appService.getLoggedInUser();
    // Initialize column visibility system
    this.initializeColumnVisibility();
  }

  private initializeColumnVisibility(): void {
    // Set up column arrays first
    this.setupColumnArrays();
    
    // Try to load from local storage first
    const savedConfig = this.kendoColumnService.getFromLocalStorage('projects', this.loginUser.userId);
    
    if (savedConfig) {
      // Load saved settings from local storage
      this.kendoHide = savedConfig.hiddenData || [];
      this.kendoColOrder = savedConfig.kendoColOrder || [...this.defaultColumns];
      this.kendoInitColOrder = [...this.kendoColOrder];
    } else {
      // Initialize with default values
      this.kendoHide = [];
      this.hiddenData = [];
      this.kendoColOrder = [...this.defaultColumns];
      this.kendoInitColOrder = [...this.defaultColumns];
    }
    
    // Apply settings
    this.applySavedColumnSettings();
  }

  private loadColumnSettingsFromServer(): void {
    const config = {
      pageName: 'projects',
      userID: this.loginUser.userId
    };

    this.kendoColumnService.getHideFields(config).subscribe({
      next: (response) => {
        if (response.isFault === false && response.Data) {
          // Parse the saved settings
          this.kendoHide = response.Data.hideData ? JSON.parse(response.Data.hideData) : [];
          this.kendoColOrder = response.Data.kendoColOrder ? JSON.parse(response.Data.kendoColOrder) : [...this.defaultColumns];
          this.kendoInitColOrder = [...this.kendoColOrder];
          
          // Apply the settings
          this.applySavedColumnSettings();
          
          console.log('Column settings loaded from server:', {
            kendoHide: this.kendoHide,
            kendoColOrder: this.kendoColOrder
          });
        } else {
          // No saved settings, use defaults
          this.kendoHide = [];
          this.kendoColOrder = [...this.defaultColumns];
          this.kendoInitColOrder = [...this.defaultColumns];
          this.applySavedColumnSettings();
        }
      },
      error: (error) => {
        console.error('Error loading column settings:', error);
        // Use defaults on error
        this.kendoHide = [];
        this.kendoColOrder = [...this.defaultColumns];
        this.kendoInitColOrder = [...this.defaultColumns];
        this.applySavedColumnSettings();
      }
    });
  }

  private setupColumnArrays(): void {
    this.gridColumns = this.gridColumnConfig.map((col) => col.field);
    this.defaultColumns = [...this.gridColumns];
  }

  private initializeGrid(): void {
    if (this.grid) {
      // Apply saved column settings
      this.applySavedColumnSettings();
    }
  }

  private applySavedColumnSettings(): void {
    if (this.kendoHide && this.kendoHide.length > 0) {
      this.hiddenFields = this.kendoHide;
    }

    if (this.kendoColOrder && this.kendoColOrder.length > 0) {
      // Apply column order
      this.gridColumnConfig.sort((a, b) => {
        const aOrder = this.kendoColOrder.indexOf(a.field);
        const bOrder = this.kendoColOrder.indexOf(b.field);
        return aOrder - bOrder;
      });
    }
  }

  // Load table data
  public loadTable(): void {
    this.loadTableWithKendoEndpoint();
  }

  // New method to load data using Kendo UI specific endpoint
  loadTableWithKendoEndpoint() {
    this.loading = true;
    this.isLoading = true;

    // Enable loader
    this.httpUtilService.loadingSubject.next(true);

    // Safety timeout to prevent loader from getting stuck
    const loadingTimeout = setTimeout(() => {
      console.warn('Loading timeout reached, resetting loading states');
      this.resetLoadingStates();
    }, 15000); // 15 seconds timeout - reduced from 30 seconds

    // Prepare state object for Kendo UI endpoint
    const state = {
      take: this.page.size,
      skip: this.skip,
      sort: this.sort,
      filter: this.filter.filters,
      search: this.searchData,
      loggedInUserId: this.loginUser.userId,
    };

    console.log('Search request state:', {
      searchTerm: this.searchData,
      state: state
    });

    this.projectsService.getProjectsForKendoGrid(state).subscribe({
      next: (data: {
        isFault?: boolean;
        responseData?: {
          data: any[];
          total: number;
          errors?: string[];
          status?: number;
        };
        data?: any[];
        total?: number;
        errors?: string[];
        status?: number;
      }) => {
        // Clear the safety timeout since we got a response
        clearTimeout(loadingTimeout);

        console.log('API Response:', data);

        // Handle the new API response structure
        if (
          data.isFault ||
          (data.responseData &&
            data.responseData.errors &&
            data.responseData.errors.length > 0)
        ) {
          const errors = data.responseData?.errors || data.errors || [];
          console.error('Kendo UI Grid errors:', errors);

          // Check if this is an authentication error
          if (data.responseData?.status === 401 || data.status === 401) {
            console.warn('Authentication error - token may be expired');
            // Don't handle empty response here, let the interceptor handle auth
            return;
          }

          this.handleEmptyResponse();
          // Always reset loading states regardless of data content
          this.loading = false;
          this.isLoading = false;
          this.httpUtilService.loadingSubject.next(false);
        } else {
          // Handle both old and new response structures
          const responseData = data.responseData || data;
          const projectData = responseData.data || [];
          const total = responseData.total || 0;

          this.IsListHasValue = projectData.length !== 0;
          this.serverSideRowData = projectData;
          this.page.totalElements = total;
          this.page.totalPages = Math.ceil(total / this.page.size);
          
          // Create a data source with total count for Kendo Grid
          this.gridData = {
            data: projectData,
            total: total
          };
          console.log('this.serverSideRowData ', this.serverSideRowData);
          console.log('this.gridData ', this.gridData);
          console.log('this.IsListHasValue ', this.IsListHasValue);
          console.log('this.page ', this.page);
          this.cdr.markForCheck();
          // Always reset loading states regardless of data content
          this.loading = false;
          this.isLoading = false;
          this.httpUtilService.loadingSubject.next(false);
        }
      },
      error: (error: unknown) => {
        // Clear the safety timeout since we got an error
        clearTimeout(loadingTimeout);

        console.error('Error loading data with Kendo UI endpoint:', error);

        // Check if this is an authentication error
        if (error && typeof error === 'object' && 'status' in error) {
          const httpError = error as any;
          if (httpError.status === 401) {
            console.warn('Authentication error - token may be expired');
            // Don't handle empty response here, let the interceptor handle auth
            return;
          }
        }

        this.handleEmptyResponse();
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
      },
      complete: () => {
        // Clear the safety timeout
        clearTimeout(loadingTimeout);

        // Ensure loading states are reset in complete block as well
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
      },
    });
  }

  private handleEmptyResponse(): void {
    this.IsListHasValue = false;
    this.serverSideRowData = [];
    this.gridData = [];
    this.page.totalElements = 0;
    this.page.totalPages = 0;

    // Ensure loading states are reset when handling empty response
    this.loading = false;
    this.isLoading = false;
    this.httpUtilService.loadingSubject.next(false);
  }

  // Method to manually reset loading states if they get stuck
  private resetLoadingStates(): void {
    this.loading = false;
    this.isLoading = false;
    this.httpUtilService.loadingSubject.next(false);
  }

  // Public method to manually refresh the grid and reset any stuck loading states
  public refreshGrid(): void {
    console.log('Manually refreshing grid...');
    this.resetLoadingStates();
    this.loadTable();
  }


  // Search functionality
  public onSearchKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      console.log('Search triggered by Enter key:', this.searchData);

      // Set loading state immediately for search
      this.loading = true;
      this.isLoading = true;
      this.httpUtilService.loadingSubject.next(true);

      this.loadTable();
    }
  }

  public onSearchChange(): void {
    console.log('Search changed:', this.searchData);
    // Trigger search with debounce
    this.searchTerms.next(this.searchData);
  }


  private applySearch(): void {
    // Set loading state immediately for search
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    this.loadTable();
  }

  public clearSearch(): void {
    // Clear search data and reset table
    this.searchData = '';

    // Set loading state for clearing search
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    // Reload table without search filter
    this.loadTable();
  }

  // Test method for External PM search
  public testExternalPMSearch(): void {
    console.log('Testing External PM search...');
    this.searchData = 'External PM'; // Test search term

    // Set loading state immediately for test search
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    this.loadTable();
  }

  // Filter functionality
  public filterChange(filter: CompositeFilterDescriptor): void {
    this.filter = filter;

    // Set loading state immediately for filtering
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    this.loadTable();
  }

  public applyAdvancedFilters(): void {
    // Apply status filter (use new 'status' field)
    if (this.appliedFilters.status) {
      this.filter.filters = this.filter.filters.filter((f) => {
        if ('field' in f) {
          return f.field !== 'status' && f.field !== 'projectStatus';
        }
        return true;
      });
      this.filter.filters.push({
        field: 'status',
        operator: 'eq',
        value: this.appliedFilters.status,
      });
    }

    // Apply center filter
    if (this.appliedFilters.center) {
      this.filter.filters = this.filter.filters.filter((f) => {
        if ('field' in f) {
          return f.field !== 'centerId';
        }
        return true;
      });
      this.filter.filters.push({
        field: 'centerId',
        operator: 'eq',
        value: this.appliedFilters.center,
      });
    }

    this.loadTable();
  }

  public clearAdvancedFilters(): void {
    this.appliedFilters = {};
    this.filter.filters = [];

    // Set loading state immediately for clearing filters
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    this.loadTable();
  }

  public clearAllFilters(): void {
    this.appliedFilters = {};
    this.filter.filters = [];

    // Set loading state immediately for clearing filters
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    this.loadTable();
  }

  // Sorting functionality
  public onSortChange(sort: SortDescriptor[]): void {
    // Check if this is the 3rd click (dir is undefined)
    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;

    if (isThirdClick) {
      // 3rd click - clear sort and use default
      this.sort = [];
      this.page.orderBy = 'lastUpdatedDate';
      this.page.orderDir = 'desc';
    } else if (sort.length > 0 && sort[0] && sort[0].dir) {
      // Valid sort with direction
      this.sort = sort;
      this.page.orderBy = sort[0].field || 'lastUpdatedDate';
      this.page.orderDir = sort[0].dir;
    } else {
      // Empty sort array or invalid sort
      this.sort = [];
      this.page.orderBy = 'lastUpdatedDate';
      this.page.orderDir = 'desc';
    }

    // Reset to first page
    this.skip = 0;
    this.page.pageNumber = 0;

    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    setTimeout(() => this.loadTable(), 0);
  }

  // Pagination functionality
  public pageChange(event: any): void {
    // Use Kendo's provided values as source of truth
    this.skip = event.skip;
    this.page.size = event.take || this.page.size;
    this.page.pageNumber = Math.floor(this.skip / this.page.size);

    // Set loading state immediately for pagination
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);

    this.loadTable();
  }



  public updateColumnVisibility(event: any): void {
    // Handle column visibility changes
    const hiddenColumns = event.hiddenColumns || [];
    this.hiddenFields = hiddenColumns;
  }

  public getHiddenField(fieldName: string): boolean {
    return this.hiddenFields.includes(fieldName);
  }









  // Selection functionality
  public onSelectionChange(event: any): void {
    this.selectedRows = event.selectedRows || [];
    this.isAllSelected =
      this.selectedRows.length === this.serverSideRowData.length;
  }

  public selectAll(): void {
    if (this.isAllSelected) {
      this.selectedRows = [];
      this.isAllSelected = false;
    } else {
      this.selectedRows = [...this.serverSideRowData];
      this.isAllSelected = true;
    }
  }

  // Grid expansion
  public toggleExpand(): void {
    // Find grid container element and toggle fullscreen class
    const gridContainer = document.querySelector(
      '.grid-container'
    ) as HTMLElement;
    if (gridContainer) {
      gridContainer.classList.toggle('fullscreen-grid');
      this.isExpanded = !this.isExpanded;
      // Refresh grid after resize to ensure proper rendering
      if (this.grid) {
        this.grid.refresh();
      }
    }
  }

  // Export functionality
  // public onExportClick(event: any): void {
  //   const exportType = event.item.value;
  //   let selectedIds: number[] = [];

  //   switch (exportType) {
  //     case 'selected':
  //       selectedIds = this.selectedRows.map((row) => row.projectId);
  //       if (selectedIds.length === 0) {
  //         //alert('Please select projects to export');
  //         return;
  //       }
  //       break;
  //     case 'filtered':
  //       // Export filtered data
  //       break;
  //     case 'all':
  //     default:
  //       // Export all data
  //       break;
  //   }

  //   this.exportProjects(exportType, selectedIds);
  // }

  // private exportProjects(exportType: string, selectedIds: number[]): void {
  //   this.projectsService.exportProjects(exportType, selectedIds).subscribe({
  //     next: (response: any) => {
  //       if (response.data) {
  //         const blob = new Blob([response.data], {
  //           type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  //         });
  //         saveAs(
  //           blob,
  //           `projects_${exportType}_${
  //             new Date().toISOString().split('T')[0]
  //           }.xlsx`
  //         );
  //       }
  //     },
  //     error: (error: any) => {
  //       console.error('Export error:', error);
  //       //alert('Error exporting projects data');
  //     },
  //   });
  // }

  onExportClick(event: any) {
    const selectedOption = event.value; // Get selected option

    let prdItems: any = [];
    if (selectedOption === 'selected') {
      prdItems = this.serverSideRowData;

      // declare the title and header data for excel
      // get the data for excel in a array format
      this.exportExcel(prdItems);
    } else if (selectedOption === 'all') {
      const queryparamsExcel = {
        pageSize: this.page.totalElements,
        sortOrder: this.page.orderDir,
        sortField: this.page.orderBy,
        pageNumber: this.page.pageNumber,
        // filter: this.filterConfiguration()
      };

      // Enable loading indicator
      this.httpUtilService.loadingSubject.next(true);
      // API call
      this.projectsService
        .getAllProjects(queryparamsExcel)
        // .pipe(map((data: any) => data as any))
        .subscribe((data) => {
          // Disable loading indicator
          this.httpUtilService.loadingSubject.next(false);
          if (data.isFault) {
            this.IsListHasValue = false;
            this.cdr.markForCheck();
            return; // Exit early if the response has a fault
          }

          this.IsListHasValue = true;
          prdItems = data.responseData.data || [];

          this.cdr.detectChanges(); // Manually trigger UI update
          this.exportExcel(prdItems);
        });
    }
  }

  exportExcel(listOfItems: any): void {
    // Define local variables for the items and current date
    let prdItems: any = listOfItems;
    let currentDate: Date = this.appService.formatMonthDate(new Date());

    console.log('prdItems', prdItems);

    // Check if the data exists and is not empty
    if (prdItems !== undefined && prdItems.length > 0) {
      // Define the title for the Excel file
      const tableTitle = 'Events';

      // Filter out hidden columns and sort by order
      // const visibleColumns = this.columnJSONFormat
      //   .filter((col: any) => !col.hidden)
      //   .sort((a: any, b: any) => a.order - b.order);

      // Create header from visible columns

      const headerArray = [
        'Project Name',
        'Internal Projet #',
        'Start Date',
        'End Date',
        'Location',
        'Manager',
        'External PM',
      ];
      // ...visibleColumns.map((col: any) => col.title),

      // Define which columns should have currency and percentage formatting
      // const currencyColumns: any = [
      //   'Pending',
      //   'ACAT',
      //   'Annuity',
      //   'AUM',
      //   'Total Assets',
      //   'Event Cost',
      //   'Gross Profit',
      // ].filter((col) => headerArray.includes(col));

      const percentageColumns: any = [];

      // Get the data for excel in an array format
      const respResult: any = [];

      // Prepare the data for export based on visible columns
      each(prdItems, (prdItem: any) => {
        // Create an array with the same length as headerArray
        const respData = Array(headerArray.length).fill(null);
        respData[0] = prdItem.eventDescription;
        respData[1] = this.appService.formatMonthDate(prdItem.event_date);
        // Fill in data for each visible column
        headerArray.forEach((col: any, i: number) => {
          const adjustedIndex = i; // +2 for 'Name' and 'Hot'
          switch (col) {
            case 'Project Name':
              respData[adjustedIndex] = prdItem.projectName;
              break;
            case 'Internal Projet #':
              respData[adjustedIndex] = prdItem.internalProjectNumber;
              break;
            case 'Start Date':
              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectStartDate);
              break;
            case 'End Date':
              respData[adjustedIndex] = this.appService.formatDate(prdItem.projectEndDate);
              break;
            case 'Location':
              respData[adjustedIndex] = prdItem.projectLocation;
              break;
            case 'Manager':
              respData[adjustedIndex] = prdItem.internalProjectManagerName;
              break;
            case 'External PM':
              respData[adjustedIndex] = prdItem.externalPMNames;
              break;
            // case 'kept_appointments':
            //   respData[adjustedIndex] = prdItem.kept_appointments;
            //   break;
            // case 'kept_appt_ratio':
            //   respData[adjustedIndex] = prdItem.kept_appt_ratio;
            //   break;
            // case 'apptKeptNo':
            //   respData[adjustedIndex] = prdItem.apptKeptNo;
            //   break;
            // case 'has_assets':
            //   respData[adjustedIndex] = prdItem.has_assets;
            //   break;
            // case 'prospects_closed':
            //   respData[adjustedIndex] = prdItem.prospects_closed;
            //   break;
            // case 'closing_ratio':
            //   respData[adjustedIndex] = prdItem.closing_ratio;
            //   break;
            // case 'totalPending':
            //   respData[adjustedIndex] = prdItem.totalPending;
            //   break;
            // case 'acatproduction':
            //   respData[adjustedIndex] = prdItem.acatproduction;
            //   break;
            // case 'annuityproduction':
            //   respData[adjustedIndex] = prdItem.annuityproduction;
            //   break;
            // case 'aumproduction':
            //   respData[adjustedIndex] = prdItem.aumproduction;
            //   break;
            // case 'totalAssets':
            //   respData[adjustedIndex] = prdItem.totalAssets;
            //   break;
            // case 'eventCost':
            //   respData[adjustedIndex] = prdItem.eventCost;
            //   break;
            // case 'grossProfit':
            //   respData[adjustedIndex] = prdItem.grossProfit;
            //   break;
            // case 'status':
            //   respData[adjustedIndex] = prdItem.status;
            //   break;
          }
        });

        respResult.push(respData);
      });

      // Define column sizes for the Excel file
      const colSize = headerArray.map((header, index) => ({
        id: index + 1,
        width: 20,
      }));

      // Generate the Excel file using the exceljsService
      this.execeljsservice.generateExcel(
        tableTitle,
        headerArray,
        respResult,
        colSize
        // currencyColumns,
        // percentageColumns
      );
    } else {
      const message = 'There are no records available to export.';
      // this.layoutUtilService.showError(message, '');
    }
  }
  // Column settings management
  public saveHead(): void {
    const settings = {
      kendoHide: this.hiddenFields,
      kendoColOrder: this.kendoColOrder,
      kendoInitColOrder: this.kendoInitColOrder,
    };

    // Save to local storage only
    this.kendoColumnService.saveToLocalStorage({
      pageName: 'projects',
      userID: this.loginUser.userId,
      hiddenData: settings.kendoHide,
      kendoColOrder: settings.kendoColOrder,
      LoggedId: this.loginUser.userId
    });

    console.log('Column settings saved locally:', settings);
                            this.customLayoutUtilsService.showSuccess('Column settings saved locally', '');

    //alert('Column settings saved locally');
  }

  private saveColumnSettingsToServer(settings: any): void {
    const config = {
      pageName: 'projects',
      userID: this.loginUser.userId,
      hiddenData: settings.kendoHide,
      kendoColOrder: settings.kendoColOrder,
      LoggedId: this.loginUser.userId
    };

    this.kendoColumnService.createHideFields(config).subscribe({
      next: (response) => {
        if (response.isFault === false) {
          console.log('Column settings saved successfully:', response);
          
                            this.customLayoutUtilsService.showSuccess('Column settings saved successfully', '');
          //alert('Column settings saved successfully');
        } else {
          console.error('Failed to save column settings:', response.message);
                            this.customLayoutUtilsService.showError('Column settings failed to save', '');

          //alert('Failed to save column settings: ' + response.message);
        }
      },
      error: (error) => {
        console.error('Error saving column settings:', error);
                            this.customLayoutUtilsService.showError('Error saving column settings', '');

        //alert('Error saving column settings. Please try again.');
      }
    });
  }

  private saveResetToServer(): void {
    // First delete existing settings
    const deleteConfig = {
      pageName: 'projects',
      userID: this.loginUser.userId
    };

    this.kendoColumnService.deleteHideFields(deleteConfig).subscribe({
      next: (response) => {
        console.log('Existing settings deleted:', response);
        // Then save the reset state (all columns visible)
        this.saveColumnSettingsToServer({
          kendoHide: [],
          kendoColOrder: this.defaultColumns,
          kendoInitColOrder: this.defaultColumns
        });
      },
      error: (error) => {
        console.error('Error deleting existing settings:', error);
        // Still try to save the reset state
        this.saveColumnSettingsToServer({
          kendoHide: [],
          kendoColOrder: this.defaultColumns,
          kendoInitColOrder: this.defaultColumns
        });
      }
    });
  }

  public resetTable(): void {
    console.log('Resetting Kendo settings for projects');
    
    // Clear all saved settings first
    this.kendoHide = [];
    this.hiddenData = [];
    this.kendoColOrder = [];
    this.kendoInitColOrder = [];
    
    // Clear local storage
    this.kendoColumnService.clearFromLocalStorage('projects');
    
    // Reset to default settings
    this.resetToDefaultSettings();
    
    // Trigger change detection to update the template
    this.cdr.detectChanges();
    
    // Force grid refresh to show all columns
    if (this.grid) {
      this.grid.refresh();
    }

    // Show success message
    console.log('Table reset to default settings');
    
    //alert('Table reset to default settings - all columns restored');
  }
  private resetToDefaultSettings(): void {
    console.log('Resetting to default settings...');

    // Reset column visibility - show all columns
    this.hiddenFields = [];
    this.gridColumns = [...this.defaultColumns];
    this.kendoColOrder = [...this.defaultColumns];

    // Reset sort state to default
    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
    this.page.orderBy = 'lastUpdatedDate';
    this.page.orderDir = 'desc';

    // Reset page state
    this.page.pageNumber = 0;
    this.skip = 0;

    // Reset all filters - clear everything
    this.filter = { logic: 'and', filters: [] };
    this.activeFilters = [];

    // Reset advanced filters
    this.appliedFilters = {};

    // Reset search
    this.searchData = '';

    // Reset advanced filters visibility
    this.showAdvancedFilters = false;

    console.log('Reset completed:', {
      hiddenFields: this.hiddenFields,
      gridColumns: this.gridColumns,
      defaultColumns: this.defaultColumns,
      sort: this.sort,
      filter: this.filter,
      searchData: this.searchData
    });

    // Reset the Kendo Grid's internal state
    if (this.grid) {
      // Clear all filters
      this.grid.filter = { logic: 'and', filters: [] };

      // Reset sorting
      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];

      // Reset column visibility - show all columns
      this.grid.columns.forEach((column: any) => {
        if (column.field && column.field !== 'action') {
          column.hidden = false;
        }
      });

      // Reset to first page
      this.grid.skip = 0;
      this.grid.pageSize = this.page.size;
    }

    // Trigger change detection
    this.cdr.detectChanges();

    // Force grid refresh to apply all changes
    if (this.grid) {
      setTimeout(() => {
        this.grid.refresh();
        // Also try to reset the grid state completely
        this.grid.reset();
      }, 100);
    }

    // Reload data with clean state
    this.loadTable();
  }
  // Navigation
  public add(): void {
    this.edit(0);
  }

  public view(projectId: number): void {
    this.router.navigate(['/projects/view', projectId]);
  }

  public edit(projectId: number): void {
    if (projectId == 0) {
      // Trigger global loader BEFORE opening modal to ensure full-page overlay
      this.httpUtilService.loadingSubject.next(true);

      // Open modal for new project
      const modalRef = this.modalService.open(ProjectPopupComponent, {
        size: 'lg',
        centered: true,
        backdrop: 'static'
      });

      modalRef.componentInstance.id = projectId;
      modalRef.componentInstance.project = null;

      modalRef.componentInstance.passEntry.subscribe((result: boolean) => {
        if (result) {
          // Refresh the grid after successful add
          this.loadTable();
        }
      });
    } else {
      // Navigate to project view for existing projects
      this.router.navigate(['/projects/view', projectId]);
    }
  }

  public delete(projectId: number): void {
    // if (confirm('Are you sure you want to delete this project?')) {
     
    // }
     this.projectsService.deleteProject({ projectId }).subscribe({
        next: (response: any) => {

          console.log("response",response)
          if (!response.isFault) {
                            this.customLayoutUtilsService.showSuccess('Project deleted successfully', '');

            //alert('Project deleted successfully');
            this.loadTable();
          }
        },
        error: (error: any) => {
          console.error('Delete error:', error);
                            this.customLayoutUtilsService.showError('error deleting project', '');

          //alert('Error deleting project');
        },
      });
  }

  // Utility methods
  public getProjectFullName(project: any): string {
    return `${project.projectFirstName || ''} ${
      project.projectLastName || ''
    }`.trim();
  }

  public getCenterName(project: any): string {
    return project.medicalCenter?.centerName || '';
  }

  public formatDate(dateString: string): string {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString();
  }

  public getStatusClass(status: string): string {
    if (!status) return 'badge-light-secondary';
    const key = status.toLowerCase();
    if (key === 'current') return 'badge-light-success';
    if (key === 'completed') return 'badge-light-primary';
    if (key === 'cancelled & archived') return 'badge-light-dark';
    if (key === 'closed & archived') return 'badge-light-info';
    return 'badge-light-secondary';
  }
  deletePop(content: any, projectId: any, projectName: any) {
    this.projectName = projectName;
    this.projectId = projectId;
    this.modalService.open(content, { centered: true });
  }

  confirmDelete() {
    // console.log('Item deleted ✅');
    this.delete(this.projectId);
    // your delete logic here
  }

  onTabActivated() {
    // This method is called when the tab is activated
    // You can add any specific logic here if needed
    console.log('Projects tab activated');
  }

  // Custom dropdown methods
  toggleExcelDropdown(event?: Event): void {
    this.isExcelDropdownOpen = !this.isExcelDropdownOpen;
    console.log('Excel dropdown toggled:', this.isExcelDropdownOpen);

    if (this.isExcelDropdownOpen && event) {
      const button = event.target as HTMLElement;
      const rect = button.getBoundingClientRect();
      this.dropdownTop = rect.bottom + window.scrollY;
      this.dropdownLeft = rect.left + window.scrollX;
      console.log('Dropdown position:', this.dropdownTop, this.dropdownLeft);
    }
  }

  closeExcelDropdown(): void {
    this.isExcelDropdownOpen = false;
    console.log('Excel dropdown closed');
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event): void {
    const target = event.target as HTMLElement;
    const dropdown = target.closest('.custom-dropdown');
    if (!dropdown && this.isExcelDropdownOpen) {
      this.closeExcelDropdown();
    }
  }
}

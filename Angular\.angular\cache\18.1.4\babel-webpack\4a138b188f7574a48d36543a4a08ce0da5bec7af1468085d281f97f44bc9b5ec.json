{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"span\", 5);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 6);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_1_div_32_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26);\n    i0.ɵɵelement(2, \"i\", 27);\n    i0.ɵɵelementStart(3, \"p\", 28);\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_1_div_33_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 32);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_1_div_33_tr_15_Template_a_click_2_listener() {\n      const permit_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r4.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"td\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"td\")(7, \"select\", 33);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_1_div_33_tr_15_Template_select_change_7_listener($event) {\n      const permit_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r4, $event.target.value));\n    });\n    i0.ɵɵelementStart(8, \"option\", 34);\n    i0.ɵɵtext(9, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"option\", 35);\n    i0.ɵɵtext(11, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"option\", 36);\n    i0.ɵɵtext(13, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"option\", 37);\n    i0.ɵɵtext(15, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"option\", 38);\n    i0.ɵɵtext(17, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"option\", 39);\n    i0.ɵɵtext(19, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"option\", 40);\n    i0.ɵɵtext(21, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"option\", 41);\n    i0.ɵɵtext(23, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(24, \"td\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"td\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const permit_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r4.permitName || permit_r4.permitType || \"N/A\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(permit_r4.permitNumber || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r4.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(17);\n    i0.ɵɵtextInterpolate(permit_r4.cycleDays || \"N/A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(permit_r4.attentionReason || permit_r4.comments || \"No comments\");\n  }\n}\nfunction ProjectViewComponent_div_1_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29)(1, \"table\", 30)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Cycle (Days)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Comments\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_1_div_33_tr_15_Template, 28, 7, \"tr\", 31);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 7)(1, \"div\", 8)(2, \"div\", 9)(3, \"h1\", 10);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 11);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_1_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(6, \"i\", 12);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(7, \"div\", 13)(8, \"div\", 14)(9, \"div\", 15)(10, \"label\");\n    i0.ɵɵtext(11, \"Manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 16);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 15)(15, \"label\");\n    i0.ɵɵtext(16, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\", 17);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 15)(20, \"label\");\n    i0.ɵɵtext(21, \"Issues\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"span\", 16);\n    i0.ɵɵtext(23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"div\", 15)(25, \"label\");\n    i0.ɵɵtext(26, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"span\", 16);\n    i0.ɵɵtext(28);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(29, \"div\", 18)(30, \"h3\", 19);\n    i0.ɵɵtext(31, \"Permits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(32, ProjectViewComponent_div_1_div_32_Template, 5, 0, \"div\", 20)(33, ProjectViewComponent_div_1_div_33_Template, 16, 1, \"div\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 22)(35, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_1_Template_button_click_35_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(36, \"i\", 24);\n    i0.ɵɵtext(37, \" Back \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectName || \"Project\");\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"N/A\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngClass\", ctx_r1.getStatusClass(ctx_r1.project.projectStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.projectPermits.length || 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectDescription || \"No comments\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nexport class ProjectViewComponent {\n  route;\n  router;\n  cdr;\n  appService;\n  projectsService;\n  permitsService;\n  modalService;\n  projectId = null;\n  project = null;\n  isLoading = false;\n  projectPermits = [];\n  loginUser = {};\n  routeSubscription = new Subscription();\n  constructor(route, router, cdr, appService, projectsService, permitsService, modalService) {\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.modalService = modalService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n      console.log('Project view - received params:', {\n        projectId: this.projectId,\n        queryParams\n      });\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n  fetchProjectDetails() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    // Pass loggedInUserId to satisfy backend auth/filters\n    this.projectsService.getProject({\n      projectId: this.projectId,\n      loggedInUserId: this.loginUser?.userId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchProjectPermits() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [{\n          field: 'projectId',\n          operator: 'eq',\n          value: this.projectId\n        }]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter(p => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    if (!this.projectId) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    // Subscribe to the modal event when it closes\n    modalRef.result.then(result => {\n      // Handle successful edit\n      if (result) {\n        console.log('Project edited successfully:', result);\n        // Refresh project details\n        this.fetchProjectDetails();\n      }\n    }, reason => {\n      // Handle modal dismissal\n      console.log('Modal dismissed:', reason);\n    });\n  }\n  viewPermit(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'project',\n        projectId: this.projectId\n      }\n    });\n  }\n  onStatusChange(permit, newStatus) {\n    if (!permit?.permitId || !newStatus) {\n      return;\n    }\n    const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n    if (!allowed.includes(newStatus)) {\n      return;\n    }\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.isLoading = true;\n    this.cdr.markForCheck();\n    this.permitsService.updatePermitInternalReviewStatus({\n      permitId: permit.permitId,\n      internalReviewStatus: newStatus\n    }).subscribe({\n      next: res => {\n        const isFault = res?.isFault || res?.responseData?.isFault;\n        if (isFault) {\n          permit.internalReviewStatus = previous;\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        }\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        permit.internalReviewStatus = previous;\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 2,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [\"class\", \"project-view-container\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"project-view-container\"], [1, \"project-title-section\"], [1, \"project-title-header\"], [1, \"project-title\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-link\", \"p-0\", \"edit-button\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\"], [1, \"project-details-card\"], [1, \"project-details-content\"], [1, \"project-detail-item\"], [1, \"project-value\"], [1, \"status-text\", 3, \"ngClass\"], [1, \"permits-section\"], [1, \"permits-title\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"back-button-section\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"empty-state\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\", \"text-muted\"], [1, \"text-muted\"], [1, \"table-responsive\"], [1, \"permits-table\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"permit-link\", 3, \"click\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0)(1, ProjectViewComponent_div_1_Template, 38, 8, \"div\", 1);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.NgSelectOption, i7.ɵNgSelectMultipleOption],\n    styles: [\".project-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 1.5rem;\\n  padding: 1.5rem;\\n  background-color: #f8f9fa;\\n  min-height: 100vh;\\n}\\n\\n.project-title-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e5eaee;\\n}\\n.project-title-section[_ngcontent-%COMP%]   .project-title-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n.project-title-section[_ngcontent-%COMP%]   .project-title-header[_ngcontent-%COMP%]   .project-title[_ngcontent-%COMP%] {\\n  font-size: 1.75rem;\\n  font-weight: 700;\\n  color: #181c32;\\n  margin: 0;\\n}\\n.project-title-section[_ngcontent-%COMP%]   .project-title-header[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%] {\\n  font-size: 1.1rem;\\n  color: #007bff;\\n  transition: color 0.2s ease;\\n}\\n.project-title-section[_ngcontent-%COMP%]   .project-title-header[_ngcontent-%COMP%]   .edit-button[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n}\\n\\n.project-details-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e5eaee;\\n}\\n.project-details-card[_ngcontent-%COMP%]   .project-details-content[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n  gap: 2rem;\\n  padding: 0;\\n}\\n\\n.project-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.5rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 0.875rem;\\n  color: #6c7293;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n  margin: 0;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  margin: 0;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.badge-green-light[_ngcontent-%COMP%] {\\n  background-color: #42c761; \\n\\n  color: #155724; \\n\\n}\\n\\n.back-button-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: flex-start;\\n  margin-top: 1rem;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n\\n.status-cancelled[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n\\n.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permits-section[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 8px;\\n  padding: 1.5rem;\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  border: 1px solid #e5eaee;\\n}\\n.permits-section[_ngcontent-%COMP%]   .permits-title[_ngcontent-%COMP%] {\\n  font-size: 1.25rem;\\n  font-weight: 600;\\n  color: #181c32;\\n  margin: 0 0 1.5rem 0;\\n}\\n.permits-section[_ngcontent-%COMP%]   .empty-state[_ngcontent-%COMP%] {\\n  padding: 3rem 0;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  margin-top: 0;\\n}\\n\\n.permits-table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  width: 100%;\\n  border-collapse: collapse;\\n}\\n.permits-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.permits-table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #3f4254;\\n  border-bottom: 2px solid #e5eaee;\\n  padding: 1rem 0.75rem;\\n  text-align: left;\\n}\\n.permits-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  border-bottom: 1px solid #e5eaee;\\n}\\n.permits-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n.permits-table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n}\\n.permits-table[_ngcontent-%COMP%]   .permit-link[_ngcontent-%COMP%] {\\n  color: #007bff;\\n  text-decoration: none;\\n  font-weight: 500;\\n  cursor: pointer;\\n}\\n.permits-table[_ngcontent-%COMP%]   .permit-link[_ngcontent-%COMP%]:hover {\\n  color: #0056b3;\\n  text-decoration: underline;\\n}\\n\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%] {\\n  color: var(--bs-primary, #0d6efd);\\n  text-decoration: none;\\n  font-weight: 700;\\n  cursor: pointer;\\n  transition: color 0.15s ease, -webkit-text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease, -webkit-text-decoration 0.15s ease;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:hover {\\n  color: #0b5ed7;\\n  text-decoration: underline;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(24, 125, 228, 0.25);\\n  border-radius: 0.25rem;\\n}\\n\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-description-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-type-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-status-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n}\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.project-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.project-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: white;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .project-view-container[_ngcontent-%COMP%] {\\n    padding: 1rem;\\n    gap: 1rem;\\n  }\\n  .project-details-card[_ngcontent-%COMP%]   .project-details-content[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n    gap: 1.5rem;\\n  }\\n  .permits-table[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .project-details-card[_ngcontent-%COMP%]   .project-details-content[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 1rem;\\n  }\\n  .project-title-section[_ngcontent-%COMP%]   .project-title-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: flex-start;\\n  }\\n  .permits-table[_ngcontent-%COMP%] {\\n    font-size: 0.8rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "combineLatest", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵlistener", "ProjectViewComponent_div_1_div_33_tr_15_Template_a_click_2_listener", "permit_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "viewPermit", "permitId", "ProjectViewComponent_div_1_div_33_tr_15_Template_select_change_7_listener", "$event", "onStatusChange", "target", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "permitName", "permitType", "ɵɵtextInterpolate", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "cycleDays", "attentionReason", "comments", "ɵɵtemplate", "ProjectViewComponent_div_1_div_33_tr_15_Template", "projectPermits", "ProjectViewComponent_div_1_Template_button_click_5_listener", "_r1", "editProject", "ProjectViewComponent_div_1_div_32_Template", "ProjectViewComponent_div_1_div_33_Template", "ProjectViewComponent_div_1_Template_button_click_35_listener", "goBack", "project", "projectName", "internalProjectManagerName", "internalProjectManager", "getStatusClass", "projectStatus", "length", "projectDescription", "ProjectViewComponent", "route", "router", "cdr", "appService", "projectsService", "permitsService", "modalService", "projectId", "loginUser", "routeSubscription", "constructor", "ngOnInit", "getLoggedInUser", "paramMap", "queryParams", "subscribe", "idParam", "get", "Number", "console", "log", "fetchProjectDetails", "fetchProjectPermits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "getProject", "loggedInUserId", "userId", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "Project", "data", "Object", "keys", "error", "faultMessage", "err", "getPermitsForKendoGrid", "take", "skip", "sort", "filter", "logic", "filters", "field", "operator", "search", "rawPermits", "p", "permitProjectId", "projectID", "project_id", "ProjectId", "ProjectID", "navigate", "NgbModalOptions", "size", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "id", "result", "then", "reason", "from", "permit", "newStatus", "allowed", "includes", "previous", "updatePermitInternalReviewStatus", "status", "toLowerCase", "replace", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "ChangeDetectorRef", "i2", "AppService", "i3", "ProjectsService", "i4", "PermitsService", "i5", "NgbModal", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_1_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription, combineLatest } from 'rxjs';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ProjectsService } from '../../services/projects.service';\nimport { PermitsService } from '../../services/permits.service';\nimport { AppService } from '../../services/app.service';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\n\n@Component({\n  selector: 'app-project-view',\n  templateUrl: './project-view.component.html',\n  styleUrls: ['./project-view.component.scss']\n})\nexport class ProjectViewComponent implements OnInit, OnDestroy {\n  public projectId: number | null = null;\n  public project: any = null;\n  public isLoading: boolean = false;\n  public projectPermits: any[] = [];\n  public loginUser: any = {};\n  private routeSubscription: Subscription = new Subscription();\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef,\n    private appService: AppService,\n    private projectsService: ProjectsService,\n    private permitsService: PermitsService,\n    private modalService: NgbModal\n  ) {}\n\n  ngOnInit(): void {\n    this.loginUser = this.appService.getLoggedInUser();\n\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([\n      this.route.paramMap,\n      this.route.queryParams\n    ]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n\n      console.log('Project view - received params:', { projectId: this.projectId, queryParams });\n      \n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      \n      this.cdr.markForCheck();\n    });\n  }\n\n  ngOnDestroy(): void {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n\n  public fetchProjectDetails(): void {\n    if (!this.projectId) { return; }\n    this.isLoading = true;\n    // Pass loggedInUserId to satisfy backend auth/filters\n    this.projectsService.getProject({ projectId: this.projectId, loggedInUserId: this.loginUser?.userId }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err) => {\n        this.isLoading = false;\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public fetchProjectPermits(): void {\n    if (!this.projectId) { return; }\n    this.isLoading = true;\n    \n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [\n          {\n            field: 'projectId',\n            operator: 'eq',\n            value: this.projectId\n          }\n        ]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter((p: any) => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public goBack(): void {\n    this.router.navigate(['/projects/list']);\n  }\n\n  public editProject(): void {\n    if (!this.projectId) { return; }\n    \n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(\n      ProjectPopupComponent,\n      NgbModalOptions\n    );\n    \n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    \n    // Subscribe to the modal event when it closes\n    modalRef.result.then(\n      (result) => {\n        // Handle successful edit\n        if (result) {\n          console.log('Project edited successfully:', result);\n          // Refresh project details\n          this.fetchProjectDetails();\n        }\n      },\n      (reason) => {\n        // Handle modal dismissal\n        console.log('Modal dismissed:', reason);\n      }\n    );\n  }\n\n  public viewPermit(permitId: number): void {\n    this.router.navigate(['/permits/view', permitId], { \n      queryParams: { from: 'project', projectId: this.projectId } \n    });\n  }\n\n  public onStatusChange(permit: any, newStatus: string): void {\n    if (!permit?.permitId || !newStatus) { return; }\n    const allowed = [\n      'Approved',\n      'Pacifica Verification',\n      'Dis-Approved',\n      'Pending',\n      'Not Required',\n      'In Review',\n      '1 Cycle Completed'\n    ];\n    if (!allowed.includes(newStatus)) { return; }\n\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.isLoading = true;\n    this.cdr.markForCheck();\n\n    this.permitsService\n      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })\n      .subscribe({\n        next: (res: any) => {\n          const isFault = res?.isFault || res?.responseData?.isFault;\n          if (isFault) {\n            permit.internalReviewStatus = previous;\n            this.isLoading = false;\n            this.cdr.markForCheck();\n          }\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        },\n        error: () => {\n          permit.internalReviewStatus = previous;\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  public getStatusClass(status: string): string {\n    if (!status) return 'status-n-a';\n    return (\n      'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-')\n    );\n  }\n\n}\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"project-view-container\" *ngIf=\"project\">\r\n  <!-- Project Title Section -->\r\n  <div class=\"project-title-section\">\r\n    <div class=\"project-title-header\">\r\n      <h1 class=\"project-title\">{{ project.projectName || \"Project\" }}</h1>\r\n      <button type=\"button\" class=\"btn btn-link p-0 edit-button\" (click)=\"editProject()\" title=\"Edit Project\">\r\n        <i class=\"fas fa-edit text-primary\"></i>\r\n      </button>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Project Details Card -->\r\n  <div class=\"project-details-card\">\r\n    <div class=\"project-details-content\">\r\n      <div class=\"project-detail-item\">\r\n        <label>Manager</label>\r\n        <span class=\"project-value\">{{ project.internalProjectManagerName || project.internalProjectManager || \"N/A\" }}</span>\r\n      </div>\r\n      <div class=\"project-detail-item\">\r\n        <label>Status</label>\r\n        <span class=\"status-text\" [ngClass]=\"getStatusClass(project.projectStatus)\">{{ project.projectStatus || \"Active\" }}</span>\r\n      </div>\r\n      <div class=\"project-detail-item\">\r\n        <label>Issues</label>\r\n        <span class=\"project-value\">{{ projectPermits.length || 0 }}</span>\r\n      </div>\r\n      <div class=\"project-detail-item\">\r\n        <label>Comments</label>\r\n        <span class=\"project-value\">{{ project.projectDescription || \"No comments\" }}</span>\r\n      </div>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Permits Section -->\r\n  <div class=\"permits-section\">\r\n    <h3 class=\"permits-title\">Permits</h3>\r\n    \r\n    <!-- Empty State for Permits -->\r\n    <div class=\"empty-state\" *ngIf=\"projectPermits.length === 0\">\r\n      <div class=\"text-center\">\r\n        <i class=\"fas fa-file-alt fa-3x mb-3 text-muted\"></i>\r\n        <p class=\"text-muted\">No permits found for this project.</p>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Permits Table -->\r\n    <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\r\n      <table class=\"permits-table\">\r\n        <thead>\r\n          <tr>\r\n            <th>Permit Type</th>\r\n            <th>Source</th>\r\n            <th>Status</th>\r\n            <th>Cycle (Days)</th>\r\n            <th>Comments</th>\r\n          </tr>\r\n        </thead>\r\n        <tbody>\r\n          <tr *ngFor=\"let permit of projectPermits\">\r\n            <td>\r\n              <a \r\n                class=\"permit-link\" \r\n                (click)=\"viewPermit(permit.permitId)\" \r\n                title=\"View Permit\"\r\n                aria-label=\"View Permit\"\r\n              >\r\n                {{ permit.permitName || permit.permitType || \"N/A\" }}\r\n              </a>\r\n            </td>\r\n            <td>{{ permit.permitNumber || \"N/A\" }}</td>\r\n            <td>\r\n              <select class=\"form-select form-select-sm w-auto\"\r\n                      [value]=\"permit.internalReviewStatus || ''\"\r\n                      (change)=\"onStatusChange(permit, $any($event.target).value)\"\r\n                      [disabled]=\"isLoading\">\r\n                <option [value]=\"''\" disabled>Select status</option>\r\n                <option value=\"Approved\">Approved</option>\r\n                <option value=\"Pacifica Verification\">Pacifica Verification</option>\r\n                <option value=\"Dis-Approved\">Dis-Approved</option>\r\n                <option value=\"Pending\">Pending</option>\r\n                <option value=\"Not Required\">Not Required</option>\r\n                <option value=\"In Review\">In Review</option>\r\n                <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\r\n              </select>\r\n            </td>\r\n            <td>{{ permit.cycleDays || \"N/A\" }}</td>\r\n            <td>{{ permit.attentionReason || permit.comments || \"No comments\" }}</td>\r\n          </tr>\r\n        </tbody>\r\n      </table>\r\n    </div>\r\n  </div>\r\n\r\n  <!-- Back Button -->\r\n  <div class=\"back-button-section\">\r\n    <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center\" (click)=\"goBack()\">\r\n      <i class=\"fas fa-arrow-left me-2\"></i>\r\n      Back\r\n    </button>\r\n  </div>\r\n</div>\r\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,aAAa,QAAQ,MAAM;AAKlD,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;ICH1EC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;IAyCAH,EADF,CAAAC,cAAA,cAA6D,cAClC;IACvBD,EAAA,CAAAI,SAAA,YAAqD;IACrDJ,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAE5DF,EAF4D,CAAAG,YAAA,EAAI,EACxD,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAK,UAAA,mBAAAC,oEAAA;MAAA,MAAAC,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,SAAA,CAAAQ,QAAA,CAA2B;IAAA,EAAC;IAIrCf,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IACLH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAkC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzCH,EADF,CAAAC,cAAA,SAAI,iBAI6B;IADvBD,EAAA,CAAAK,UAAA,oBAAAW,0EAAAC,MAAA;MAAA,MAAAV,SAAA,GAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAAUF,MAAA,CAAAO,cAAA,CAAAX,SAAA,EAAAU,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElEpB,EAAA,CAAAC,cAAA,iBAA8B;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IACLH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,IAAgE;IACtEF,EADsE,CAAAG,YAAA,EAAK,EACtE;;;;;IArBCH,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAAsB,kBAAA,MAAAf,SAAA,CAAAgB,UAAA,IAAAhB,SAAA,CAAAiB,UAAA,eACF;IAEExB,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAAyB,iBAAA,CAAAlB,SAAA,CAAAmB,YAAA,UAAkC;IAG5B1B,EAAA,CAAAqB,SAAA,GAA2C;IAE3CrB,EAFA,CAAA2B,UAAA,UAAApB,SAAA,CAAAqB,oBAAA,OAA2C,aAAAjB,MAAA,CAAAkB,SAAA,CAErB;IACpB7B,EAAA,CAAAqB,SAAA,EAAY;IAAZrB,EAAA,CAAA2B,UAAA,aAAY;IAUpB3B,EAAA,CAAAqB,SAAA,IAA+B;IAA/BrB,EAAA,CAAAyB,iBAAA,CAAAlB,SAAA,CAAAuB,SAAA,UAA+B;IAC/B9B,EAAA,CAAAqB,SAAA,GAAgE;IAAhErB,EAAA,CAAAyB,iBAAA,CAAAlB,SAAA,CAAAwB,eAAA,IAAAxB,SAAA,CAAAyB,QAAA,kBAAgE;;;;;IApCpEhC,EAJR,CAAAC,cAAA,cAAgE,gBACjC,YACpB,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAEhBF,EAFgB,CAAAG,YAAA,EAAK,EACd,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAiC,UAAA,KAAAC,gDAAA,kBAA0C;IAgChDlC,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAhCuBH,EAAA,CAAAqB,SAAA,IAAiB;IAAjBrB,EAAA,CAAA2B,UAAA,YAAAhB,MAAA,CAAAwB,cAAA,CAAiB;;;;;;IAtD5CnC,EAJN,CAAAC,cAAA,aAAoD,aAEf,aACC,aACN;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrEH,EAAA,CAAAC,cAAA,iBAAwG;IAA7CD,EAAA,CAAAK,UAAA,mBAAA+B,4DAAA;MAAApC,EAAA,CAAAQ,aAAA,CAAA6B,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA2B,WAAA,EAAa;IAAA,EAAC;IAChFtC,EAAA,CAAAI,SAAA,YAAwC;IAG9CJ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAMAH,EAHN,CAAAC,cAAA,cAAkC,cACK,cACF,aACxB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmF;IACjHF,EADiH,CAAAG,YAAA,EAAO,EAClH;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,gBAA4E;IAAAD,EAAA,CAAAE,MAAA,IAAuC;IACrHF,EADqH,CAAAG,YAAA,EAAO,EACtH;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC/D;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAiD;IAGnFF,EAHmF,CAAAG,YAAA,EAAO,EAChF,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,eAA6B,cACD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAWtCH,EARA,CAAAiC,UAAA,KAAAM,0CAAA,kBAA6D,KAAAC,0CAAA,mBAQG;IA6ClExC,EAAA,CAAAG,YAAA,EAAM;IAIJH,EADF,CAAAC,cAAA,eAAiC,kBACyE;IAAnBD,EAAA,CAAAK,UAAA,mBAAAoC,6DAAA;MAAAzC,EAAA,CAAAQ,aAAA,CAAA6B,GAAA;MAAA,MAAA1B,MAAA,GAAAX,EAAA,CAAAY,aAAA;MAAA,OAAAZ,EAAA,CAAAa,WAAA,CAASF,MAAA,CAAA+B,MAAA,EAAQ;IAAA,EAAC;IACrG1C,EAAA,CAAAI,SAAA,aAAsC;IACtCJ,EAAA,CAAAE,MAAA,cACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAhG0BH,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAAyB,iBAAA,CAAAd,MAAA,CAAAgC,OAAA,CAAAC,WAAA,cAAsC;IAYlC5C,EAAA,CAAAqB,SAAA,GAAmF;IAAnFrB,EAAA,CAAAyB,iBAAA,CAAAd,MAAA,CAAAgC,OAAA,CAAAE,0BAAA,IAAAlC,MAAA,CAAAgC,OAAA,CAAAG,sBAAA,UAAmF;IAIrF9C,EAAA,CAAAqB,SAAA,GAAiD;IAAjDrB,EAAA,CAAA2B,UAAA,YAAAhB,MAAA,CAAAoC,cAAA,CAAApC,MAAA,CAAAgC,OAAA,CAAAK,aAAA,EAAiD;IAAChD,EAAA,CAAAqB,SAAA,EAAuC;IAAvCrB,EAAA,CAAAyB,iBAAA,CAAAd,MAAA,CAAAgC,OAAA,CAAAK,aAAA,aAAuC;IAIvFhD,EAAA,CAAAqB,SAAA,GAAgC;IAAhCrB,EAAA,CAAAyB,iBAAA,CAAAd,MAAA,CAAAwB,cAAA,CAAAc,MAAA,MAAgC;IAIhCjD,EAAA,CAAAqB,SAAA,GAAiD;IAAjDrB,EAAA,CAAAyB,iBAAA,CAAAd,MAAA,CAAAgC,OAAA,CAAAO,kBAAA,kBAAiD;IAUvDlD,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAA2B,UAAA,SAAAhB,MAAA,CAAAwB,cAAA,CAAAc,MAAA,OAAiC;IAQ5BjD,EAAA,CAAAqB,SAAA,EAA+B;IAA/BrB,EAAA,CAAA2B,UAAA,SAAAhB,MAAA,CAAAwB,cAAA,CAAAc,MAAA,KAA+B;;;AD1ClE,OAAM,MAAOE,oBAAoB;EASrBC,KAAA;EACAC,MAAA;EACAC,GAAA;EACAC,UAAA;EACAC,eAAA;EACAC,cAAA;EACAC,YAAA;EAdHC,SAAS,GAAkB,IAAI;EAC/BhB,OAAO,GAAQ,IAAI;EACnBd,SAAS,GAAY,KAAK;EAC1BM,cAAc,GAAU,EAAE;EAC1ByB,SAAS,GAAQ,EAAE;EAClBC,iBAAiB,GAAiB,IAAIhE,YAAY,EAAE;EAE5DiE,YACUV,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,cAA8B,EAC9BC,YAAsB;IANtB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;EACnB;EAEHK,QAAQA,CAAA;IACN,IAAI,CAACH,SAAS,GAAG,IAAI,CAACL,UAAU,CAACS,eAAe,EAAE;IAElD;IACA,IAAI,CAACH,iBAAiB,GAAG/D,aAAa,CAAC,CACrC,IAAI,CAACsD,KAAK,CAACa,QAAQ,EACnB,IAAI,CAACb,KAAK,CAACc,WAAW,CACvB,CAAC,CAACC,SAAS,CAAC,CAAC,CAACF,QAAQ,EAAEC,WAAW,CAAC,KAAI;MACvC,MAAME,OAAO,GAAGH,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAACV,SAAS,GAAGS,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI;MAEjDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAAEb,SAAS,EAAE,IAAI,CAACA,SAAS;QAAEO;MAAW,CAAE,CAAC;MAE1F,IAAI,IAAI,CAACP,SAAS,EAAE;QAClB,IAAI,CAACc,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;MAEA,IAAI,CAACpB,GAAG,CAACqB,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAACf,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACgB,WAAW,EAAE;IACtC;EACF;EAEOJ,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACd,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAAC9B,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAAC2B,eAAe,CAACsB,UAAU,CAAC;MAAEnB,SAAS,EAAE,IAAI,CAACA,SAAS;MAAEoB,cAAc,EAAE,IAAI,CAACnB,SAAS,EAAEoB;IAAM,CAAE,CAAC,CAACb,SAAS,CAAC;MAC/Gc,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrD,SAAS,GAAG,KAAK;QACtB0C,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEU,GAAG,CAAC;QACzC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB;UACA,IAAI,CAACxC,OAAO,GAAGuC,GAAG,CAACE,YAAY,EAAEC,OAAO,IAAIH,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACE,YAAY,IAAI,IAAI;UAC9Fb,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC7B,OAAO,CAAC;UACnD4B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEe,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC7C,OAAO,IAAI,EAAE,CAAC,CAAC;UACzE;QACF,CAAC,MAAM;UACL4B,OAAO,CAACkB,KAAK,CAAC,qBAAqB,EAAEP,GAAG,CAACQ,YAAY,CAAC;UACtD,IAAI,CAAC/C,OAAO,GAAG,IAAI;QACrB;QACA,IAAI,CAACW,GAAG,CAACqB,YAAY,EAAE;MACzB,CAAC;MACDc,KAAK,EAAGE,GAAG,IAAI;QACb,IAAI,CAAC9D,SAAS,GAAG,KAAK;QACtB0C,OAAO,CAACkB,KAAK,CAAC,iCAAiC,EAAEE,GAAG,CAAC;QACrD,IAAI,CAACrC,GAAG,CAACqB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOD,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACf,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAAC9B,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAAC4B,cAAc,CAACmC,sBAAsB,CAAC;MACzCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;QACNC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,IAAI;UACdhF,KAAK,EAAE,IAAI,CAACuC;SACb;OAEJ;MACD0C,MAAM,EAAE,EAAE;MACVtB,cAAc,EAAE,IAAI,CAACnB,SAAS,CAACoB;KAChC,CAAC,CAACb,SAAS,CAAC;MACXc,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAACrD,SAAS,GAAG,KAAK;QACtB0C,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEU,GAAG,CAAC;QACjD,IAAIA,GAAG,EAAEC,OAAO,EAAE;UAChBZ,OAAO,CAACkB,KAAK,CAAC,iCAAiC,EAAEP,GAAG,CAACQ,YAAY,CAAC;UAClE,IAAI,CAACvD,cAAc,GAAG,EAAE;QAC1B,CAAC,MAAM;UACL,MAAMmE,UAAU,GAAGpB,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACI,IAAI,IAAI,EAAE;UAC3D;UACA,IAAI,CAACnD,cAAc,GAAG,CAACmE,UAAU,IAAI,EAAE,EAAEN,MAAM,CAAEO,CAAM,IAAI;YACzD,MAAMC,eAAe,GAAGD,CAAC,EAAE5C,SAAS,IAAI4C,CAAC,EAAEE,SAAS,IAAIF,CAAC,EAAEG,UAAU,IAAIH,CAAC,EAAEI,SAAS,IAAIJ,CAAC,EAAEK,SAAS;YACrG,OAAO,IAAI,CAACjD,SAAS,IAAI,IAAI,GAAGW,MAAM,CAACkC,eAAe,CAAC,KAAKlC,MAAM,CAAC,IAAI,CAACX,SAAS,CAAC,GAAG,IAAI;UAC3F,CAAC,CAAC;UACFY,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAACrC,cAAc,CAAC;QAC1E;QACA,IAAI,CAACmB,GAAG,CAACqB,YAAY,EAAE;MACzB,CAAC;MACDc,KAAK,EAAGE,GAAQ,IAAI;QAClB,IAAI,CAAC9D,SAAS,GAAG,KAAK;QACtB0C,OAAO,CAACkB,KAAK,CAAC,gCAAgC,EAAEE,GAAG,CAAC;QACpD,IAAI,CAACxD,cAAc,GAAG,EAAE;QACxB,IAAI,CAACmB,GAAG,CAACqB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOjC,MAAMA,CAAA;IACX,IAAI,CAACW,MAAM,CAACwD,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEOvE,WAAWA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACqB,SAAS,EAAE;MAAE;IAAQ;IAE/B,MAAMmD,eAAe,GAKjB;MACFC,IAAI,EAAE,IAAI;MAAE;MACZC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAACzD,YAAY,CAAC0D,IAAI,CACrCrH,qBAAqB,EACrB+G,eAAe,CAChB;IAED;IACAK,QAAQ,CAACE,iBAAiB,CAACC,EAAE,GAAG,IAAI,CAAC3D,SAAS;IAC9CwD,QAAQ,CAACE,iBAAiB,CAAC1E,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACAwE,QAAQ,CAACI,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAIA,MAAM,EAAE;QACVhD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE+C,MAAM,CAAC;QACnD;QACA,IAAI,CAAC9C,mBAAmB,EAAE;MAC5B;IACF,CAAC,EACAgD,MAAM,IAAI;MACT;MACAlD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEiD,MAAM,CAAC;IACzC,CAAC,CACF;EACH;EAEO3G,UAAUA,CAACC,QAAgB;IAChC,IAAI,CAACsC,MAAM,CAACwD,QAAQ,CAAC,CAAC,eAAe,EAAE9F,QAAQ,CAAC,EAAE;MAChDmD,WAAW,EAAE;QAAEwD,IAAI,EAAE,SAAS;QAAE/D,SAAS,EAAE,IAAI,CAACA;MAAS;KAC1D,CAAC;EACJ;EAEOzC,cAAcA,CAACyG,MAAW,EAAEC,SAAiB;IAClD,IAAI,CAACD,MAAM,EAAE5G,QAAQ,IAAI,CAAC6G,SAAS,EAAE;MAAE;IAAQ;IAC/C,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,uBAAuB,EACvB,cAAc,EACd,SAAS,EACT,cAAc,EACd,WAAW,EACX,mBAAmB,CACpB;IACD,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAAE;IAAQ;IAE5C,MAAMG,QAAQ,GAAGJ,MAAM,CAAC/F,oBAAoB;IAC5C+F,MAAM,CAAC/F,oBAAoB,GAAGgG,SAAS;IACvC,IAAI,CAAC/F,SAAS,GAAG,IAAI;IACrB,IAAI,CAACyB,GAAG,CAACqB,YAAY,EAAE;IAEvB,IAAI,CAAClB,cAAc,CAChBuE,gCAAgC,CAAC;MAAEjH,QAAQ,EAAE4G,MAAM,CAAC5G,QAAQ;MAAEa,oBAAoB,EAAEgG;IAAS,CAAE,CAAC,CAChGzD,SAAS,CAAC;MACTc,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAMC,OAAO,GAAGD,GAAG,EAAEC,OAAO,IAAID,GAAG,EAAEE,YAAY,EAAED,OAAO;QAC1D,IAAIA,OAAO,EAAE;UACXwC,MAAM,CAAC/F,oBAAoB,GAAGmG,QAAQ;UACtC,IAAI,CAAClG,SAAS,GAAG,KAAK;UACtB,IAAI,CAACyB,GAAG,CAACqB,YAAY,EAAE;QACzB;QACA,IAAI,CAAC9C,SAAS,GAAG,KAAK;QACtB,IAAI,CAACyB,GAAG,CAACqB,YAAY,EAAE;MACzB,CAAC;MACDc,KAAK,EAAEA,CAAA,KAAK;QACVkC,MAAM,CAAC/F,oBAAoB,GAAGmG,QAAQ;QACtC,IAAI,CAAClG,SAAS,GAAG,KAAK;QACtB,IAAI,CAACyB,GAAG,CAACqB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEO5B,cAAcA,CAACkF,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,OACE,SAAS,GAAGA,MAAM,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE7E;;qCA1NWhF,oBAAoB,EAAAnD,EAAA,CAAAoI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAtI,EAAA,CAAAoI,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAAvI,EAAA,CAAAoI,iBAAA,CAAApI,EAAA,CAAAwI,iBAAA,GAAAxI,EAAA,CAAAoI,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAA1I,EAAA,CAAAoI,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAA5I,EAAA,CAAAoI,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAA9I,EAAA,CAAAoI,iBAAA,CAAAW,EAAA,CAAAC,QAAA;EAAA;;UAApB7F,oBAAoB;IAAA8F,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCJjCvJ,EATA,CAAAiC,UAAA,IAAAwH,mCAAA,iBAA0D,IAAAC,mCAAA,kBASN;;;QAT9C1J,EAAA,CAAA2B,UAAA,SAAA6H,GAAA,CAAA3H,SAAA,CAAe;QASgB7B,EAAA,CAAAqB,SAAA,EAAa;QAAbrB,EAAA,CAAA2B,UAAA,SAAA6H,GAAA,CAAA7G,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
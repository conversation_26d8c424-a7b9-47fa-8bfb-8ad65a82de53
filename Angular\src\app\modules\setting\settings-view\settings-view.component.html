<div>
  <div class="row g-5 settings-cards">
    <div class="col-xl-4">
      <div class="card card-flush">
        <!-- [routerLink]="(readTempPermission===false)  ? null:'/template/templatelist'" -->
        <div class="card-header flex-nowrap pt-5 cursor-pointer">
          <h3 class="card-title align-items-start flex-column cursor-pointer">
            <span class="card-label fw-bold text-hover-primary text-dark"
              >Municipalities</span
            >
          </h3>
          <div class="card-toolbar">
            <button
              type="button"
              disabled="true"
              (click)="PermitNavigate()"
              class="btn btn-sm btn-light"
            >
              View All
            </button>
          </div>
        </div>
        <div class="card-body cursor-pointer pb-0">
          <!-- <div class="notice d-flex rounded">
            <span
              [inlineSVG]="'./assets/media/icons/duotune/general/gen044.svg'"
              class="svg-icon svg-icon-4 svg-icon-dark me-4"
            ></span>
            <div class="d-flex flex-stack flex-grow-1">
              <div class="text-justify">
                <p class="mb-0 fs-7 mb-4"></p>
              </div>
            </div>
          </div> -->
          <div class="d-flex flex-wrap justify-content-center pt-2">
            <div>
              <div class="d-flex flex-wrap">
                <div
                  class="border border-gray-300 border-dashed bg-light-primary rounded min-w-75px py-2 px-4 me-2 mb-3"
                >
                  <span
                    class="fs-3 d-flex justify-content-center fw-bold text-primary"
                    >{{ municipalitiesCount }}
                  </span>
                  <div
                    class="fw-semibold d-flex justify-content-center text-primary"
                  >
                    Municipalities
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer text-center">
          <button
            type="button"
            class="btn btn-sm btn-primary btn-elevate"
            disabled
          >
            Add Municipalities
          </button>
        </div>
      </div>
    </div>

    <div class="col-md-4">
      <div class="card card-flush">
        <div class="card-header flex-nowrap pt-5 cursor-pointer">
          <h3 class="card-title align-items-start flex-column cursor-pointer">
            <span class="card-label fw-bold text-hover-primary text-dark"
              >User Management</span
            >
          </h3>
          <div class="card-toolbar">
            <a class="btn btn-sm btn-light" (click)="userNavigate()"
              >View All</a
            >
          </div>
        </div>
        <div class="card-body cursor-pointer">
          <div class="d-flex flex-wrap justify-content-center pt-2">
            <div class="d-flex flex-wrap">
              <div
                class="border border-gray-300 border-dashed bg-light-primary rounded min-w-75px py-2 px-4 me-2 mb-3"
              >
                <span
                  class="fs-3 d-flex justify-content-center fw-bold text-primary"
                  >{{ usersCount }}</span
                >
                <div
                  class="fw-semibold d-flex justify-content-center text-primary"
                >
                  Users
                </div>
              </div>
              <div
                class="border border-gray-300 border-dashed bg-light-info rounded min-w-75px py-2 px-4 me-2 mb-3"
              >
                <span
                  class="fs-3 d-flex justify-content-center fw-bold text-info"
                >
                  {{ rolesCount }}
                </span>
                <div
                  class="fw-semibold d-flex justify-content-center text-info"
                >
                  Roles
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer cardBody text-center">
          <button
            type="button"
            disabled="true"
            class="btn btn-sm btn-primary btn-elevate"
          >
            Add User</button
          >&nbsp;
          <!-- (click)="addUser()" -->
        </div>
      </div>
    </div>
    <div class="col-md-4">
      <div class="card card-flush">
        <div class="card-header flex-nowrap pt-5 cursor-pointer">
          <h3 class="card-title align-items-start flex-column cursor-pointer">
            <span class="card-label fw-bold text-hover-primary text-dark"
              >Email Templates</span
            >
          </h3>
           <div class="card-toolbar">
            <a class="btn btn-sm btn-light" (click)="emailtemplateNavigate()"
              >View All</a
            >
          </div>
        </div>
        <div class="card-body cursor-pointer">
          <div class="d-flex flex-wrap justify-content-center pt-2">
            <div class="d-flex flex-wrap">
              <div
                class="border border-gray-300 border-dashed bg-light-primary rounded min-w-75px py-2 px-4 me-2 mb-3"
              >
                <span
                  class="fs-3 d-flex justify-content-center fw-bold text-primary"
                >
                  {{ emailTemplatesCount }}
                </span>
                <div
                  class="fw-semibold d-flex justify-content-center text-primary"
                >
                  Total
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="card-footer cardBody text-center">
          <button
            type="button"
            class="btn btn-sm btn-primary btn-elevate"
            disabled
          >
            Update Email Template
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row g-5 g-xl-10 pt-5">
    <div class="col-xl-12 mb-5 mb-xl-10">
      <div class="card card-custom gutter-b">
        <div>
          <!-- begin: Filtration form -->
          <div class="card-body">
            <h3 class="card-title align-items-start">
              <span class="card-label fw-bold text-dark">Activity Log</span>
            </h3>
            <div class="text-muted mb-3">
              <i class="fas fa-clock me-2"></i>
              <span class="fst-italic">Coming Soon</span>
            </div>
            <div class="form form-label-right mb-5"></div>
            <ng-template #NoResult>
              <tbody>
                <tr>
                  <td colspan="7" class="text-center fw-bolder fs-5 mt-2">
                    No Results found
                  </td>
                </tr>
              </tbody>
            </ng-template>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

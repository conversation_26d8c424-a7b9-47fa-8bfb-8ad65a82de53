import { Component, OnInit, ViewChild, AfterViewInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';
import { AppService } from '../services/app.service';
import { PermitListComponent } from './permit-list/permit-list.component';

@Component({
  selector: 'app-permits',
  templateUrl: './permits.component.html',
  styleUrls: ['./permits.component.scss']
})
export class PermitsComponent implements OnInit, AfterViewInit, OnDestroy {
  selectedTab = 'Permits'; //store default selected tab
  loginUser: any;
  showNavBar = true; // Control visibility of navigation bar
  private routeSubscription: Subscription = new Subscription();
  
  @ViewChild(PermitListComponent) permitListComponent: PermitListComponent;
  
  constructor(
    public AppService: AppService,
    private router: Router
  ) {
    // set the default paging options
  }
  
  ngOnInit() {
    // Ensure Permits tab is selected by default
    this.selectedTab = 'Permits';
    
    // Check if we're on the view route and hide nav bar accordingly
    this.checkRouteAndToggleNavBar();
    
    // Subscribe to route changes to update nav bar visibility
    this.routeSubscription = this.router.events
      .pipe(filter(event => event instanceof NavigationEnd))
      .subscribe((event: NavigationEnd) => {
        this.checkRouteAndToggleNavBar();
      });
  }

  ngAfterViewInit() {
    // Any initialization after view is ready
  }
  
  ngOnDestroy() {
    // Clean up subscription
    if (this.routeSubscription) {
      this.routeSubscription.unsubscribe();
    }
  }
  
  checkRouteAndToggleNavBar() {
    // Hide nav bar when on permits/view route
    this.showNavBar = !this.router.url.includes('/permits/view/');
  }
  
  onNavChange(tabName: string) {
    console.log(`Switching to tab: ${tabName}`);
    this.selectedTab = tabName;
    
    // Small delay to ensure the component is rendered before calling tab activation
    setTimeout(() => {
      switch (tabName) {
        case 'Permits':
          if (this.permitListComponent) {
            this.permitListComponent.onTabActivated();
          }
          break;
      }
    }, 100);
  }
}

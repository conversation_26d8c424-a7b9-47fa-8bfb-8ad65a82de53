.input-group-text {
  padding-top: 5px;
  padding-bottom: 5px;
  padding-left: 10px;
  padding-right: 10px;
}

$darkNavy: #213140;
$teal1: #11a7db;
$teal2: #11a7db;
$charcoal: #555555;

$activeShadow: 0 0 10px rgba($teal1, 0.5);
:host ::ng-deep .modal {
  --bs-modal-padding: 1rem !important;
}
.mx-10px {
  margin-right: 6rem !important;
  margin-left: 8rem !important;
}

// @mixin focusOutline {outline: dotted 1px #CCC; outline-offset: .45rem;}
@mixin hideInput {
  width: 0;
  height: 0;
  position: absolute;
  left: -9999px;
}

body:not(:-moz-handler-blocked) fieldset {
  display: table-cell;
}

.toggle {
  // box-sizing: border-box;
  font-size: 0;
  display: flex;
  flex-flow: row nowrap;
  justify-content: flex-start;
  align-items: stretch;

  input {
    @include hideInput;
  }

  input + label {
    margin: 0;
    padding: 0.6rem 1.5rem;
    box-sizing: border-box;
    position: relative;
    display: inline-block;
    border: solid 1px #ddd;
    background-color: #fff;
    font-size: 1.1rem;
    line-height: 160%;
    font-weight: 600;
    text-align: center;

    &:first-of-type {
      border-radius: 6px 0 0 6px;
    }

    &:last-of-type {
      border-radius: 0 6px 6px 0;
    }
  }

  input:hover + label {
    border-color: $darkNavy;
  }

  input:checked + label {
    background-color: $teal2;
    color: #fff;
    box-shadow: $activeShadow;
    border-color: $teal2;
    z-index: 1;
  }

  input:checked:disabled + label {
    background-color: #e1e3ea; // Background color for selected, disabled radio button
    color: white; // Text color for selected, disabled radio button
    border-color: #e1e3ea; // Border color for selected, disabled radio button
    box-shadow: none;

    & input:hover + label {
      border-color: #ddd;
    }
  }

  // input:focus + label {@include focusOutline;}
}

.color-picker {
  opacity: 1 !important;
  visibility: visible !important;
}

.k-picker-md {
  font-size: 12px !important;
  line-height: 1.4285714286;
}

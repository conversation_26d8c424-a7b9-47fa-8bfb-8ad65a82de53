import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { PermitsComponent } from './permits.component';
import { PermitListComponent } from './permit-list/permit-list.component';
import { PermitViewComponent } from './permit-view/permit-view.component';



const routes: Routes = [
    {
        path: '',
        component: PermitsComponent,
        children: [
            {
                path: 'list',
                component: PermitListComponent,
            },
            {
                path: 'view/:id',
                component: PermitViewComponent,
            },
            {
                path: '',
                redirectTo: 'list',
                pathMatch: 'full'
            },
            { path: '**', redirectTo: 'list', pathMatch: 'full' },
        ]
    }
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule],
})
export class PermitsRoutingModule { }

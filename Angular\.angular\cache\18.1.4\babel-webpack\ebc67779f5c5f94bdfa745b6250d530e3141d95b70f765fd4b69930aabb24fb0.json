{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, catchError, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/custom-layout.utils.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Permit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Permit - \", ctx_r0.permitNumber, \"\");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_16_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_24_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"span\", 42);\n    i0.ɵɵtext(3, \"Validating...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_34_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberValidationError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_34_div_1_Template, 2, 0, \"div\", 3)(2, PermitPopupComponent_ng_container_20_div_34_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"permitNumberExists\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberValidationError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_42_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_57_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_57_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 21);\n    i0.ɵɵtext(1, \"*\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_63_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_63_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_65_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 8)(2, \"div\", 43);\n    i0.ɵɵtext(3, \" The syncing of permit details may take up to 3 minutes. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(4, \"br\");\n    i0.ɵɵelementStart(5, \"div\")(6, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_ng_container_20_div_65_Template_button_click_6_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.syncPermitDetails());\n    });\n    i0.ɵɵelement(7, \"i\", 45);\n    i0.ɵɵtext(8, \" Sync \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 8)(3, \"label\", 20);\n    i0.ɵɵtext(4, \"Project \");\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"ng-select\", 22);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_20_Template_ng_select_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onProjectChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_20_div_8_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"div\", 8)(11, \"label\", 20);\n    i0.ɵɵtext(12, \"Permit / Sub Project Name \");\n    i0.ɵɵelementStart(13, \"span\", 21);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"input\", 24);\n    i0.ɵɵtemplate(16, PermitPopupComponent_ng_container_20_div_16_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 19)(18, \"div\", 8)(19, \"label\", 20);\n    i0.ɵɵtext(20, \"Location\");\n    i0.ɵɵelementStart(21, \"span\", 21);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(23, \"input\", 25);\n    i0.ɵɵtemplate(24, PermitPopupComponent_ng_container_20_div_24_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\", 26)(27, \"label\", 20);\n    i0.ɵɵtext(28, \"Permit # \");\n    i0.ɵɵelementStart(29, \"span\", 21);\n    i0.ɵɵtext(30, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 27)(32, \"input\", 28);\n    i0.ɵɵlistener(\"blur\", function PermitPopupComponent_ng_container_20_Template_input_blur_32_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.triggerPermitNumberValidation());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, PermitPopupComponent_ng_container_20_div_33_Template, 4, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, PermitPopupComponent_ng_container_20_div_34_Template, 3, 2, \"div\", 23)(35, PermitPopupComponent_ng_container_20_div_35_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 26)(37, \"label\", 20);\n    i0.ɵɵtext(38, \"Permit Category \");\n    i0.ɵɵelementStart(39, \"span\", 21);\n    i0.ɵɵtext(40, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(41, \"ng-select\", 30);\n    i0.ɵɵtemplate(42, PermitPopupComponent_ng_container_20_div_42_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 31)(44, \"div\", 8)(45, \"div\", 32);\n    i0.ɵɵelement(46, \"i\", 33);\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48, \"For External Review, permit details can be retrieved from the Municipality City website when Municipality is chosen.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(49, \"div\", 19)(50, \"div\", 26)(51, \"div\", 8)(52, \"label\", 20);\n    i0.ɵɵtext(53, \"Permit Review Type\");\n    i0.ɵɵelementStart(54, \"span\", 21);\n    i0.ɵɵtext(55, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(56, \"ng-select\", 34);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_20_Template_ng_select_change_56_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitReviewTypeChange($event));\n    })(\"clear\", function PermitPopupComponent_ng_container_20_Template_ng_select_clear_56_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitReviewTypeClear());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(57, PermitPopupComponent_ng_container_20_div_57_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 8)(59, \"label\", 20);\n    i0.ɵɵtext(60, \"Permit Municipality (External Review)\");\n    i0.ɵɵtemplate(61, PermitPopupComponent_ng_container_20_span_61_Template, 2, 0, \"span\", 35);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(62, \"ng-select\", 36);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_20_Template_ng_select_change_62_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitMunicipalityChange($event));\n    })(\"clear\", function PermitPopupComponent_ng_container_20_Template_ng_select_clear_62_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onPermitMunicipalityClear());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(63, PermitPopupComponent_ng_container_20_div_63_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(64, \"div\", 37);\n    i0.ɵɵtemplate(65, PermitPopupComponent_ng_container_20_div_65_Template, 9, 0, \"div\", 38);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_14_0;\n    let tmp_16_0;\n    let tmp_17_0;\n    let tmp_20_0;\n    let tmp_22_0;\n    let tmp_23_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.projects)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_6_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPermitNumberValidating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_9_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.permitNumberValidationError && !((tmp_10_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.categories)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_14_0.touched) && ((tmp_14_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_14_0.invalid));\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"items\", ctx_r0.reviewTypeArray)(\"clearable\", (tmp_16_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_16_0.value);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_17_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_17_0.touched) && ((tmp_17_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_17_0.invalid));\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPermitMunicipalRequired);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"items\", ctx_r0.muncipalities)(\"clearable\", (tmp_20_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_20_0.value)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_22_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_22_0.touched) && ((tmp_22_0 = ctx_r0.permitForm.get(\"permitMunicipality\")) == null ? null : tmp_22_0.invalid));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_23_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_23_0.value) === \"External\" && !ctx_r0.getSyncButtonDisableStatus() && ctx_r0.id === 0);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_19_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_27_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_38_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_38_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 46)(3, \"label\", 20);\n    i0.ɵɵtext(4, \"Review Responsible Party\");\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"input\", 47);\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_21_div_8_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 46)(10, \"label\", 20);\n    i0.ɵɵtext(11, \"Primary Contact Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 46)(14, \"label\", 20);\n    i0.ɵɵtext(15, \"Permit Status \");\n    i0.ɵɵelementStart(16, \"span\", 21);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(18, \"ng-select\", 49);\n    i0.ɵɵtemplate(19, PermitPopupComponent_ng_container_21_div_19_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 19)(21, \"div\", 46)(22, \"label\", 20);\n    i0.ɵɵtext(23, \"Permit Type \");\n    i0.ɵɵelementStart(24, \"span\", 21);\n    i0.ɵɵtext(25, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(26, \"ng-select\", 50);\n    i0.ɵɵtemplate(27, PermitPopupComponent_ng_container_21_div_27_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 46)(29, \"label\", 20);\n    i0.ɵɵtext(30, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 46)(33, \"label\", 20);\n    i0.ɵɵtext(34, \"Applied Date \");\n    i0.ɵɵelementStart(35, \"span\", 21);\n    i0.ɵɵtext(36, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(37, \"input\", 52);\n    i0.ɵɵtemplate(38, PermitPopupComponent_ng_container_21_div_38_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 46)(41, \"label\", 20);\n    i0.ɵɵtext(42, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 46)(45, \"label\", 20);\n    i0.ɵɵtext(46, \"Completed Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 54);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 46)(49, \"label\", 20);\n    i0.ɵɵtext(50, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"input\", 55);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 19)(53, \"div\", 8)(54, \"label\", 20);\n    i0.ɵɵtext(55, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"textarea\", 56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_5_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_1_0.touched) && ((tmp_1_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_1_0.invalid));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", ctx_r0.statuses)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.permitTypes)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_9_0.invalid));\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_10_0.touched) && ((tmp_10_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_10_0.invalid));\n  }\n}\nfunction PermitPopupComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 57);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 58);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.permitForm.invalid || ctx_r0.isPermitNumberValidating);\n  }\n}\nfunction PermitPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToNextTab());\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PermitPopupComponent {\n  modal;\n  fb;\n  projectsService;\n  permitsService;\n  appService;\n  httpUtilService;\n  customLayoutUtilsService;\n  cdr;\n  id = 0; // 0 = Add, otherwise Edit\n  isHideInternalReviewStatus = true; // 0 = Add, otherwise Edit\n  permit; // incoming permit data (for edit)\n  passEntry = new EventEmitter();\n  permitForm;\n  projects = [];\n  reviewTypeArray = ['Internal', 'External', 'Both'];\n  loginUser = {};\n  isLoading = false;\n  muncipalities = [];\n  selectedTab = 'basic';\n  // dropdown options\n  permitTypes = ['Access Control System - Commercial', 'Addition - Commercial', 'Addition - Residential', 'Backflow - Commercial', 'Building Miscellaneous - Commercial', 'Building Move Permit - Residential', 'Building Revisions - Commercial Revision', 'Certificate of Completion', 'Certificate of Occupancy - Commercial', 'Commercial - LV Data Voice Cable Sub-Permit', 'Demolition - Commercial', 'Document Submittal - Commercial Building', 'Electrical Sub-Permit - Commercial', 'Engineering Construction Traffic & Parking Management Plan', 'Fence - Commercial', 'Fire Alarm - Fire', 'Fire Sprinkler/Fire Suppression - Fire', 'Foundation Only - Commercial', 'Gas Sub-Permit - Commercial', 'General Electrical - Commercial', 'General Paving - Paving', 'General Sign Permit', 'Generator - Commercial', 'Interceptor - Commercial', 'Interior (<5000 sq ft) - Commercial', 'Irrigation - Commercial', 'Landscape Non-Residential and Multi-Family', 'Low Voltage - Commercial', 'Mechanical Sub-Permit - Commercial', 'Monument - Sign', 'Mural - Sign', 'New Building - Commercial', 'Plumbing Sub-Permit - Commercial', 'Pool Plumbing Commercial (Sub-Permit)', 'Public Art Permit Application', 'Remodel - Commercial', 'Right-of-Way | ENG A - General', 'Sewer Cap for Demo - Commercial', 'Windows and Doors - Commercial'];\n  categories = ['Primary', 'Sub Permit', 'Industrial', 'Municipal', 'Environmental'];\n  statuses = ['Canceled', 'Complete', 'Expired', 'Fees Due', 'In Review', 'Issued', 'On Hold', 'Requires Resubmit', 'Requires Resubmit for Prescreen', 'Submitted - Online', 'Void'];\n  internalStatusArray = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n  permitNumber;\n  isPermitMunicipalRequired = false;\n  iscityreviewLinkMunicipalRequired = false;\n  cityReviewLink = '';\n  syncedPermitData = {};\n  permitNumberValidationError = '';\n  isPermitNumberValidating = false;\n  constructor(modal, fb, projectsService, permitsService, appService, httpUtilService, customLayoutUtilsService, cdr) {\n    this.modal = modal;\n    this.fb = fb;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.appService = appService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.cdr = cdr;\n    // Subscribe to loading state\n    this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading;\n    });\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadMunicipalities();\n    this.loadForm();\n    this.loadProjects();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n  }\n  loadForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      permitCategory: ['', Validators.required],\n      permitType: ['', Validators.required],\n      description: [''],\n      permitReviewType: ['', Validators.required],\n      location: ['', Validators.required],\n      primaryContact: [''],\n      permitAppliedDate: ['', Validators.required],\n      permitExpirationDate: [''],\n      permitIssueDate: [''],\n      permitFinalDate: [''],\n      permitCompleteDate: [''],\n      permitStatus: ['', Validators.required],\n      internalReviewStatus: ['', Validators.required],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      reviewResponsibleParty: ['', Validators.required],\n      permitMunicipality: ['']\n      // cityReviewLink: [''],\n    });\n  }\n  setupPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    if (permitNumberControl) {\n      // Only clear server-side validation while typing; don't call API here\n      permitNumberControl.valueChanges.pipe(distinctUntilChanged()).subscribe(() => {\n        this.permitNumberValidationError = '';\n        if (permitNumberControl.hasError('permitNumberExists')) {\n          const errors = {\n            ...permitNumberControl.errors\n          };\n          delete errors['permitNumberExists'];\n          permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n        }\n      });\n    }\n  }\n  triggerPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    const projectIdControl = this.permitForm.get('projectId');\n    const permitNumber = permitNumberControl?.value;\n    const projectId = projectIdControl?.value;\n    const enteredNormalized = (permitNumber || '').toString().trim().toLowerCase();\n    const originalNormalized = (this.permitNumber || '').toString().trim().toLowerCase();\n    // If empty, or in edit mode and value hasn't changed, just clear any prior error and skip API\n    if (!enteredNormalized || this.id !== 0 && enteredNormalized === originalNormalized) {\n      this.isPermitNumberValidating = false;\n      this.permitNumberValidationError = '';\n      if (permitNumberControl?.hasError('permitNumberExists')) {\n        const errors = {\n          ...permitNumberControl.errors\n        };\n        delete errors['permitNumberExists'];\n        permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n      }\n      return;\n    }\n    if (enteredNormalized && projectId) {\n      this.isPermitNumberValidating = true;\n      this.validatePermitNumber(permitNumber, projectId).pipe(map(res => res?.responseData ?? res)).subscribe(result => {\n        this.isPermitNumberValidating = false;\n        if (result && result.exists) {\n          this.permitNumberValidationError = result.message;\n          permitNumberControl?.setErrors({\n            'permitNumberExists': true\n          });\n        } else {\n          this.permitNumberValidationError = '';\n          if (permitNumberControl?.hasError('permitNumberExists')) {\n            const errors = {\n              ...permitNumberControl.errors\n            };\n            delete errors['permitNumberExists'];\n            permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n          }\n        }\n      });\n    }\n  }\n  validatePermitNumber(permitNumber, projectId) {\n    this.isPermitNumberValidating = true;\n    this.permitNumberValidationError = '';\n    const params = {\n      permitNumber: permitNumber,\n      projectId: projectId,\n      permitId: this.id !== 0 ? this.id : null // Include permitId for edit mode\n    };\n    return this.permitsService.validatePermitNumber(params).pipe(catchError(() => {\n      this.isPermitNumberValidating = false;\n      return of({\n        exists: false,\n        message: ''\n      });\n    }));\n  }\n  loadProjects() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = {\n      paginate: false\n    };\n    this.projectsService.getAllProjectsData(params).subscribe({\n      next: response => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.projects = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.projects = [];\n      }\n    });\n  }\n  loadMunicipalities() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = {\n      loggedinUser: this.loginUser.userId\n    };\n    this.permitsService.getAllMunicipalities(params).subscribe({\n      next: response => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.muncipalities = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.muncipalities = [];\n      }\n    });\n  }\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getPermit({\n      permitId: this.id,\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.data;\n          this.permitNumber = permitData.permitNumber;\n          this.permitForm.patchValue({\n            projectId: permitData.projectId,\n            permitNumber: permitData.permitNumber,\n            permitReviewType: permitData.permitReviewType,\n            permitCategory: permitData.permitCategory,\n            permitType: permitData.permitType,\n            description: permitData.description,\n            permitName: permitData.permitName,\n            location: permitData.location,\n            internalReviewStatus: permitData.internalReviewStatus,\n            primaryContact: permitData.primaryContact,\n            permitAppliedDate: permitData.permitAppliedDate ? this.formatDateForInput(permitData.permitAppliedDate) : '',\n            permitExpirationDate: permitData.permitExpirationDate ? this.formatDateForInput(permitData.permitExpirationDate) : '',\n            permitIssueDate: permitData.permitIssueDate ? this.formatDateForInput(permitData.permitIssueDate) : '',\n            permitFinalDate: permitData.permitFinalDate ? this.formatDateForInput(permitData.permitFinalDate) : '',\n            permitCompleteDate: permitData.permitCompleteDate ? this.formatDateForInput(permitData.permitCompleteDate) : '',\n            permitStatus: permitData.permitStatus,\n            attentionReason: permitData.attentionReason,\n            internalNotes: permitData.internalNotes,\n            actionTaken: permitData.actionTaken,\n            reviewResponsibleParty: permitData.reviewResponsibleParty,\n            permitMunicipality: permitData.permitMunicipality\n            // cityReviewLink: permitData.cityReviewLink,\n          });\n          this.onPermitReviewTypeChange(permitData.permitReviewType);\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n  }\n  formatDateForInput(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    // Use local timezone to avoid date shifting\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  preparePermitData() {\n    const formData = this.permitForm.value;\n    let permitRequestData = {};\n    permitRequestData.projectId = formData.projectId;\n    permitRequestData.permitNumber = formData.permitNumber;\n    permitRequestData.permitReviewType = formData.permitReviewType;\n    permitRequestData.permitCategory = formData.permitCategory;\n    permitRequestData.permitType = formData.permitType;\n    permitRequestData.description = formData.description;\n    permitRequestData.location = formData.location;\n    permitRequestData.primaryContact = formData.primaryContact;\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\n    permitRequestData.permitExpirationDate = formData.permitExpirationDate || null;\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\n    permitRequestData.permitFinalDate = formData.permitFinalDate || null;\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\n    permitRequestData.permitStatus = formData.permitStatus;\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\n    permitRequestData.permitName = formData.permitName;\n    permitRequestData.attentionReason = formData.attentionReason;\n    permitRequestData.internalNotes = formData.internalNotes;\n    permitRequestData.actionTaken = formData.actionTaken;\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\n    permitRequestData.cityReviewLink = this.cityReviewLink;\n    permitRequestData.loggedInUserId = this.loginUser.userId;\n    permitRequestData.syncedPermitData = this.syncedPermitData;\n    const caseId = this.syncedPermitData.EntityId || this.syncedPermitData.permitId || this.syncedPermitData.Id || null;\n    permitRequestData.permitEntityID = caseId;\n    if (this.id !== 0) {\n      permitRequestData.permitId = this.id;\n    }\n    return permitRequestData;\n  }\n  save() {\n    let controls = this.permitForm.controls;\n    console.log('Permit Data:', this.permitForm.value);\n    if (this.permitForm.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.customLayoutUtilsService.showError('Please fill all required fields', '');\n      return;\n    }\n    let permitData = this.preparePermitData();\n    console.log('Permit Data:', permitData);\n    if (this.id === 0) {\n      this.create(permitData);\n    } else {\n      this.edit(permitData);\n    }\n  }\n  create(permitData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.createPermit(permitData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  edit(permitData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.updatePermit(permitData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'notes';\n    }\n    this.cdr.markForCheck();\n  }\n  goToPreviousTab() {\n    if (this.selectedTab === 'notes') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n    this.cdr.markForCheck();\n  }\n  onProjectChange(event) {\n    this.permitForm.patchValue({\n      location: event.projectLocation\n    });\n    // console.log(\"project loacation\",event)\n    // Auto-fill Permit / Sub Project Name for Add mode when selecting a project\n    if (this.id === 0) {\n      const permitNameControl = this.permitForm.get('permitName');\n      const currentValue = (permitNameControl?.value || '').toString().trim();\n      const projectName = event?.projectName || event?.project?.projectName || '';\n      if (permitNameControl && projectName && (currentValue === '' || permitNameControl.pristine)) {\n        permitNameControl.setValue(projectName);\n        permitNameControl.markAsDirty();\n      }\n    }\n  }\n  onPermitReviewTypeChange(event) {\n    const permitControl = this.permitForm.get('permitMunicipality');\n    // const cityReviewControl = this.permitForm.get('cityReviewLink');\n    if (event === 'External') {\n      this.isPermitMunicipalRequired = true;\n      // this.iscityreviewLinkMunicipalRequired = true;\n      permitControl?.setValidators([Validators.required]);\n      // cityReviewControl?.setValidators([Validators.required]);\n    } else {\n      this.isPermitMunicipalRequired = false;\n      // this.iscityreviewLinkMunicipalRequired = false;\n      permitControl?.clearValidators();\n      // cityReviewControl?.clearValidators();\n    }\n    permitControl?.updateValueAndValidity();\n    // cityReviewControl?.updateValueAndValidity();\n  }\n  onPermitReviewTypeClear() {\n    this.permitForm.patchValue({\n      permitReviewType: null\n    });\n    // Also clear the municipality when review type is cleared\n    this.permitForm.patchValue({\n      permitMunicipality: null\n    });\n    this.cityReviewLink = '';\n    this.isPermitMunicipalRequired = false;\n    // Clear validators for municipality\n    const permitControl = this.permitForm.get('permitMunicipality');\n    permitControl?.clearValidators();\n    permitControl?.updateValueAndValidity();\n  }\n  onPermitMunicipalityChange(event) {\n    console.log('event 0 ', event);\n    this.cityReviewLink = event.cityWebsiteLink + '#/permit/';\n    this.getSyncButtonDisableStatus();\n  }\n  onPermitMunicipalityClear() {\n    this.permitForm.patchValue({\n      permitMunicipality: null\n    });\n    this.cityReviewLink = '';\n    this.getSyncButtonDisableStatus();\n  }\n  syncPermitDetails() {\n    const formData = this.permitForm.value;\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getPermitDetails({\n      permitNumber: formData.permitNumber,\n      municipalityId: formData.permitMunicipality\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.permit;\n          this.syncedPermitData = permitData;\n          this.permitForm.patchValue({\n            permitType: permitData.permitType,\n            description: permitData.description,\n            location: permitData.address,\n            permitAppliedDate: permitData.applyDate ? this.formatDateForInput(permitData.applyDate) : '',\n            permitExpirationDate: permitData.expireDate ? this.formatDateForInput(permitData.expireDate) : '',\n            permitIssueDate: permitData.issueDate ? this.formatDateForInput(permitData.issueDate) : '',\n            permitFinalDate: permitData.finalDate ? this.formatDateForInput(permitData.finalDate) : '',\n            permitCompleteDate: permitData.completeDate ? this.formatDateForInput(permitData.completeDate) : '',\n            permitStatus: permitData.permitStatus\n          });\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData.message);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n  }\n  getSyncButtonDisableStatus() {\n    const reviewType = this.permitForm.get('permitReviewType')?.value;\n    const permitNumber = this.permitForm.get('permitNumber')?.value;\n    const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\n    const isExternal = reviewType === 'External';\n    const hasPermitNumber = !!permitNumber;\n    const hasPermitMunicipality = !!permitMunicipality;\n    console.log('isExternal ', isExternal);\n    console.log('hasPermitNumber ', hasPermitNumber);\n    console.log('hasPermitMunicipality ', hasPermitMunicipality);\n    // Disable if any of the conditions are not satisfied\n    return !(isExternal && hasPermitNumber && hasPermitMunicipality);\n  }\n  static ɵfac = function PermitPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitPopupComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitPopupComponent,\n    selectors: [[\"app-permit-popup\"]],\n    inputs: {\n      id: \"id\",\n      isHideInternalReviewStatus: \"isHideInternalReviewStatus\",\n      permit: \"permit\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 31,\n    vars: 14,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"modal-footer\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"mt-4\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"bindLabel\", \"projectName\", \"formControlName\", \"projectId\", \"bindValue\", \"projectId\", \"placeholder\", \"Select Project\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"class\", \"text-danger mt-1 small\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"permitName\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-6\"], [1, \"position-relative\"], [\"type\", \"text\", \"formControlName\", \"permitNumber\", 1, \"form-control\", \"form-control-sm\", 3, \"blur\"], [\"class\", \"position-absolute top-50 end-0 translate-middle-y me-2\", 4, \"ngIf\"], [\"formControlName\", \"permitCategory\", \"placeholder\", \"Select Category\", 3, \"items\", \"clearable\", \"multiple\"], [1, \"row\", \"mt-3\"], [1, \"text-muted\", \"small\", \"d-flex\", \"align-items-center\", 2, \"white-space\", \"normal\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [\"formControlName\", \"permitReviewType\", 3, \"change\", \"clear\", \"items\", \"clearable\"], [\"class\", \"text-danger\", 4, \"ngIf\"], [\"bindLabel\", \"cityName\", \"formControlName\", \"permitMunicipality\", \"bindValue\", \"municipalityId\", \"placeholder\", \"Select Municipality\", 3, \"change\", \"clear\", \"items\", \"clearable\", \"multiple\"], [1, \"col-xl-6\", \"align-con\"], [\"class\", \"row mt-3\", 4, \"ngIf\"], [1, \"text-danger\", \"mt-1\", \"small\"], [1, \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"small\", \"me-3\", 2, \"white-space\", \"normal\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"col-xl-4\"], [\"type\", \"text\", \"formControlName\", \"reviewResponsibleParty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"primaryContact\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"permitStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"formControlName\", \"permitType\", \"placeholder\", \"Select Type\", 3, \"items\", \"clearable\", \"multiple\"], [\"type\", \"date\", \"formControlName\", \"permitIssueDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitAppliedDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitExpirationDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitCompleteDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitFinalDate\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"description\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"]],\n    template: function PermitPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, PermitPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, PermitPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_i_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"ul\", 10)(13, \"li\", 11)(14, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_14_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(15, \" Basic Info \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 11)(17, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_17_listener($event) {\n          return ctx.showTab(\"details\", $event);\n        });\n        i0.ɵɵtext(18, \" Permit Details \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(19, \"form\", 13);\n        i0.ɵɵtemplate(20, PermitPopupComponent_ng_container_20_Template, 66, 24, \"ng-container\", 3)(21, PermitPopupComponent_ng_container_21_Template, 57, 10, \"ng-container\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 14)(23, \"div\");\n        i0.ɵɵtemplate(24, PermitPopupComponent_button_24_Template, 2, 0, \"button\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\")(26, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_button_click_26_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵtext(27, \" Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(28, \"\\u00A0 \");\n        i0.ɵɵtemplate(29, PermitPopupComponent_button_29_Template, 2, 1, \"button\", 17)(30, PermitPopupComponent_button_30_Template, 2, 0, \"button\", 18);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.selectedTab === \"details\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.permitForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i9.NgSelectComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "distinctUntilChanged", "catchError", "map", "of", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "permitNumber", "ɵɵtemplate", "PermitPopupComponent_ng_container_20_div_8_div_1_Template", "ɵɵproperty", "tmp_2_0", "permitForm", "get", "errors", "PermitPopupComponent_ng_container_20_div_16_div_1_Template", "PermitPopupComponent_ng_container_20_div_24_div_1_Template", "permitNumberValidationError", "PermitPopupComponent_ng_container_20_div_34_div_1_Template", "PermitPopupComponent_ng_container_20_div_34_div_2_Template", "tmp_3_0", "PermitPopupComponent_ng_container_20_div_42_div_1_Template", "PermitPopupComponent_ng_container_20_div_57_div_1_Template", "PermitPopupComponent_ng_container_20_div_63_div_1_Template", "ɵɵelement", "ɵɵlistener", "PermitPopupComponent_ng_container_20_div_65_Template_button_click_6_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "syncPermitDetails", "ɵɵelementContainerStart", "PermitPopupComponent_ng_container_20_Template_ng_select_change_7_listener", "$event", "_r2", "onProjectChange", "PermitPopupComponent_ng_container_20_div_8_Template", "PermitPopupComponent_ng_container_20_div_16_Template", "PermitPopupComponent_ng_container_20_div_24_Template", "PermitPopupComponent_ng_container_20_Template_input_blur_32_listener", "triggerPermitNumberValidation", "PermitPopupComponent_ng_container_20_div_33_Template", "PermitPopupComponent_ng_container_20_div_34_Template", "PermitPopupComponent_ng_container_20_div_35_Template", "PermitPopupComponent_ng_container_20_div_42_Template", "PermitPopupComponent_ng_container_20_Template_ng_select_change_56_listener", "onPermitReviewTypeChange", "PermitPopupComponent_ng_container_20_Template_ng_select_clear_56_listener", "onPermitReviewTypeClear", "PermitPopupComponent_ng_container_20_div_57_Template", "PermitPopupComponent_ng_container_20_span_61_Template", "PermitPopupComponent_ng_container_20_Template_ng_select_change_62_listener", "onPermitMunicipalityChange", "PermitPopupComponent_ng_container_20_Template_ng_select_clear_62_listener", "onPermitMunicipalityClear", "PermitPopupComponent_ng_container_20_div_63_Template", "PermitPopupComponent_ng_container_20_div_65_Template", "projects", "tmp_4_0", "touched", "invalid", "tmp_5_0", "tmp_6_0", "ɵɵclassProp", "tmp_7_0", "isPermitNumberValidating", "tmp_9_0", "tmp_10_0", "categories", "tmp_14_0", "reviewTypeArray", "tmp_16_0", "value", "tmp_17_0", "isPermitMunicipalRequired", "muncipalities", "tmp_20_0", "tmp_22_0", "tmp_23_0", "getSyncButtonDisableStatus", "id", "PermitPopupComponent_ng_container_21_div_8_div_1_Template", "PermitPopupComponent_ng_container_21_div_19_div_1_Template", "PermitPopupComponent_ng_container_21_div_27_div_1_Template", "PermitPopupComponent_ng_container_21_div_38_div_1_Template", "PermitPopupComponent_ng_container_21_div_8_Template", "PermitPopupComponent_ng_container_21_div_19_Template", "PermitPopupComponent_ng_container_21_div_27_Template", "PermitPopupComponent_ng_container_21_div_38_Template", "tmp_1_0", "statuses", "permitTypes", "PermitPopupComponent_button_24_Template_button_click_0_listener", "_r4", "goToPreviousTab", "PermitPopupComponent_button_29_Template_button_click_0_listener", "_r5", "save", "PermitPopupComponent_button_30_Template_button_click_0_listener", "_r6", "goToNextTab", "PermitPopupComponent", "modal", "fb", "projectsService", "permitsService", "appService", "httpUtilService", "customLayoutUtilsService", "cdr", "isHideInternalReviewStatus", "permit", "passEntry", "loginUser", "isLoading", "selectedTab", "internalStatusArray", "iscityreviewLinkMunicipalRequired", "cityReviewLink", "syncedPermitData", "constructor", "loadingSubject", "subscribe", "loading", "ngOnInit", "getLoggedInUser", "loadMunicipalities", "loadForm", "loadProjects", "setupPermitNumberValidation", "patchForm", "group", "projectId", "required", "permitName", "permitCategory", "permitType", "description", "permitReviewType", "location", "primaryContact", "permitAppliedDate", "permitExpirationDate", "permitIssueDate", "permitFinalDate", "permitCompleteDate", "permitStatus", "internalReviewStatus", "attentionReason", "internalNotes", "actionTaken", "reviewResponsibleParty", "permitMunicipality", "permitNumberControl", "valueChanges", "pipe", "<PERSON><PERSON><PERSON><PERSON>", "setErrors", "Object", "keys", "length", "projectIdControl", "enteredNormalized", "toString", "trim", "toLowerCase", "originalNormalized", "validatePermitNumber", "res", "responseData", "result", "exists", "message", "params", "permitId", "next", "paginate", "getAllProjectsData", "response", "data", "error", "console", "loggedinUser", "userId", "getAllMunicipalities", "get<PERSON><PERSON><PERSON>", "loggedInUserId", "permitResponse", "<PERSON><PERSON><PERSON>", "permitData", "patchValue", "formatDateForInput", "warn", "err", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "preparePermitData", "formData", "permitRequestData", "caseId", "EntityId", "Id", "permitEntityID", "controls", "log", "for<PERSON>ach", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "showError", "create", "edit", "createPermit", "showSuccess", "emit", "close", "updatePermit", "showTab", "tab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "projectLocation", "permitNameControl", "currentValue", "projectName", "project", "pristine", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permitControl", "setValidators", "clearValidators", "updateValueAndValidity", "cityWebsiteLink", "getPermitDetails", "municipalityId", "address", "applyDate", "expireDate", "issueDate", "finalDate", "completeDate", "reviewType", "isExternal", "hasPermitNumber", "hasPermitMunicipality", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "FormBuilder", "i3", "ProjectsService", "i4", "PermitsService", "i5", "AppService", "i6", "HttpUtilsService", "i7", "CustomLayoutUtilsService", "ChangeDetectorRef", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "PermitPopupComponent_Template", "rf", "ctx", "PermitPopupComponent_div_4_Template", "PermitPopupComponent_div_5_Template", "PermitPopupComponent_Template_i_click_7_listener", "dismiss", "PermitPopupComponent_Template_a_click_14_listener", "PermitPopupComponent_Template_a_click_17_listener", "PermitPopupComponent_ng_container_20_Template", "PermitPopupComponent_ng_container_21_Template", "PermitPopupComponent_button_24_Template", "PermitPopupComponent_Template_button_click_26_listener", "PermitPopupComponent_button_29_Template", "PermitPopupComponent_button_30_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.html"], "sourcesContent": ["import {\n  Component,\n  Input,\n  Output,\n  EventEmitter,\n  ChangeDetectorRef,\n  OnInit,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ProjectsService } from '../../services/projects.service';\nimport { PermitsService } from '../../services/permits.service';\nimport { AppService } from '../../services/app.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { even } from '@rxweb/reactive-form-validators';\nimport { debounceTime, distinctUntilChanged, switchMap, catchError, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\n\n@Component({\n  selector: 'app-permit-popup',\n  templateUrl: './permit-popup.component.html',\n})\nexport class PermitPopupComponent {\n  @Input() id: number = 0; // 0 = Add, otherwise Edit\n  @Input() isHideInternalReviewStatus: Boolean = true; // 0 = Add, otherwise Edit\n  @Input() permit: any; // incoming permit data (for edit)\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\n\n  permitForm: FormGroup;\n  projects: any[] = [];\n  reviewTypeArray: any[] = ['Internal', 'External', 'Both'];\n  loginUser: any = {};\n  isLoading: boolean = false;\n  muncipalities: any = [];\n  selectedTab: any = 'basic';\n  // dropdown options\n  permitTypes = [\n    'Access Control System - Commercial',\n    'Addition - Commercial',\n    'Addition - Residential',\n    'Backflow - Commercial',\n    'Building Miscellaneous - Commercial',\n    'Building Move Permit - Residential',\n    'Building Revisions - Commercial Revision',\n    'Certificate of Completion',\n    'Certificate of Occupancy - Commercial',\n    'Commercial - LV Data Voice Cable Sub-Permit',\n    'Demolition - Commercial',\n    'Document Submittal - Commercial Building',\n    'Electrical Sub-Permit - Commercial',\n    'Engineering Construction Traffic & Parking Management Plan',\n    'Fence - Commercial',\n    'Fire Alarm - Fire',\n    'Fire Sprinkler/Fire Suppression - Fire',\n    'Foundation Only - Commercial',\n    'Gas Sub-Permit - Commercial',\n    'General Electrical - Commercial',\n    'General Paving - Paving',\n    'General Sign Permit',\n    'Generator - Commercial',\n    'Interceptor - Commercial',\n    'Interior (<5000 sq ft) - Commercial',\n    'Irrigation - Commercial',\n    'Landscape Non-Residential and Multi-Family',\n    'Low Voltage - Commercial',\n    'Mechanical Sub-Permit - Commercial',\n    'Monument - Sign',\n    'Mural - Sign',\n    'New Building - Commercial',\n    'Plumbing Sub-Permit - Commercial',\n    'Pool Plumbing Commercial (Sub-Permit)',\n    'Public Art Permit Application',\n    'Remodel - Commercial',\n    'Right-of-Way | ENG A - General',\n    'Sewer Cap for Demo - Commercial',\n    'Windows and Doors - Commercial',\n  ];\n  categories = [\n    'Primary',\n    'Sub Permit',\n    'Industrial',\n    'Municipal',\n    'Environmental',\n  ];\n  statuses = [\n    'Canceled',\n    'Complete',\n    'Expired',\n    'Fees Due',\n    'In Review',\n    'Issued',\n    'On Hold',\n    'Requires Resubmit',\n    'Requires Resubmit for Prescreen',\n    'Submitted - Online',\n    'Void',\n  ];\n  internalStatusArray =['Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed']\n  permitNumber: any;\n  isPermitMunicipalRequired: boolean = false;\n  iscityreviewLinkMunicipalRequired: boolean=false;\n  cityReviewLink:any ='';\n  syncedPermitData:any ={};\n  permitNumberValidationError: string = '';\n  isPermitNumberValidating: boolean = false;\n  constructor(\n    public modal: NgbActiveModal,\n    private fb: FormBuilder,\n    private projectsService: ProjectsService,\n    private permitsService: PermitsService,\n    private appService: AppService,\n    private httpUtilService: HttpUtilsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private cdr: ChangeDetectorRef\n  ) {\n    // Subscribe to loading state\n    this.httpUtilService.loadingSubject.subscribe((loading) => {\n      this.isLoading = loading;\n    });\n  }\n\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadMunicipalities();\n    this.loadForm();\n    this.loadProjects();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n  }\n\n  loadForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      permitCategory: ['', Validators.required],\n      permitType: ['', Validators.required],\n      description: [''],\n      permitReviewType: ['', Validators.required],\n      location: ['',Validators.required],\n      primaryContact: [''],\n      permitAppliedDate: ['', Validators.required],\n      permitExpirationDate: [''],\n      permitIssueDate: [''],\n      permitFinalDate: [''],\n      permitCompleteDate: [''],\n      permitStatus: ['', Validators.required],\n      internalReviewStatus: ['', Validators.required],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      reviewResponsibleParty: ['', Validators.required],\n      permitMunicipality: [''],\n      // cityReviewLink: [''],\n    });\n  }\n\n  setupPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    if (permitNumberControl) {\n      // Only clear server-side validation while typing; don't call API here\n      permitNumberControl.valueChanges\n        .pipe(distinctUntilChanged())\n        .subscribe(() => {\n          this.permitNumberValidationError = '';\n          if (permitNumberControl.hasError('permitNumberExists')) {\n            const errors: any = { ...permitNumberControl.errors };\n            delete errors['permitNumberExists'];\n            permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n          }\n        });\n    }\n  }\n\n  triggerPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    const projectIdControl = this.permitForm.get('projectId');\n    const permitNumber = permitNumberControl?.value;\n    const projectId = projectIdControl?.value;\n\n    const enteredNormalized = (permitNumber || '').toString().trim().toLowerCase();\n    const originalNormalized = (this.permitNumber || '').toString().trim().toLowerCase();\n\n    // If empty, or in edit mode and value hasn't changed, just clear any prior error and skip API\n    if (!enteredNormalized || (this.id !== 0 && enteredNormalized === originalNormalized)) {\n      this.isPermitNumberValidating = false;\n      this.permitNumberValidationError = '';\n      if (permitNumberControl?.hasError('permitNumberExists')) {\n        const errors: any = { ...permitNumberControl.errors };\n        delete errors['permitNumberExists'];\n        permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n      }\n      return;\n    }\n\n    if (enteredNormalized && projectId) {\n      this.isPermitNumberValidating = true;\n      this.validatePermitNumber(permitNumber, projectId)\n        .pipe(map((res: any) => res?.responseData ?? res))\n        .subscribe((result: any) => {\n          this.isPermitNumberValidating = false;\n          if (result && result.exists) {\n            this.permitNumberValidationError = result.message;\n            permitNumberControl?.setErrors({ 'permitNumberExists': true });\n          } else {\n            this.permitNumberValidationError = '';\n            if (permitNumberControl?.hasError('permitNumberExists')) {\n              const errors: any = { ...permitNumberControl.errors };\n              delete errors['permitNumberExists'];\n              permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n            }\n          }\n        });\n    }\n  }\n\n  validatePermitNumber(permitNumber: string, projectId: number) {\n    this.isPermitNumberValidating = true;\n    this.permitNumberValidationError = '';\n\n    const params = {\n      permitNumber: permitNumber,\n      projectId: projectId,\n      permitId: this.id !== 0 ? this.id : null // Include permitId for edit mode\n    };\n\n    return this.permitsService.validatePermitNumber(params).pipe(\n      catchError(() => {\n        this.isPermitNumberValidating = false;\n        return of({ exists: false, message: '' });\n      })\n    );\n  }\n\n  loadProjects() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = { paginate: false };\n    this.projectsService.getAllProjectsData(params).subscribe({\n      next: (response: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.projects = response.responseData.data;\n        }\n      },\n      error: (error: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.projects = [];\n      },\n    });\n  }\n\n  loadMunicipalities() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = { loggedinUser: this.loginUser.userId };\n    this.permitsService.getAllMunicipalities(params).subscribe({\n      next: (response: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.muncipalities = response.responseData.data;\n        }\n      },\n      error: (error: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.muncipalities = [];\n      },\n    });\n  }\n\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService\n      .getPermit({ permitId: this.id, loggedInUserId: this.loginUser.userId })\n      .subscribe({\n        next: (permitResponse: any) => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!permitResponse.isFault) {\n            let permitData = permitResponse.responseData.data;\n            this.permitNumber = permitData.permitNumber;\n            this.permitForm.patchValue({\n              projectId: permitData.projectId,\n              permitNumber: permitData.permitNumber,\n              permitReviewType: permitData.permitReviewType,\n              permitCategory: permitData.permitCategory,\n              permitType: permitData.permitType,\n              description: permitData.description,\n              permitName: permitData.permitName,\n              location: permitData.location,\n              internalReviewStatus:permitData.internalReviewStatus,\n              primaryContact: permitData.primaryContact,\n              permitAppliedDate: permitData.permitAppliedDate\n                ? this.formatDateForInput(permitData.permitAppliedDate)\n                : '',\n              permitExpirationDate: permitData.permitExpirationDate\n                ? this.formatDateForInput(permitData.permitExpirationDate)\n                : '',\n              permitIssueDate: permitData.permitIssueDate\n                ? this.formatDateForInput(permitData.permitIssueDate)\n                : '',\n                  permitFinalDate: permitData.permitFinalDate\n                ? this.formatDateForInput(permitData.permitFinalDate)\n                : '',\n              permitCompleteDate: permitData.permitCompleteDate\n                ? this.formatDateForInput(permitData.permitCompleteDate)\n                : '',\n              permitStatus: permitData.permitStatus,\n              attentionReason: permitData.attentionReason,\n              internalNotes: permitData.internalNotes,\n              actionTaken: permitData.actionTaken,\n              reviewResponsibleParty: permitData.reviewResponsibleParty,\n              permitMunicipality: permitData.permitMunicipality,\n              // cityReviewLink: permitData.cityReviewLink,\n            });\n            this.onPermitReviewTypeChange(permitData.permitReviewType)\n          } else {\n            console.warn(\n              'Permit response has isFault = true',\n              permitResponse.responseData\n            );\n          }\n        },\n        error: (err) => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('API call failed', err);\n        },\n      });\n  }\n\n  formatDateForInput(dateString: string): string {\n    if (!dateString) return '';\n\n    const date = new Date(dateString);\n    // Use local timezone to avoid date shifting\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    return `${year}-${month}-${day}`;\n  }\n\n  preparePermitData() {\n    const formData = this.permitForm.value;\n\n    let permitRequestData: any = {};\n    permitRequestData.projectId = formData.projectId;\n    permitRequestData.permitNumber = formData.permitNumber;\n    permitRequestData.permitReviewType = formData.permitReviewType;\n    permitRequestData.permitCategory = formData.permitCategory;\n    permitRequestData.permitType = formData.permitType;\n    permitRequestData.description = formData.description;\n    permitRequestData.location = formData.location;\n    permitRequestData.primaryContact = formData.primaryContact;\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\n    permitRequestData.permitExpirationDate =\n      formData.permitExpirationDate || null;\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\n    permitRequestData.permitFinalDate =\n      formData.permitFinalDate || null;\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\n    permitRequestData.permitStatus = formData.permitStatus;\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\n    permitRequestData.permitName = formData.permitName;\n    permitRequestData.attentionReason = formData.attentionReason;\n    permitRequestData.internalNotes = formData.internalNotes;\n    permitRequestData.actionTaken = formData.actionTaken;\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\n    permitRequestData.cityReviewLink = this.cityReviewLink;\n    permitRequestData.loggedInUserId = this.loginUser.userId;\n    permitRequestData.syncedPermitData = this.syncedPermitData;\n    const caseId =  this.syncedPermitData.EntityId ||  this.syncedPermitData.permitId ||  this.syncedPermitData.Id || null;\n    permitRequestData.permitEntityID = caseId\n    if (this.id !== 0) {\n      permitRequestData.permitId = this.id;\n    }\n\n    return permitRequestData;\n  }\n\n  save() {\n    let controls = this.permitForm.controls;\n    console.log('Permit Data:', this.permitForm.value);\n    if (this.permitForm.invalid) {\n      Object.keys(controls).forEach((controlName) =>\n        controls[controlName].markAsTouched()\n      );\n      this.customLayoutUtilsService.showError(\n        'Please fill all required fields',\n        ''\n      );\n      return;\n    }\n    let permitData: any = this.preparePermitData();\n    console.log('Permit Data:', permitData);\n    if (this.id === 0) {\n      this.create(permitData);\n    } else {\n      this.edit(permitData);\n    }\n  }\n\n  create(permitData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.createPermit(permitData).subscribe((res: any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n\n  edit(permitData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.updatePermit(permitData).subscribe((res) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  showTab(tab: any, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'notes';\n    }\n    this.cdr.markForCheck();\n  }\n\n  goToPreviousTab() {\n    if (this.selectedTab === 'notes') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n    this.cdr.markForCheck();\n  }\n\n  onProjectChange(event:any){\n     this.permitForm.patchValue({\n              location: event.projectLocation })\n    // console.log(\"project loacation\",event)\n    // Auto-fill Permit / Sub Project Name for Add mode when selecting a project\n    if (this.id === 0) {\n      const permitNameControl = this.permitForm.get('permitName');\n      const currentValue = (permitNameControl?.value || '').toString().trim();\n      const projectName = event?.projectName || event?.project?.projectName || '';\n      if (permitNameControl && projectName && (currentValue === '' || permitNameControl.pristine)) {\n        permitNameControl.setValue(projectName);\n        permitNameControl.markAsDirty();\n      }\n    }\n\n  }\n\nonPermitReviewTypeChange(event: any) {\n  const permitControl = this.permitForm.get('permitMunicipality');\n  // const cityReviewControl = this.permitForm.get('cityReviewLink');\n\n  if (event === 'External') {\n    this.isPermitMunicipalRequired = true;\n    // this.iscityreviewLinkMunicipalRequired = true;\n\n    permitControl?.setValidators([Validators.required]);\n    // cityReviewControl?.setValidators([Validators.required]);\n  } else {\n    this.isPermitMunicipalRequired = false;\n    // this.iscityreviewLinkMunicipalRequired = false;\n\n    permitControl?.clearValidators();\n    // cityReviewControl?.clearValidators();\n  }\n\n  permitControl?.updateValueAndValidity();\n  // cityReviewControl?.updateValueAndValidity();\n}\n\nonPermitReviewTypeClear() {\n  this.permitForm.patchValue({ permitReviewType: null });\n  // Also clear the municipality when review type is cleared\n  this.permitForm.patchValue({ permitMunicipality: null });\n  this.cityReviewLink = '';\n  this.isPermitMunicipalRequired = false;\n  \n  // Clear validators for municipality\n  const permitControl = this.permitForm.get('permitMunicipality');\n  permitControl?.clearValidators();\n  permitControl?.updateValueAndValidity();\n}\n\nonPermitMunicipalityChange(event:any){\n  console.log('event 0 ',event);\n  this.cityReviewLink= event.cityWebsiteLink+'#/permit/';\n  this.getSyncButtonDisableStatus()\n}\n\nonPermitMunicipalityClear(){\n  this.permitForm.patchValue({ permitMunicipality: null });\n  this.cityReviewLink = '';\n  this.getSyncButtonDisableStatus();\n}\n\nsyncPermitDetails(){\n   const formData = this.permitForm.value;\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService\n      .getPermitDetails({ permitNumber: formData.permitNumber, municipalityId: formData.permitMunicipality })\n      .subscribe({\n        next: (permitResponse: any) => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!permitResponse.isFault) {\n            let permitData = permitResponse.responseData.permit;\n            this.syncedPermitData = permitData\n            this.permitForm.patchValue({\n              permitType: permitData.permitType,\n              description: permitData.description,\n              location: permitData.address,\n              permitAppliedDate: permitData.applyDate\n                ? this.formatDateForInput(permitData.applyDate)\n                : '',\n              permitExpirationDate: permitData.expireDate\n                ? this.formatDateForInput(permitData.expireDate)\n                : '',\n              permitIssueDate: permitData.issueDate\n                ? this.formatDateForInput(permitData.issueDate)\n                : '',\n              permitFinalDate: permitData.finalDate\n                ? this.formatDateForInput(permitData.finalDate)\n                : '',\n              permitCompleteDate: permitData.completeDate\n                ? this.formatDateForInput(permitData.completeDate)\n                : '',\n              permitStatus: permitData.permitStatus,\n\n            });\n          } else {\n            console.warn(\n              'Permit response has isFault = true',\n              permitResponse.responseData.message\n            );\n          }\n        },\n        error: (err) => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('API call failed', err);\n        },\n      });\n}\n\ngetSyncButtonDisableStatus(): boolean {\n  const reviewType = this.permitForm.get('permitReviewType')?.value;\n  const permitNumber = this.permitForm.get('permitNumber')?.value;\n  const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\n\n  const isExternal = reviewType === 'External';\n  const hasPermitNumber = !!permitNumber;\n  const hasPermitMunicipality = !!permitMunicipality;\n\n  console.log('isExternal ', isExternal)\n  console.log('hasPermitNumber ', hasPermitNumber)\n  console.log('hasPermitMunicipality ', hasPermitMunicipality)\n  // Disable if any of the conditions are not satisfied\n  return !(isExternal && hasPermitNumber && hasPermitMunicipality);\n}\n}\n", "<div class=\"modal-content h-auto\">\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container> \n        <div *ngIf=\"id === 0\">Add Permit</div>\n        <div *ngIf=\"id !== 0\">Edit Permit - {{ permitNumber }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"modal.dismiss()\"></i>\n    </div>\n  </div>\n\n  <div class=\"modal-body\">\n    <!-- Loading overlay removed; global loader handles this -->\n\n    <div class=\"row\">\n      <div class=\"col-xl-12\">\n        <div class=\"d-flex\">\n          <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\">\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'basic' }\" (click)=\"showTab('basic', $event)\">\n                Basic Info\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'details' }\" (click)=\"showTab('details', $event)\">\n                Permit Details\n              </a>\n            </li>\n            <!-- <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'notes' }\" (click)=\"showTab('notes', $event)\">\n                Notes/Actions\n              </a>\n            </li> -->\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <form class=\"form form-label-right\" [formGroup]=\"permitForm\">\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Project <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"projects\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"projectName\"\n              formControlName=\"projectId\" bindValue=\"projectId\" placeholder=\"Select Project\"\n              (change)=\"onProjectChange($event)\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('projectId')?.touched && permitForm.get('projectId')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('projectId')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Permit / Sub Project Name <span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitName\" />\n            <div *ngIf=\"permitForm.get('permitName')?.touched && permitForm.get('permitName')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitName')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n                <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Location<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"location\" />\n            <div *ngIf=\"permitForm.get('location')?.touched && permitForm.get('location')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('location')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Permit # <span class=\"text-danger\">*</span></label>\n            <div class=\"position-relative\">\n              <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitNumber\" \n                     (blur)=\"triggerPermitNumberValidation()\"\n                     [class.is-invalid]=\"permitForm.get('permitNumber')?.invalid && permitForm.get('permitNumber')?.touched\" />\n              <div *ngIf=\"isPermitNumberValidating\" class=\"position-absolute top-50 end-0 translate-middle-y me-2\">\n                <div class=\"spinner-border spinner-border-sm text-primary\" role=\"status\">\n                  <span class=\"visually-hidden\">Validating...</span>\n                </div>\n              </div>\n            </div>\n            <!-- Validation error messages -->\n            <div *ngIf=\"permitForm.get('permitNumber')?.touched && permitForm.get('permitNumber')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['required']\">\n                Required Field\n              </div>\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['permitNumberExists']\">\n                {{ permitNumberValidationError }}\n              </div>\n            </div>\n            <!-- Show validation error even if field is not touched but has error (e.g., after blur) -->\n            <div *ngIf=\"permitNumberValidationError && !permitForm.get('permitNumber')?.touched\"\n              class=\"text-danger mt-1 small\">\n              {{ permitNumberValidationError }}\n            </div>\n          </div>\n          <!-- <div  class=\"col-xl-4\">\n            <ng-container *ngIf=\"isHideInternalReviewStatus\">\n\n            <label class=\"fw-bold form-label mb-2\">Internal Review Status <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"internalStatusArray\" [clearable]=\"false\" [multiple]=\"false\"\n              formControlName=\"internalReviewStatus\" placeholder=\"Select Status\">\n            </ng-select>\n            <div\n              *ngIf=\"permitForm.get('internalReviewStatus')?.touched && permitForm.get('internalReviewStatus')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('internalReviewStatus')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n            </ng-container>\n\n          </div> -->\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Permit Category <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"categories\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitCategory\"\n              placeholder=\"Select Category\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitCategory')?.touched && permitForm.get('permitCategory')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitCategory')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n\n        </div>\n                <div class=\"row mt-3\">\n          <div class=\"col-xl-12\">\n            <div class=\"text-muted small d-flex align-items-center\" style=\"white-space: normal;\">\n              <i class=\"fas fa-info-circle me-2\"></i>\n              <span>For External Review, permit details can be retrieved from the Municipality City website when Municipality is chosen.</span>\n            </div>\n          </div>\n        </div>\n         <div class=\"row mt-4\">\n           <div class=\"col-xl-6\">\n            <div class=\"col-xl-12\">\n             <label class=\"fw-bold form-label mb-2\">Permit Review Type<span\n                 class=\"text-danger\">*</span></label>\n             <ng-select [items]=\"reviewTypeArray\" formControlName=\"permitReviewType\"\n               [clearable]=\"permitForm.get('permitReviewType')?.value\"\n               (change)=\"onPermitReviewTypeChange($event)\" (clear)=\"onPermitReviewTypeClear()\">\n             </ng-select>\n             <div *ngIf=\"permitForm.get('permitReviewType')?.touched && permitForm.get('permitReviewType')?.invalid\"\n               class=\"text-danger mt-1 small\">\n               <div *ngIf=\"permitForm.get('permitReviewType')?.errors?.['required']\">\n                  Required Field\n               </div>\n             </div>\n           </div>\n           <div class=\"col-xl-12\">\n             <label class=\"fw-bold form-label mb-2\">Permit Municipality (External Review)<span\n                 *ngIf=\"isPermitMunicipalRequired\" class=\"text-danger\">*</span></label>\n             <ng-select [items]=\"muncipalities\" [clearable]=\"permitForm.get('permitMunicipality')?.value\" [multiple]=\"false\" bindLabel=\"cityName\"\n               formControlName=\"permitMunicipality\" bindValue=\"municipalityId\" placeholder=\"Select Municipality\"\n               (change)=\"onPermitMunicipalityChange($event)\" (clear)=\"onPermitMunicipalityClear()\">\n             </ng-select>\n             <div *ngIf=\"permitForm.get('permitMunicipality')?.touched && permitForm.get('permitMunicipality')?.invalid\"\n               class=\"text-danger mt-1 small\">\n               <div *ngIf=\"permitForm.get('permitMunicipality')?.errors?.['required']\">\n                  Required Field\n               </div>\n             </div>\n           </div>\n           </div>\n           <div class=\"col-xl-6 align-con\">\n             <div *ngIf=\"permitForm.get('permitReviewType')?.value === 'External' && !getSyncButtonDisableStatus() && id === 0\" class=\"row mt-3\">\n           <div class=\"col-xl-12\">\n             <div class=\"small me-3\" style=\"white-space: normal;\">\n               The syncing of permit details may take up to 3 minutes.\n             </div><br>\n             <div>\n               <button type=\"button\" class=\"btn btn-primary btn-sm\"\n                       (click)=\"syncPermitDetails()\">\n                 <i class=\"fas fa-sync-alt\"></i> Sync\n               </button>\n             </div>\n           </div>\n         </div>\n           </div>\n         </div>\n        \n      </ng-container>\n      <ng-container *ngIf=\"selectedTab == 'details'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Review Responsible Party<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"reviewResponsibleParty\" />\n            <div\n              *ngIf=\"permitForm.get('reviewResponsibleParty')?.touched && permitForm.get('reviewResponsibleParty')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('reviewResponsibleParty')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Primary Contact Name</label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"primaryContact\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Status <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"statuses\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitStatus\"\n              placeholder=\"Select Status\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitStatus')?.touched && permitForm.get('permitStatus')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitStatus')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Type <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"permitTypes\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitType\"\n              placeholder=\"Select Type\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitType')?.touched && permitForm.get('permitType')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitType')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Issue Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitIssueDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Applied Date <span class=\"text-danger\">*</span></label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitAppliedDate\" />\n            <div *ngIf=\"permitForm.get('permitAppliedDate')?.touched && permitForm.get('permitAppliedDate')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitAppliedDate')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n\n\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Expiration Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitExpirationDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Completed Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitCompleteDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Final Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitFinalDate\" />\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Description</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"description\"></textarea>\n          </div>\n        </div>\n      </ng-container>\n      <!-- <ng-container *ngIf=\"selectedTab == 'role'\">\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Permit City Review Link<span *ngIf=\"isPermitMunicipalRequired\"\n                class=\"text-danger\">*</span></label>\n            <input type=\"url\" class=\"form-control form-control-sm\" formControlName=\"cityReviewLink\" />\n            <div *ngIf=\"permitForm.get('cityReviewLink')?.touched && permitForm.get('cityReviewLink')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('cityReviewLink')?.errors?.['required']\">\n                Permit City Review Link is required\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n\n        </div>\n\n\n      </ng-container> -->\n      <!-- <ng-container *ngIf=\"selectedTab == 'notes'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Attention Reason</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"attentionReason\"></textarea>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Internal Notes</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"internalNotes\"></textarea>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Action Taken</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"actionTaken\"></textarea>\n          </div>\n        </div>\n      </ng-container> -->\n    </form>\n  </div>\n\n  <div class=\"modal-footer justify-content-between\">\n    <div>\n      <button *ngIf=\"selectedTab == 'details'\" type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate\"\n        (click)=\"goToPreviousTab()\">\n        Previous\n      </button>\n    </div>\n    <div>\n      <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate mr-2\" (click)=\"modal.dismiss()\">\n        Cancel</button>&nbsp;\n      <!-- *ngIf=\"selectedTab == 'notes'\"  -->\n      <button type=\"button\" class=\"btn btn-primary btn-sm\" *ngIf=\"selectedTab == 'details'\"\n        [disabled]=\"permitForm.invalid || isPermitNumberValidating\" (click)=\"save()\">\n        Save\n      </button>\n      <button *ngIf=\"selectedTab == 'basic'\" type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"goToNextTab()\">\n        Next\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAIEA,YAAY,QAGP,eAAe;AACtB,SAAiCC,UAAU,QAAyB,gBAAgB;AAQpF,SAAuBC,oBAAoB,EAAaC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAC/F,SAASC,EAAE,QAAQ,MAAM;;;;;;;;;;;;;;;;ICbjBC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACtCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,YAAA,KAAgC;;;;;IAiDhDP,EAAA,CAAAC,cAAA,UAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAC,yDAAA,iBAA+D;IAGjET,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAuD;;;;;IAa7Dd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAO,0DAAA,iBAAgE;IAGlEf,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAY9Dd,EAAA,CAAAC,cAAA,UAA8D;IAC3DD,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAQ,0DAAA,iBAA8D;IAGhEhB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAsD;;;;;IAexDd,EAFJ,CAAAC,cAAA,cAAqG,cAC1B,eACzC;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;;;;;IAKNH,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAW,2BAAA,MACF;;;;;IAPFjB,EAAA,CAAAC,cAAA,cACiC;IAI/BD,EAHA,CAAAQ,UAAA,IAAAU,0DAAA,iBAAkE,IAAAC,0DAAA,iBAGU;IAG9EnB,EAAA,CAAAG,YAAA,EAAM;;;;;;IANEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;IAG1Dd,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAU,OAAA,GAAAd,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,uBAAoE;;;;;IAK5Ed,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAW,2BAAA,MACF;;;;;IA0BEjB,EAAA,CAAAC,cAAA,UAAoE;IACjED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAa,0DAAA,iBAAoE;IAGtErB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA4D;IAA5DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA4D;;;;;IA0BjEd,EAAA,CAAAC,cAAA,UAAsE;IACnED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAc,0DAAA,iBAAsE;IAGxEtB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA8D;IAA9DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA8D;;;;;IAMMd,EAAA,CAAAC,cAAA,eAClB;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAOhEH,EAAA,CAAAC,cAAA,UAAwE;IACrED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAe,0DAAA,iBAAwE;IAG1EvB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAgE;IAAhEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAgE;;;;;;IASxEd,EAFA,CAAAC,cAAA,cAAoI,aAC/G,cACgC;IACnDD,EAAA,CAAAE,MAAA,gEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAAAH,EAAA,CAAAwB,SAAA,SAAI;IAERxB,EADF,CAAAC,cAAA,UAAK,iBAEmC;IAA9BD,EAAA,CAAAyB,UAAA,mBAAAC,6EAAA;MAAA1B,EAAA,CAAA2B,aAAA,CAAAC,GAAA;MAAA,MAAAtB,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAyB,iBAAA,EAAmB;IAAA,EAAC;IACnC/B,EAAA,CAAAwB,SAAA,YAA+B;IAACxB,EAAA,CAAAE,MAAA,aAClC;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;;IAzJTH,EAAA,CAAAgC,uBAAA,GAA6C;IAGvChC,EAFJ,CAAAC,cAAA,cAAsB,aACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAC,cAAA,oBAEqC;IAAnCD,EAAA,CAAAyB,UAAA,oBAAAQ,0EAAAC,MAAA;MAAAlC,EAAA,CAAA2B,aAAA,CAAAQ,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAA8B,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IACpClC,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,IAAA6B,mDAAA,kBACiC;IAMrCrC,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3GH,EAAA,CAAAwB,SAAA,iBAAuF;IACvFxB,EAAA,CAAAQ,UAAA,KAAA8B,oDAAA,kBACiC;IAMrCtC,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFI,CAAAC,cAAA,eAAsB,cACL,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAwB,SAAA,iBAAqF;IACrFxB,EAAA,CAAAQ,UAAA,KAAA+B,oDAAA,kBACiC;IAMrCvC,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAExFH,EADF,CAAAC,cAAA,eAA+B,iBAGoF;IAD1GD,EAAA,CAAAyB,UAAA,kBAAAe,qEAAA;MAAAxC,EAAA,CAAA2B,aAAA,CAAAQ,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAQxB,MAAA,CAAAmC,6BAAA,EAA+B;IAAA,EAAC;IAD/CzC,EAAA,CAAAG,YAAA,EAEiH;IACjHH,EAAA,CAAAQ,UAAA,KAAAkC,oDAAA,kBAAqG;IAKvG1C,EAAA,CAAAG,YAAA,EAAM;IAYNH,EAVA,CAAAQ,UAAA,KAAAmC,oDAAA,kBACiC,KAAAC,oDAAA,kBAUA;IAGnC5C,EAAA,CAAAG,YAAA,EAAM;IAmBJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACjGH,EAAA,CAAAwB,SAAA,qBAEY;IACZxB,EAAA,CAAAQ,UAAA,KAAAqC,oDAAA,kBACiC;IAOrC7C,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFI,CAAAC,cAAA,eAAsB,cACL,eACgE;IACnFD,EAAA,CAAAwB,SAAA,aAAuC;IACvCxB,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4HAAoH;IAGhIF,EAHgI,CAAAG,YAAA,EAAO,EAC7H,EACF,EACF;IAIDH,EAHJ,CAAAC,cAAA,eAAsB,eACE,cACE,iBACiB;IAAAD,EAAA,CAAAE,MAAA,0BAAkB;IAAAF,EAAA,CAAAC,cAAA,gBACjC;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACxCH,EAAA,CAAAC,cAAA,qBAEkF;IAApCD,EAA5C,CAAAyB,UAAA,oBAAAqB,2EAAAZ,MAAA;MAAAlC,EAAA,CAAA2B,aAAA,CAAAQ,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAyC,wBAAA,CAAAb,MAAA,CAAgC;IAAA,EAAC,mBAAAc,0EAAA;MAAAhD,EAAA,CAAA2B,aAAA,CAAAQ,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAA2C,uBAAA,EAAyB;IAAA,EAAC;IACjFjD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAA0C,oDAAA,kBACiC;IAKnClD,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAuB,iBACkB;IAAAD,EAAA,CAAAE,MAAA,6CAAqC;IAAAF,EAAA,CAAAQ,UAAA,KAAA2C,qDAAA,mBAClB;IAAQnD,EAAA,CAAAG,YAAA,EAAQ;IAC1EH,EAAA,CAAAC,cAAA,qBAEsF;IAAtCD,EAA9C,CAAAyB,UAAA,oBAAA2B,2EAAAlB,MAAA;MAAAlC,EAAA,CAAA2B,aAAA,CAAAQ,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAA+C,0BAAA,CAAAnB,MAAA,CAAkC;IAAA,EAAC,mBAAAoB,0EAAA;MAAAtD,EAAA,CAAA2B,aAAA,CAAAQ,GAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAAUxB,MAAA,CAAAiD,yBAAA,EAA2B;IAAA,EAAC;IACrFvD,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,KAAAgD,oDAAA,kBACiC;IAMnCxD,EADA,CAAAG,YAAA,EAAM,EACA;IACNH,EAAA,CAAAC,cAAA,eAAgC;IAC9BD,EAAA,CAAAQ,UAAA,KAAAiD,oDAAA,kBAAoI;IAcxIzD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;;;;;;;;;IAvJQH,EAAA,CAAAI,SAAA,GAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAAoD,QAAA,CAAkB,oBAAoB,mBAAmB;IAI9D1D,EAAA,CAAAI,SAAA,EAAkF;IAAlFJ,EAAA,CAAAU,UAAA,WAAAiD,OAAA,GAAArD,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAA8C,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAArD,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAA8C,OAAA,CAAAE,OAAA,EAAkF;IAalF7D,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAAoD,OAAA,GAAAxD,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAiD,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAxD,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAiD,OAAA,CAAAD,OAAA,EAAoF;IAYpF7D,EAAA,CAAAI,SAAA,GAAgF;IAAhFJ,EAAA,CAAAU,UAAA,WAAAqD,OAAA,GAAAzD,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAkD,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAAzD,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAkD,OAAA,CAAAF,OAAA,EAAgF;IAc7E7D,EAAA,CAAAI,SAAA,GAAuG;IAAvGJ,EAAA,CAAAgE,WAAA,iBAAAC,OAAA,GAAA3D,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAoD,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAA3D,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAoD,OAAA,CAAAL,OAAA,EAAuG;IACxG5D,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAA4D,wBAAA,CAA8B;IAOhClE,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAAyD,OAAA,GAAA7D,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAsD,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAA7D,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAsD,OAAA,CAAAN,OAAA,EAAwF;IAUxF7D,EAAA,CAAAI,SAAA,EAA6E;IAA7EJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAW,2BAAA,OAAAmD,QAAA,GAAA9D,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuD,QAAA,CAAAR,OAAA,EAA6E;IAwBxE5D,EAAA,CAAAI,SAAA,GAAoB;IAAqBJ,EAAzC,CAAAU,UAAA,UAAAJ,MAAA,CAAA+D,UAAA,CAAoB,oBAAoB,mBAAmB;IAGhErE,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAAU,UAAA,WAAA4D,QAAA,GAAAhE,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAAyD,QAAA,CAAAV,OAAA,OAAAU,QAAA,GAAAhE,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAAyD,QAAA,CAAAT,OAAA,EAA4F;IAsBtF7D,EAAA,CAAAI,SAAA,IAAyB;IAClCJ,EADS,CAAAU,UAAA,UAAAJ,MAAA,CAAAiE,eAAA,CAAyB,eAAAC,QAAA,GAAAlE,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA2D,QAAA,CAAAC,KAAA,CACqB;IAGnDzE,EAAA,CAAAI,SAAA,EAAgG;IAAhGJ,EAAA,CAAAU,UAAA,WAAAgE,QAAA,GAAApE,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA6D,QAAA,CAAAd,OAAA,OAAAc,QAAA,GAAApE,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA6D,QAAA,CAAAb,OAAA,EAAgG;IASjG7D,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAqE,yBAAA,CAA+B;IACzB3E,EAAA,CAAAI,SAAA,EAAuB;IAA2DJ,EAAlF,CAAAU,UAAA,UAAAJ,MAAA,CAAAsE,aAAA,CAAuB,eAAAC,QAAA,GAAAvE,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAgE,QAAA,CAAAJ,KAAA,CAA0D,mBAAmB;IAIzGzE,EAAA,CAAAI,SAAA,EAAoG;IAApGJ,EAAA,CAAAU,UAAA,WAAAoE,QAAA,GAAAxE,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAiE,QAAA,CAAAlB,OAAA,OAAAkB,QAAA,GAAAxE,MAAA,CAAAM,UAAA,CAAAC,GAAA,yCAAAiE,QAAA,CAAAjB,OAAA,EAAoG;IASpG7D,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAU,UAAA,WAAAqE,QAAA,GAAAzE,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAAkE,QAAA,CAAAN,KAAA,qBAAAnE,MAAA,CAAA0E,0BAAA,MAAA1E,MAAA,CAAA2E,EAAA,OAA2G;;;;;IAyBhHjF,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA0E,yDAAA,iBAA4E;IAG9ElF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAoE;;;;;IAgB1Ed,EAAA,CAAAC,cAAA,UAAkE;IAC/DD,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA2E,0DAAA,iBAAkE;IAGpEnF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;;;;;IAehEd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA4E,0DAAA,iBAAgE;IAGlEpF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAc9Dd,EAAA,CAAAC,cAAA,UAAuE;IACpED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAA6E,0DAAA,iBAAuE;IAGzErF,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA+D;IAA/DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA+D;;;;;IArD7Ed,EAAA,CAAAgC,uBAAA,GAA+C;IAGzChC,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzGH,EAAA,CAAAwB,SAAA,gBAAmG;IACnGxB,EAAA,CAAAQ,UAAA,IAAA8E,mDAAA,kBAEiC;IAKnCtF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAAwB,SAAA,iBAA2F;IAC7FxB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAAwB,SAAA,qBAEY;IACZxB,EAAA,CAAAQ,UAAA,KAAA+E,oDAAA,kBACiC;IAOrCvF,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC7FH,EAAA,CAAAwB,SAAA,qBAEY;IACZxB,EAAA,CAAAQ,UAAA,KAAAgF,oDAAA,kBACiC;IAKnCxF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAwB,SAAA,iBAA4F;IAC9FxB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAAwB,SAAA,iBAA8F;IAC9FxB,EAAA,CAAAQ,UAAA,KAAAiF,oDAAA,kBACiC;IAMrCzF,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAJJ,CAAAC,cAAA,eAAsB,eAGE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAAwB,SAAA,iBAAiG;IACnGxB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAAwB,SAAA,iBAA+F;IACjGxB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAAwB,SAAA,iBAA4F;IAEhGxB,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAAwB,SAAA,oBAAiG;IAErGxB,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;IA3ECH,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAU,UAAA,WAAAgF,OAAA,GAAApF,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAA6E,OAAA,CAAA9B,OAAA,OAAA8B,OAAA,GAAApF,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAA6E,OAAA,CAAA7B,OAAA,EAA4G;IAapG7D,EAAA,CAAAI,SAAA,IAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAAqF,QAAA,CAAkB,oBAAoB,mBAAmB;IAG9D3F,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAAoD,OAAA,GAAAxD,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAiD,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAAxD,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAiD,OAAA,CAAAD,OAAA,EAAwF;IAYnF7D,EAAA,CAAAI,SAAA,GAAqB;IAAqBJ,EAA1C,CAAAU,UAAA,UAAAJ,MAAA,CAAAsF,WAAA,CAAqB,oBAAoB,mBAAmB;IAGjE5F,EAAA,CAAAI,SAAA,EAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAAyD,OAAA,GAAA7D,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAsD,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAA7D,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAsD,OAAA,CAAAN,OAAA,EAAoF;IAcpF7D,EAAA,CAAAI,SAAA,IAAkG;IAAlGJ,EAAA,CAAAU,UAAA,WAAA0D,QAAA,GAAA9D,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAuD,QAAA,CAAAR,OAAA,OAAAQ,QAAA,GAAA9D,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAuD,QAAA,CAAAP,OAAA,EAAkG;;;;;;IAgF9G7D,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAyB,UAAA,mBAAAoE,gEAAA;MAAA7F,EAAA,CAAA2B,aAAA,CAAAmE,GAAA;MAAA,MAAAxF,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAAyF,eAAA,EAAiB;IAAA,EAAC;IAC3B/F,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBAC+E;IAAjBD,EAAA,CAAAyB,UAAA,mBAAAuE,gEAAA;MAAAhG,EAAA,CAAA2B,aAAA,CAAAsE,GAAA;MAAA,MAAA3F,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA4F,IAAA,EAAM;IAAA,EAAC;IAC5ElG,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAU,UAAA,aAAAJ,MAAA,CAAAM,UAAA,CAAAiD,OAAA,IAAAvD,MAAA,CAAA4D,wBAAA,CAA2D;;;;;;IAG7DlE,EAAA,CAAAC,cAAA,iBAA4G;IAAxBD,EAAA,CAAAyB,UAAA,mBAAA0E,gEAAA;MAAAnG,EAAA,CAAA2B,aAAA,CAAAyE,GAAA;MAAA,MAAA9F,MAAA,GAAAN,EAAA,CAAA6B,aAAA;MAAA,OAAA7B,EAAA,CAAA8B,WAAA,CAASxB,MAAA,CAAA+F,WAAA,EAAa;IAAA,EAAC;IACzGrG,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADrUf,OAAM,MAAOmG,oBAAoB;EAoFtBC,KAAA;EACCC,EAAA;EACAC,eAAA;EACAC,cAAA;EACAC,UAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,GAAA;EA1FD7B,EAAE,GAAW,CAAC,CAAC,CAAC;EAChB8B,0BAA0B,GAAY,IAAI,CAAC,CAAC;EAC5CC,MAAM,CAAM,CAAC;EACZC,SAAS,GAAsB,IAAIvH,YAAY,EAAE;EAE3DkB,UAAU;EACV8C,QAAQ,GAAU,EAAE;EACpBa,eAAe,GAAU,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;EACzD2C,SAAS,GAAQ,EAAE;EACnBC,SAAS,GAAY,KAAK;EAC1BvC,aAAa,GAAQ,EAAE;EACvBwC,WAAW,GAAQ,OAAO;EAC1B;EACAxB,WAAW,GAAG,CACZ,oCAAoC,EACpC,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,qCAAqC,EACrC,oCAAoC,EACpC,0CAA0C,EAC1C,2BAA2B,EAC3B,uCAAuC,EACvC,6CAA6C,EAC7C,yBAAyB,EACzB,0CAA0C,EAC1C,oCAAoC,EACpC,4DAA4D,EAC5D,oBAAoB,EACpB,mBAAmB,EACnB,wCAAwC,EACxC,8BAA8B,EAC9B,6BAA6B,EAC7B,iCAAiC,EACjC,yBAAyB,EACzB,qBAAqB,EACrB,wBAAwB,EACxB,0BAA0B,EAC1B,qCAAqC,EACrC,yBAAyB,EACzB,4CAA4C,EAC5C,0BAA0B,EAC1B,oCAAoC,EACpC,iBAAiB,EACjB,cAAc,EACd,2BAA2B,EAC3B,kCAAkC,EAClC,uCAAuC,EACvC,+BAA+B,EAC/B,sBAAsB,EACtB,gCAAgC,EAChC,iCAAiC,EACjC,gCAAgC,CACjC;EACDvB,UAAU,GAAG,CACX,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,eAAe,CAChB;EACDsB,QAAQ,GAAG,CACT,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,QAAQ,EACR,SAAS,EACT,mBAAmB,EACnB,iCAAiC,EACjC,oBAAoB,EACpB,MAAM,CACP;EACD0B,mBAAmB,GAAE,CAAC,UAAU,EAAC,uBAAuB,EAAC,cAAc,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,mBAAmB,CAAC;EACjI9G,YAAY;EACZoE,yBAAyB,GAAY,KAAK;EAC1C2C,iCAAiC,GAAU,KAAK;EAChDC,cAAc,GAAM,EAAE;EACtBC,gBAAgB,GAAM,EAAE;EACxBvG,2BAA2B,GAAW,EAAE;EACxCiD,wBAAwB,GAAY,KAAK;EACzCuD,YACSlB,KAAqB,EACpBC,EAAe,EACfC,eAAgC,EAChCC,cAA8B,EAC9BC,UAAsB,EACtBC,eAAiC,EACjCC,wBAAkD,EAClDC,GAAsB;IAPvB,KAAAP,KAAK,GAALA,KAAK;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,GAAG,GAAHA,GAAG;IAEX;IACA,IAAI,CAACF,eAAe,CAACc,cAAc,CAACC,SAAS,CAAEC,OAAO,IAAI;MACxD,IAAI,CAACT,SAAS,GAAGS,OAAO;IAC1B,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACX,SAAS,GAAG,IAAI,CAACP,UAAU,CAACmB,eAAe,EAAE;IAClD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,2BAA2B,EAAE;IAClC,IAAI,IAAI,CAACjD,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACkD,SAAS,EAAE;IAClB;EACF;EAEAH,QAAQA,CAAA;IACN,IAAI,CAACpH,UAAU,GAAG,IAAI,CAAC4F,EAAE,CAAC4B,KAAK,CAAC;MAC9BC,SAAS,EAAE,CAAC,EAAE,EAAE1I,UAAU,CAAC2I,QAAQ,CAAC;MACpCC,UAAU,EAAE,CAAC,EAAE,EAAE5I,UAAU,CAAC2I,QAAQ,CAAC;MACrC/H,YAAY,EAAE,CAAC,EAAE,EAAEZ,UAAU,CAAC2I,QAAQ,CAAC;MACvCE,cAAc,EAAE,CAAC,EAAE,EAAE7I,UAAU,CAAC2I,QAAQ,CAAC;MACzCG,UAAU,EAAE,CAAC,EAAE,EAAE9I,UAAU,CAAC2I,QAAQ,CAAC;MACrCI,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,gBAAgB,EAAE,CAAC,EAAE,EAAEhJ,UAAU,CAAC2I,QAAQ,CAAC;MAC3CM,QAAQ,EAAE,CAAC,EAAE,EAACjJ,UAAU,CAAC2I,QAAQ,CAAC;MAClCO,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,iBAAiB,EAAE,CAAC,EAAE,EAAEnJ,UAAU,CAAC2I,QAAQ,CAAC;MAC5CS,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,YAAY,EAAE,CAAC,EAAE,EAAExJ,UAAU,CAAC2I,QAAQ,CAAC;MACvCc,oBAAoB,EAAE,CAAC,EAAE,EAAEzJ,UAAU,CAAC2I,QAAQ,CAAC;MAC/Ce,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,sBAAsB,EAAE,CAAC,EAAE,EAAE7J,UAAU,CAAC2I,QAAQ,CAAC;MACjDmB,kBAAkB,EAAE,CAAC,EAAE;MACvB;KACD,CAAC;EACJ;EAEAvB,2BAA2BA,CAAA;IACzB,MAAMwB,mBAAmB,GAAG,IAAI,CAAC9I,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC;IAC/D,IAAI6I,mBAAmB,EAAE;MACvB;MACAA,mBAAmB,CAACC,YAAY,CAC7BC,IAAI,CAAChK,oBAAoB,EAAE,CAAC,CAC5B+H,SAAS,CAAC,MAAK;QACd,IAAI,CAAC1G,2BAA2B,GAAG,EAAE;QACrC,IAAIyI,mBAAmB,CAACG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;UACtD,MAAM/I,MAAM,GAAQ;YAAE,GAAG4I,mBAAmB,CAAC5I;UAAM,CAAE;UACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;UACnC4I,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAAClJ,MAAM,CAAC,CAACmJ,MAAM,GAAG,CAAC,GAAGnJ,MAAM,GAAG,IAAI,CAAC;QAC/E;MACF,CAAC,CAAC;IACN;EACF;EAEA2B,6BAA6BA,CAAA;IAC3B,MAAMiH,mBAAmB,GAAG,IAAI,CAAC9I,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC;IAC/D,MAAMqJ,gBAAgB,GAAG,IAAI,CAACtJ,UAAU,CAACC,GAAG,CAAC,WAAW,CAAC;IACzD,MAAMN,YAAY,GAAGmJ,mBAAmB,EAAEjF,KAAK;IAC/C,MAAM4D,SAAS,GAAG6B,gBAAgB,EAAEzF,KAAK;IAEzC,MAAM0F,iBAAiB,GAAG,CAAC5J,YAAY,IAAI,EAAE,EAAE6J,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE;IAC9E,MAAMC,kBAAkB,GAAG,CAAC,IAAI,CAAChK,YAAY,IAAI,EAAE,EAAE6J,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE;IAEpF;IACA,IAAI,CAACH,iBAAiB,IAAK,IAAI,CAAClF,EAAE,KAAK,CAAC,IAAIkF,iBAAiB,KAAKI,kBAAmB,EAAE;MACrF,IAAI,CAACrG,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACjD,2BAA2B,GAAG,EAAE;MACrC,IAAIyI,mBAAmB,EAAEG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QACvD,MAAM/I,MAAM,GAAQ;UAAE,GAAG4I,mBAAmB,CAAC5I;QAAM,CAAE;QACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;QACnC4I,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAAClJ,MAAM,CAAC,CAACmJ,MAAM,GAAG,CAAC,GAAGnJ,MAAM,GAAG,IAAI,CAAC;MAC/E;MACA;IACF;IAEA,IAAIqJ,iBAAiB,IAAI9B,SAAS,EAAE;MAClC,IAAI,CAACnE,wBAAwB,GAAG,IAAI;MACpC,IAAI,CAACsG,oBAAoB,CAACjK,YAAY,EAAE8H,SAAS,CAAC,CAC/CuB,IAAI,CAAC9J,GAAG,CAAE2K,GAAQ,IAAKA,GAAG,EAAEC,YAAY,IAAID,GAAG,CAAC,CAAC,CACjD9C,SAAS,CAAEgD,MAAW,IAAI;QACzB,IAAI,CAACzG,wBAAwB,GAAG,KAAK;QACrC,IAAIyG,MAAM,IAAIA,MAAM,CAACC,MAAM,EAAE;UAC3B,IAAI,CAAC3J,2BAA2B,GAAG0J,MAAM,CAACE,OAAO;UACjDnB,mBAAmB,EAAEI,SAAS,CAAC;YAAE,oBAAoB,EAAE;UAAI,CAAE,CAAC;QAChE,CAAC,MAAM;UACL,IAAI,CAAC7I,2BAA2B,GAAG,EAAE;UACrC,IAAIyI,mBAAmB,EAAEG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;YACvD,MAAM/I,MAAM,GAAQ;cAAE,GAAG4I,mBAAmB,CAAC5I;YAAM,CAAE;YACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;YACnC4I,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAAClJ,MAAM,CAAC,CAACmJ,MAAM,GAAG,CAAC,GAAGnJ,MAAM,GAAG,IAAI,CAAC;UAC/E;QACF;MACF,CAAC,CAAC;IACN;EACF;EAEA0J,oBAAoBA,CAACjK,YAAoB,EAAE8H,SAAiB;IAC1D,IAAI,CAACnE,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACjD,2BAA2B,GAAG,EAAE;IAErC,MAAM6J,MAAM,GAAG;MACbvK,YAAY,EAAEA,YAAY;MAC1B8H,SAAS,EAAEA,SAAS;MACpB0C,QAAQ,EAAE,IAAI,CAAC9F,EAAE,KAAK,CAAC,GAAG,IAAI,CAACA,EAAE,GAAG,IAAI,CAAC;KAC1C;IAED,OAAO,IAAI,CAACyB,cAAc,CAAC8D,oBAAoB,CAACM,MAAM,CAAC,CAAClB,IAAI,CAC1D/J,UAAU,CAAC,MAAK;MACd,IAAI,CAACqE,wBAAwB,GAAG,KAAK;MACrC,OAAOnE,EAAE,CAAC;QAAE6K,MAAM,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE,CAAC;IAC3C,CAAC,CAAC,CACH;EACH;EAEA5C,YAAYA,CAAA;IACV,IAAI,CAACrB,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMF,MAAM,GAAG;MAAEG,QAAQ,EAAE;IAAK,CAAE;IAClC,IAAI,CAACxE,eAAe,CAACyE,kBAAkB,CAACJ,MAAM,CAAC,CAACnD,SAAS,CAAC;MACxDqD,IAAI,EAAGG,QAAa,IAAI;QACtB,IAAI,CAACvE,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIG,QAAQ,IAAIA,QAAQ,CAACT,YAAY,EAAE;UACrC,IAAI,CAAChH,QAAQ,GAAGyH,QAAQ,CAACT,YAAY,CAACU,IAAI;QAC5C;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACzE,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAAC3H,QAAQ,GAAG,EAAE;MACpB;KACD,CAAC;EACJ;EAEAqE,kBAAkBA,CAAA;IAChB,IAAI,CAACnB,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMF,MAAM,GAAG;MAAES,YAAY,EAAE,IAAI,CAACrE,SAAS,CAACsE;IAAM,CAAE;IACtD,IAAI,CAAC9E,cAAc,CAAC+E,oBAAoB,CAACX,MAAM,CAAC,CAACnD,SAAS,CAAC;MACzDqD,IAAI,EAAGG,QAAa,IAAI;QACtB,IAAI,CAACvE,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIG,QAAQ,IAAIA,QAAQ,CAACT,YAAY,EAAE;UACrC,IAAI,CAAC9F,aAAa,GAAGuG,QAAQ,CAACT,YAAY,CAACU,IAAI;QACjD;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAACzE,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACzG,aAAa,GAAG,EAAE;MACzB;KACD,CAAC;EACJ;EAEAuD,SAASA,CAAA;IACP,IAAI,CAACvB,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACtE,cAAc,CAChBgF,SAAS,CAAC;MAAEX,QAAQ,EAAE,IAAI,CAAC9F,EAAE;MAAE0G,cAAc,EAAE,IAAI,CAACzE,SAAS,CAACsE;IAAM,CAAE,CAAC,CACvE7D,SAAS,CAAC;MACTqD,IAAI,EAAGY,cAAmB,IAAI;QAC5B,IAAI,CAAChF,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACY,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAAClB,YAAY,CAACU,IAAI;UACjD,IAAI,CAAC7K,YAAY,GAAGuL,UAAU,CAACvL,YAAY;UAC3C,IAAI,CAACK,UAAU,CAACmL,UAAU,CAAC;YACzB1D,SAAS,EAAEyD,UAAU,CAACzD,SAAS;YAC/B9H,YAAY,EAAEuL,UAAU,CAACvL,YAAY;YACrCoI,gBAAgB,EAAEmD,UAAU,CAACnD,gBAAgB;YAC7CH,cAAc,EAAEsD,UAAU,CAACtD,cAAc;YACzCC,UAAU,EAAEqD,UAAU,CAACrD,UAAU;YACjCC,WAAW,EAAEoD,UAAU,CAACpD,WAAW;YACnCH,UAAU,EAAEuD,UAAU,CAACvD,UAAU;YACjCK,QAAQ,EAAEkD,UAAU,CAAClD,QAAQ;YAC7BQ,oBAAoB,EAAC0C,UAAU,CAAC1C,oBAAoB;YACpDP,cAAc,EAAEiD,UAAU,CAACjD,cAAc;YACzCC,iBAAiB,EAAEgD,UAAU,CAAChD,iBAAiB,GAC3C,IAAI,CAACkD,kBAAkB,CAACF,UAAU,CAAChD,iBAAiB,CAAC,GACrD,EAAE;YACNC,oBAAoB,EAAE+C,UAAU,CAAC/C,oBAAoB,GACjD,IAAI,CAACiD,kBAAkB,CAACF,UAAU,CAAC/C,oBAAoB,CAAC,GACxD,EAAE;YACNC,eAAe,EAAE8C,UAAU,CAAC9C,eAAe,GACvC,IAAI,CAACgD,kBAAkB,CAACF,UAAU,CAAC9C,eAAe,CAAC,GACnD,EAAE;YACFC,eAAe,EAAE6C,UAAU,CAAC7C,eAAe,GAC3C,IAAI,CAAC+C,kBAAkB,CAACF,UAAU,CAAC7C,eAAe,CAAC,GACnD,EAAE;YACNC,kBAAkB,EAAE4C,UAAU,CAAC5C,kBAAkB,GAC7C,IAAI,CAAC8C,kBAAkB,CAACF,UAAU,CAAC5C,kBAAkB,CAAC,GACtD,EAAE;YACNC,YAAY,EAAE2C,UAAU,CAAC3C,YAAY;YACrCE,eAAe,EAAEyC,UAAU,CAACzC,eAAe;YAC3CC,aAAa,EAAEwC,UAAU,CAACxC,aAAa;YACvCC,WAAW,EAAEuC,UAAU,CAACvC,WAAW;YACnCC,sBAAsB,EAAEsC,UAAU,CAACtC,sBAAsB;YACzDC,kBAAkB,EAAEqC,UAAU,CAACrC;YAC/B;WACD,CAAC;UACF,IAAI,CAAC1G,wBAAwB,CAAC+I,UAAU,CAACnD,gBAAgB,CAAC;QAC5D,CAAC,MAAM;UACL2C,OAAO,CAACW,IAAI,CACV,oCAAoC,EACpCL,cAAc,CAAClB,YAAY,CAC5B;QACH;MACF,CAAC;MACDW,KAAK,EAAGa,GAAG,IAAI;QACb,IAAI,CAACtF,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEa,GAAG,CAAC;MACvC;KACD,CAAC;EACN;EAEAF,kBAAkBA,CAACG,UAAkB;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC;IACA,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEAE,iBAAiBA,CAAA;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACnM,UAAU,CAAC6D,KAAK;IAEtC,IAAIuI,iBAAiB,GAAQ,EAAE;IAC/BA,iBAAiB,CAAC3E,SAAS,GAAG0E,QAAQ,CAAC1E,SAAS;IAChD2E,iBAAiB,CAACzM,YAAY,GAAGwM,QAAQ,CAACxM,YAAY;IACtDyM,iBAAiB,CAACrE,gBAAgB,GAAGoE,QAAQ,CAACpE,gBAAgB;IAC9DqE,iBAAiB,CAACxE,cAAc,GAAGuE,QAAQ,CAACvE,cAAc;IAC1DwE,iBAAiB,CAACvE,UAAU,GAAGsE,QAAQ,CAACtE,UAAU;IAClDuE,iBAAiB,CAACtE,WAAW,GAAGqE,QAAQ,CAACrE,WAAW;IACpDsE,iBAAiB,CAACpE,QAAQ,GAAGmE,QAAQ,CAACnE,QAAQ;IAC9CoE,iBAAiB,CAACnE,cAAc,GAAGkE,QAAQ,CAAClE,cAAc;IAC1DmE,iBAAiB,CAAClE,iBAAiB,GAAGiE,QAAQ,CAACjE,iBAAiB,IAAI,IAAI;IACxEkE,iBAAiB,CAACjE,oBAAoB,GACpCgE,QAAQ,CAAChE,oBAAoB,IAAI,IAAI;IACvCiE,iBAAiB,CAAChE,eAAe,GAAG+D,QAAQ,CAAC/D,eAAe,IAAI,IAAI;IACpEgE,iBAAiB,CAAC/D,eAAe,GAC/B8D,QAAQ,CAAC9D,eAAe,IAAI,IAAI;IAClC+D,iBAAiB,CAAC9D,kBAAkB,GAAG6D,QAAQ,CAAC7D,kBAAkB,IAAI,IAAI;IAC1E8D,iBAAiB,CAAC7D,YAAY,GAAG4D,QAAQ,CAAC5D,YAAY;IACtD6D,iBAAiB,CAAC5D,oBAAoB,GAAG2D,QAAQ,CAAC3D,oBAAoB;IACtE4D,iBAAiB,CAACzE,UAAU,GAAGwE,QAAQ,CAACxE,UAAU;IAClDyE,iBAAiB,CAAC3D,eAAe,GAAG0D,QAAQ,CAAC1D,eAAe;IAC5D2D,iBAAiB,CAAC1D,aAAa,GAAGyD,QAAQ,CAACzD,aAAa;IACxD0D,iBAAiB,CAACzD,WAAW,GAAGwD,QAAQ,CAACxD,WAAW;IACpDyD,iBAAiB,CAACxD,sBAAsB,GAAGuD,QAAQ,CAACvD,sBAAsB;IAC1EwD,iBAAiB,CAACvD,kBAAkB,GAAGsD,QAAQ,CAACtD,kBAAkB;IAClEuD,iBAAiB,CAACzF,cAAc,GAAG,IAAI,CAACA,cAAc;IACtDyF,iBAAiB,CAACrB,cAAc,GAAG,IAAI,CAACzE,SAAS,CAACsE,MAAM;IACxDwB,iBAAiB,CAACxF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC1D,MAAMyF,MAAM,GAAI,IAAI,CAACzF,gBAAgB,CAAC0F,QAAQ,IAAK,IAAI,CAAC1F,gBAAgB,CAACuD,QAAQ,IAAK,IAAI,CAACvD,gBAAgB,CAAC2F,EAAE,IAAI,IAAI;IACtHH,iBAAiB,CAACI,cAAc,GAAGH,MAAM;IACzC,IAAI,IAAI,CAAChI,EAAE,KAAK,CAAC,EAAE;MACjB+H,iBAAiB,CAACjC,QAAQ,GAAG,IAAI,CAAC9F,EAAE;IACtC;IAEA,OAAO+H,iBAAiB;EAC1B;EAEA9G,IAAIA,CAAA;IACF,IAAImH,QAAQ,GAAG,IAAI,CAACzM,UAAU,CAACyM,QAAQ;IACvC/B,OAAO,CAACgC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC1M,UAAU,CAAC6D,KAAK,CAAC;IAClD,IAAI,IAAI,CAAC7D,UAAU,CAACiD,OAAO,EAAE;MAC3BkG,MAAM,CAACC,IAAI,CAACqD,QAAQ,CAAC,CAACE,OAAO,CAAEC,WAAW,IACxCH,QAAQ,CAACG,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAAC5G,wBAAwB,CAAC6G,SAAS,CACrC,iCAAiC,EACjC,EAAE,CACH;MACD;IACF;IACA,IAAI5B,UAAU,GAAQ,IAAI,CAACgB,iBAAiB,EAAE;IAC9CxB,OAAO,CAACgC,GAAG,CAAC,cAAc,EAAExB,UAAU,CAAC;IACvC,IAAI,IAAI,CAAC7G,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC0I,MAAM,CAAC7B,UAAU,CAAC;IACzB,CAAC,MAAM;MACL,IAAI,CAAC8B,IAAI,CAAC9B,UAAU,CAAC;IACvB;EACF;EAEA6B,MAAMA,CAAC7B,UAAe;IACpB,IAAI,CAAClF,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACtE,cAAc,CAACmH,YAAY,CAAC/B,UAAU,CAAC,CAACnE,SAAS,CAAE8C,GAAQ,IAAI;MAClE,IAAI,CAAC7D,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACP,GAAG,CAACoB,OAAO,EAAE;QAChB,IAAI,CAAChF,wBAAwB,CAACiH,WAAW,CAACrD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC5D,SAAS,CAAC8G,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACxH,KAAK,CAACyH,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACnH,wBAAwB,CAAC6G,SAAS,CAACjD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC5D,SAAS,CAAC8G,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EAEAH,IAAIA,CAAC9B,UAAe;IAClB,IAAI,CAAClF,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACtE,cAAc,CAACuH,YAAY,CAACnC,UAAU,CAAC,CAACnE,SAAS,CAAE8C,GAAG,IAAI;MAC7D,IAAI,CAAC7D,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACP,GAAG,CAACoB,OAAO,EAAE;QAChB,IAAI,CAAChF,wBAAwB,CAACiH,WAAW,CAACrD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC5D,SAAS,CAAC8G,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAACxH,KAAK,CAACyH,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACnH,wBAAwB,CAAC6G,SAAS,CAACjD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC5D,SAAS,CAAC8G,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EACAG,OAAOA,CAACC,GAAQ,EAAEjM,MAAW;IAC3B,IAAI,CAACkF,WAAW,GAAG+G,GAAG;IACtB,IAAI,CAACrH,GAAG,CAACsH,YAAY,EAAE;EACzB;EAEA/H,WAAWA,CAAA;IACT,IAAI,IAAI,CAACe,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACN,GAAG,CAACsH,YAAY,EAAE;EACzB;EAEArI,eAAeA,CAAA;IACb,IAAI,IAAI,CAACqB,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACN,GAAG,CAACsH,YAAY,EAAE;EACzB;EAEAhM,eAAeA,CAACiM,KAAS;IACtB,IAAI,CAACzN,UAAU,CAACmL,UAAU,CAAC;MAClBnD,QAAQ,EAAEyF,KAAK,CAACC;KAAiB,CAAC;IAC5C;IACA;IACA,IAAI,IAAI,CAACrJ,EAAE,KAAK,CAAC,EAAE;MACjB,MAAMsJ,iBAAiB,GAAG,IAAI,CAAC3N,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3D,MAAM2N,YAAY,GAAG,CAACD,iBAAiB,EAAE9J,KAAK,IAAI,EAAE,EAAE2F,QAAQ,EAAE,CAACC,IAAI,EAAE;MACvE,MAAMoE,WAAW,GAAGJ,KAAK,EAAEI,WAAW,IAAIJ,KAAK,EAAEK,OAAO,EAAED,WAAW,IAAI,EAAE;MAC3E,IAAIF,iBAAiB,IAAIE,WAAW,KAAKD,YAAY,KAAK,EAAE,IAAID,iBAAiB,CAACI,QAAQ,CAAC,EAAE;QAC3FJ,iBAAiB,CAACK,QAAQ,CAACH,WAAW,CAAC;QACvCF,iBAAiB,CAACM,WAAW,EAAE;MACjC;IACF;EAEF;EAEF9L,wBAAwBA,CAACsL,KAAU;IACjC,MAAMS,aAAa,GAAG,IAAI,CAAClO,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC/D;IAEA,IAAIwN,KAAK,KAAK,UAAU,EAAE;MACxB,IAAI,CAAC1J,yBAAyB,GAAG,IAAI;MACrC;MAEAmK,aAAa,EAAEC,aAAa,CAAC,CAACpP,UAAU,CAAC2I,QAAQ,CAAC,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAAC3D,yBAAyB,GAAG,KAAK;MACtC;MAEAmK,aAAa,EAAEE,eAAe,EAAE;MAChC;IACF;IAEAF,aAAa,EAAEG,sBAAsB,EAAE;IACvC;EACF;EAEAhM,uBAAuBA,CAAA;IACrB,IAAI,CAACrC,UAAU,CAACmL,UAAU,CAAC;MAAEpD,gBAAgB,EAAE;IAAI,CAAE,CAAC;IACtD;IACA,IAAI,CAAC/H,UAAU,CAACmL,UAAU,CAAC;MAAEtC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IACxD,IAAI,CAAClC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC5C,yBAAyB,GAAG,KAAK;IAEtC;IACA,MAAMmK,aAAa,GAAG,IAAI,CAAClO,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC/DiO,aAAa,EAAEE,eAAe,EAAE;IAChCF,aAAa,EAAEG,sBAAsB,EAAE;EACzC;EAEA5L,0BAA0BA,CAACgL,KAAS;IAClC/C,OAAO,CAACgC,GAAG,CAAC,UAAU,EAACe,KAAK,CAAC;IAC7B,IAAI,CAAC9G,cAAc,GAAE8G,KAAK,CAACa,eAAe,GAAC,WAAW;IACtD,IAAI,CAAClK,0BAA0B,EAAE;EACnC;EAEAzB,yBAAyBA,CAAA;IACvB,IAAI,CAAC3C,UAAU,CAACmL,UAAU,CAAC;MAAEtC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IACxD,IAAI,CAAClC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACvC,0BAA0B,EAAE;EACnC;EAEAjD,iBAAiBA,CAAA;IACd,MAAMgL,QAAQ,GAAG,IAAI,CAACnM,UAAU,CAAC6D,KAAK;IACrC,IAAI,CAACmC,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACtE,cAAc,CAChByI,gBAAgB,CAAC;MAAE5O,YAAY,EAAEwM,QAAQ,CAACxM,YAAY;MAAE6O,cAAc,EAAErC,QAAQ,CAACtD;IAAkB,CAAE,CAAC,CACtG9B,SAAS,CAAC;MACTqD,IAAI,EAAGY,cAAmB,IAAI;QAC5B,IAAI,CAAChF,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACY,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAAClB,YAAY,CAAC1D,MAAM;UACnD,IAAI,CAACQ,gBAAgB,GAAGsE,UAAU;UAClC,IAAI,CAAClL,UAAU,CAACmL,UAAU,CAAC;YACzBtD,UAAU,EAAEqD,UAAU,CAACrD,UAAU;YACjCC,WAAW,EAAEoD,UAAU,CAACpD,WAAW;YACnCE,QAAQ,EAAEkD,UAAU,CAACuD,OAAO;YAC5BvG,iBAAiB,EAAEgD,UAAU,CAACwD,SAAS,GACnC,IAAI,CAACtD,kBAAkB,CAACF,UAAU,CAACwD,SAAS,CAAC,GAC7C,EAAE;YACNvG,oBAAoB,EAAE+C,UAAU,CAACyD,UAAU,GACvC,IAAI,CAACvD,kBAAkB,CAACF,UAAU,CAACyD,UAAU,CAAC,GAC9C,EAAE;YACNvG,eAAe,EAAE8C,UAAU,CAAC0D,SAAS,GACjC,IAAI,CAACxD,kBAAkB,CAACF,UAAU,CAAC0D,SAAS,CAAC,GAC7C,EAAE;YACNvG,eAAe,EAAE6C,UAAU,CAAC2D,SAAS,GACjC,IAAI,CAACzD,kBAAkB,CAACF,UAAU,CAAC2D,SAAS,CAAC,GAC7C,EAAE;YACNvG,kBAAkB,EAAE4C,UAAU,CAAC4D,YAAY,GACvC,IAAI,CAAC1D,kBAAkB,CAACF,UAAU,CAAC4D,YAAY,CAAC,GAChD,EAAE;YACNvG,YAAY,EAAE2C,UAAU,CAAC3C;WAE1B,CAAC;QACJ,CAAC,MAAM;UACLmC,OAAO,CAACW,IAAI,CACV,oCAAoC,EACpCL,cAAc,CAAClB,YAAY,CAACG,OAAO,CACpC;QACH;MACF,CAAC;MACDQ,KAAK,EAAGa,GAAG,IAAI;QACb,IAAI,CAACtF,eAAe,CAACc,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEa,GAAG,CAAC;MACvC;KACD,CAAC;EACR;EAEAlH,0BAA0BA,CAAA;IACxB,MAAM2K,UAAU,GAAG,IAAI,CAAC/O,UAAU,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAE4D,KAAK;IACjE,MAAMlE,YAAY,GAAG,IAAI,CAACK,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE4D,KAAK;IAC/D,MAAMgF,kBAAkB,GAAG,IAAI,CAAC7I,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAE4D,KAAK;IAE3E,MAAMmL,UAAU,GAAGD,UAAU,KAAK,UAAU;IAC5C,MAAME,eAAe,GAAG,CAAC,CAACtP,YAAY;IACtC,MAAMuP,qBAAqB,GAAG,CAAC,CAACrG,kBAAkB;IAElD6B,OAAO,CAACgC,GAAG,CAAC,aAAa,EAAEsC,UAAU,CAAC;IACtCtE,OAAO,CAACgC,GAAG,CAAC,kBAAkB,EAAEuC,eAAe,CAAC;IAChDvE,OAAO,CAACgC,GAAG,CAAC,wBAAwB,EAAEwC,qBAAqB,CAAC;IAC5D;IACA,OAAO,EAAEF,UAAU,IAAIC,eAAe,IAAIC,qBAAqB,CAAC;EAClE;;qCA/iBaxJ,oBAAoB,EAAAtG,EAAA,CAAA+P,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAjQ,EAAA,CAAA+P,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAnQ,EAAA,CAAA+P,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAArQ,EAAA,CAAA+P,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAAvQ,EAAA,CAAA+P,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAAzQ,EAAA,CAAA+P,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA3Q,EAAA,CAAA+P,iBAAA,CAAAa,EAAA,CAAAC,wBAAA,GAAA7Q,EAAA,CAAA+P,iBAAA,CAAA/P,EAAA,CAAA8Q,iBAAA;EAAA;;UAApBxK,oBAAoB;IAAAyK,SAAA;IAAAC,MAAA;MAAA/L,EAAA;MAAA8B,0BAAA;MAAAC,MAAA;IAAA;IAAAiK,OAAA;MAAAhK,SAAA;IAAA;IAAAiK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrB7BvR,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAAgC,uBAAA,GAAc;QAEZhC,EADA,CAAAQ,UAAA,IAAAiR,mCAAA,iBAAsB,IAAAC,mCAAA,iBACA;;QAE1B1R,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WACgD;QAA1BD,EAAA,CAAAyB,UAAA,mBAAAkQ,iDAAA;UAAA,OAASH,GAAA,CAAAjL,KAAA,CAAAqL,OAAA,EAAe;QAAA,EAAC;QAE1E5R,EAF2E,CAAAG,YAAA,EAAI,EACvE,EACF;QAUMH,EARZ,CAAAC,cAAA,aAAwB,aAGL,cACQ,cACD,cACqF,cAChF,aAEkE;QAAnCD,EAAA,CAAAyB,UAAA,mBAAAoQ,kDAAA3P,MAAA;UAAA,OAASsP,GAAA,CAAAtD,OAAA,CAAQ,OAAO,EAAAhM,MAAA,CAAS;QAAA,EAAC;QAClFlC,EAAA,CAAAE,MAAA,oBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAEsE;QAArCD,EAAA,CAAAyB,UAAA,mBAAAqQ,kDAAA5P,MAAA;UAAA,OAASsP,GAAA,CAAAtD,OAAA,CAAQ,SAAS,EAAAhM,MAAA,CAAS;QAAA,EAAC;QACtFlC,EAAA,CAAAE,MAAA,wBACF;QAWVF,EAXU,CAAAG,YAAA,EAAI,EACD,EAOF,EACD,EACF,EACF;QAENH,EAAA,CAAAC,cAAA,gBAA6D;QA+J3DD,EA9JA,CAAAQ,UAAA,KAAAuR,6CAAA,4BAA6C,KAAAC,6CAAA,4BA8JE;QA+HnDhS,EADE,CAAAG,YAAA,EAAO,EACH;QAGJH,EADF,CAAAC,cAAA,eAAkD,WAC3C;QACHD,EAAA,CAAAQ,UAAA,KAAAyR,uCAAA,qBAC8B;QAGhCjS,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,WAAK,kBAC4F;QAA1BD,EAAA,CAAAyB,UAAA,mBAAAyQ,uDAAA;UAAA,OAASV,GAAA,CAAAjL,KAAA,CAAAqL,OAAA,EAAe;QAAA,EAAC;QAC5F5R,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAAAH,EAAA,CAAAE,MAAA,eACjB;QAKAF,EAJA,CAAAQ,UAAA,KAAA2R,uCAAA,qBAC+E,KAAAC,uCAAA,qBAG6B;QAKlHpS,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;QA3VQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAA8Q,GAAA,CAAAvM,EAAA,OAAc;QACdjF,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAA8Q,GAAA,CAAAvM,EAAA,OAAc;QAiBZjF,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAqS,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAApK,WAAA,cAA+C;QAM/CpH,EAAA,CAAAI,SAAA,GAAiD;QAAjDJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAqS,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAApK,WAAA,gBAAiD;QAezBpH,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAU,UAAA,cAAA8Q,GAAA,CAAA5Q,UAAA,CAAwB;QAC3CZ,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAA8Q,GAAA,CAAApK,WAAA,YAA4B;QA8J5BpH,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAA8Q,GAAA,CAAApK,WAAA,cAA8B;QAmIpCpH,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAA8Q,GAAA,CAAApK,WAAA,cAA8B;QASepH,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAA8Q,GAAA,CAAApK,WAAA,cAA8B;QAI3EpH,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAA8Q,GAAA,CAAApK,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
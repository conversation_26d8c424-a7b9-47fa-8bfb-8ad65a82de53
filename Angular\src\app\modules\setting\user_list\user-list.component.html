<!-- Full Screen Loading Overlay -->
<div *ngIf="loading || isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 text-primary fs-5">Loading...</div>
  </div>
</div>

<div class="grid-container">
  <kendo-grid
    #normalGrid
    [data]="serverSideRowData"
    [pageSize]="page.size"
    [sort]="sort"
    [pageable]="{
      pageSizes: [10, 15, 20, 50, 100],
      previousNext: true,
      info: true,
      type: 'numeric',
      buttonCount: 5
    }"
    [sortable]="{ allowUnsort: true, mode: 'single' }"
    [groupable]="false"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    (columnReorder)="onColumnReorder($event)"
    (selectionChange)="onSelectionChange($event)"
    [reorderable]="true"
    style="width: auto; overflow-x: auto"
    [resizable]="false"
    [height]="720"
    [skip]="page.pageNumber * page.size"
    [filter]="filter"
    [columnMenu]="{ filter: true }"
    (filterChange)="filterChange($event)"
    (pageChange)="pageChange($event)"
    (sortChange)="onSortChange($event)"
    (columnVisibilityChange)="updateColumnVisibility($event)"
  >
    <ng-template kendoGridToolbarTemplate>
      <!-- Search Section -->
      <div class="d-flex align-items-center me-3 search-section">
        <kendo-textbox
          [style.width.px]="500"
          placeholder="Search..."
          [(ngModel)]="searchData"
          [clearButton]="true"
          (keydown)="onSearchKeyDown($event)"
          (ngModelChange)="onSearchChange()"
          (clear)="clearSearch()"
        ></kendo-textbox>
      </div>

      <kendo-grid-spacer></kendo-grid-spacer>

      <!-- Total Count - Repositioned to the right -->
      <div class="d-flex align-items-center me-3">
        <span class="text-muted">Total: </span>
        <span class="fw-bold ms-1">{{ page.totalElements || 0 }}</span>
      </div>

      <!-- Action Buttons -->
      <button type="button" class="btn btn-icon btn-sm me-2" (click)="add()" title="Add User">
        <span
          [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'"
          class="svg-icon svg-icon-3 text-primary"
        ></span>
      </button>

      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="toggleExpand()"
        title="Toggle Grid Expansion"
      >
        <i
          class="fas text-secondary"
          [class.fa-expand]="!isExpanded"
          [class.fa-compress]="isExpanded"
        ></i>
      </button>

      <!-- <kendo-dropdownbutton
        text="Export Excel"
        iconClass="fas fa-file-excel"
        [data]="exportOptions"
        class="custom-dropdown"
        (itemClick)="onExportClick($event)"
        title="Export"
      >
      </kendo-dropdownbutton> -->

      <!-- Save Column Settings Button -->
      <!-- <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="saveHead()"
        title="Save Column Settings"
      >
        <i class="fas fa-save text-success"></i>
      </button> -->

      <!-- Reset Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="resetTable()"
        title="Reset to Default"
      >
        <i class="fas fa-undo text-warning"></i>
      </button>

      <!-- Refresh Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="refreshGrid()"
        title="Refresh Grid Data"
      >
        <i class="fas fa-sync-alt text-info"></i>
      </button>
    </ng-template>

    <!-- Advanced Filters Panel -->
    <ng-template kendoGridToolbarTemplate>
      <div
        *ngIf="showAdvancedFilters"
        class="advanced-filters-panel p-3 bg-light border-bottom"
      >
        <div class="row">
          <div class="col-md-3">
            <label class="form-label">Status</label>
            <kendo-dropdownlist
              [data]="advancedFilterOptions.status"
              [(ngModel)]="appliedFilters.status"
              textField="text"
              valueField="value"
              placeholder="Select Status"
            >
            </kendo-dropdownlist>
          </div>
          <div class="col-md-3">
            <label class="form-label">Role</label>
            <kendo-dropdownlist
              [data]="advancedFilterOptions.roles"
              [(ngModel)]="appliedFilters.role"
              textField="text"
              valueField="value"
              placeholder="Select Role"
            >
            </kendo-dropdownlist>
          </div>
          <div class="col-md-3 d-flex align-items-end">
            <button
              kendoButton
              (click)="applyAdvancedFilters()"
              class="btn-primary me-2"
            >
              <i class="fas fa-check"></i> Apply Filters
            </button>
            <button
              kendoButton
              (click)="clearAllFilters()"
              class="btn-secondary"
            >
              <i class="fas fa-times"></i> Clear
            </button>
          </div>
        </div>
      </div>
    </ng-template>

    <ng-container *ngFor="let column of gridColumns">
      <!-- Action Column -->
      <kendo-grid-column
        *ngIf="column === 'action'"
        title="Actions"
        [width]="125"
        [sticky]="true"
        [reorderable]="!fixedColumns.includes('action')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [includeInChooser]="false"
        [columnMenu]="false"
        [style]="{ 'background-color': '#efefef !important' }"
        [hidden]="getHiddenField('action')"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <a
            title="Edit"
            class="btn btn-icon btn-sm"
            (click)="edit(dataItem.userId)"
          >
            <span
              [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'"
              class="svg-icon svg-icon-3 svg-icon-primary"
            >
            </span>
          </a>
          <!-- Delete button hidden -->
          <!-- <a
            title="Delete"
            class="btn btn-icon btn-sm"
            (click)="deleteUser(dataItem)"
          >
            <span
              [inlineSVG]="'./assets/media/icons/duotune/general/gen027.svg'"
              class="svg-icon svg-icon-3 svg-icon-danger"
            >
            </span>
          </a> -->
          <a
            *ngIf="dataItem.IsLocked"
            title="Unlock"
            class="btn btn-icon btn-sm"
            (click)="unlockUser(dataItem)"
          >
            <span
              [inlineSVG]="'./assets/media/icons/duotune/general/gen037.svg'"
              class="svg-icon svg-icon-3 svg-icon-warning"
            >
            </span>
          </a>
        </ng-template>
      </kendo-grid-column>

      <!-- First Name Column -->
      <kendo-grid-column
        *ngIf="column === 'userFullName'"
        field="userFullName"
        title="Name"
        [width]="150"
        [sticky]="true"
        [reorderable]="!fixedColumns.includes('userFullName')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [includeInChooser]="false"
        [hidden]="getHiddenField('userFullName')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span class="fw-bolder cursor-pointer">
              {{ dataItem.userFullName }}
            </span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Last Name Column -->
      <!-- <kendo-grid-column *ngIf="column === 'lastName'"
        field="LastName"
        title="Last Name"
        [width]="150"
        [sticky]="true"
        [reorderable]="!fixedColumns.includes('LastName')"
        [headerStyle]="{ 'background-color': '#edf0f3','font-weight':'600' }"
        [includeInChooser]="false"
        [hidden]="getHiddenField('LastName')"
        [filterable]="true">
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span class="fw-bolder cursor-pointer">
              {{ dataItem.LastName }}
            </span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu [column]="column" [filter]="filter" [extra]="true">
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column> -->

      <!-- Email Column -->
      <kendo-grid-column
        *ngIf="column === 'email'"
        field="email"
        title="Email"
        [width]="250"
        [sticky]="false"
        [reorderable]="!fixedColumns.includes('email')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('email')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span [innerHTML]="dataItem.email || '-'"></span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Title Column -->
      <kendo-grid-column
        *ngIf="column === 'title'"
        field="title"
        title="Title"
        [width]="120"
        [sticky]="false"
        [reorderable]="!fixedColumns.includes('title')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('title')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span [innerHTML]="dataItem.title || '-'"></span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="true"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-startswith-operator></kendo-filter-startswith-operator>
            <kendo-filter-endswith-operator></kendo-filter-endswith-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Phone Column -->
      <kendo-grid-column
        *ngIf="column === 'phoneNo'"
        field="phoneNo"
        title="Phone"
        [width]="120"
        [sticky]="false"
        [reorderable]="!fixedColumns.includes('phoneNo')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('phoneNo')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span [innerHTML]="dataItem.phoneNo || '-'"></span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Role Column -->
      <kendo-grid-column
        *ngIf="column === 'roleName'"
        field="roleName"
        title="Role"
        [width]="120"
        [sticky]="false"
        [reorderable]="!fixedColumns.includes('roleName')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('roleName')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <div>
            <span [innerHTML]="dataItem.roleName || '-'"></span>
          </div>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-grid-string-filter-menu
            [column]="column"
            [filter]="filter"
            [extra]="false"
            operator="contains"
          >
            <kendo-filter-contains-operator></kendo-filter-contains-operator>
          </kendo-grid-string-filter-menu>
        </ng-template>
      </kendo-grid-column>

      <!-- Status Column -->
      <kendo-grid-column
        *ngIf="column === 'userStatus'"
        field="userStatus"
        title="Status"
        [width]="100"
        [sticky]="false"
        [reorderable]="!fixedColumns.includes('userStatus')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        [hidden]="getHiddenField('userStatus')"
        [filterable]="true"
      >
        <ng-template kendoGridCellTemplate let-dataItem>
          <span
            *ngIf="dataItem.userStatus === 'Active'"
            ngbTooltip="Active"
            [inlineSVG]="'./assets/media/icons/duotune/general/gen037.svg'"
            class="svg-icon svg-icon-3 svg-icon-success"
            style="margin-left: 1.5rem"
          >
          </span>
          <span
            *ngIf="dataItem.userStatus === 'Inactive'"
            ngbTooltip="Inactive"
            [inlineSVG]="'./assets/media/icons/duotune/general/gen040.svg'"
            class="svg-icon svg-icon-3 svg-icon-danger text-danger"
            style="margin-left: 1.5rem"
          >
          </span>
        </ng-template>
        <ng-template kendoGridFilterMenuTemplate let-filter let-column="column">
          <kendo-dropdownlist
            [data]="filterOptions"
            [value]="getFilterValue(filter, column)"
            (valueChange)="onStatusFilterChange($event, filter, column)"
            textField="text"
            valueField="value"
          >
          </kendo-dropdownlist>
        </ng-template>
      </kendo-grid-column>

      <!-- Updated Date Column -->
      <kendo-grid-column
        *ngIf="column === 'lastUpdatedDate'"
        field="lastUpdatedDate"
        title="Updated Date"
        [width]="160"
        [sticky]="false"
        [reorderable]="!fixedColumns.includes('lastUpdatedDate')"
        [headerStyle]="{ 'background-color': '#edf0f3', 'font-weight': '600' }"
        filter="date"
        format="MM/dd/yyyy"
        [maxResizableWidth]="240"
        [hidden]="getHiddenField('lastUpdatedDate')"
        [filterable]="true"
      >
        
        <ng-template kendoGridCellTemplate let-dataItem>
          <span class="text-gray-600 fs-1r">{{
            dataItem.lastUpdatedDate | date : "MM/dd/yyyy hh:mm a"
          }}</span>
          <br /><span class="text-gray-600 fs-1r">{{
            dataItem.lastUpdatedByFullName
          }}</span>
        </ng-template>
        <ng-template
          kendoGridFilterMenuTemplate
          let-filter
          let-column="column"
          let-filterService="filterService"
        >
          <kendo-grid-date-filter-menu
            [column]="column"
            [filter]="filter"
            operator="eq"
            [filterService]="filterService"
          >
            <kendo-filter-eq-operator></kendo-filter-eq-operator>
            <kendo-filter-neq-operator></kendo-filter-neq-operator>
            <kendo-filter-before-operator></kendo-filter-before-operator>
            <kendo-filter-before-eq-operator></kendo-filter-before-eq-operator>
            <kendo-filter-after-operator></kendo-filter-after-operator>
            <kendo-filter-after-eq-operator></kendo-filter-after-eq-operator>
          </kendo-grid-date-filter-menu>
        </ng-template>
      </kendo-grid-column>
    </ng-container>

    <ng-template kendoGridNoRecordsTemplate>
      <div class="custom-no-records" *ngIf="!loading && !isLoading">
        <div class="text-center">
          <i class="fas fa-users text-muted mb-2" style="font-size: 2rem"></i>
          <p class="text-muted">No users found</p>
          <button kendoButton (click)="loadTable()" class="btn-primary">
            <i class="fas fa-refresh me-2"></i>Refresh
          </button>
        </div>
      </div>
    </ng-template>
  </kendo-grid>
</div>

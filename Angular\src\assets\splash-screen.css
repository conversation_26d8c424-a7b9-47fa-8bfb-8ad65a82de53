body {
  margin: 0;
  padding: 0;
}

.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  font-family: Helvetica, 'sans-serif';
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  line-height: 1;
  font-size: 14px;
  font-weight: 400;
  z-index: 0; /* keep behind global loader */
}

.splash-screen span {
  color: #ffffff;
  transition: none !important;
  text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
}

.splash-screen img {
  margin-left: calc(100vw - 100%);
  margin-bottom: 30px;
  height: 30px !important;
}

[data-bs-theme="dark"] .splash-screen {
  background-color: #151521;
  color: #92929F;
}

[data-bs-theme="dark"] .splash-screen span {
  color: #92929F;
}


#root {
  opacity: 1;
  transition: opacity 1s ease-in-out;
}

import { Routes } from '@angular/router';
import { AuthGuard } from '../modules/auth/services/auth.guard';

const Routing: Routes = [
  {
    path: 'dashboard',
    canActivate: [AuthGuard],
    loadChildren: () => import('./dashboard/dashboard.module').then((m) => m.DashboardModule),
      data: { layout: 'light-sidebar' },
  },
  {
    path: 'setting',
    canActivate: [AuthGuard],
    loadChildren: () =>
      import('../modules/setting/setting.module').then((m) => m.SettingModule),
    data: { layout: 'light-sidebar' },
  },
  {
    path: 'projects',
    canActivate: [AuthGuard],
    loadChildren: () =>
      import('../modules/projects/projects.module').then((m) => m.ProjectsModule),
    data: { layout: 'light-sidebar' },
  },
    {
    path: 'permits',
    canActivate: [AuthGuard],
    loadChildren: () =>
      import('../modules/permits/permits.module').then((m) => m.PermitsModule),
    data: { layout: 'light-sidebar' },
  },
  {
    path: 'user',
    canActivate: [AuthGuard],
    loadChildren: () =>
      import('../modules/loggedin-user-details/loggedin-user-details.module').then((m) => m.LoggedinUserDetailsModule),
    data: { layout: 'light-sidebar' },
  },
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'error/404',
  },
];

export { Routing };

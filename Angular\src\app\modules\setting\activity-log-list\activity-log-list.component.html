<!-- Full Screen Loading Overlay -->
<div *ngIf="loading || isLoading" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 fs-5">Loading...</div>
  </div>
</div>

<div class="grid-container">
  <kendo-grid #normalGrid [data]="gridData" [pageSize]="page.size" [sort]="sort"
    [pageable]="{
      pageSizes: [10, 15, 20, 50, 100],
      previousNext: true,
      info: true,
      type: 'numeric',
      buttonCount: 5
    }"
    [total]="page.totalElements"
    [sortable]="{ allowUnsort: true, mode: 'single' }"
    [groupable]="false"
    [selectable]="{ checkboxOnly: true, mode: 'multiple' }"
    (columnReorder)="onColumnReorder($event)"
    (selectionChange)="onSelectionChange($event)"
    [reorderable]="true"
    style="width: auto;overflow-x:auto"
    [resizable]="false"
    [height]="720"
    [skip]="skip"
    [filter]="filter"
    [columnMenu]="{ filter: true }"
    (filterChange)="filterChange($event)"
    (pageChange)="pageChange($event)"
    (sortChange)="onSortChange($event)"
    (columnVisibilityChange)="updateColumnVisibility($event)">

    <ng-template kendoGridToolbarTemplate>
      <!-- Search Section -->
      <div class="d-flex align-items-center me-3 search-section">
        <kendo-textbox [style.width.px]="500" placeholder="Search..." [(ngModel)]="searchData"
          [clearButton]="true" (keydown)="onSearchKeyDown($event)"
          (ngModelChange)="onSearchChange()"></kendo-textbox>
      </div>

      <kendo-grid-spacer></kendo-grid-spacer>

      <!-- Total Count - Repositioned to the right -->
      <div class="d-flex align-items-center me-3">
        <span class="text-muted me-2">Total:</span>
        <span class="fw-bold">{{ page.totalElements }}</span>
      </div>

      <!-- Reset Button -->
      <button
        type="button"
        class="btn btn-icon btn-sm me-2"
        (click)="resetTable()"
        title="Reset to Default"
      >
        <i class="fas fa-undo text-warning"></i>
      </button>

      <!-- Refresh Button -->
      <button
        type="button"
        class="btn btn-info btn-sm me-2"
        (click)="refreshGrid()"
        title="Refresh Grid Data"
      >
        <i class="fas fa-sync-alt"></i>
      </button>

    </ng-template>

    <!-- Advanced Filters Panel -->
    <div *ngIf="showAdvancedFilters" class="advanced-filters-panel p-3 bg-light border-bottom">
      <div class="row">
        <div class="col-md-3">
          <label class="form-label">Status</label>
          <kendo-dropdownlist
            [data]="advancedFilterOptions.status"
            [textField]="'text'"
            [valueField]="'value'"
            [(ngModel)]="appliedFilters.status"
            class="w-100">
          </kendo-dropdownlist>
        </div>
        <div class="col-md-3">
          <label class="form-label">User Type</label>
          <kendo-dropdownlist
            [data]="advancedFilterOptions.userTypes"
            [textField]="'text'"
            [valueField]="'value'"
            [(ngModel)]="appliedFilters.userType"
            class="w-100">
          </kendo-dropdownlist>
        </div>
        <div class="col-md-6 d-flex align-items-end">
          <button kendoButton [themeColor]="'primary'" (click)="applyAdvancedFilters()" class="me-2">
            Apply Filters
          </button>
          <button kendoButton [themeColor]="'secondary'" (click)="clearAdvancedFilters()">
            Clear Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Log Statistics -->
    <div class="log-statistics p-3 bg-light border-bottom">
      <div class="row text-center">
        <div class="col-md-4">
          <div class="stat-item">
            <h4 class="text-success mb-0">{{ logStatistics.successLogs }}</h4>
            <small class="text-muted">Success Logs</small>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-item">
            <h4 class="text-danger mb-0">{{ logStatistics.failedLogs }}</h4>
            <small class="text-muted">Failed Logs</small>
          </div>
        </div>
        <div class="col-md-4">
          <div class="stat-item">
            <h4 class="text-primary mb-0">{{ logStatistics.totalLogs }}</h4>
            <small class="text-muted">Total Logs</small>
          </div>
        </div>
      </div>
    </div>

    <!-- Grid Columns -->
    <kendo-grid-column field="action" title="Actions" [width]="100" [sortable]="false" [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        <div class="btn-group btn-group-sm">
          <button kendoButton [themeColor]="'primary'" (click)="viewLog(dataItem)" class="btn-sm">
            <i class="fas fa-eye"></i>
          </button>
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="activityEvent" title="Event" [width]="180" [filterable]="true">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        <div class="d-flex align-items-center">
          <i [class]="getEventIcon(dataItem.activityEvent)" class="me-2 text-primary"></i>
          <span class="fw-bold">{{ dataItem.activityEvent }}</span>
        </div>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="eventDescription" title="Description" [width]="250" [filterable]="true">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        {{ truncateText(dataItem.eventDescription, 40) }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="tableName" title="Table" [width]="120" [filterable]="true">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        <span class="badge bg-info">{{ dataItem.tableName }}</span>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="activityStatus" title="Status" [width]="100" [filterable]="true">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        <span class="badge" [ngClass]="getStatusClass(dataItem.activityStatus)">
          {{ dataItem.activityStatus }}
        </span>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="activityUserType" title="User Type" [width]="120" [filterable]="true">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        <span class="badge bg-secondary">{{ dataItem.activityUserType }}</span>
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="createdDate" title="Created Date" [width]="180" [filterable]="true" format="MM/dd/yyyy">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        {{ formatDate(dataItem.createdDate) }}
      </ng-template>
    </kendo-grid-column>

    <kendo-grid-column field="createdByUserFullName" title="User" [width]="150" [filterable]="false">
      <ng-template kendoGridCellTemplate let-dataItem="dataItem">
        {{ dataItem.createdByUserFullName || 'System' }}
      </ng-template>
    </kendo-grid-column>

    <!-- No Data Template -->
    <ng-template kendoGridNoRecordsTemplate>
      <div class="custom-no-records" *ngIf="!loading && !isLoading">
        <div class="text-center">
          <i class="fas fa-clipboard-list text-muted mb-2" style="font-size: 2rem;"></i>
          <p class="text-muted">No activity logs found</p>
          <button kendoButton (click)="loadTable()" class="btn-primary">
            <i class="fas fa-refresh me-2"></i>Refresh
          </button>
        </div>
      </div>
    </ng-template>
  </kendo-grid>
</div>

<!-- Empty State -->
<div *ngIf="!loading && !isLoading && !IsListHasValue" class="text-center p-5">
  <i class="fas fa-clipboard-list fa-4x text-muted mb-4"></i>
  <h3 class="text-muted mb-3">No Activity Logs Available</h3>
  <p class="text-muted mb-4">Activity logs will appear here as users perform actions in the system.</p>
</div>

import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  Output,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { ProjectsService } from '../../../modules/services/projects.service'; // adjust path
import { PermitsService } from '../../../modules/services/permits.service'; // adjust path
import { AppService } from '../../services/app.service';
import { HttpUtilsService } from '../../services/http-utils.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { UserService } from '../../services/user.service';

@Component({
  selector: 'app-project-popup',
  templateUrl: './project-popup.component.html',
})
export class ProjectPopupComponent {
  @Input() id: number = 0; // 0 = Add, otherwise Edit
  @Input() project: any; // incoming project data (for edit)
  @Output() passEntry: EventEmitter<any> = new EventEmitter();
  projectForm: FormGroup | null = null;
  loginUser: any = {};
  managers: any = [];
  externalPMs: any = [];
  isLoading: boolean = false;
  selectedTab: string = 'basic'; //store navigation tab
  projectName: any;
  originalStatus: string = 'Current'; // Store original status for validation
  statusOptions: any[] = [
    { label: 'Current', value: 'Current' },
    { label: 'Completed', value: 'Completed' },
    { label: 'Cancelled & Archived', value: 'Cancelled & Archived' },
    { label: 'Closed & Archived', value: 'Closed & Archived' }
  ];
  constructor(
    public modal: NgbActiveModal,
    private cdr: ChangeDetectorRef,
        
    private fb: FormBuilder,
    private appService: AppService,
    private httpUtilService: HttpUtilsService,
    private customLayoutUtilsService: CustomLayoutUtilsService,
    private projectsService: ProjectsService, // adjust path
    private permitsService: PermitsService, // adjust path
    private usersService: UserService
  ) {
    // Subscribe to loading state
    this.httpUtilService.loadingSubject.subscribe((loading) => {
      this.isLoading = loading;
    });
  }

  ngOnInit() {
    this.loginUser = this.appService.getLoggedInUser();
    this.loadForm();

    // Set loading state immediately for edit mode
    if (this.id !== 0) {
      this.isLoading = true;
      this.httpUtilService.loadingSubject.next(true);
      // Wait for both manager lists to load before patching form
      this.loadManagersAndPatchForm();
    } else {
      // For add mode, turn off the loader that was turned on in the parent component
      this.httpUtilService.loadingSubject.next(false);
      this.isLoading = false;
      // Load managers without loading state
      this.loadInternalManager();
      this.loadExternalManagers();
    }
  }



  loadForm() {
    this.projectForm = this.fb.group({
      projectName: ['', Validators.required],
      status: ['Current', Validators.required], // Default to 'Current' status
      internalProjectNo: ['', Validators.required],
      startDate: [''],
      endDate: [''], // Make end date optional
      location: ['', Validators.required],
      projectDescription: [''],
      manager: [null, Validators.required],
      externalPM: [[]], // Remove required validator for external PM as it's optional
    });

    // Trigger change detection to update the view
    this.cdr.detectChanges();
  }
  // Load MedicalCenters
  loadInternalManager(): void {
    this.usersService
      .getUserlistForDropdown({ roleName: 'Internal PM' })
      .subscribe((internalPMSResponse: any) => {
        const users = internalPMSResponse?.responseData?.users;
        this.managers = Array.isArray(users) ? users : [];
      });
  }
  // Load roles for advanced filters
  loadExternalManagers(): void {
    this.usersService
      .getUserlistForDropdown({ roleName: 'External PM' })
      .subscribe((externalPMSResponse: any) => {
        const users = externalPMSResponse?.responseData?.users;
        this.externalPMs = Array.isArray(users) ? users : [];
      });
  }

  // Load both manager lists and then patch the form
  loadManagersAndPatchForm(): void {
    // Use Promise.all to wait for both API calls to complete
    const internalPMsPromise = this.usersService
      .getUserlistForDropdown({ roleName: 'Internal PM' })
      .toPromise();

    const externalPMsPromise = this.usersService
      .getUserlistForDropdown({ roleName: 'External PM' })
      .toPromise();

    Promise.all([internalPMsPromise, externalPMsPromise])
      .then(([internalResponse, externalResponse]) => {
        // Set the manager lists
        this.managers = Array.isArray(internalResponse?.responseData?.users)
          ? internalResponse.responseData.users : [];
        this.externalPMs = Array.isArray(externalResponse?.responseData?.users)
          ? externalResponse.responseData.users : [];

        // Now patch the form with the loaded data
        this.patchForm();
      })
      .catch(error => {
        console.error('Error loading manager lists:', error);
        // Still try to patch form even if manager lists fail
        this.patchForm();
      });
  }

  patchForm() {
    // Loading state is already set in ngOnInit for edit mode
    this.projectsService
      .getProject({ projectId: this.id, loggedInUserId: this.loginUser.userId })
      .subscribe({
        next: (projectResponse: any) => {
          this.httpUtilService.loadingSubject.next(false);
          if (!projectResponse.isFault) {
            let projectData = projectResponse.responseData.Project;
            // this.projectId = projectData.projectId
            console.log('Project data received:', projectData);
            console.log('Available managers:', this.managers);
            console.log('Available external PMs:', this.externalPMs);
            this.projectName = projectData.projectName;

            // Format dates for HTML date inputs (YYYY-MM-DD format)
            const formatDateForInput = (dateValue: any) => {
              if (!dateValue) return '';
              const date = new Date(dateValue);
              return date.toISOString().split('T')[0];
            };

            // The manager is already stored as userId in the database
            const getManagerId = (managerId: any) => {
              if (!managerId) return null;
              return parseInt(managerId);
            };

            // Parse external PM names string to array of userIds
            const parseExternalPMs = (externalPMNamesString: string) => {
              if (!externalPMNamesString) return [];
              // Split comma-separated names and find matching userIds
              const pmNames = externalPMNamesString.split(',').map(name => name.trim()).filter(name => name !== '');
              return pmNames.map(name => {
                const pm = this.externalPMs.find((epm: any) => epm.userFullName === name);
                return pm ? pm.userId : null;
              }).filter(id => id !== null);
            };

            const formData = {
              projectName: projectData.projectName,
              status: projectData.status || 'Current', // Default to 'Current' if no status
              internalProjectNo: projectData.internalProjectNumber,
              startDate: formatDateForInput(projectData.projectStartDate),
              endDate: formatDateForInput(projectData.projectEndDate),
              location: projectData.projectLocation,
              projectDescription: projectData.projectDescription,
              manager: getManagerId(projectData.internalProjectManager),
              externalPM: parseExternalPMs(projectData.externalPMNames),
            };

            // Store original status for validation
            this.originalStatus = projectData.status || 'Current';

            console.log('Form data to patch:', formData);
            if (this.projectForm) {
              this.projectForm.patchValue(formData);
            }
          } else {
            console.warn(
              'User response has isFault = true',
              projectResponse.responseData
            );
          }
        },
        error: (err) => {
          this.httpUtilService.loadingSubject.next(false);
          console.error('API call failed', err);
        },
      });
  }
  save() {
    if (!this.projectForm) {
      this.customLayoutUtilsService.showError(
        'Form is not initialized. Please try again.',
        ''
      );
      return;
    }

    let controls = this.projectForm!.controls;
    if (this.projectForm!.invalid) {
      Object.keys(controls).forEach((controlName) =>
        controls[controlName].markAsTouched()
      );
      this.customLayoutUtilsService.showError(
        'Please fill all required fields',
        ''
      );
      return;
    }

    // Check if status is being changed from Current to another status
    const currentStatus = this.projectForm!.get('status')?.value;
    if (this.id !== 0 && this.originalStatus === 'Current' && currentStatus !== 'Current') {
      // Validate that all permits are approved before allowing status change
      this.validatePermitsBeforeStatusChange(currentStatus);
      return;
    }

    let projectData: any = this.prepareProjectData();
    console.log('Line: 203', projectData);
    if (this.id === 0) {
      this.create(projectData);
    } else {
      this.edit(projectData);
    }
  }

  prepareProjectData() {
    if (!this.projectForm) {
      throw new Error('Form is not initialized');
    }

    const formData = this.projectForm!.value;
    let projectRequestData: any = {};
    projectRequestData.projectName = formData.projectName;
    projectRequestData.status = formData.status;
    projectRequestData.internalProjectNumber = formData.internalProjectNo;
    projectRequestData.projectStartDate = formData.startDate;
    projectRequestData.projectEndDate = formData.endDate;
    projectRequestData.projectLocation = formData.location;
    projectRequestData.projectDescription = formData.projectDescription;

    // Send manager userId directly to backend
    projectRequestData.internalProjectManager = formData.manager;

    // Convert external PM userIds to comma-separated string for backend
    const getExternalPMIds = (userIds: number[]) => {
      if (!userIds || !Array.isArray(userIds)) return '';
      return userIds.filter(id => id !== null && id !== undefined).join(',');
    };

    projectRequestData.externalPM = getExternalPMIds(formData.externalPM);
    projectRequestData.loggedInUserId = this.loginUser.userId;

    console.log('Prepared project data for backend:', projectRequestData);
    return projectRequestData;
  }
  // API to update the user details based on the userid
  edit(projectData: any) {
    projectData.projectId = this.id
    this.httpUtilService.loadingSubject.next(true);
    this.projectsService.updateProject(projectData).subscribe((res) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true);
        this.modal.close();
      } else {
        this.customLayoutUtilsService.showError(res.responseData.message, '');
        this.passEntry.emit(false);
      }
    });
  }
  // API to save new user details
  create(projectData: any) {
    this.httpUtilService.loadingSubject.next(true);
    this.projectsService.createProject(projectData).subscribe((res: any) => {
      this.httpUtilService.loadingSubject.next(false);
      if (!res.isFault) {
        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');
        this.passEntry.emit(true);
        this.modal.close();
      } else {
        this.customLayoutUtilsService.showError(res.responseData.message, '');
        this.passEntry.emit(false);
      }
    });
  }
  showTab(tab: any, $event: any) {
    this.selectedTab = tab;
    this.cdr.markForCheck();
  }
  changeInternalManager(event: any) {}
  changeexternalPM(event: any) {}
  controlHasError(validation: any, controlName: string | number): boolean {
    if (!this.projectForm) {
      return false;
    }

    const control = this.projectForm!.controls[controlName];
    if (!control) {
      return false;
    }
    let result =
      control.hasError(validation) && (control.dirty || control.touched);
    return result;
  }
    goToPreviousTab() {
    // if (this.selectedTab === 'notes') {
    //   this.selectedTab = 'details';
    // } else if (this.selectedTab === 'details') {
    // }
    this.selectedTab = 'basic';
    this.cdr.markForCheck();
  }

  validatePermitsBeforeStatusChange(newStatus: string) {
    this.httpUtilService.loadingSubject.next(true);
    this.permitsService.getPermitsByProjectId(this.id).subscribe({
      next: (response: any) => {
        this.httpUtilService.loadingSubject.next(false);
        if (!response.isFault) {
          const permits = response.responseData?.permits || [];
          
          // Check if all permits are approved
          const unapprovedPermits = permits.filter((permit: any) => 
            permit.permitStatus !== 'Approved'
          );

          if (unapprovedPermits.length > 0) {
            const permitNumbers = unapprovedPermits.map((p: any) => p.permitNumber).join(', ');
            this.customLayoutUtilsService.showError(
              `Cannot change project status to "${newStatus}". The following permits are not approved: ${permitNumbers}`,
              'Status Change Not Allowed'
            );
            return;
          }

          // All permits are approved, proceed with status change
          let projectData: any = this.prepareProjectData();
          this.edit(projectData);
        } else {
          this.customLayoutUtilsService.showError(
            'Error checking permit status. Please try again.',
            'Validation Error'
          );
        }
      },
      error: (error) => {
        this.httpUtilService.loadingSubject.next(false);
        console.error('Error validating permits:', error);
        this.customLayoutUtilsService.showError(
          'Error checking permit status. Please try again.',
          'Validation Error'
        );
      }
    });
  }
}

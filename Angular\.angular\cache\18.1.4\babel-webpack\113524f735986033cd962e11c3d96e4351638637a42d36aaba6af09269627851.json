{"ast": null, "code": "import { menuReinitialization } from 'src/app/_metronic/kt/kt-helpers';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"src/app/modules/services/app.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"../../../../partials/layout/extras/dropdown-inner/user-inner/user-inner.component\";\nimport * as i4 from \"../../../../shared/keenicon/keenicon.component\";\nfunction NavbarComponent_ng_container_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"div\", 6);\n    i0.ɵɵelement(3, \"app-keenicon\", 7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", ctx_r0.btnIconClass);\n  }\n}\nexport class NavbarComponent {\n  appService;\n  appHeaderDefaulMenuDisplay;\n  isRtl;\n  itemClass = 'ms-1 ms-lg-3';\n  btnClass = 'btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px w-md-40px h-md-40px';\n  userAvatarClass = 'symbol-35px symbol-md-40px';\n  btnIconClass = 'fs-2 fs-md-1';\n  initials = '';\n  constructor(appService) {\n    this.appService = appService;\n  }\n  ngAfterViewInit() {\n    menuReinitialization();\n  }\n  ngOnInit() {\n    const user = this.appService.getLoggedInUser();\n    this.initials = this.appService.getUserInitials(user);\n  }\n  static ɵfac = function NavbarComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AppService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: NavbarComponent,\n    selectors: [[\"app-navbar\"]],\n    inputs: {\n      appHeaderDefaulMenuDisplay: \"appHeaderDefaulMenuDisplay\",\n      isRtl: \"isRtl\"\n    },\n    decls: 6,\n    vars: 4,\n    consts: [[1, \"app-navbar-item\", 3, \"ngClass\"], [\"data-kt-menu-trigger\", \"{default: 'click', lg: 'hover'}\", \"data-kt-menu-attach\", \"parent\", \"data-kt-menu-placement\", \"bottom-end\", 1, \"cursor-pointer\", \"symbol\", 3, \"ngClass\"], [1, \"symbol-label\", \"bg-secondary\", \"text-white\", \"fw-bold\", \"d-flex\", \"align-items-center\", \"justify-content-center\"], [\"data-kt-menu\", \"true\"], [4, \"ngIf\"], [\"title\", \"Show header menu\", 1, \"app-navbar-item\", \"d-lg-none\", \"ms-2\", \"me-n3\"], [\"id\", \"kt_app_header_menu_toggle\", 1, \"btn\", \"btn-icon\", \"btn-active-color-primary\", \"w-35px\", \"h-35px\"], [\"name\", \"element-4\", 1, \"fs-1\", 3, \"ngClass\"]],\n    template: function NavbarComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵtext(3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelement(4, \"app-user-inner\", 3);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, NavbarComponent_ng_container_5_Template, 4, 1, \"ng-container\", 4);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngClass\", ctx.itemClass);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngClass\", ctx.userAvatarClass);\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate1(\" \", ctx.initials, \" \");\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.appHeaderDefaulMenuDisplay);\n      }\n    },\n    dependencies: [i2.NgClass, i2.NgIf, i3.UserInnerComponent, i4.KeeniconComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["menuReinitialization", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "btnIconClass", "NavbarComponent", "appService", "appHeaderDefaulMenuDisplay", "isRtl", "itemClass", "btnClass", "userAvatarClass", "initials", "constructor", "ngAfterViewInit", "ngOnInit", "user", "getLoggedInUser", "getUserInitials", "ɵɵdirectiveInject", "i1", "AppService", "selectors", "inputs", "decls", "vars", "consts", "template", "NavbarComponent_Template", "rf", "ctx", "ɵɵtext", "ɵɵtemplate", "NavbarComponent_ng_container_5_Template", "ɵɵtextInterpolate1"], "sources": ["D:\\permittracker\\Angular\\src\\app\\_metronic\\layout\\components\\header\\navbar\\navbar.component.ts", "D:\\permittracker\\Angular\\src\\app\\_metronic\\layout\\components\\header\\navbar\\navbar.component.html"], "sourcesContent": ["import { AfterViewInit, Component, Input, OnInit } from '@angular/core';\nimport { menuReinitialization } from 'src/app/_metronic/kt/kt-helpers';\nimport { AppService } from 'src/app/modules/services/app.service';\n\n@Component({\n\tselector: 'app-navbar',\n\ttemplateUrl: './navbar.component.html',\n\tstyleUrls: ['./navbar.component.scss'],\n})\nexport class NavbarComponent implements OnInit, AfterViewInit {\n\t@Input() appHeaderDefaulMenuDisplay: boolean;\n\t@Input() isRtl: boolean;\n\n\titemClass: string = 'ms-1 ms-lg-3';\n\tbtnClass: string = 'btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary w-35px h-35px w-md-40px h-md-40px';\n\tuserAvatarClass: string = 'symbol-35px symbol-md-40px';\n\tbtnIconClass: string = 'fs-2 fs-md-1';\n\tinitials: string = '';\n\n\tconstructor(private appService: AppService) { }\n\n\tngAfterViewInit(): void {\n\t\tmenuReinitialization();\n\t}\n\n\tngOnInit(): void {\n\t\tconst user = this.appService.getLoggedInUser();\n\t\tthis.initials = this.appService.getUserInitials(user);\n\t}\n\n}\n", "<!--begin::Search-->\r\n<!-- <div class=\"app-navbar-item align-items-stretch\" [ngClass]=\"itemClass\">\r\n  \r\n  <div id=\"kt_header_search\" class=\"header-search d-flex align-items-stretch\" data-kt-search-keypress=\"true\"\r\n    data-kt-search-min-length=\"2\" data-kt-search-enter=\"enter\" data-kt-search-layout=\"menu\"\r\n    data-kt-menu-trigger=\"{default: 'hover'}\" data-kt-menu-overflow=\"false\" data-kt-menu-permanent=\"true\"\r\n    data-kt-menu-placement=\"bottom-end\" data-kt-search=\"true\">\r\n   \r\n    <div class=\"d-flex align-items-center\" data-kt-search-element=\"toggle\" id=\"kt_header_search_toggle\">\r\n      <div [ngClass]=\"btnClass\">\r\n        <app-keenicon name=\"magnifier\" [class]=\"btnIconClass\"></app-keenicon>\r\n      </div>\r\n    </div>\r\n    \r\n    <app-search-result-inner data-kt-search-element=\"content\" data-kt-menu=\"true\"\r\n      class=\"menu menu-sub menu-sub-dropdown p-7 w-325px w-md-375px\"></app-search-result-inner>\r\n  </div>\r\n\r\n</div> -->\r\n<!--end::Search-->\r\n\r\n<!--begin::Activities-->\r\n<!-- <div class=\"app-navbar-item\" [ngClass]=\"itemClass\">\r\n\r\n  <div [ngClass]=\"btnClass\" id=\"kt_activities_toggle\">\r\n    <app-keenicon name=\"messages\" class=\"fs-2\"></app-keenicon>\r\n  </div>\r\n  \r\n</div> -->\r\n<!--end::Activities-->\r\n\r\n<!--begin::Notifications-->\r\n<!-- <div class=\"app-navbar-item\" [ngClass]=\"itemClass\">\r\n\r\n  <div [ngClass]=\"btnClass\" data-kt-menu-trigger=\"{default: 'click', lg: 'hover'}\" data-kt-menu-attach=\"parent\"\r\n    data-kt-menu-placement=\"bottom-end\">\r\n    <app-keenicon name=\"notification-status\" class=\"fs-2\"></app-keenicon>\r\n  </div>\r\n  <app-notifications-inner></app-notifications-inner>\r\n \r\n</div> -->\r\n<!--end::Notifications-->\r\n\r\n<!--begin::Chat-->\r\n<!-- <div class=\"app-navbar-item\" [ngClass]=\"itemClass\">\r\n  <div [ngClass]=\"btnClass\" class=\"position-relative\" id=\"kt_drawer_chat_toggle\">\r\n    <app-keenicon name=\"message-text-2\" class=\"fs-2\"></app-keenicon>\r\n    <span\r\n      class=\"bullet bullet-dot bg-success h-6px w-6px position-absolute translate-middle top-0 start-50 animation-blink\">\r\n    </span>\r\n  </div>\r\n</div> -->\r\n<!--end::Chat-->\r\n\r\n<!--begin::Quick links-->\r\n<!-- <div class=\"app-navbar-item\" [ngClass]=\"itemClass\">\r\n  \r\n  <div [ngClass]=\"btnClass\" data-kt-menu-trigger=\"click\" data-kt-menu-attach=\"parent\"\r\n    data-kt-menu-placement=\"bottom-end\">\r\n    <app-keenicon name=\"element-11\" class=\"fs-2\"></app-keenicon>\r\n  </div>\r\n\r\n  <app-quick-links-inner></app-quick-links-inner>\r\n</div> -->\r\n<!--end::Quick links-->\r\n\r\n<!--begin::Theme mode-->\r\n<!-- <div class=\"app-navbar-item\" [ngClass]=\"itemClass\">\r\n  <app-theme-mode-switcher [toggleBtnClass]=\"btnClass\" toggleBtnClass=\"{`btn-active-light-primary btn-custom ${toolbarButtonHeightClass}`}\"></app-theme-mode-switcher>\r\n</div> -->\r\n<!--end::Theme mode-->\r\n\r\n<!--begin::User menu-->\r\n<div class=\"app-navbar-item\" [ngClass]=\"itemClass\">\r\n  <!--begin::Menu wrapper-->\r\n  <div class=\"cursor-pointer symbol\" [ngClass]=\"userAvatarClass\" data-kt-menu-trigger=\"{default: 'click', lg: 'hover'}\" data-kt-menu-attach=\"parent\" data-kt-menu-placement=\"bottom-end\">\r\n    <div class=\"symbol-label bg-secondary text-white fw-bold d-flex align-items-center justify-content-center\">\r\n      {{ initials }}\r\n    </div>\r\n  </div>\r\n  <app-user-inner data-kt-menu='true'></app-user-inner>\r\n  <!--end::Menu wrapper-->\r\n  </div>\r\n<!--end::User menu-->\r\n\r\n<!--begin::Header menu toggle-->\r\n<ng-container *ngIf=\"appHeaderDefaulMenuDisplay\">\r\n  <div class=\"app-navbar-item d-lg-none ms-2 me-n3\" title=\"Show header menu\">\r\n    <div class=\"btn btn-icon btn-active-color-primary w-35px h-35px\" id=\"kt_app_header_menu_toggle\">\r\n      <app-keenicon name=\"element-4\" class=\"fs-1\" [ngClass]=\"btnIconClass\"></app-keenicon>\r\n    </div>\r\n  </div>\r\n</ng-container>\r\n<!--end::Header menu toggle-->\r\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,iCAAiC;;;;;;;;ICqFtEC,EAAA,CAAAC,uBAAA,GAAiD;IAE7CD,EADF,CAAAE,cAAA,aAA2E,aACuB;IAC9FF,EAAA,CAAAG,SAAA,sBAAoF;IAExFH,EADE,CAAAI,YAAA,EAAM,EACF;;;;;IAF0CJ,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAC,YAAA,CAAwB;;;ADhF1E,OAAM,MAAOC,eAAe;EAUPC,UAAA;EATXC,0BAA0B;EAC1BC,KAAK;EAEdC,SAAS,GAAW,cAAc;EAClCC,QAAQ,GAAW,oHAAoH;EACvIC,eAAe,GAAW,4BAA4B;EACtDP,YAAY,GAAW,cAAc;EACrCQ,QAAQ,GAAW,EAAE;EAErBC,YAAoBP,UAAsB;IAAtB,KAAAA,UAAU,GAAVA,UAAU;EAAgB;EAE9CQ,eAAeA,CAAA;IACdnB,oBAAoB,EAAE;EACvB;EAEAoB,QAAQA,CAAA;IACP,MAAMC,IAAI,GAAG,IAAI,CAACV,UAAU,CAACW,eAAe,EAAE;IAC9C,IAAI,CAACL,QAAQ,GAAG,IAAI,CAACN,UAAU,CAACY,eAAe,CAACF,IAAI,CAAC;EACtD;;qCAnBYX,eAAe,EAAAT,EAAA,CAAAuB,iBAAA,CAAAC,EAAA,CAAAC,UAAA;EAAA;;UAAfhB,eAAe;IAAAiB,SAAA;IAAAC,MAAA;MAAAhB,0BAAA;MAAAC,KAAA;IAAA;IAAAgB,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCmExBjC,EAHJ,CAAAE,cAAA,aAAmD,aAEsI,aAC1E;QACzGF,EAAA,CAAAmC,MAAA,GACF;QACFnC,EADE,CAAAI,YAAA,EAAM,EACF;QACNJ,EAAA,CAAAG,SAAA,wBAAqD;QAErDH,EAAA,CAAAI,YAAA,EAAM;QAIRJ,EAAA,CAAAoC,UAAA,IAAAC,uCAAA,0BAAiD;;;QAbpBrC,EAAA,CAAAM,UAAA,YAAA4B,GAAA,CAAArB,SAAA,CAAqB;QAEbb,EAAA,CAAAK,SAAA,EAA2B;QAA3BL,EAAA,CAAAM,UAAA,YAAA4B,GAAA,CAAAnB,eAAA,CAA2B;QAE1Df,EAAA,CAAAK,SAAA,GACF;QADEL,EAAA,CAAAsC,kBAAA,MAAAJ,GAAA,CAAAlB,QAAA,MACF;QAQWhB,EAAA,CAAAK,SAAA,GAAgC;QAAhCL,EAAA,CAAAM,UAAA,SAAA4B,GAAA,CAAAvB,0BAAA,CAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
.nav-line-tabs {
  .nav-link {
    cursor: pointer;
    transition: all 0.3s ease;
    
    &.active {
      color: #009ef7 !important;
      border-bottom: 2px solid #009ef7;
      font-weight: 600;
    }
    
    &:hover {
      color: #009ef7 !important;
    }
  }
}

// Tab content transitions
.tab-content {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Ensure Back button is vertically centered next to tabs
.card-body > .d-flex.justify-content-between.align-items-center {
  align-items: center;
}

// Back button visual to match project/permit pages
.back-button {
  display: inline-flex;
  align-items: center;
  align-self: center; /* ensure vertical centering in flex header */
  padding: .15rem .5rem; /* compact */
  border-radius: .55rem;
  font-weight: 600;
  font-size: .8rem;
  line-height: 1;
  margin-top: .1rem; /* slight visual balance */
  // keep color from btn-light-primary; only adjust icon size
  i { font-size: .75rem; }
}
// Dates Section
.dates-section {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5eaee;
  background-color: #f8f9fa;
}
:host ::ng-deep .modal {
  --bs-modal-padding: 1rem !important;
}
.date-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 2rem;
}

.date-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;

  label {
    font-size: 0.75rem;
    font-weight: 600;
    color: #6c7293;
    text-transform: uppercase;
    letter-spacing: 0.05rem;
  }

  .date-value {
    font-size: 0.875rem;
    color: #3f4254;
    font-weight: 500;
  }
}

.review-status {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
  border: 1px solid transparent;

  // Status color variations for review status
  &.status-approved {
    background-color: #e8f5e8;
    color: #1b5e20;
    border: 1px solid #c8e6c9;
  }

  &.status-pending {
    background-color: #fff3e0;
    color: #e65100;
    border: 1px solid #ffcc02;
  }

  &.status-under-review {
    background-color: #e8eaf6;
    color: #3949ab;
    border: 1px solid #c5cae9;
  }

  &.status-rejected {
    background-color: #ffebee;
    color: #c62828;
    border: 1px solid #ffcdd2;
  }

  &.status-requires-re-submit {
    background-color: #fff8e1;
    color: #f57f17;
    border: 1px solid #ffecb3;
  }

  &.status-approved-w-conditions {
    background-color: #f3e5f5;
    color: #7b1fa2;
    border: 1px solid #e1bee7;
  }

  &.status-complete {
    background-color: #e8f5e8;
    color: #1b5e20;
    border: 1px solid #c8e6c9;
  }

  &.status-n-a {
    background-color: #f5f5f5;
    color: #757575;
    border: 1px solid #e0e0e0;
  }
}

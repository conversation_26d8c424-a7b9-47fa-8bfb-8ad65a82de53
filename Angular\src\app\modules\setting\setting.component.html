<div class="card mb-5 mb-xl-5">
    <div class="card-body pb-0 pt-0">
        <div class="d-flex justify-content-between align-items-center">
            <ul class="nav nav-stretch nav-line-tabs nav-line-tabs-3x border-transparent fs-5 fw-bold flex-nowrap">
                <li class="nav-item">
                    <a class="nav-link text-active-primary me-6 cursor-pointer"
                        [ngClass]="{'active': selectedTab === 'Users'}" 
                        (click)="onNavChange('Users')">
                        Users
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link text-active-primary me-6 cursor-pointer"
                        [ngClass]="{'active': selectedTab === 'Roles'}" 
                        (click)="onNavChange('Roles')">
                        Roles
                    </a>
                </li>
            </ul>
            
            <!-- Back Button -->
            <div class="d-flex align-items-center">
                <button 
                    type="button" 
                    class="btn btn-sm btn-light-primary d-flex align-items-center back-button"
                    (click)="goBack()"
                    title="Go back to Settings Dashboard">
                    <i class="fa fa-arrow-left me-2"></i>
                    Back
                </button>
            </div>
        </div>
    </div>

    <div *ngIf="selectedTab==='Users'">
        <app-user-list></app-user-list>
    </div>
   <div *ngIf="selectedTab==='Roles'">
        <app-role-list></app-role-list>
    </div>
</div>

import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component, Input } from '@angular/core';
import { NgbActiveModal, NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { PermitsService } from '../../services/permits.service';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';

@Component({
  selector: 'app-add-edit-internal-review',
  standalone: false,
  templateUrl: './add-edit-internal-review.component.html',
  styleUrl: './add-edit-internal-review.component.scss',
})
export class AddEditInternalReviewComponent {
  @Input() permitId: number | null = null;
  @Input() reviewData: any = null; // For edit mode
  @Input() loggedInUserId: string = 'user'; // Should be passed from parent
  @Input() permitNumber: string = '';

  reviewForm!: FormGroup;
  isEdit: boolean = false;
  isLoading: boolean = false;
  activeTab: 'details' | 'comments' = 'details';

  constructor(
    private fb: FormBuilder,
    public modal: NgbActiveModal,
    private modalService: NgbModal,
    private permitsService: PermitsService,
        private customLayoutUtilsService: CustomLayoutUtilsService,

  ) {}

  ngOnInit(): void {
    this.isEdit = !!this.reviewData;

    this.reviewForm = this.fb.group({
      reviewCategory: [this.reviewData?.reviewCategory || '', Validators.required],
      typeCodeDrawing: [this.reviewData?.typeCodeDrawing || '', Validators.required],
      reviewComments: [this.reviewData?.reviewComments || '', Validators.required],
      nonComplianceItems: [this.reviewData?.nonComplianceItems || ''],
      aeResponse: [this.reviewData?.aeResponse || ''],
      internalReviewer: [this.reviewData?.internalReviewer || '', Validators.required],
      internalVerificationStatus: [this.reviewData?.internalVerificationStatus || '', Validators.required],
      reviewedDate: [this.reviewData?.reviewedDate ? this.formatDateForInput(this.reviewData.reviewedDate) : '', Validators.required],
      completedDate: [
        this.reviewData?.completedDate
          ? this.formatDateForInput(this.reviewData.completedDate)
          : ''
      ],
    });
  }

  setActiveTab(tab: 'details' | 'comments'): void {
    this.activeTab = tab;
  }

  goToPrevious(): void {
    if (this.activeTab === 'comments') {
      this.activeTab = 'details';
    }
  }

  private formatDateForInput(date: string | Date): string {
    if (!date) return '';
    const d = new Date(date);
    return d.toISOString().split('T')[0]; // Format as YYYY-MM-DD for input[type="date"]
  }

  onSubmit(): void {
    if (this.reviewForm.valid && this.permitId) {
      this.isLoading = true;

      const formData = {
        ...this.reviewForm.value,
        permitId: this.permitId,
        loggedInUserId: this.loggedInUserId
      };

      if (this.isEdit && this.reviewData?.commentsId) {
        // Update existing review
        formData.commentsId = this.reviewData.commentsId;

        this.permitsService.updateInternalReview(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            if (res?.isFault) {
              //alert(res.faultMessage || 'Failed to update internal review');
            } else {
                                  this.customLayoutUtilsService.showSuccess(res.responseData.message, '');

              //alert('Internal review updated successfully!');
              this.modal.close('updated');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
                                this.customLayoutUtilsService.showError('error updating internal review', '');

            //alert('Error updating internal review');
            console.error(err);
          }
        });
      } else {
        // Create new review
        this.permitsService.addInternalReview(formData).subscribe({
          next: (res: any) => {
            this.isLoading = false;
            if (res?.isFault) {
              //alert(res.faultMessage || 'Failed to create internal review');
                                  this.customLayoutUtilsService.showError(res.faultMessage || 'Failed to create interval review', '');

            } else {
              //alert('Internal review created successfully!');
                                  this.customLayoutUtilsService.showSuccess('Internal review created successfully!', '');

              this.modal.close('created');
            }
          },
          error: (err: any) => {
            this.isLoading = false;
                                this.customLayoutUtilsService.showError('error creating internal review', '');

            //alert('Error creating internal review');
            console.error(err);
          }
        });
      }
    } else {
      this.reviewForm.markAllAsTouched();
      if (!this.permitId) {
                            this.customLayoutUtilsService.showError('Permit Id is required', '');

        //alert('Permit ID is required');
      }
    }
  }

  onCancel(): void {
    this.modal.dismiss('cancelled');
  }

  get isFormValid(): boolean {
    return this.reviewForm.valid;
  }

  get isDetailsValid(): boolean {
    if (!this.reviewForm) { return false; }
    const controls = this.reviewForm.controls as any;
    return (
      !!controls.reviewCategory?.valid &&
      !!controls.typeCodeDrawing?.valid &&
      !!controls.internalReviewer?.valid &&
      !!controls.internalVerificationStatus?.valid &&
      !!controls.reviewedDate?.valid
    );
  }
}

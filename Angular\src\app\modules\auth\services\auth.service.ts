import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';
import { Observable, BehaviorSubject, of, Subscription } from 'rxjs';
import { map, catchError, switchMap, finalize } from 'rxjs/operators';
import { UserModel } from '../models/user.model';
import { AuthModel } from '../models/auth.model';
import { AuthHTTPService } from './auth-http';
import { environment } from 'src/environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { AppSettings } from 'src/app/app.settings';
import {jwtDecode} from 'jwt-decode';
import { AppService } from '../../services/app.service';

export type UserType = UserModel | undefined;

@Injectable({
  providedIn: 'root',
})
export class AuthService implements OnDestroy {
  // private fields
  private unsubscribe: Subscription[] = []; // Read more: => https://brianflove.com/2016/12/11/anguar-2-unsubscribe-observables/
  private authLocalStorageToken = `${environment.appVersion}-${environment.USERDATA_KEY}`;
  private tokenExpirationTimer: any;
  // public fields
  currentUser$: Observable<UserType>;
  isLoading$: Observable<boolean>;
  currentUserSubject: BehaviorSubject<UserType>;
  isLoadingSubject: BehaviorSubject<boolean>;

  get currentUserValue(): UserType {
    return this.currentUserSubject.value;
  }

  set currentUserValue(user: UserType) {
    this.currentUserSubject.next(user);
  }

  constructor(
    private authHttpService: AuthHTTPService,
    private router: Router,
    private http: HttpClient,
    private activatedRoute: ActivatedRoute,
    public appService:AppService
  ) {
    this.isLoadingSubject = new BehaviorSubject<boolean>(false);
    this.currentUserSubject = new BehaviorSubject<UserType>(undefined);
    this.currentUser$ = this.currentUserSubject.asObservable();
    this.isLoading$ = this.isLoadingSubject.asObservable();
    const subscr = this.getUserByToken().subscribe();
    this.unsubscribe.push(subscr);
  }

  // public methods
  public login(data: any): Observable<any>{
    return this.http.post(AppSettings.REST_ENDPOINT + '/login', data);
  }
  // public login(data: any, value: any): Observable<any>{
  //   return this.http.post(AppSettings.REST_ENDPOINT + '/login', data);
  // }
  public resetPassword(data: any): Observable<any>{
    return this.http.post(AppSettings.REST_ENDPOINT + '/resetPassword', data);
  }

  logout() {
    localStorage.removeItem(this.authLocalStorageToken);
    this.router.navigate(['/auth/login'], {
      queryParams: {},
    });
  }

  public forgotPassword(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/forgotPassword', data);
  }

  getUserByToken(): Observable<UserType> {
    const auth = this.getAuthFromLocalStorage();
    if (!auth || !auth.authToken) {
      return of(undefined);
    }

    this.isLoadingSubject.next(true);
    return this.authHttpService.getUserByToken(auth.authToken).pipe(
      map((user: UserType) => {
        if (user) {
          this.currentUserSubject.next(user);
        } else {
          this.logout();
        }
        return user;
      }),
      finalize(() => this.isLoadingSubject.next(false))
    );
  }


  // private methods
  private setAuthFromLocalStorage(auth: AuthModel): boolean {
    // store auth authToken/refreshToken/epiresIn in local storage to keep user logged in between page refreshes
    if (auth && auth.authToken) {
      localStorage.setItem(this.authLocalStorageToken, JSON.stringify(auth));
      return true;
    }
    return false;
  }

  private getAuthFromLocalStorage(): AuthModel | undefined {
    try {
      const lsValue = localStorage.getItem(this.authLocalStorageToken);
      if (!lsValue) {
        return undefined;
      }

      const authData = JSON.parse(lsValue);
      return authData;
    } catch (error) {
      console.error(error);
      return undefined;
    }
  }

  getToken(): string {
    let accessToken =this.appService.getLocalStorageItem('permit_access', false);
    return accessToken;
  }

  getExpirationTime(){
    const expirationTime = parseInt(this.appService.getLocalStorageItem('permit_exp',false), 10);
    return expirationTime;
  }

  setToken(token: string, expiresIn: any): void {
    const expirationTime =  expiresIn;
    this.appService.setLocalStorageItem('permit_access', token, false);
    this.appService.setLocalStorageItem('permit_exp', expirationTime.toString(),false);
  }
  isLoggedIn(): boolean {
    const token = this.getToken();
    if (token) {
      const expirationTime = parseInt(this.appService.getLocalStorageItem('permit_exp',false), 10);

      let timestamp = new Date().getTime()
      const currentTime :any= Math.floor(timestamp / 1000) ;
      return expirationTime > currentTime;
    }
    return false;
  }

  stopTokenExpirationTimer(): void {
    if (this.tokenExpirationTimer) {
      clearTimeout(this.tokenExpirationTimer);
      this.tokenExpirationTimer = null;
    }
  }

  refreshToken() {
    const expiredToken = this.getToken();
    let currentUser =  this.appService.getLocalStorageItem('permitUser',true);
    if (expiredToken) {
      // Make API call to refresh token
      // Pass expired token as authorization header
      // If call is successful, update token and expiration time
      // If call fails, log user out and redirect to login page

      let accessToken = this.appService.getLocalStorageItem('permit_access', false);
      let headers = new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': 'Bearer '+accessToken });
    let options = { headers: headers };
      this.http.post(AppSettings.REST_ENDPOINT + '/getTokenForUser', {UserId:currentUser.UserId},options).subscribe(async (userResponse:any)=>{
        if(userResponse.isFault === false){
          this.appService.setLocalStorageItem("permitUser", userResponse.responseData.data, true);
          const token = userResponse.responseData.data.AccessToken;
          const decodedToken:any = jwtDecode(token);
          const expirationTime = decodedToken.exp;

        await  this.setToken(userResponse.responseData.data.AccessToken,expirationTime);

          return true;
        }else{
          this.appService.logout();
          return false;
        }
      })
    } else {
      this.appService.removeToken();
      return false;
    }
  }


  ngOnDestroy() {
    this.unsubscribe.forEach((sb) => sb.unsubscribe());
  }
}

import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, mergeMap } from 'rxjs/operators';
import { AppSettings } from 'src/app/app.settings';

@Injectable({
  providedIn: 'root'
})
export class RoleService {

  constructor(private http: HttpClient) { }

  // Get all roles with basic pagination
  public getAllRoles(queryParams: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getAllRole`, queryParams)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getAllRoles:', err);
          return of({
            data: [],
            total: 0,
            errors: ['Error retrieving roles']
          });
        })
      );
  }

  // Get roles for Kendo UI Grid
  public getRolesForKendoGrid(state: any): Observable<any> {
    const requestBody = {
      take: state.take || 10,
      skip: state.skip || 0,
      sort: state.sort || [],
      filter: state.filter || { logic: 'and', filters: [] },
      search: state.search || '',
      loggedInUserId: state.loggedInUserId
    };

    return this.http.post(`${AppSettings.REST_ENDPOINT}/getRolesForKendoGrid`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getRolesForKendoGrid:', err);
          return of({
            data: [],
            total: 0,
            errors: ['Error retrieving roles']
          });
        })
      );
  }

  // Create new role
  public createRole(data: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/createRole`, data);
  }

  // Update existing role
  public updateRole(data: any): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/editRole`, data);
  }

  // Get role by ID
  public getRole(id: number): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getRoleById`, { roleId: id });
  }

  // Delete role
  public deleteRole(id: number, loggedInUserId: number): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/deleteRole`, { 
      roleId: id, 
      loggedInUserId: loggedInUserId 
    });
  }

  // Get default permissions
  public getDefaultPermissions(): Observable<any> {
    return this.http.post(`${AppSettings.REST_ENDPOINT}/getDefaultPermissions`, {});
  }
}

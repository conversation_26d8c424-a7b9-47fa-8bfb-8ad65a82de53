<!-- Full Screen Loading Overlay -->
<div *ngIf="isLoading$ | async" class="fullscreen-loading-overlay">
  <div class="loading-content">
    <div class="custom-colored-spinner" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <div class="mt-4 fs-5">Updating password...</div>
  </div>
</div>

<div class="modal-content">
    <div class="modal-header">
        <div class="modal-title h5 fs-3" id="example-modal-sizes-title-lg">
            <ng-container>
                <div class="fw-bold fs-3 text-white">Change Password</div>
            </ng-container>
        </div>
        <div class="float-right cursor-pointer ir-12">
           <a class="btn btn-icon  btn-sm pl-08" (click)="modal.dismiss()" *ngIf="showClose">
                <i class="fa-solid fs-2 fa-xmark text-white"></i>
            </a>
        </div>
    </div>
    <div class="modal-body ">
        <ng-container>
            <form class="form form-label-right" [formGroup]="changePassword">
                <div class="card-body response-list">
                    <div class="form-group ">
                        <label class="fw-semibold fs-6 mb-2">Current Password&nbsp;<span
                                class="text-danger">*</span></label>
                        <div class="input-group mb-0">
                            <input [type]="cpasswordShown === true ?'password':'text'"
                                class="form-control form-control-sm" name="existingPassword" (change)="checkSamePassword()"
                                formControlName="existingPassword" placeholder="Type Here" required maxlength="20">
                            <div class="input-group-text">
                                <span [ngClass]="cpasswordShown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'"
                                    (click)="cpasswordShown===true? cshowpassword(false):cshowpassword(true)"></span>
                            </div>
                        </div>
                        <span class="custom-error-css" *ngIf="isControlHasError('existingPassword', 'required')">
                            Required field</span>&nbsp;
                    </div>
                    <div class="form-group">
                        <label class="fw-semibold fs-6 mb-2">New Password&nbsp;<span
                                class="text-danger">*</span></label>
                        <div class="input-group mb-0">
                            <input [type]="newPasswordShown === true ?'password':'text'"
                                class="form-control form-control-sm" name="password" formControlName="password"
                                placeholder="Type Here" required maxlength="20" (change)="checkSamePassword()">
                            <div class="input-group-text">
                                <span [ngClass]="newPasswordShown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'"
                                    (click)="newPasswordShown===true? newshowpassword(false):newshowpassword(true)"></span>
                            </div>
                        </div>
                        <span class="custom-error-css" *ngIf="isControlHasError('password', 'required')"> Required
                            field</span>
                        <span class="custom-error-css" *ngIf="isControlHasError('password', 'minlength')"> Minimum
                            password length: {{passwordMinLength}}</span>
                        <span class="custom-error-css" *ngIf="isControlHasError('password', 'maxlength')"> Required
                            20</span>
                        <span class="custom-error-css" *ngIf="samePassword">New Password shouldn't be same as Current
                            Password</span>&nbsp;
                    </div>

                    <div class="form-group mb-0">
                        <label class="fw-semibold fs-6 mb-2">Confirm Password&nbsp;<span
                                class="text-danger">*</span></label>
                        <div class="input-group mb-0">
                            <input [type]="newConpasswordShown === true ?'password':'text'"
                                class="form-control form-control-sm" name="confirmPassword"
                                formControlName="confirmPassword" placeholder="Type Here" required maxlength="20">
                            <div class="input-group-text">
                                <span [ngClass]="newConpasswordShown===true? 'bi bi-eye-slash-fill':'bi bi-eye-fill'"
                                    (click)="newConpasswordShown===true? newconshowpassword(false):newconshowpassword(true)"></span>
                            </div>
                        </div>
                        <span class="custom-error-css" *ngIf="isControlHasError('confirmPassword', 'required')">
                            Required field</span>&nbsp;
                        <span class="custom-error-css" *ngIf="isControlHasError('confirmPassword', 'minlength')">
                            Required {{passwordMinLength}}</span>&nbsp;
                        <span class="custom-error-css" *ngIf="isControlHasError('confirmPassword', 'maxlength')">
                            Required 20</span> &nbsp;
                        <span class="custom-error-css" *ngIf="confirmPasswordError">Passwords doesn't match</span>

                    </div>

                    <div class="form-group">
                        <app-password-strength [passwordToCheck]="changePassword.controls['password'].value"
                            [minLength]='passwordMinLength'
                            (passwordStrength)="onPasswordChange($event)"></app-password-strength>
                    </div>
                </div>
            </form>
        </ng-container>
    </div>
    <div class="modal-footer">
        <button type="button" class="btn btn-danger btn-sm btn-elevate mr-2" *ngIf="showClose"
            (click)="modal.dismiss()">Cancel</button>
        <ng-container>
            <button type="button" class="btn btn-primary btn-elevate btn-sm" (click)="save()"
                [disabled]="onValidityCheck()">
                Save</button>
        </ng-container>
    </div>
</div>

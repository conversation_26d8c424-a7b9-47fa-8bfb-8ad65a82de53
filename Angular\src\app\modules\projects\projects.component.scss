// Projects component styles
.nav-line-tabs {
  .nav-link {
    cursor: pointer;
    transition: all 0.3s ease;

    &.active {
      color: #009ef7 !important;
      border-bottom: 2px solid #009ef7;
      font-weight: 600;
    }

    &:hover {
      color: #009ef7 !important;
    }
  }
}

// Tab content transitions
.tab-content {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

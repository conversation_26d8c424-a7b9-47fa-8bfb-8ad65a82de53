import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, ViewChild, OnInit, OnDestroy, AfterViewInit } from '@angular/core';
import { FormControl } from '@angular/forms';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import _, { each } from 'lodash';
import { map, Subject, debounceTime, distinctUntilChanged, Subscription } from 'rxjs';
import { AppSettings } from 'src/app/app.settings';
import { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';
import { HttpUtilsService } from '../../services/http-utils.service';
import { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';
import { Page } from '../../shared/data/pagination.module';
import { AppService } from '../../services/app.service';
import { UserService } from '../../services/user.service';
import { RoleService } from '../../services/role.service';
import { RoleEditComponent } from '../role-edit/role-edit.component';
import { ExceljsService } from '../../services/exceljs.service';
import { KendoColumnService } from '../../services/kendo-column.service';
import { SortDescriptor } from '@progress/kendo-data-query';
import { FilterDescriptor, CompositeFilterDescriptor } from '@progress/kendo-data-query';

// Type definitions
interface RoleData {
  roleId: number;
  Name: string;
  Permissions: string;
  Status: string;
  Description: string;
  LastUpdatedDate: string;
  LastUpdatedUserFullName: string;
  CreatedDate: string;
  CreatedUserFullName: string;
  HasUser: number;
}

// Type for page configuration
interface PageConfig {
  size: number;
  pageNumber: number;
  totalElements: number;
  totalPages: number;
  orderBy: string;
  orderDir: string;
}

@Component({
  selector: 'app-role-list',
  templateUrl: './role-list.component.html',
  styleUrl: './role-list.component.scss'
})
export class RoleListComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('normalGrid') grid: any;

  // Data
  public serverSideRowData: RoleData[] = [];
  public gridData: RoleData[] = [];
  public IsListHasValue: boolean = false;

  public loading: boolean = false;
  public isLoading: boolean = false;

  loginUser: Record<string, any> = {};

  // Search
  public searchData: string = '';
  private searchTerms = new Subject<string>();
  private searchSubscription: Subscription;

  // Enhanced Filters for Kendo UI
  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };
  public activeFilters: Array<{
    field: string;
    operator: string;
    value: any;
  }> = [];

  public filterOptions: Array<{ text: string; value: string | null }> = [
    { text: 'All', value: null },
    { text: 'Active', value: 'Active' },
    { text: 'Inactive', value: 'Inactive' }
  ];

  // Advanced filter options
  public advancedFilterOptions = {
    status: [
      { text: 'All', value: null },
      { text: 'Active', value: 'Active' },
      { text: 'Inactive', value: 'Inactive' }
    ] as Array<{ text: string; value: string | null }>
  };

  // Filter state
  public showAdvancedFilters = false;
  public appliedFilters: {
    status: string | null;
  } = { status: null };

  // Kendo Grid properties
  public page: PageConfig = {
    size: 10,
    pageNumber: 0,
    totalElements: 0,
    totalPages: 0,
    orderBy: 'lastUpdatedDate',
    orderDir: 'desc'
  };
  public skip: number = 0;
  public sort: SortDescriptor[] = [
    { field: 'lastUpdatedDate', dir: 'desc' }
  ];

  // Column visibility system properties
  public kendoHide: any;
  public hiddenData: any = [];
  public kendoColOrder: any = [];
  public kendoInitColOrder: any = [];
  public hiddenFields: any = [];

  // Column configuration for the new system
  public gridColumns: string[] = [];
  public defaultColumns: string[] = [];
  public fixedColumns: string[] = [];
  public draggableColumns: string[] = [];
  public normalGrid: any;
  public expandedGrid: any;
  public isExpanded = false;

  // Enhanced Columns with Kendo UI features
  public gridColumnConfig: Array<{
    field: string;
    title: string;
    width: number;
    isFixed: boolean;
    type: string;
    filterable?: boolean;
    order: number;
  }> = [
    { field: 'action', title: 'Action', width: 80, isFixed: true, type: 'action', order: 1 },
    { field: 'roleName', title: 'Name', width: 200, isFixed: true, type: 'text', filterable: true, order: 2 },
    { field: 'rolePermissions', title: 'Permissions', width: 300, isFixed: false, type: 'text', filterable: true, order: 3 },
    { field: 'status', title: 'Status', width: 120, type: 'status', isFixed: false, filterable: true, order: 4 },
    { field: 'lastUpdatedDate', title: 'Updated Date', width: 180, isFixed: false, type: 'date', filterable: true, order: 5 }
  ];

  // OLD SYSTEM - to be removed
  public columnData = [
    { name: 'Name', prop: 'roleName', order: 'desc', width: '17%', sort: true },
    { name: 'Permissions', prop: 'rolePermissions', order: 'desc', width: '40%', sort: true },
    { name: 'Status', prop: 'status', order: 'desc', width: '11%', sort: true },
    { name: 'Updated', prop: 'lastUpdatedDate', order: 'desc', width: '18%', sort: true },
    { name: 'Action', prop: 'Action', order: 'desc', width: '12%', sort: false }
  ];

  // Router subscription for saving state on navigation
  private routerSubscription: Subscription;

  // Storage key for state persistence
  private readonly GRID_STATE_KEY = 'roles-grid-state';

  // Export options
  public exportOptions: Array<{ text: string; value: string }> = [
    { text: 'Export All', value: 'all' },
    { text: 'Export Selected', value: 'selected' },
    { text: 'Export Filtered', value: 'filtered' }
  ];

  // Selection state
  public selectedRoles: RoleData[] = [];
  public isAllSelected: boolean = false;

  // Statistics
  public roleStatistics: {
    activeRoles: number;
    inactiveRoles: number;
    totalRoles: number;
  } = {
    activeRoles: 0,
    inactiveRoles: 0,
    totalRoles: 0
  };

  // Bulk operations
  public showBulkActions = false;
  public bulkActionStatus: string = 'Active';

  // Legacy properties (keeping for backward compatibility)
  pageSize: number = AppSettings.PAGE_SIZE;
  pageSizeOptions: any = AppSettings.PAGE_SIZE_OPTIONS;
  itemsPerPage = new FormControl(this.pageSize);
  defaultOrder = 'desc';
  defaultOrderBy = 'lastUpdatedDate';
  defaultRoles: any = [];
  statusData: boolean = false;
  permissionArray: any = [];
  selectedTab = 'All';
  innerWidth: any;
  displayMobile: boolean = false;

  constructor(
    private cdr: ChangeDetectorRef,
    private modalService: NgbModal,
    public exceljsService: ExceljsService,
    private httpUtilService: HttpUtilsService,
    public AppService: AppService,
    private layoutUtilService: CustomLayoutUtilsService,
    private UserService: UserService,
    private roleService: RoleService,
    private kendoColumnService: KendoColumnService,
  ) {
    // set the default paging options
    this.page.pageNumber = 0;
    this.page.size = this.pageSize;
    this.page.orderBy = 'LastUpdatedDate';
    this.page.orderDir = 'desc';
  }

  ngOnInit(): void {
    this.loginUser = this.AppService.getLoggedInUser();
    console.log('Login user loaded:', this.loginUser);

    this.innerWidth = window.innerWidth;
    if((this.innerWidth >= 320) && (this.innerWidth <768)){
     this.displayMobile = true
    }else{
     this.displayMobile = false
    }

    // Setup search with debounce
    this.searchSubscription = this.searchTerms.pipe(
      debounceTime(500),
      distinctUntilChanged()
    ).subscribe((searchTerm) => {
      console.log('Search triggered with term:', searchTerm);
      this.page.pageNumber = 0;
      this.skip = 0;
      // Set loading state for search
      this.loading = true;
      this.isLoading = true;
      this.loadTable();
    });

    // Load roles for advanced filters
    this.loadRoles();

    // Initialize with default page load
    this.onPageLoad();

    // Initialize new column visibility system
    this.initializeColumnVisibilitySystem();

    // Load column configuration after a short delay to ensure loginUser is available
    setTimeout(() => {
      this.loadColumnConfigFromDatabase();
    }, 100);

    localStorage.removeItem('keyword');
  }

  /**
   * Initialize the new column visibility system
   */
  private initializeColumnVisibilitySystem(): void {
    // Initialize default columns
    this.defaultColumns = this.gridColumnConfig.map(col => col.field);
    this.gridColumns = [...this.defaultColumns];

    // Set fixed columns (first 2 columns)
    this.fixedColumns = ['action', 'roleName'];

    // Set draggable columns (all except fixed)
    this.draggableColumns = this.defaultColumns.filter(col => !this.fixedColumns.includes(col));

    // Initialize normal and expanded grid references
    this.normalGrid = this.grid;
    this.expandedGrid = this.grid;
  }

  ngAfterViewInit(): void {
    // Load the table after the view is initialized
    // Small delay to ensure the grid is properly rendered
    setTimeout(() => {
      this.loadTable();
    }, 200);
  }

  // Method to handle initial page load
  onPageLoad(): void {
    // Initialize the component with default data
    this.page.pageNumber = 0;
    this.skip = 0;
    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
    this.filter = { logic: 'and', filters: [] };
    this.searchData = '';

    // Load the data
    this.loadTable();
  }

  // Load roles for advanced filters
  private loadRoles(): void {
    this.UserService.getDefaultPermissions({}).subscribe((permissions: any) => {
      this.permissionArray = permissions.responseData;
    });
  }

  // Method to handle when the component becomes visible
  onTabActivated(): void {
    // Refresh the data when the tab is activated
    this.loadTable();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }
    this.searchTerms.complete();
  }
  // function to get roles data from API
  loadTable(){
    // Use the new Kendo UI specific endpoint
    this.loadTableWithKendoEndpoint();
  }

  // New method to load data using Kendo UI specific endpoint
  loadTableWithKendoEndpoint() {
    this.loading = true;
    this.isLoading = true;

    // Enable loader
    this.httpUtilService.loadingSubject.next(true);

    // Prepare state object for Kendo UI endpoint
    console.log('=== BACKEND REQUEST DEBUG ===');
    console.log('this.sort:', this.sort);
    console.log('page.orderBy:', this.page.orderBy);
    console.log('page.orderDir:', this.page.orderDir);

    const state = {
      take: this.page.size,
      skip: this.skip,
      sort: this.sort, // Send exactly what Kendo has
      filter: this.filter,
      search: this.searchData,
      loggedInUserId: this.loginUser.userId,
      orderBy: this.page.orderBy,
      orderDir: this.page.orderDir
    };

    console.log('Loading roles table with search term:', this.searchData);
    console.log('Full state object:', state);
    console.log('=== END BACKEND REQUEST DEBUG ===');

    this.roleService.getRolesForKendoGrid(state).subscribe({
      next: (data: {
        isFault?: boolean;
        responseData?: {
          data: any[];
          total: number;
          errors?: string[];
        };
        data?: any[];
        total?: number;
        errors?: string[];
      }) => {
        // Handle the new API response structure
        if (data.isFault || (data.responseData && data.responseData.errors && data.responseData.errors.length > 0)) {
          const errors = data.responseData?.errors || data.errors || [];
          console.error('Kendo UI Grid errors:', errors);
          this.handleEmptyResponse();
        } else {
          // Handle both old and new response structures
          const responseData = data.responseData || data;
          const roleData = responseData.data || [];
          const total = responseData.total || 0;

          this.IsListHasValue = roleData.length !== 0;
          this.serverSideRowData = this.sortGridDataClientSide(roleData, this.sort);
          this.gridData = this.serverSideRowData;
          this.page.totalElements = total;
          this.page.totalPages = Math.ceil(total / this.page.size);
        }
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
        this.cdr.markForCheck();
      },
      error: (error: unknown) => {
        console.error('Error loading data with Kendo UI endpoint:', error);
        this.handleEmptyResponse();
        this.loading = false;
        this.isLoading = false;
        this.httpUtilService.loadingSubject.next(false);
        this.cdr.markForCheck();
      }
    });
  }

  // Handle empty response
  private handleEmptyResponse(): void {
    this.IsListHasValue = false;
    this.serverSideRowData = [];
    this.gridData = [];
    this.page.totalElements = 0;
    this.page.totalPages = 0;
    this.loading = false;
    this.isLoading = false;
  }

  // Kendo Grid event handlers
  public pageChange(event: { skip: number; take: number }): void {
    this.skip = event.skip;
    this.page.size = event.take;
    this.page.pageNumber = event.skip / event.take;
    // Set loading state for pagination
    this.loading = true;
    this.isLoading = true;
    this.loadTableWithKendoEndpoint();
  }

  public onSortChange(sort: SortDescriptor[]): void {
    // Check if this is the 3rd click (dir is undefined)
    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;

    if (isThirdClick) {
      // 3rd click - clear sort and use default
      this.sort = [];
      this.page.orderBy = 'lastUpdatedDate';
      this.page.orderDir = 'desc';
    } else if (sort.length > 0 && sort[0] && sort[0].dir) {
      // Valid sort with direction
      this.sort = sort;
      const field = sort[0].field;
      const dir = sort[0].dir;

      // Normalize field names to match backend mapping
      let normalizedField = field;
      switch (field) {
        case 'Name': normalizedField = 'roleName'; break;
        case 'Status': normalizedField = 'status'; break;
        case 'UpdatedDate':
        case 'LastUpdatedDate':
        case 'lastUpdatedByFullName':
          normalizedField = 'lastUpdatedDate'; break;
      }

      this.page.orderBy = normalizedField;
      this.page.orderDir = dir;
    } else {
      // Empty sort array or invalid sort
      this.sort = [];
      this.page.orderBy = 'lastUpdatedDate';
      this.page.orderDir = 'desc';
    }

    // Reset to first page
    this.page.pageNumber = 0;
    this.skip = 0;
    // Set loading state for sorting
    this.loading = true;
    this.isLoading = true;
    console.log('=== END SORT CHANGE DEBUG ===');
    this.loadTableWithKendoEndpoint();
  }

  public filterChange(event: any): void {
    this.filter = event.filter;
    this.page.pageNumber = 0;
    this.skip = 0;
    // Set loading state for filtering
    this.loading = true;
    this.isLoading = true;
    this.loadTableWithKendoEndpoint();
  }

  // Client-side fallback sorting to guarantee visible order
  private sortGridDataClientSide(data: any[], sort: SortDescriptor[] | undefined): any[] {
    if (!Array.isArray(data) || !sort || sort.length === 0) return data;
    const s = sort[0];
    const field = (s.field as string) || 'lastUpdatedDate';
    const dir = (s.dir || 'asc') === 'asc' ? 1 : -1;

    const parseUsDate = (val: string) => {
      // Supports formats like 8/21/25 1:58 PM
      const m = val && val.match(/^(\d{1,2})\/(\d{1,2})\/(\d{2,4})\s+(\d{1,2}):(\d{2})\s*(AM|PM)$/i);
      if (!m) return NaN;
      let month = parseInt(m[1], 10) - 1;
      const day = parseInt(m[2], 10);
      let year = parseInt(m[3], 10);
      if (year < 100) year += 2000;
      let hour = parseInt(m[4], 10);
      const minute = parseInt(m[5], 10);
      const ampm = m[6].toUpperCase();
      if (ampm === 'PM' && hour < 12) hour += 12;
      if (ampm === 'AM' && hour === 12) hour = 0;
      return new Date(year, month, day, hour, minute).getTime();
    };

    const getVal = (item: any) => {
      const v = item[field];
      if (field === 'lastUpdatedDate') {
        if (v instanceof Date) return v.getTime();
        const ts = Date.parse(v);
        if (!isNaN(ts)) return ts;
        const us = parseUsDate(String(v));
        return isNaN(us) ? 0 : us;
      }
      if (typeof v === 'string') return v.toLowerCase();
      return v;
    };

    try {
      const sorted = [...data].sort((a, b) => {
        const av = getVal(a);
        const bv = getVal(b);
        if (av == null && bv == null) return 0;
        if (av == null) return -1 * dir;
        if (bv == null) return 1 * dir;
        if (av > bv) return 1 * dir;
        if (av < bv) return -1 * dir;
        return 0;
      });
      return sorted;
    } catch {
      return data;
    }
  }

  public onSelectionChange(event: any): void {
    this.selectedRoles = event.selectedRows || [];
    this.isAllSelected = this.selectedRoles.length === this.serverSideRowData.length;
  }

  // Search methods
  public onSearchKeyDown(event: any): void {
    if (event.key === 'Enter') {
      this.searchTerms.next(this.searchData);
    }
  }

  // Handle search input changes
  onSearchInput(): void {
    // Trigger search on every input change with debouncing
    console.log('Search input changed:', this.searchData);
    this.searchTerms.next(this.searchData);
  }

  // Handle search model changes
  onSearchChange(): void {
    // Trigger search when model changes
    console.log('Search model changed:', this.searchData);
    this.searchTerms.next(this.searchData);
  }

  public clearSearch(): void {
    // Clear search data and trigger search
    this.searchData = '';
    // Set loading state for clear search
    this.loading = true;
    this.isLoading = true;
    this.searchTerms.next('');
  }

  // Advanced filter methods
  public toggleAdvancedFilters(): void {
    this.showAdvancedFilters = !this.showAdvancedFilters;
  }

  public applyAdvancedFilters(): void {
    this.page.pageNumber = 0;
    this.skip = 0;
    this.loadTableWithKendoEndpoint();
  }

  public clearAllFilters(): void {
    this.appliedFilters = { status: null };
    this.activeFilters = [];
    this.filter = { logic: 'and', filters: [] };
    this.page.pageNumber = 0;
    this.skip = 0;
    this.loadTableWithKendoEndpoint();
  }

  // Grid expansion methods
  public toggleExpand(): void {
    // Find grid container element and toggle fullscreen class
    const gridContainer = document.querySelector('.grid-container') as HTMLElement;
    if (gridContainer) {
      gridContainer.classList.toggle('fullscreen-grid');
      this.isExpanded = !this.isExpanded;
      // Refresh grid after resize to ensure proper rendering
      if (this.grid) {
        this.grid.refresh();
      }
    }
  }

  // Export methods
  public onExportClick(event: { item: { value: string } }): void {
    switch (event.item.value) {
      case 'all':
        this.exportall();
        break;
      case 'selected':
        if (this.selectedRoles.length > 0) {
          this.exportRowData(this.selectedRoles);
        } else {
          this.layoutUtilService.showError('Please select roles to export', '');
        }
        break;
      case 'filtered':
        this.exportRowData(this.serverSideRowData);
        break;
      default:
        this.exportall();
    }
  }

  // Column visibility methods
  /**
   * Saves the current state of column visibility and order in the grid.
   */
  saveHead(): void {
    // Check if loginUser is available
    if (!this.loginUser || !this.loginUser.userId) {
      console.error('loginUser not available:', this.loginUser);
      this.layoutUtilService.showError('User not logged in. Please refresh the page.', '');
      return;
    }

    const nonHiddenColumns: any[] = [];
    const hiddenColumns: any[] = [];

    if (this.grid && this.grid.columns) {
      this.grid.columns.forEach((column: any) => {
        if (!column.hidden) {
          const columnData = {
            title: column.title,
            field: column.field,
            hidden: column.hidden
          };
          nonHiddenColumns.push(columnData);
        } else {
          const columnData = {
            title: column.title,
            field: column.field,
            hidden: column.hidden
          };
          hiddenColumns.push(columnData);
        }
      });
    }

    const draggableColumnsOrder = this.gridColumns
      .filter(col => !this.fixedColumns.includes(col))
      .map((field, index) => ({
        field,
        orderIndex: index
      }));

    // Prepare data for backend
    const userData = {
      pageName: 'Roles',
      userID: this.loginUser.userId,
      hiddenData: hiddenColumns,
      kendoColOrder: draggableColumnsOrder,
      LoggedId: this.loginUser.userId
    };

    // Show loading state
    this.httpUtilService.loadingSubject.next(true);

    // Save to backend
    this.kendoColumnService.createHideFields(userData).subscribe({
      next: (res) => {
        this.httpUtilService.loadingSubject.next(false);
        if (!res.isFault) {
          // Update local state
          this.hiddenData = hiddenColumns;
          this.kendoColOrder = draggableColumnsOrder;
          this.hiddenFields = this.hiddenData.map((col: any) => col.field);

          // Also save to localStorage as backup
          this.kendoColumnService.saveToLocalStorage(userData);

          this.layoutUtilService.showSuccess(res.message || 'Column settings saved successfully.', '');
        } else {
          this.layoutUtilService.showError(res.message || 'Failed to save column settings.', '');
        }
        this.cdr.markForCheck();
      },
      error: (error) => {
        this.httpUtilService.loadingSubject.next(false);
        console.error('Error saving column settings:', error);

        // Fallback to localStorage on error
        this.kendoColumnService.saveToLocalStorage(userData);

        // Update local state
        this.hiddenData = hiddenColumns;
        this.kendoColOrder = draggableColumnsOrder;
        this.hiddenFields = this.hiddenData.map((col: any) => col.field);

        this.layoutUtilService.showError('Failed to save to server. Settings saved locally.', '');
        this.cdr.markForCheck();
      }
    });
  }

  /**
   * Reset the current state of column visibility and order in the grid to its original state.
   */
  resetTable(): void {
    // Check if loginUser is available
    if (!this.loginUser || !this.loginUser.userId) {
      console.error('loginUser not available:', this.loginUser);
      this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');
      return;
    }

    // Double-check authentication token
    const token = this.AppService.getLocalStorageItem('permitToken', true);
    if (!token) {
      console.error('Authentication token not found');
      this.layoutUtilService.showError('Authentication token not found. Please login again.', '');
      return;
    }

    // Reset all state variables
    this.searchData = '';
    this.activeFilters = [];
    this.filter = { logic: 'and', filters: [] };
    this.skip = 0;
    this.page.pageNumber = 0;
    this.gridColumns = [...this.defaultColumns];

    // Reset sort state to default
    this.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
    this.page.orderBy = 'lastUpdatedDate';
    this.page.orderDir = 'desc';

    // Reset advanced filters
    this.appliedFilters = { status: null };

    // Reset advanced filters visibility
    this.showAdvancedFilters = false;

    // Reset column order index
    if (this.grid && this.grid.columns) {
      this.grid.columns.forEach((column: any) => {
        const index = this.gridColumns.indexOf(column.field);
        if (index !== -1) {
          column.orderIndex = index;
        }
        // Reset column visibility - show all columns
        if (column.field && column.field !== 'action') {
          column.hidden = false;
        }
      });
    }

    // Clear hidden columns
    this.hiddenData = [];
    this.kendoColOrder = [];
    this.hiddenFields = [];

    // Reset the Kendo Grid's internal state
    if (this.grid) {
      // Clear all filters
      this.grid.filter = { logic: 'and', filters: [] };
      
      // Reset sorting
      this.grid.sort = [{ field: 'lastUpdatedDate', dir: 'desc' }];
      
      // Reset to first page
      this.grid.skip = 0;
      this.grid.pageSize = this.page.size;
    }

    // Prepare reset data
    const userData = {
      pageName: 'Roles',
      userID: this.loginUser.userId,
      hiddenData: [],
      kendoColOrder: [],
      LoggedId: this.loginUser.userId
    };

    // Only clear local settings; do not call server
    this.kendoColumnService.clearFromLocalStorage('Roles');

    // Show loader and refresh grid
    this.loading = true;
    this.isLoading = true;
    this.httpUtilService.loadingSubject.next(true);
    this.cdr.detectChanges();

    // Force grid refresh to apply all changes
    if (this.grid) {
      setTimeout(() => {
        this.grid.refresh();
        this.grid.reset();
      }, 100);
    }

    this.loadTable();
  }

  /**
   * Refresh grid data
   */
  refreshGrid(): void {
    // Set loading state to show full-screen loader
    this.loading = true;
    this.isLoading = true;

    // Reset to first page and clear any applied filters
    this.page.pageNumber = 0;
    this.skip = 0;
    this.filter = { logic: 'and', filters: [] };
    this.gridFilter = { logic: 'and', filters: [] };
    this.activeFilters = [];
    this.appliedFilters = { status: null };

    // Clear search data
    this.searchData = '';

    // Load fresh data from API
    this.loadTable();
  }

  /**
   * Loads and applies the saved column order from the user preferences or configuration.
   */
  loadSavedColumnOrder(kendoColOrder: any): void {
    try {
      const savedOrder = kendoColOrder;
      if (savedOrder) {
        const parsedOrder = savedOrder;
        if (Array.isArray(parsedOrder) && parsedOrder.length > 0) {
          // Get only the draggable columns from saved order
          const savedDraggableColumns = parsedOrder
            .sort((a, b) => a.orderIndex - b.orderIndex)
            .map(col => col.field)
            .filter(field => !this.fixedColumns.includes(field));

          // Add any missing draggable columns at the end
          const missingColumns = this.draggableColumns.filter(col => !savedDraggableColumns.includes(col));

          // Combine fixed columns with saved draggable columns
          this.gridColumns = [
            ...this.fixedColumns,
            ...savedDraggableColumns,
            ...missingColumns
          ];
        } else {
          this.gridColumns = [...this.defaultColumns];
        }
      } else {
        this.gridColumns = [...this.defaultColumns];
      }
    } catch (error) {
      this.gridColumns = [...this.defaultColumns];
    }
  }

  /**
   * Checks if a given column is marked as hidden.
   */
  getHiddenField(columnName: any): boolean {
    return this.hiddenFields.indexOf(columnName) > -1;
  }

  /**
   * Handles the column reordering event triggered when a column is moved by the user.
   */
  onColumnReorder(event: any): void {
    const { columns, newIndex, oldIndex } = event;

    // Prevent reordering of fixed columns
    if (this.fixedColumns.includes(columns[oldIndex].field) || this.fixedColumns.includes(columns[newIndex].field)) {
      return;
    }

    // Update the gridColumns array
    const reorderedColumns = [...this.gridColumns];
    const [movedColumn] = reorderedColumns.splice(oldIndex, 1);
    reorderedColumns.splice(newIndex, 0, movedColumn);

    this.gridColumns = reorderedColumns;
    this.cdr.markForCheck();
  }

  /**
   * Handles column visibility changes from the Kendo Grid.
   */
  updateColumnVisibility(event: any): void {
    const { column, hidden } = event;

    // Update hiddenData array
    const existingIndex = this.hiddenData.findIndex((item: any) => item.field === column.field);

    if (hidden && existingIndex === -1) {
      // Add to hidden columns
      this.hiddenData.push({
        title: column.title,
        field: column.field,
        hidden: true
      });
    } else if (!hidden && existingIndex !== -1) {
      // Remove from hidden columns
      this.hiddenData.splice(existingIndex, 1);
    }

    // Update hiddenFields array
    this.hiddenFields = this.hiddenData.map((col: any) => col.field);

    this.cdr.markForCheck();
  }

  /**
   * Loads the saved column configuration from the backend or localStorage as fallback.
   */
  private loadColumnConfigFromDatabase(): void {
    try {
      // First try to load from backend
      if (this.loginUser && this.loginUser.userId) {
        this.kendoColumnService.getHideFields({
          pageName: 'Roles',
          userID: this.loginUser.userId
        }).subscribe({
          next: (res) => {
            if (!res.isFault && res.Data) {
              this.kendoHide = res.Data;
              this.hiddenData = res.Data.hideData ? JSON.parse(res.Data.hideData) : [];
              this.kendoInitColOrder = res.Data.kendoColOrder ? JSON.parse(res.Data.kendoColOrder) : [];
              this.hiddenFields = this.hiddenData.map((col: any) => col.field);

              // Update grid columns based on the hidden fields
              if (this.grid && this.grid.columns) {
                this.grid.columns.forEach((column: any) => {
                  if (this.hiddenData.some((item: any) => item.title === column.title && item.hidden)) {
                    column.includeInChooser = true;
                    column.hidden = true;
                  } else {
                    column.hidden = false;
                  }
                });
              }

              // Load saved column order and update grid
              this.loadSavedColumnOrder(this.kendoInitColOrder);

              // Also save to localStorage as backup
              this.kendoColumnService.saveToLocalStorage({
                pageName: 'Roles',
                userID: this.loginUser.userId,
                hiddenData: this.hiddenData,
                kendoColOrder: this.kendoInitColOrder
              });
            }
          },
          error: (error) => {
            console.error('Error loading from backend, falling back to localStorage:', error);
            this.loadFromLocalStorageFallback();
          }
        });
      } else {
        // Fallback to localStorage if no user ID
        this.loadFromLocalStorageFallback();
      }
    } catch (error) {
      console.error('Error loading column configuration:', error);
      this.loadFromLocalStorageFallback();
    }
  }

  /**
   * Fallback method to load column configuration from localStorage
   */
  private loadFromLocalStorageFallback(): void {
    try {
      const savedConfig = this.kendoColumnService.getFromLocalStorage('Roles', this.loginUser?.UserId || 0);
      if (savedConfig) {
        this.kendoHide = savedConfig;
        this.hiddenData = savedConfig.hiddenData || [];
        this.kendoInitColOrder = savedConfig.kendoColOrder || [];
        this.hiddenFields = this.hiddenData.map((col: any) => col.field);

        // Update grid columns based on the hidden fields
        if (this.grid && this.grid.columns) {
          this.grid.columns.forEach((column: any) => {
            if (this.hiddenData.some((item: any) => item.title === column.title && item.hidden)) {
              column.includeInChooser = true;
              column.hidden = true;
            } else {
              column.hidden = false;
            }
          });
        }

        // Load saved column order and update grid
        this.loadSavedColumnOrder(this.kendoInitColOrder);
      }
    } catch (error) {
      console.error('Error loading from localStorage fallback:', error);
    }
  }
  //function to form permission from API
  formatPermission(pemissions:any){
    let permArray:any = JSON.parse(pemissions);
    let permData= '';

    each(permArray, (r:any, i:any)=>{
      _.forEach(r, function (value, key) {
      let rArray = [];
      value.indexOf('Read') === -1?'':rArray.push('Read');
      value.indexOf('Write') === -1?'':rArray.push('Write');
      value.indexOf('Delete') === -1?'':rArray.push('Delete');
        let nIndex =value.length>0?rArray.join(', '):'None';
        permData +="<span class='badge badge-light-primary me-2 mt-1'>"+key+" : "+ nIndex+"</span>"
      });

    });
    return permData;
  }

  // function to search the data when there is a typing in the search box
  search(event: any) {
    this.page.pageNumber = 0;
    this.loadTable();
  }
   //function to filter data from search
  filterConfiguration(): any {
    let filter: any = {};
    let searchText: any
    if(this.searchData === null){
      searchText= ' ';
    }else{
      searchText= this.searchData;
    }
    filter.paginate = true;
    filter.Category = this.selectedTab;
    filter.search = searchText.trim();
    return filter;
  }

  // function to get the roles data based on the page selection in Pagination
  serverSideSetPage(event: any) {
    this.page.pageNumber = event - 1;
    this.loadTable();
  }

  // function to get the roles data based on the sort selection
  // Params : Orderby - field to be sorted, OrderDir - asc/desc
  changeOrder(Orderby: string, OrderDir: String) {
    this.defaultOrder = OrderDir === 'desc' ? 'asc' : 'desc';
    let indexColumn = this.columnData.findIndex(cd => cd.prop === Orderby);
    this.columnData[indexColumn].order = this.defaultOrder;
    this.cdr.markForCheck()
    this.defaultOrderBy = Orderby;
    this.page.orderDir = this.defaultOrder;
    this.page.orderBy = Orderby;
    this.loadTable();
  }

  // function to get the roles based on the items per page selection
  pageLimit(num:any) {
    this.pageSize = Number(num);
    this.page.pageNumber = 0;
    if (this.serverSideRowData) this.page.size = Number(num);
    this.loadTable();
  }


 // function to create a new roles
  create() {
    this.edit(0);
  }
  // function to edit a particular role
  edit(id: number) {
    var NgbModalOptions: any = {
      size: 'lg', backdrop: 'static',
      keyboard: false, scrollable: true
    }
    const modalRef = this.modalService.open(RoleEditComponent, NgbModalOptions);
    modalRef.componentInstance.id = id;
    modalRef.componentInstance.permissions = this.permissionArray;
    //get response from edit user modal
    modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {
      if (receivedEntry == true) {
        this.loadTable();
      }
    })
  }
   //function to export particular page data
  exportRowData(rowdata: any) {
    if (rowdata !== undefined) {
      if (rowdata.length > 0) {
        // declare the title and header data for excel
        const tableTitle = 'Roles';
        const headerArray = [
          'Name',
          'Permissions',
          'Status',
          'Description',
          'Created Date',
           'Created Time',
           'Created By User',
           'Modified Date',
           'Modified Time',
           'Modified By User'
        ];

        // get the data for excel in a array format
        const respResult: any = [];
        each(rowdata, (rowdata: any) => {
          const respData = [];
            respData.push( rowdata.Name);
            respData.push(this.formatexcelPermission(rowdata.Permissions));
            respData.push( rowdata.Status);
            respData.push( rowdata.Description);
            respData.push(this.AppService.unixDate(rowdata.CreatedDate));
            respData.push(this.AppService.unixTime(rowdata.CreatedDate));
            respData.push( rowdata.CreatedUserFullName);
            respData.push(this.AppService.unixDate(rowdata.LastUpdatedDate));
            respData.push(this.AppService.unixTime(rowdata.LastUpdatedDate));
            respData.push( rowdata.LastUpdatedUserFullName);
            respResult.push(respData);
        }
        );
        // assign the width for each column
        const colSize = [
          {
            id: 1,
            width: 30
          }, {
            id: 2,
            width: 50
          }, {
            id: 3,
            width: 40
          }, {
            id: 4,
            width: 30
          }, {
            id: 5,
            width: 30
          }, {
            id: 6,
            width: 30
          }, {
            id: 7,
            width: 30
          }, {
            id: 8,
            width: 30
          }, {
            id: 9,
            width: 30
          }, {
            id: 10,
            width: 30
          },];
        this.cdr.markForCheck();
        this.exceljsService.generateExcel(tableTitle, headerArray, respResult, colSize);
      } else {
        this.layoutUtilService.showError('There is no available data to export', '')
      }
    } else {
      this.layoutUtilService.showError('There is no available data to export', '')
    }
  }
  // function to get all the users data from api call and then export those in a excel file
  exportall() {
    const queryparams = {
      paginate: false
    };
    this.httpUtilService.loadingSubject.next(true);
    this.UserService.getAllRolesWithUserInfo(queryparams).pipe(
      map((data: any) => data as any)).subscribe((data:any) => {
        if (!data.isFault) {
          this.exportRowData(data.responseData);
          this.cdr.markForCheck();
          this.httpUtilService.loadingSubject.next(false);
        } else {
          this.httpUtilService.loadingSubject.next(false);
        }
      });

  }
  // function to display the popup to get confirmation to delete roles
  deleteRoles(role:any){
    var NgbModalOptions: any = {
      size: 'md', backdrop: 'static',
      keyboard: false, scrollable: true
    }
    const modalRef = this.modalService.open(ConfirmationDialogComponent, NgbModalOptions);
    modalRef.componentInstance.id = role.roleId;
    modalRef.componentInstance.showClose = true;
    modalRef.componentInstance.description = "Are you sure to delete this role?"
    modalRef.componentInstance.actionButtonText = "Yes"
    modalRef.componentInstance.cancelButtonText = "Cancel"
    modalRef.componentInstance.title = "Delete Role - "+ role.roleName

    modalRef.componentInstance.passEntry.subscribe((receivedEntry: any) => {
      console.log('receivedEntry ',receivedEntry)
      if (receivedEntry.success == true) {
        let reStart = {
          roleId: role.roleId,
          loggedInUserId:this.loginUser.userId
        }
        this.httpUtilService.loadingSubject.next(true);
        this.UserService.deleteRole(reStart).subscribe((data: any) => {
          if (!data.isFault) {
            this.httpUtilService.loadingSubject.next(false);
            this.layoutUtilService.showSuccess(data.responseData.message, '');
            this.ngOnInit()
          }
        })
      }
    })
  }
  //function to format permission for export excel
  formatexcelPermission(pemissions:any){
    let permArray:any = JSON.parse(pemissions);
    let permData= '';

    each(permArray, (r:any, i:any)=>{
      _.forEach(r, function (value, key) {
      let rArray = [];
      value.indexOf('Read') === -1?'':rArray.push('Read');
      value.indexOf('Write') === -1?'':rArray.push('Write');
      value.indexOf('Delete') === -1?'':rArray.push('Delete');
        let nIndex =value.length>0?rArray.join(', '):'None';
        permData +=key+" : "+ nIndex+'\n';

      });

    });
    return permData;
  }

}

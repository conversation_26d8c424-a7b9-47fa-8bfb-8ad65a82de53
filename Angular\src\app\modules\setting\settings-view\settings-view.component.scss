/* Disabled button styling */
.btn-light:disabled,
.btn-light[style*="pointer-events: none"] {
  background-color: #e9ecef !important;
  border-color: #dee2e6 !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* Disabled primary button styling */
.btn-primary:disabled {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #ffffff !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

/* Ensure Add User button (functional) doesn't get disabled styling */
.card-footer .btn-primary:not(:disabled) {
  background-color: #1b7e6c !important; /* Custom color as requested */
  border-color: #1b7e6c !important; /* Custom color as requested */
  color: #ffffff !important;
  cursor: pointer !important;
  opacity: 1 !important;
}

.card-footer .btn-primary:not(:disabled):hover {
  background-color: #156b5a !important; /* Darker shade of #1b7e6c */
  border-color: #156b5a !important; /* Darker shade of #1b7e6c */
  transform: translateY(-1px) !important;
  box-shadow: 0 2px 4px rgba(27, 126, 108, 0.25) !important; /* Shadow using the custom color */
}

/* Hover effect for disabled buttons */
.btn-light:disabled:hover,
.btn-light[style*="pointer-events: none"]:hover {
  background-color: #e9ecef !important;
  border-color: #dee2e6 !important;
  color: #6c757d !important;
  transform: none !important;
  box-shadow: none !important;
}

.btn-primary:disabled:hover {
  background-color: #6c757d !important;
  border-color: #6c757d !important;
  color: #ffffff !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Card styling for disabled sections */
.card-toolbar .btn-light:disabled,
.card-toolbar .btn-light[style*="pointer-events: none"] {
  position: relative;
}

.card-footer .btn-primary:disabled {
  position: relative;
}

/* Optional: Add a subtle indicator that the feature is disabled */
.card-toolbar .btn-light:disabled::after,
.card-toolbar .btn-light[style*="pointer-events: none"]::after {
  content: " (Coming Soon)";
  font-size: 0.75rem;
  font-style: italic;
  color: #6c757d;
}

.card-footer .btn-primary:disabled::after {
  content: " (Coming Soon)";
  font-size: 0.75rem;
  font-style: italic;
  color: #ffffff;
  opacity: 0.8;
}

/* Activity Log Coming Soon styling */
.card-body .text-muted .fas.fa-clock {
  color: #6c757d;
  font-size: 0.875rem;
}

.card-body .text-muted .fst-italic {
  color: #6c757d;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Disable Activity Log section interactions */
.card-custom .card-body {
  opacity: 0.7;
  pointer-events: none;
}

.card-custom .card-body:hover {
  opacity: 0.7;
}

/* Ensure equal height for all cards in first row */
.settings-cards {
  display: flex;
  flex-wrap: wrap;
}

.settings-cards > [class^='col-'],
.settings-cards > [class*=' col-'] {
  display: flex;
}

.settings-cards .card.card-flush {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.settings-cards .card.card-flush .card-body {
  flex: 1 1 auto;
}

.settings-cards .card.card-flush .card-footer {
  margin-top: auto;
}
{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/http-utils.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/forms\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_button_22_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_button_22_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(1, \"i\", 27);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 28)(2, \"div\", 29)(3, \"div\", 30)(4, \"label\");\n    i0.ɵɵtext(5, \"Location\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 31);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 32)(9, \"label\");\n    i0.ɵɵtext(10, \"Project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 31);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 32)(14, \"label\");\n    i0.ɵɵtext(15, \"External project manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 31);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 30)(19, \"label\");\n    i0.ɵɵtext(20, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 31);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 32)(24, \"label\");\n    i0.ɵɵtext(25, \"Start date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 31);\n    i0.ɵɵtext(27);\n    i0.ɵɵpipe(28, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 32)(30, \"label\");\n    i0.ɵɵtext(31, \"End date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\", 31);\n    i0.ɵɵtext(33);\n    i0.ɵɵpipe(34, \"date\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectLocation || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.externalPMNames || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectDescription || \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStartDate ? i0.ɵɵpipeBind2(28, 6, ctx_r1.project.projectStartDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectEndDate ? i0.ɵɵpipeBind2(34, 9, ctx_r1.project.projectEndDate, \"MM/dd/yyyy\") : \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35)(1, \"div\", 36);\n    i0.ɵɵelement(2, \"i\", 37);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 42);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_a_click_2_listener() {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r5.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"select\", 44);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_select_change_10_listener($event) {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r5, $event.target.value));\n    });\n    i0.ɵɵelementStart(11, \"option\", 45);\n    i0.ɵɵtext(12, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 46);\n    i0.ɵɵtext(14, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 47);\n    i0.ɵɵtext(16, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 48);\n    i0.ɵɵtext(18, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 49);\n    i0.ɵɵtext(20, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 50);\n    i0.ɵɵtext(22, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 51);\n    i0.ɵɵtext(24, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 52);\n    i0.ɵɵtext(26, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"td\")(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵpipe(30, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"td\", 53)(32, \"span\", 54);\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitName || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitReviewType || \"\", \"\\n\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r5.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r5.permitAppliedDate ? i0.ɵɵpipeBind2(30, 8, permit_r5.permitAppliedDate, \"MM/dd/yyyy\") : \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(permit_r5.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38)(1, \"table\", 39)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Permit #\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Submitted Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\", 40);\n    i0.ɵɵtext(13, \"Ball in Court\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template, 34, 11, \"tr\", 41);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_ng_container_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, ProjectViewComponent_div_2_ng_container_25_div_1_Template, 5, 0, \"div\", 33)(2, ProjectViewComponent_div_2_ng_container_25_div_2_Template, 16, 1, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 18)(14, \"ul\", 19)(15, \"li\", 20)(16, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_16_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"details\", $event));\n    });\n    i0.ɵɵtext(17, \" Project details \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\", 20)(19, \"a\", 21);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_a_click_19_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.showTab(\"permits\", $event));\n    });\n    i0.ɵɵtext(20, \" Permits list \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"div\", 22);\n    i0.ɵɵtemplate(22, ProjectViewComponent_div_2_button_22_Template, 2, 0, \"button\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 24);\n    i0.ɵɵtemplate(24, ProjectViewComponent_div_2_ng_container_24_Template, 35, 12, \"ng-container\", 25)(25, ProjectViewComponent_div_2_ng_container_25_Template, 3, 2, \"ng-container\", 25);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate2(\"Project # \", ctx_r1.project.internalProjectNumber || \"\", \" - \", ctx_r1.project.projectName || \"\", \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"Active\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(8, _c0, ctx_r1.selectedTab === \"details\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx_r1.selectedTab === \"permits\"));\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab === \"details\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"details\" && ctx_r1.project);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.selectedTab == \"permits\");\n  }\n}\nexport class ProjectViewComponent {\n  router;\n  route;\n  appService;\n  projectsService;\n  httpUtilService;\n  project = null;\n  projectPermits = [];\n  isLoading = false;\n  constructor(router, route, appService, projectsService, httpUtilService) {\n    this.router = router;\n    this.route = route;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.httpUtilService = httpUtilService;\n  }\n  ngOnInit() {\n    const idParam = this.route.snapshot.paramMap.get('id');\n    const projectId = idParam ? Number(idParam) : null;\n    if (projectId) {\n      this.loadProject(projectId);\n      this.loadPermits(projectId);\n    }\n  }\n  loadProject(projectId) {\n    this.isLoading = true;\n    this.httpUtilService.loadingSubject.next(true);\n    this.projectsService.getProject({\n      projectId,\n      loggedInUserId: this.appService.getLoggedInUser()?.userId\n    }).subscribe({\n      next: res => {\n        this.project = res?.responseData?.Project ?? null;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: () => {\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      }\n    });\n  }\n  loadPermits(projectId) {\n    // If there's a dedicated service method, use it; otherwise keep empty list.\n    // This prevents template errors while keeping logic simple.\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    // Navigate to the same view or open an editor as needed; placeholder for now\n    this.router.navigate(['/projects/view', this.project?.projectId ?? '']);\n  }\n  viewPermit(permitId) {\n    // Navigate to permit details route if available\n    this.router.navigate(['/permits/view', permitId]);\n  }\n  onStatusChange(permit, newStatus) {\n    // Placeholder: wire to API if needed\n    if (permit) {\n      permit.internalReviewStatus = newStatus;\n    }\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.HttpUtilsService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", \"status-active\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"mb-2\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [1, \"card-header\", \"border-0\", \"py-2\", \"d-flex\", \"justify-content-between\", \"align-items-center\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-4\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"d-flex\", \"align-items-center\", \"gap-2\", 2, \"margin-right\", \"16px\"], [\"type\", \"button\", \"class\", \"btn btn-link p-0\", \"title\", \"Edit Project\", 3, \"click\", 4, \"ngIf\"], [1, \"card-body\"], [4, \"ngIf\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", 2, \"font-size\", \"1.1rem\"], [1, \"project-details-content\"], [1, \"project-details-grid\"], [1, \"project-detail-item\", \"span-2\"], [1, \"project-value\"], [1, \"project-detail-item\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\"], [1, \"ball-in-court-col\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"badge\", \"badge-green-light\", \"ms-1\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"], [1, \"ball-in-court-cell\"], [1, \"wrap-text\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 26, 12, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i5.NgClass, i5.NgForOf, i5.NgIf, i6.NgSelectOption, i6.ɵNgSelectMultipleOption, i5.DatePipe],\n    styles: [\".project-view-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0;\\n}\\n\\n.project-view-container[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  padding-bottom: 0.25rem;\\n}\\n.project-view-container[_ngcontent-%COMP%]   .card-body[_ngcontent-%COMP%] {\\n  padding-top: 0.5rem;\\n}\\n\\n.project-details-header[_ngcontent-%COMP%] {\\n  padding: 0 1.5rem;\\n  border-bottom: 1px solid #e5eaee;\\n  background: transparent;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  padding-top: 0.5rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #3f4254;\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 0.5rem;\\n  align-items: center;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%]   .btn-sm[_ngcontent-%COMP%] {\\n  font-size: 0.875rem !important;\\n  padding: 0.375rem 0.75rem !important;\\n  line-height: 1.5 !important;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 0.3rem;\\n  padding: 0.15rem 0.5rem;\\n  border-radius: 0.55rem;\\n  background-color: #f3f6f9;\\n  color: #3f4254;\\n  border: 1px solid #e5eaee;\\n  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);\\n  font-weight: 600;\\n  font-size: 0.8rem;\\n  line-height: 1;\\n  transition: background-color 0.2s ease, box-shadow 0.2s ease, transform 0.02s ease;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  color: #5e6e82;\\n  font-size: 0.75rem;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:hover, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:hover {\\n  background-color: #eef2f7;\\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.07);\\n  text-decoration: none;\\n}\\n.project-details-header[_ngcontent-%COMP%]   .back-button[_ngcontent-%COMP%]:active, \\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]:active {\\n  transform: translateY(1px);\\n  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);\\n}\\n.project-details-header[_ngcontent-%COMP%]   .portal-button[_ngcontent-%COMP%]   i[_ngcontent-%COMP%] {\\n  margin-right: 0.25rem;\\n}\\n\\n.project-details-content[_ngcontent-%COMP%] {\\n  padding: 1rem 1.5rem;\\n}\\n\\n.project-summary[_ngcontent-%COMP%]   .summary-left[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.35rem;\\n}\\n.project-summary[_ngcontent-%COMP%]   .project-title-large[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 800;\\n  color: #111827;\\n  margin-bottom: 0.25rem;\\n}\\n.project-summary[_ngcontent-%COMP%]   .detail-line[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  color: #374151;\\n}\\n.project-summary[_ngcontent-%COMP%]   .label[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #111827;\\n  margin-right: 0.35rem;\\n}\\n.project-summary[_ngcontent-%COMP%]   .value[_ngcontent-%COMP%] {\\n  color: #374151;\\n}\\n.project-summary[_ngcontent-%COMP%]   .summary-right[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n}\\n\\n.project-details-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n  gap: 1.5rem;\\n}\\n\\n.project-detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   label[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  display: block; \\n\\n  margin-bottom: 0.15rem;\\n  font-size: 15px;\\n  display: block;\\n  line-height: 1.2;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%], \\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  margin-top: 0.1rem;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-label[_ngcontent-%COMP%] {\\n  font-weight: 600; \\n\\n  display: block; \\n\\n  margin-bottom: 0.25rem; \\n\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .badge-green-light[_ngcontent-%COMP%] {\\n  background-color: #42c761; \\n\\n  color: #155724; \\n\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-value[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  color: #3f4254;\\n  font-weight: 500;\\n  margin: 0; \\n\\n  padding: 0.25rem 0; \\n\\n  border-bottom: none;\\n}\\n.project-detail-item[_ngcontent-%COMP%]   .project-status[_ngcontent-%COMP%] {\\n  display: block;\\n  vertical-align: top;\\n  padding: 0.5rem 0;\\n  font-size: 1rem;\\n  font-weight: 600;\\n  text-align: left;\\n  background: transparent;\\n  border: none;\\n  min-width: 0;\\n  border-radius: 0;\\n}\\n\\n.project-detail-item.span-2[_ngcontent-%COMP%] {\\n  grid-column: span 2;\\n}\\n\\n.status-active[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-inactive[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.status-completed[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n\\n.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n\\n.status-cancelled[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n\\n.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.table-responsive[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n  padding: 0 0.5rem 0 0.5rem;\\n  margin-top: 0;\\n}\\n\\n.table[_ngcontent-%COMP%] {\\n  margin-bottom: 0;\\n  overflow: hidden;\\n  table-layout: fixed;\\n  width: 100%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #3f4254;\\n  border-bottom: 2px solid #e5eaee;\\n  padding: 1rem 0.75rem;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%] {\\n  transition: none;\\n  border-bottom: 1px solid #e5eaee;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%] {\\n  padding: 0.75rem;\\n  vertical-align: middle;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-number-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-description-col[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-type-col[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.permit-status-col[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-number-cell[_ngcontent-%COMP%] {\\n  width: 25%;\\n  white-space: nowrap;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-description-cell[_ngcontent-%COMP%] {\\n  width: 40%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-type-cell[_ngcontent-%COMP%] {\\n  width: 20%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.permit-status-cell[_ngcontent-%COMP%] {\\n  width: 15%;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child {\\n  width: 30%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child, \\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   tr[_ngcontent-%COMP%]:hover   td[_ngcontent-%COMP%]:first-child   a[_ngcontent-%COMP%] {\\n  text-decoration: none;\\n}\\n.table[_ngcontent-%COMP%]   thead[_ngcontent-%COMP%]   th.ball-in-court-col[_ngcontent-%COMP%] {\\n  width: 25%;\\n}\\n.table[_ngcontent-%COMP%]   tbody[_ngcontent-%COMP%]   td.ball-in-court-cell[_ngcontent-%COMP%] {\\n  white-space: normal;\\n  word-break: break-word;\\n  overflow-wrap: anywhere;\\n}\\n\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%] {\\n  color: var(--bs-primary, #0d6efd);\\n  text-decoration: none;\\n  font-weight: 700;\\n  cursor: pointer;\\n  transition: color 0.15s ease, -webkit-text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease;\\n  transition: color 0.15s ease, text-decoration 0.15s ease, -webkit-text-decoration 0.15s ease;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:hover {\\n  color: #0b5ed7;\\n  text-decoration: underline;\\n}\\n.permit-number-cell[_ngcontent-%COMP%]   .permit-number-link[_ngcontent-%COMP%]:focus {\\n  outline: none;\\n  box-shadow: 0 0 0 2px rgba(24, 125, 228, 0.25);\\n  border-radius: 0.25rem;\\n}\\n\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  font-weight: 600;\\n  border: 1px solid transparent;\\n  text-transform: uppercase;\\n  letter-spacing: 0.05rem;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-pending[_ngcontent-%COMP%] {\\n  background-color: #fff3e0;\\n  color: #e65100;\\n  border: 1px solid #ffcc02;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-under-review[_ngcontent-%COMP%] {\\n  background-color: #e8eaf6;\\n  color: #3949ab;\\n  border: 1px solid #c5cae9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-rejected[_ngcontent-%COMP%] {\\n  background-color: #ffebee;\\n  color: #c62828;\\n  border: 1px solid #ffcdd2;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-submitted[_ngcontent-%COMP%] {\\n  background-color: #e3f2fd;\\n  color: #1565c0;\\n  border: 1px solid #bbdefb;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-resubmit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-conditional-approval[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-void[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-complete[_ngcontent-%COMP%] {\\n  background-color: #e8f5e8;\\n  color: #1b5e20;\\n  border: 1px solid #c8e6c9;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-approved-w-conditions[_ngcontent-%COMP%] {\\n  background-color: #f3e5f5;\\n  color: #7b1fa2;\\n  border: 1px solid #e1bee7;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-requires-re-submit[_ngcontent-%COMP%] {\\n  background-color: #fff8e1;\\n  color: #f57f17;\\n  border: 1px solid #ffecb3;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-unknown[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n.permit-status-cell[_ngcontent-%COMP%]   .status-text.status-n-a[_ngcontent-%COMP%] {\\n  background-color: #f5f5f5;\\n  color: #757575;\\n  border: 1px solid #e0e0e0;\\n}\\n\\n.permit-description-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-type-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%], \\n.permit-status-cell[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  max-width: 100%;\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  font-size: 0.75rem;\\n  padding: 0.375rem 0.75rem;\\n  border-radius: 0.375rem;\\n  font-weight: 600;\\n  transition: all 0.2s ease;\\n}\\n.permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);\\n}\\n\\n.title-wrap[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 0.25rem;\\n}\\n\\n.title-line[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: baseline;\\n  gap: 0.75rem;\\n}\\n\\n.project-title[_ngcontent-%COMP%] {\\n  font-size: 1.05rem;\\n  font-weight: 700;\\n  color: #181c32;\\n}\\n\\n.status-text[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  color: #3f4254;\\n  padding: 0.25rem 0.5rem;\\n  border-radius: 0.25rem;\\n  border: 1px solid transparent;\\n  width: -moz-fit-content;\\n  width: fit-content;\\n}\\n\\n.project-number-line[_ngcontent-%COMP%] {\\n  font-size: 0.85rem;\\n  color: #6c7293;\\n  padding-bottom: 0.25rem;\\n}\\n\\n.fullscreen-loading-overlay[_ngcontent-%COMP%] {\\n  position: fixed;\\n  top: 0;\\n  left: 0;\\n  width: 100vw;\\n  height: 100vh;\\n  background: rgba(255, 255, 255, 0.9);\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  z-index: 9999;\\n}\\n.fullscreen-loading-overlay[_ngcontent-%COMP%]   .loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  background: white;\\n  padding: 2rem;\\n  border-radius: 8px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n@media (max-width: 768px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n    gap: 1rem;\\n  }\\n  .project-detail-item.span-2[_ngcontent-%COMP%] {\\n    grid-column: auto;\\n  }\\n  .table-responsive[_ngcontent-%COMP%] {\\n    font-size: 0.875rem;\\n  }\\n  .permit-actions-cell[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n    font-size: 0.7rem;\\n    padding: 0.25rem 0.5rem;\\n  }\\n}\\n@media (max-width: 576px) {\\n  .project-details-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n    gap: 0.75rem;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 1rem;\\n    align-items: flex-start;\\n  }\\n  .project-details-header[_ngcontent-%COMP%]   .button-group[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: flex-end;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProjectViewComponent_div_2_button_22_Template_button_click_0_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "editProject", "ɵɵelement", "ɵɵelementContainerStart", "ɵɵadvance", "ɵɵtextInterpolate", "project", "projectLocation", "internalProjectManagerName", "internalProjectManager", "externalPMNames", "projectDescription", "projectStartDate", "ɵɵpipeBind2", "projectEndDate", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_a_click_2_listener", "permit_r5", "_r4", "$implicit", "viewPermit", "permitId", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template_select_change_10_listener", "$event", "onStatusChange", "target", "value", "ɵɵtextInterpolate1", "permitName", "permitReviewType", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "permitAppliedDate", "attentionReason", "ɵɵtemplate", "ProjectViewComponent_div_2_ng_container_25_div_2_tr_15_Template", "projectPermits", "ProjectViewComponent_div_2_ng_container_25_div_1_Template", "ProjectViewComponent_div_2_ng_container_25_div_2_Template", "length", "ProjectViewComponent_div_2_Template_button_click_10_listener", "_r1", "goBack", "ProjectViewComponent_div_2_Template_a_click_16_listener", "showTab", "ProjectViewComponent_div_2_Template_a_click_19_listener", "ProjectViewComponent_div_2_button_22_Template", "ProjectViewComponent_div_2_ng_container_24_Template", "ProjectViewComponent_div_2_ng_container_25_Template", "ɵɵtextInterpolate2", "internalProjectNumber", "projectName", "projectStatus", "ɵɵpureFunction1", "_c0", "selectedTab", "ProjectViewComponent", "router", "route", "appService", "projectsService", "httpUtilService", "constructor", "ngOnInit", "idParam", "snapshot", "paramMap", "get", "projectId", "Number", "loadProject", "loadPermits", "loadingSubject", "next", "getProject", "loggedInUserId", "getLoggedInUser", "userId", "subscribe", "res", "responseData", "Project", "error", "navigate", "permit", "newStatus", "ɵɵdirectiveInject", "i1", "Router", "ActivatedRoute", "i2", "AppService", "i3", "ProjectsService", "i4", "HttpUtilsService", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\r\nimport { ActivatedRoute, Router } from '@angular/router';\r\nimport { AppService } from '../../services/app.service';\r\nimport { ProjectsService } from '../../services/projects.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\n\r\n@Component({\r\n  selector: 'app-project-view',\r\n  templateUrl: './project-view.component.html',\r\n  styleUrls: ['./project-view.component.scss']\r\n})\r\nexport class ProjectViewComponent implements OnInit {\r\n  project: any = null;\r\n  projectPermits: any[] = [];\r\n  isLoading = false;\r\n\r\n  constructor(\r\n    private router: Router,\r\n    private route: ActivatedRoute,\r\n    private appService: AppService,\r\n    private projectsService: ProjectsService,\r\n    private httpUtilService: HttpUtilsService\r\n  ) {}\r\n\r\n  ngOnInit(): void {\r\n    const idParam = this.route.snapshot.paramMap.get('id');\r\n    const projectId = idParam ? Number(idParam) : null;\r\n    if (projectId) {\r\n      this.loadProject(projectId);\r\n      this.loadPermits(projectId);\r\n    }\r\n  }\r\n\r\n  loadProject(projectId: number): void {\r\n    this.isLoading = true;\r\n    this.httpUtilService.loadingSubject.next(true);\r\n    this.projectsService.getProject({ projectId, loggedInUserId: this.appService.getLoggedInUser()?.userId })\r\n      .subscribe({\r\n        next: (res: any) => {\r\n          this.project = res?.responseData?.Project ?? null;\r\n          this.isLoading = false;\r\n          this.httpUtilService.loadingSubject.next(false);\r\n        },\r\n        error: () => {\r\n          this.isLoading = false;\r\n          this.httpUtilService.loadingSubject.next(false);\r\n        }\r\n      });\r\n  }\r\n\r\n  loadPermits(projectId: number): void {\r\n    // If there's a dedicated service method, use it; otherwise keep empty list.\r\n    // This prevents template errors while keeping logic simple.\r\n  }\r\n\r\n  goBack(): void {\r\n    this.router.navigate(['/projects/list']);\r\n  }\r\n\r\n  editProject(): void {\r\n    // Navigate to the same view or open an editor as needed; placeholder for now\r\n    this.router.navigate(['/projects/view', this.project?.projectId ?? '']);\r\n  }\r\n\r\n  viewPermit(permitId: number): void {\r\n    // Navigate to permit details route if available\r\n    this.router.navigate(['/permits/view', permitId]);\r\n  }\r\n\r\n  onStatusChange(permit: any, newStatus: string): void {\r\n    // Placeholder: wire to API if needed\r\n    if (permit) {\r\n      permit.internalReviewStatus = newStatus;\r\n    }\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div class=\"project-view-container\">\n  <!-- Project Details Card -->\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"project\">\n    <!-- Project Details Header -->\n    <div class=\"project-details-header\">\n      <div class=\"header-content\">\n        <div class=\"title-wrap\">\n          <div class=\"title-line\">\n            <span class=\"project-title\">Project # {{ project.internalProjectNumber || \"\" }} - {{ project.projectName || \"\" }}</span>\n            <span class=\"status-text status-active\">{{ project.projectStatus || \"Active\" }}</span>\n          </div>\n        </div>\n        <div class=\"button-group\">\n          <!-- <button type=\"button\" class=\"btn portal-button\" (click)=\"editProject()\">\n            <i class=\"fa fa-pencil\"></i>Edit\n          </button> -->\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center mb-2\" (click)=\"goBack()\">\n            <i class=\"fas fa-arrow-left me-2\"></i>\n            Back\n          </button>\n        </div>\n      </div>\n    </div>\n    <!-- Card Header with Tabs -->\n    <div class=\"card-header border-0 py-2 d-flex justify-content-between align-items-center\">\n      <!-- Tabs -->\n      <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-4 fw-bold flex-nowrap\">\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'details' }\"\n            (click)=\"showTab('details', $event)\">\n            Project details\n          </a>\n        </li>\n        <li class=\"nav-item\">\n          <a class=\"nav-link text-active-primary me-6 cursor-pointer\" [ngClass]=\"{ active: selectedTab === 'permits' }\"\n            (click)=\"showTab('permits', $event)\">\n            Permits list\n          </a>\n        </li>\n      </ul>\n       <div class=\"d-flex align-items-center gap-2\" style=\"margin-right: 16px;\">\n        <!-- Edit icon - only show when permit details tab is active -->\n        <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"editProject()\" *ngIf=\"selectedTab === 'details'\" title=\"Edit Project\">\n          <i class=\"fas fa-edit text-primary\" style=\"font-size: 1.1rem;\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Card Body with Tab Content -->\n    <div class=\"card-body\">\n      <!-- Project Details Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'details' && project\">\n        <div class=\"project-details-content\">\n          <div class=\"project-details-grid\">\n            <div class=\"project-detail-item span-2\">\n           <label >Location</label>\n              <span class=\"project-value\">{{ project.projectLocation || \"\" }}</span>\n            </div>\n             <div class=\"project-detail-item\">\n               <label>Project manager</label>\n               <span class=\"project-value\">{{ project.internalProjectManagerName || project.internalProjectManager || \"\" }}</span>\n             </div>\n            <div class=\"project-detail-item\">\n              <label>External project manager</label>\n              <span class=\"project-value\">{{ project.externalPMNames || \"\" }}</span>\n            </div>\n            <div class=\"project-detail-item span-2\">\n              <label>Description</label>\n              <span class=\"project-value\">{{ project.projectDescription || \"\" }}</span>\n            </div>\n            <div class=\"project-detail-item\">\n              <label>Start date</label>\n              <span class=\"project-value\">{{\n                project.projectStartDate\n                ? (project.projectStartDate | date : \"MM/dd/yyyy\")\n                : \"\"\n              }}</span>\n            </div>\n            <div class=\"project-detail-item\">\n              <label>End date</label>\n              <span class=\"project-value\">{{\n                project.projectEndDate\n                ? (project.projectEndDate | date : \"MM/dd/yyyy\")\n                : \"\"\n              }}</span>\n            </div>\n           </div>\n         </div>\n       </ng-container>\n\n      <!-- Permits List Tab Content -->\n      <ng-container *ngIf=\"selectedTab == 'permits'\">\n        <!-- Empty State for Permits -->\n        <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"projectPermits.length === 0\">\n          <div class=\"text-center\">\n            <i class=\"fas fa-file-alt fa-3x mb-3\"></i>\n            <p>No permits found for this project.</p>\n          </div>\n        </div>\n\n        <!-- Permits Table -->\n        <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\n          <table class=\"table\">\n            <thead>\n              <tr>\n                <th>Permit Name</th>\n                <th>Permit #</th>\n                <th>Status</th>\n                <th>Submitted Date</th>\n                <th class=\"ball-in-court-col\">Ball in Court</th>\n              </tr>\n            </thead>\n            <tbody>\n              <tr *ngFor=\"let permit of projectPermits\">\n                <td>\n                  <a \n                    class=\"fw-bold\" \n                    (click)=\"viewPermit(permit.permitId)\" \n                    title=\"View Permit\"\n                    aria-label=\"View Permit\"\n                  >\n                    {{ permit.permitName || \"\" }}  \n                  </a>\n              <span class=\"badge badge-green-light ms-1\">\n  {{ permit.permitReviewType || \"\" }}\n</span>\n                </td>\n                <td>\n                  <span>{{ permit.permitNumber || \"\" }}</span>\n                </td>\n                <td>\n                  <select class=\"form-select form-select-sm w-auto\"\n                          [value]=\"permit.internalReviewStatus || ''\"\n                          (change)=\"onStatusChange(permit, $any($event.target).value)\"\n                          [disabled]=\"isLoading\">\n                    <option [value]=\"''\" disabled>Select status</option>\n                    <option value=\"Approved\">Approved</option>\n                    <option value=\"Pacifica Verification\">Pacifica Verification</option>\n                    <option value=\"Dis-Approved\">Dis-Approved</option>\n                    <option value=\"Pending\">Pending</option>\n                    <option value=\"Not Required\">Not Required</option>\n                    <option value=\"In Review\">In Review</option>\n                    <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\n                  </select>\n                </td>\n                <td>\n                  <span>{{ permit.permitAppliedDate ? (permit.permitAppliedDate | date : 'MM/dd/yyyy') : '' }}</span>\n                </td>\n                <td class=\"ball-in-court-cell\">\n                  <span class=\"wrap-text\">{{ permit.attentionReason || '' }}</span>\n                </td>\n              </tr>\n            </tbody>\n          </table>\n        </div>\n      </ng-container>\n    </div>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;;;;ICIMA,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA4CEH,EAAA,CAAAC,cAAA,iBAA8H;IAA/ED,EAAA,CAAAI,UAAA,mBAAAC,sEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpEX,EAAA,CAAAY,SAAA,YAAmE;IACrEZ,EAAA,CAAAG,YAAA,EAAS;;;;;IAOXH,EAAA,CAAAa,uBAAA,GAA0D;IAIrDb,EAHH,CAAAC,cAAA,cAAqC,cACD,cACQ,YACjC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEHH,EADF,CAAAC,cAAA,cAAiC,YACxB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgF;IAC9GF,EAD8G,CAAAG,YAAA,EAAO,EAC/G;IAELH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvCH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAmC;IACjEF,EADiE,CAAAG,YAAA,EAAO,EAClE;IAEJH,EADF,CAAAC,cAAA,eAAwC,aAC/B;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1BH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IACpEF,EADoE,CAAAG,YAAA,EAAO,EACrE;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IACJF,EADI,CAAAG,YAAA,EAAO,EACL;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAI1B;;IAGPF,EAHO,CAAAG,YAAA,EAAO,EACL,EACD,EACF;;;;;IA/B2BH,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAC,eAAA,OAAmC;IAIlCjB,EAAA,CAAAc,SAAA,GAAgF;IAAhFd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAE,0BAAA,IAAAV,MAAA,CAAAQ,OAAA,CAAAG,sBAAA,OAAgF;IAIjFnB,EAAA,CAAAc,SAAA,GAAmC;IAAnCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAI,eAAA,OAAmC;IAInCpB,EAAA,CAAAc,SAAA,GAAsC;IAAtCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAK,kBAAA,OAAsC;IAItCrB,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAM,gBAAA,GAAAtB,EAAA,CAAAuB,WAAA,QAAAf,MAAA,CAAAQ,OAAA,CAAAM,gBAAA,qBAI1B;IAI0BtB,EAAA,CAAAc,SAAA,GAI1B;IAJ0Bd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAAQ,cAAA,GAAAxB,EAAA,CAAAuB,WAAA,QAAAf,MAAA,CAAAQ,OAAA,CAAAQ,cAAA,qBAI1B;;;;;IAUNxB,EADF,CAAAC,cAAA,cAAkH,cACvF;IACvBD,EAAA,CAAAY,SAAA,YAA0C;IAC1CZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACrC,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAI,UAAA,mBAAAqB,mFAAA;MAAA,MAAAC,SAAA,GAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAqB,UAAA,CAAAH,SAAA,CAAAI,QAAA,CAA2B;IAAA,EAAC;IAIrC9B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACRH,EAAA,CAAAC,cAAA,eAA2C;IACvDD,EAAA,CAAAE,MAAA,GACF;IACgBF,EADhB,CAAAG,YAAA,EAAO,EACc;IAEHH,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACzC;IAEHH,EADF,CAAAC,cAAA,SAAI,kBAI6B;IADvBD,EAAA,CAAAI,UAAA,oBAAA2B,0FAAAC,MAAA;MAAA,MAAAN,SAAA,GAAA1B,EAAA,CAAAM,aAAA,CAAAqB,GAAA,EAAAC,SAAA;MAAA,MAAApB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAyB,cAAA,CAAAP,SAAA,EAAAM,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElEnC,EAAA,CAAAC,cAAA,kBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAAsF;;IAC9FF,EAD8F,CAAAG,YAAA,EAAO,EAChG;IAEHH,EADF,CAAAC,cAAA,cAA+B,gBACL;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE9DF,EAF8D,CAAAG,YAAA,EAAO,EAC9D,EACF;;;;;IA9BCH,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAoC,kBAAA,MAAAV,SAAA,CAAAW,UAAA,YACF;IAEhBrC,EAAA,CAAAc,SAAA,GACF;IADEd,EAAA,CAAAoC,kBAAA,MAAAV,SAAA,CAAAY,gBAAA,aACF;IAGwBtC,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAa,YAAA,OAA+B;IAI7BvC,EAAA,CAAAc,SAAA,GAA2C;IAE3Cd,EAFA,CAAAwC,UAAA,UAAAd,SAAA,CAAAe,oBAAA,OAA2C,aAAAjC,MAAA,CAAAkC,SAAA,CAErB;IACpB1C,EAAA,CAAAc,SAAA,EAAY;IAAZd,EAAA,CAAAwC,UAAA,aAAY;IAWhBxC,EAAA,CAAAc,SAAA,IAAsF;IAAtFd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAiB,iBAAA,GAAA3C,EAAA,CAAAuB,WAAA,QAAAG,SAAA,CAAAiB,iBAAA,qBAAsF;IAGpE3C,EAAA,CAAAc,SAAA,GAAkC;IAAlCd,EAAA,CAAAe,iBAAA,CAAAW,SAAA,CAAAkB,eAAA,OAAkC;;;;;IA5C5D5C,EAJR,CAAAC,cAAA,cAAgE,gBACzC,YACZ,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACvBH,EAAA,CAAAC,cAAA,cAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAK,EAC7C,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAA6C,UAAA,KAAAC,+DAAA,mBAA0C;IAyChD9C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAzCuBH,EAAA,CAAAc,SAAA,IAAiB;IAAjBd,EAAA,CAAAwC,UAAA,YAAAhC,MAAA,CAAAuC,cAAA,CAAiB;;;;;IAtBhD/C,EAAA,CAAAa,uBAAA,GAA+C;IAU7Cb,EARA,CAAA6C,UAAA,IAAAG,yDAAA,kBAAkH,IAAAC,yDAAA,mBAQlD;;;;;IARejD,EAAA,CAAAc,SAAA,EAAiC;IAAjCd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAAuC,cAAA,CAAAG,MAAA,OAAiC;IAQjFlD,EAAA,CAAAc,SAAA,EAA+B;IAA/Bd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAAuC,cAAA,CAAAG,MAAA,KAA+B;;;;;;IA7F1DlD,EANV,CAAAC,cAAA,aAAsD,aAEhB,cACN,cACF,cACE,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAqF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxHH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAEnFF,EAFmF,CAAAG,YAAA,EAAO,EAClF,EACF;IAKJH,EAJF,CAAAC,cAAA,cAA0B,kBAIqF;IAAnBD,EAAA,CAAAI,UAAA,mBAAA+C,6DAAA;MAAAnD,EAAA,CAAAM,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA6C,MAAA,EAAQ;IAAA,EAAC;IAC1GrD,EAAA,CAAAY,SAAA,aAAsC;IACtCZ,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAMAH,EAJN,CAAAC,cAAA,eAAyF,cAEgB,cAChF,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAkD,wDAAAtB,MAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+C,OAAA,CAAQ,SAAS,EAAAvB,MAAA,CAAS;IAAA,EAAC;IACpChC,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACD;IAEHH,EADF,CAAAC,cAAA,cAAqB,aAEoB;IAArCD,EAAA,CAAAI,UAAA,mBAAAoD,wDAAAxB,MAAA;MAAAhC,EAAA,CAAAM,aAAA,CAAA8C,GAAA;MAAA,MAAA5C,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAA+C,OAAA,CAAQ,SAAS,EAAAvB,MAAA,CAAS;IAAA,EAAC;IACpChC,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAG,YAAA,EAAI,EACD,EACF;IACJH,EAAA,CAAAC,cAAA,eAAyE;IAExED,EAAA,CAAA6C,UAAA,KAAAY,6CAAA,qBAA8H;IAIlIzD,EADE,CAAAG,YAAA,EAAM,EACF;IAGNH,EAAA,CAAAC,cAAA,eAAuB;IA0CrBD,EAxCA,CAAA6C,UAAA,KAAAa,mDAAA,6BAA0D,KAAAC,mDAAA,2BAwCX;IAkEnD3D,EADE,CAAAG,YAAA,EAAM,EACF;;;;IArJgCH,EAAA,CAAAc,SAAA,GAAqF;IAArFd,EAAA,CAAA4D,kBAAA,eAAApD,MAAA,CAAAQ,OAAA,CAAA6C,qBAAA,eAAArD,MAAA,CAAAQ,OAAA,CAAA8C,WAAA,WAAqF;IACzE9D,EAAA,CAAAc,SAAA,GAAuC;IAAvCd,EAAA,CAAAe,iBAAA,CAAAP,MAAA,CAAAQ,OAAA,CAAA+C,aAAA,aAAuC;IAmBrB/D,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAAgE,eAAA,IAAAC,GAAA,EAAAzD,MAAA,CAAA0D,WAAA,gBAAiD;IAMjDlE,EAAA,CAAAc,SAAA,GAAiD;IAAjDd,EAAA,CAAAwC,UAAA,YAAAxC,EAAA,CAAAgE,eAAA,KAAAC,GAAA,EAAAzD,MAAA,CAAA0D,WAAA,gBAAiD;IAQvClE,EAAA,CAAAc,SAAA,GAA+B;IAA/Bd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAA0D,WAAA,eAA+B;IAS1FlE,EAAA,CAAAc,SAAA,GAAyC;IAAzCd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAA0D,WAAA,iBAAA1D,MAAA,CAAAQ,OAAA,CAAyC;IAwCzChB,EAAA,CAAAc,SAAA,EAA8B;IAA9Bd,EAAA,CAAAwC,UAAA,SAAAhC,MAAA,CAAA0D,WAAA,cAA8B;;;AD1FnD,OAAM,MAAOC,oBAAoB;EAMrBC,MAAA;EACAC,KAAA;EACAC,UAAA;EACAC,eAAA;EACAC,eAAA;EATVxD,OAAO,GAAQ,IAAI;EACnB+B,cAAc,GAAU,EAAE;EAC1BL,SAAS,GAAG,KAAK;EAEjB+B,YACUL,MAAc,EACdC,KAAqB,EACrBC,UAAsB,EACtBC,eAAgC,EAChCC,eAAiC;IAJjC,KAAAJ,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;EACtB;EAEHE,QAAQA,CAAA;IACN,MAAMC,OAAO,GAAG,IAAI,CAACN,KAAK,CAACO,QAAQ,CAACC,QAAQ,CAACC,GAAG,CAAC,IAAI,CAAC;IACtD,MAAMC,SAAS,GAAGJ,OAAO,GAAGK,MAAM,CAACL,OAAO,CAAC,GAAG,IAAI;IAClD,IAAII,SAAS,EAAE;MACb,IAAI,CAACE,WAAW,CAACF,SAAS,CAAC;MAC3B,IAAI,CAACG,WAAW,CAACH,SAAS,CAAC;IAC7B;EACF;EAEAE,WAAWA,CAACF,SAAiB;IAC3B,IAAI,CAACrC,SAAS,GAAG,IAAI;IACrB,IAAI,CAAC8B,eAAe,CAACW,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACb,eAAe,CAACc,UAAU,CAAC;MAAEN,SAAS;MAAEO,cAAc,EAAE,IAAI,CAAChB,UAAU,CAACiB,eAAe,EAAE,EAAEC;IAAM,CAAE,CAAC,CACtGC,SAAS,CAAC;MACTL,IAAI,EAAGM,GAAQ,IAAI;QACjB,IAAI,CAAC1E,OAAO,GAAG0E,GAAG,EAAEC,YAAY,EAAEC,OAAO,IAAI,IAAI;QACjD,IAAI,CAAClD,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC8B,eAAe,CAACW,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDS,KAAK,EAAEA,CAAA,KAAK;QACV,IAAI,CAACnD,SAAS,GAAG,KAAK;QACtB,IAAI,CAAC8B,eAAe,CAACW,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD;KACD,CAAC;EACN;EAEAF,WAAWA,CAACH,SAAiB;IAC3B;IACA;EAAA;EAGF1B,MAAMA,CAAA;IACJ,IAAI,CAACe,MAAM,CAAC0B,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEAnF,WAAWA,CAAA;IACT;IACA,IAAI,CAACyD,MAAM,CAAC0B,QAAQ,CAAC,CAAC,gBAAgB,EAAE,IAAI,CAAC9E,OAAO,EAAE+D,SAAS,IAAI,EAAE,CAAC,CAAC;EACzE;EAEAlD,UAAUA,CAACC,QAAgB;IACzB;IACA,IAAI,CAACsC,MAAM,CAAC0B,QAAQ,CAAC,CAAC,eAAe,EAAEhE,QAAQ,CAAC,CAAC;EACnD;EAEAG,cAAcA,CAAC8D,MAAW,EAAEC,SAAiB;IAC3C;IACA,IAAID,MAAM,EAAE;MACVA,MAAM,CAACtD,oBAAoB,GAAGuD,SAAS;IACzC;EACF;;qCA/DW7B,oBAAoB,EAAAnE,EAAA,CAAAiG,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAnG,EAAA,CAAAiG,iBAAA,CAAAC,EAAA,CAAAE,cAAA,GAAApG,EAAA,CAAAiG,iBAAA,CAAAI,EAAA,CAAAC,UAAA,GAAAtG,EAAA,CAAAiG,iBAAA,CAAAM,EAAA,CAAAC,eAAA,GAAAxG,EAAA,CAAAiG,iBAAA,CAAAQ,EAAA,CAAAC,gBAAA;EAAA;;UAApBvC,oBAAoB;IAAAwC,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCVjCjH,EAAA,CAAA6C,UAAA,IAAAsE,mCAAA,iBAA0D;QAS1DnH,EAAA,CAAAC,cAAA,aAAoC;QAElCD,EAAA,CAAA6C,UAAA,IAAAuE,mCAAA,mBAAsD;QA4JxDpH,EAAA,CAAAG,YAAA,EAAM;;;QAvKAH,EAAA,CAAAwC,UAAA,SAAA0E,GAAA,CAAAxE,SAAA,CAAe;QAWoB1C,EAAA,CAAAc,SAAA,GAAa;QAAbd,EAAA,CAAAwC,UAAA,SAAA0E,GAAA,CAAAlG,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
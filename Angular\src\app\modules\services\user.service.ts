import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, from, mergeMap, Observable, of, delay } from 'rxjs';
import { AppSettings } from 'src/app/app.settings';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  constructor(private http: HttpClient) {}
  private handleError<T>(operation = 'operation', result?: any) {
    return (error: any): Observable<any> => {
      // TODO: send the error to remote logging infrastructure
      console.error(error); // log to console instead

      // Let the app keep running by returning an empty result.
      return from(result);
    };
  }

  public getAllUsers(queryParams: any): Observable<any> {
    // Prepare the request body with Kendo UI specific parameters
    const requestBody = {
      pageSize: queryParams.pageSize,
      pageNumber: queryParams.pageNumber,
      sortField: queryParams.sortField,
      sortOrder: queryParams.sortOrder,
      paginate: true,
      search: queryParams.filter?.search || '',
      columnFilter: queryParams.filter?.columnFilter || [],
    };

    return this.http
      .post(`${AppSettings.REST_ENDPOINT}/getAllUsers`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getAllUsers:', err);
          return of({
            error: { isFault: true },
            message: 'Error retrieving users',
          });
        })
      );
  }

  // New method specifically for Kendo UI Grid operations
  public getUsersForKendoGrid(state: any): Observable<any> {
    const requestBody = {
      take: state.take || 10,
      skip: state.skip || 0,
      sort: state.sort || [],
      filter: state.filter || { logic: 'and', filters: [] },
      search: state.search || '',
    };

    return this.http
      .post(`${AppSettings.REST_ENDPOINT}/getUsersForKendoGrid`, requestBody)
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          console.error('Error in getUsersForKendoGrid:', err);
          return of({
            data: [],
            total: 0,
            errors: ['Error retrieving users'],
          });
        })
      );
  }

  public createUser(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/createUser', data);
  }

  public updateUser(data: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/editUserDetailsById',
      data
    );
  }
  public editUserProfile(data: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/editUserProfile',
      data
    );
  }

  public getUser(data: any): Observable<any> {
    console.log('call user service', data);
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/getUserDetailsById',
      data
    );
  }

  public deleteUser(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/deleteUser', data);
  }

  public bulkUpdateUserStatus(data: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/bulkUpdateUserStatus',
      data
    );
  }

  public getUserStatistics(): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getUserStatistics', {});
  }

  public searchUsers(searchParams: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/searchUsers',
      searchParams
    );
  }

  public exportUsers(exportParams: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/exportUsers',
      exportParams
    );
  }

  public unlockUser(data: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/userUnlockByAdmin',
      data
    );
  }
  public uploadImage(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/uploadUserImage', data);
  }

  public saveColumnConfiguration(data: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/saveColumnConfiguration',
      data
    );
  }

  public getColumnConfiguration(params: any): Observable<any> {
    return this.http.get(
      AppSettings.REST_ENDPOINT + '/getColumnConfiguration',
      { params }
    );
  }

  public getAllRolesWithUserInfo(queryParams: any): Observable<any> {
    return this.http
      .post(
        `${AppSettings.REST_ENDPOINT}/getAllRolesWithUserInfo?pageSize=${queryParams.pageSize}&sortDir=${queryParams.sortOrder}&sortField=${queryParams.sortField}&start=${queryParams.pageNumber}`,
        queryParams.filter
      )
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          return of(err);
        })
      );
  }

  public getAllRoles(queryParams: any): Observable<any> {
    return this.http
      .post(
        `${AppSettings.REST_ENDPOINT}/getAllRole?pageSize=${queryParams.pageSize}&sortDir=${queryParams.sortOrder}&sortField=${queryParams.sortField}&start=${queryParams.pageNumber}`,
        queryParams.filter
      )
      .pipe(
        mergeMap((res: any) => {
          return of(res);
        }),
        catchError((err: any) => {
          return of(err);
        })
      );
  }

  public getRole(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getRoleById', data);
  }
  public getAllCounts(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/getAllCounts ', data);
  }

  public editRole(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/editRole', data);
  }
  public addRole(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/createRole', data);
  }

  public deleteRole(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/deleteRole', data);
  }
  public getDefaultPermissions(data: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/getDefaultPermissions',
      data
    );
  }
  public changePassword(data: any): Observable<any> {
    return this.http.post(AppSettings.REST_ENDPOINT + '/changePassword', data);
  }

  public getUserlistForDropdown(data: any): Observable<any> {
    return this.http.post(
      AppSettings.REST_ENDPOINT + '/getUserlistForDropdown',
      data
    );
  }
}

{"ast": null, "code": "import { Subscription, combineLatest } from 'rxjs';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../services/app.service\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/forms\";\nfunction ProjectViewComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"span\", 6);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26)(3, \"div\", 27)(4, \"label\");\n    i0.ɵɵtext(5, \"Manager\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 28);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 27)(9, \"label\");\n    i0.ɵɵtext(10, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 29);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(13, \"div\", 27)(14, \"label\");\n    i0.ɵɵtext(15, \"Issues\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 28);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 27)(19, \"label\");\n    i0.ɵɵtext(20, \"Comments\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 28);\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 30)(24, \"button\", 31);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_div_13_Template_button_click_24_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.editProject());\n    });\n    i0.ɵɵelement(25, \"i\", 32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.project.internalProjectManagerName || ctx_r1.project.internalProjectManager || \"John Smith\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"In Progress\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.issuesCount || \"2\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r1.project.comments || \"City review delayed\");\n  }\n}\nfunction ProjectViewComponent_div_2_div_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33)(1, \"div\", 34);\n    i0.ɵɵelement(2, \"i\", 35);\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"No permits found for this project.\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ProjectViewComponent_div_2_div_19_tr_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"a\", 39);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_div_19_tr_15_Template_a_click_2_listener() {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewPermit(permit_r5.permitId));\n    });\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 40);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"td\")(7, \"span\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"td\")(10, \"select\", 41);\n    i0.ɵɵlistener(\"change\", function ProjectViewComponent_div_2_div_19_tr_15_Template_select_change_10_listener($event) {\n      const permit_r5 = i0.ɵɵrestoreView(_r4).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.onStatusChange(permit_r5, $event.target.value));\n    });\n    i0.ɵɵelementStart(11, \"option\", 42);\n    i0.ɵɵtext(12, \"Select status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"option\", 43);\n    i0.ɵɵtext(14, \"Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"option\", 44);\n    i0.ɵɵtext(16, \"Pacifica Verification\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"option\", 45);\n    i0.ɵɵtext(18, \"Dis-Approved\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"option\", 46);\n    i0.ɵɵtext(20, \"Pending\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"option\", 47);\n    i0.ɵɵtext(22, \"Not Required\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"option\", 48);\n    i0.ɵɵtext(24, \"In Review\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"option\", 49);\n    i0.ɵɵtext(26, \"1 Cycle Completed\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(27, \"td\")(28, \"span\");\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(30, \"td\")(31, \"span\", 50);\n    i0.ɵɵtext(32);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const permit_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitName || \"\", \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", permit_r5.permitReviewType || \"\", \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.permitNumber || \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"value\", permit_r5.internalReviewStatus || \"\")(\"disabled\", ctx_r1.isLoading);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"value\", \"\");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(permit_r5.cycleDays || \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(permit_r5.attentionReason || \"\");\n  }\n}\nfunction ProjectViewComponent_div_2_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"table\", 37)(2, \"thead\")(3, \"tr\")(4, \"th\");\n    i0.ɵɵtext(5, \"Permit Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"th\");\n    i0.ɵɵtext(7, \"Source\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"th\");\n    i0.ɵɵtext(9, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"th\");\n    i0.ɵɵtext(11, \"Cycle (Days)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"th\");\n    i0.ɵɵtext(13, \"Comments\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"tbody\");\n    i0.ɵɵtemplate(15, ProjectViewComponent_div_2_div_19_tr_15_Template, 33, 8, \"tr\", 38);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(15);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.projectPermits);\n  }\n}\nfunction ProjectViewComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 8)(1, \"div\", 9)(2, \"div\", 10)(3, \"div\", 11)(4, \"div\", 12)(5, \"span\", 13);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 14);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 15)(10, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function ProjectViewComponent_div_2_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelement(11, \"i\", 17);\n    i0.ɵɵtext(12, \" Back \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵtemplate(13, ProjectViewComponent_div_2_div_13_Template, 26, 4, \"div\", 18);\n    i0.ɵɵelement(14, \"hr\", 19);\n    i0.ɵɵelementStart(15, \"div\", 20)(16, \"h5\", 21);\n    i0.ɵɵtext(17, \"Permits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, ProjectViewComponent_div_2_div_18_Template, 5, 0, \"div\", 22)(19, ProjectViewComponent_div_2_div_19_Template, 16, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectName || \"Project A\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.project.projectStatus || \"In Progress\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.project);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.projectPermits.length > 0);\n  }\n}\nexport class ProjectViewComponent {\n  route;\n  router;\n  cdr;\n  appService;\n  projectsService;\n  permitsService;\n  modalService;\n  projectId = null;\n  project = null;\n  isLoading = false;\n  selectedTab = 'details';\n  projectPermits = [];\n  loginUser = {};\n  routeSubscription = new Subscription();\n  constructor(route, router, cdr, appService, projectsService, permitsService, modalService) {\n    this.route = route;\n    this.router = router;\n    this.cdr = cdr;\n    this.appService = appService;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.modalService = modalService;\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([this.route.paramMap, this.route.queryParams]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n      console.log('Project view - received params:', {\n        projectId: this.projectId,\n        queryParams\n      });\n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      this.cdr.markForCheck();\n    });\n  }\n  ngOnDestroy() {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n  fetchProjectDetails() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    this.projectsService.getProject({\n      projectId: this.projectId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  fetchProjectPermits() {\n    if (!this.projectId) {\n      return;\n    }\n    this.isLoading = true;\n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [{\n          field: 'projectId',\n          operator: 'eq',\n          value: this.projectId\n        }]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: res => {\n        this.isLoading = false;\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter(p => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: err => {\n        this.isLoading = false;\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  goBack() {\n    this.router.navigate(['/projects/list']);\n  }\n  editProject() {\n    if (!this.projectId) {\n      return;\n    }\n    const NgbModalOptions = {\n      size: 'lg',\n      // Large modal size\n      backdrop: 'static',\n      // Prevents closing when clicking outside\n      keyboard: false,\n      // Disables closing with the Escape key\n      scrollable: true // Allows scrolling inside the modal\n    };\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(ProjectPopupComponent, NgbModalOptions);\n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    // Subscribe to the modal event when it closes\n    modalRef.result.then(result => {\n      // Handle successful edit\n      if (result) {\n        console.log('Project edited successfully:', result);\n        // Refresh project details\n        this.fetchProjectDetails();\n      }\n    }, reason => {\n      // Handle modal dismissal\n      console.log('Modal dismissed:', reason);\n    });\n  }\n  viewPermit(permitId) {\n    this.router.navigate(['/permits/view', permitId], {\n      queryParams: {\n        from: 'project',\n        projectId: this.projectId\n      }\n    });\n  }\n  onStatusChange(permit, newStatus) {\n    if (!permit?.permitId || !newStatus) {\n      return;\n    }\n    const allowed = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n    if (!allowed.includes(newStatus)) {\n      return;\n    }\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.isLoading = true;\n    this.cdr.markForCheck();\n    this.permitsService.updatePermitInternalReviewStatus({\n      permitId: permit.permitId,\n      internalReviewStatus: newStatus\n    }).subscribe({\n      next: res => {\n        const isFault = res?.isFault || res?.responseData?.isFault;\n        if (isFault) {\n          permit.internalReviewStatus = previous;\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        }\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      },\n      error: () => {\n        permit.internalReviewStatus = previous;\n        this.isLoading = false;\n        this.cdr.markForCheck();\n      }\n    });\n  }\n  getStatusClass(status) {\n    if (!status) return 'status-n-a';\n    return 'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-');\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  static ɵfac = function ProjectViewComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ProjectViewComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i2.AppService), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.NgbModal));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ProjectViewComponent,\n    selectors: [[\"app-project-view\"]],\n    decls: 3,\n    vars: 2,\n    consts: [[\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"project-view-container\"], [\"class\", \"card shadow-sm rounded-3\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"text-primary\", \"fs-5\"], [1, \"card\", \"shadow-sm\", \"rounded-3\"], [1, \"project-details-header\"], [1, \"header-content\"], [1, \"title-wrap\"], [1, \"title-line\"], [1, \"project-title\"], [1, \"status-text\", \"status-active\"], [1, \"button-group\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"d-flex\", \"align-items-center\", \"mb-2\", 3, \"click\"], [1, \"fas\", \"fa-arrow-left\", \"me-2\"], [\"class\", \"project-details-section\", 4, \"ngIf\"], [1, \"project-divider\"], [1, \"permits-section\"], [1, \"permits-title\"], [\"class\", \"d-flex justify-content-center align-items-center py-5 text-muted\", 4, \"ngIf\"], [\"class\", \"table-responsive\", 4, \"ngIf\"], [1, \"project-details-section\"], [1, \"project-details-content\"], [1, \"project-details-grid\"], [1, \"project-detail-item\"], [1, \"project-value\"], [1, \"project-value\", \"status-bold\"], [1, \"edit-icon-container\"], [\"type\", \"button\", \"title\", \"Edit Project\", 1, \"btn\", \"btn-link\", \"p-0\", 3, \"click\"], [1, \"fas\", \"fa-edit\", \"text-primary\", \"edit-pen-icon\"], [1, \"d-flex\", \"justify-content-center\", \"align-items-center\", \"py-5\", \"text-muted\"], [1, \"text-center\"], [1, \"fas\", \"fa-file-alt\", \"fa-3x\", \"mb-3\"], [1, \"table-responsive\"], [1, \"table\", \"permits-table\"], [4, \"ngFor\", \"ngForOf\"], [\"title\", \"View Permit\", \"aria-label\", \"View Permit\", 1, \"fw-bold\", 3, \"click\"], [1, \"badge\", \"badge-green-light\", \"ms-1\"], [1, \"form-select\", \"form-select-sm\", \"w-auto\", 3, \"change\", \"value\", \"disabled\"], [\"disabled\", \"\", 3, \"value\"], [\"value\", \"Approved\"], [\"value\", \"Pacifica Verification\"], [\"value\", \"Dis-Approved\"], [\"value\", \"Pending\"], [\"value\", \"Not Required\"], [\"value\", \"In Review\"], [\"value\", \"1 Cycle Completed\"], [1, \"wrap-text\"]],\n    template: function ProjectViewComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, ProjectViewComponent_div_0_Template, 7, 0, \"div\", 0);\n        i0.ɵɵelementStart(1, \"div\", 1);\n        i0.ɵɵtemplate(2, ProjectViewComponent_div_2_Template, 20, 5, \"div\", 2);\n        i0.ɵɵelementEnd();\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.project);\n      }\n    },\n    dependencies: [i6.NgForOf, i6.NgIf, i7.NgSelectOption, i7.ɵNgSelectMultipleOption],\n    styles: [\"var[_ngcontent-%COMP%]   resource[_ngcontent-%COMP%];\\n\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\tvar __webpack_modules__ = ({\\n\\n\\n 903:\\n\\n\\n\\n\\n\\n (() => {\\n\\nthrow new Error(\\\"Module build failed (from ./node_modules/@angular-devkit/build-angular/node_modules/sass-loader/dist/cjs.js):\\\\nunmatched \\\\\\\"}\\\\\\\".\\\\n    \\u2577\\\\n139 \\u2502 }\\\\n    \\u2502 ^\\\\n    \\u2575\\\\n  src\\\\\\\\app\\\\\\\\modules\\\\\\\\projects\\\\\\\\project-view\\\\\\\\project-view.component.scss 139:1  root stylesheet\\\");\\n\\n\\n })\\n\\n\\n \\t})[_ngcontent-%COMP%];\\n\\n\\n\\n \\t\\n\\n \\t//[_ngcontent-%COMP%]   startup\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   Load[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   and[_ngcontent-%COMP%]   return[_ngcontent-%COMP%]   exports\\n\\n[_ngcontent-%COMP%]   //[_ngcontent-%COMP%]   This[_ngcontent-%COMP%]   entry[_ngcontent-%COMP%]   module[_ngcontent-%COMP%]   doesn't[_ngcontent-%COMP%]   tell[_ngcontent-%COMP%]   about[_ngcontent-%COMP%]   it's[_ngcontent-%COMP%]   top-level[_ngcontent-%COMP%]   declarations[_ngcontent-%COMP%]   so[_ngcontent-%COMP%]   it[_ngcontent-%COMP%]   can't[_ngcontent-%COMP%]   be[_ngcontent-%COMP%]   inlined\\n\\n[_ngcontent-%COMP%]   var[_ngcontent-%COMP%]   __webpack_exports__[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] {};\\n\\n \\t__webpack_modules__[903]();\\n\\n \\tresource = __webpack_exports__;\\n\\n \\t\\n\\n })()\\n;\"]\n  });\n}", "map": {"version": 3, "names": ["Subscription", "combineLatest", "ProjectPopupComponent", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵlistener", "ProjectViewComponent_div_2_div_13_Template_button_click_24_listener", "ɵɵrestoreView", "_r3", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "editProject", "ɵɵelement", "ɵɵadvance", "ɵɵtextInterpolate", "project", "internalProjectManagerName", "internalProjectManager", "projectStatus", "issuesCount", "comments", "ProjectViewComponent_div_2_div_19_tr_15_Template_a_click_2_listener", "permit_r5", "_r4", "$implicit", "viewPermit", "permitId", "ProjectViewComponent_div_2_div_19_tr_15_Template_select_change_10_listener", "$event", "onStatusChange", "target", "value", "ɵɵtextInterpolate1", "permitName", "permitReviewType", "permitNumber", "ɵɵproperty", "internalReviewStatus", "isLoading", "cycleDays", "attentionReason", "ɵɵtemplate", "ProjectViewComponent_div_2_div_19_tr_15_Template", "projectPermits", "ProjectViewComponent_div_2_Template_button_click_10_listener", "_r1", "goBack", "ProjectViewComponent_div_2_div_13_Template", "ProjectViewComponent_div_2_div_18_Template", "ProjectViewComponent_div_2_div_19_Template", "projectName", "length", "ProjectViewComponent", "route", "router", "cdr", "appService", "projectsService", "permitsService", "modalService", "projectId", "selectedTab", "loginUser", "routeSubscription", "constructor", "ngOnInit", "getLoggedInUser", "paramMap", "queryParams", "subscribe", "idParam", "get", "Number", "console", "log", "activeTab", "fetchProjectDetails", "fetchProjectPermits", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ngOnDestroy", "unsubscribe", "getProject", "next", "res", "<PERSON><PERSON><PERSON>", "responseData", "Project", "data", "Object", "keys", "error", "faultMessage", "err", "getPermitsForKendoGrid", "take", "skip", "sort", "filter", "logic", "filters", "field", "operator", "search", "loggedInUserId", "userId", "rawPermits", "p", "permitProjectId", "projectID", "project_id", "ProjectId", "ProjectID", "navigate", "NgbModalOptions", "size", "backdrop", "keyboard", "scrollable", "modalRef", "open", "componentInstance", "id", "result", "then", "reason", "from", "permit", "newStatus", "allowed", "includes", "previous", "updatePermitInternalReviewStatus", "getStatusClass", "status", "toLowerCase", "replace", "showTab", "tab", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "ChangeDetectorRef", "i2", "AppService", "i3", "ProjectsService", "i4", "PermitsService", "i5", "NgbModal", "selectors", "decls", "vars", "consts", "template", "ProjectViewComponent_Template", "rf", "ctx", "ProjectViewComponent_div_0_Template", "ProjectViewComponent_div_2_Template"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\projects\\project-view\\project-view.component.html"], "sourcesContent": ["import { ChangeDetectorRef, Component, OnInit, OnDestroy } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { Subscription, combineLatest } from 'rxjs';\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ProjectsService } from '../../services/projects.service';\nimport { PermitsService } from '../../services/permits.service';\nimport { AppService } from '../../services/app.service';\nimport { ProjectPopupComponent } from '../project-popup/project-popup.component';\n\n@Component({\n  selector: 'app-project-view',\n  templateUrl: './project-view.component.html',\n  styleUrls: ['./project-view.component.scss']\n})\nexport class ProjectViewComponent implements OnInit, OnDestroy {\n  public projectId: number | null = null;\n  public project: any = null;\n  public isLoading: boolean = false;\n  public selectedTab: string = 'details';\n  public projectPermits: any[] = [];\n  public loginUser: any = {};\n  private routeSubscription: Subscription = new Subscription();\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private cdr: ChangeDetectorRef,\n    private appService: AppService,\n    private projectsService: ProjectsService,\n    private permitsService: PermitsService,\n    private modalService: NgbModal\n  ) {}\n\n  ngOnInit(): void {\n    this.loginUser = this.appService.getLoggedInUser();\n\n    // Combine route params and query params to handle both together\n    this.routeSubscription = combineLatest([\n      this.route.paramMap,\n      this.route.queryParams\n    ]).subscribe(([paramMap, queryParams]) => {\n      const idParam = paramMap.get('id');\n      this.projectId = idParam ? Number(idParam) : null;\n\n      console.log('Project view - received params:', { projectId: this.projectId, queryParams });\n      \n      // Handle active tab from query params\n      const activeTab = queryParams['activeTab'];\n      if (activeTab && (activeTab === 'permits' || activeTab === 'details')) {\n        this.selectedTab = activeTab;\n        console.log('Setting selectedTab from query params:', activeTab);\n      } else {\n        console.log('No valid activeTab found, keeping default:', this.selectedTab);\n      }\n\n      if (this.projectId) {\n        this.fetchProjectDetails();\n        this.fetchProjectPermits();\n      }\n      \n      this.cdr.markForCheck();\n    });\n  }\n\n  ngOnDestroy(): void {\n    if (this.routeSubscription) {\n      this.routeSubscription.unsubscribe();\n    }\n  }\n\n  public fetchProjectDetails(): void {\n    if (!this.projectId) { return; }\n    this.isLoading = true;\n    this.projectsService.getProject({ projectId: this.projectId }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Project API Response:', res);\n        if (!res?.isFault) {\n          // Try different response structures\n          this.project = res.responseData?.Project || res.responseData?.data || res.responseData || null;\n          console.log('Project data assigned:', this.project);\n          console.log('Project fields available:', Object.keys(this.project || {}));\n          // Don't override selectedTab here - let query params handle it\n        } else {\n          console.error('API returned fault:', res.faultMessage);\n          this.project = null;\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err) => {\n        this.isLoading = false;\n        console.error('Error fetching project details:', err);\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public fetchProjectPermits(): void {\n    if (!this.projectId) { return; }\n    this.isLoading = true;\n    \n    // Get permits for this specific project\n    this.permitsService.getPermitsForKendoGrid({\n      take: 100,\n      skip: 0,\n      sort: [],\n      filter: {\n        logic: 'and',\n        filters: [\n          {\n            field: 'projectId',\n            operator: 'eq',\n            value: this.projectId\n          }\n        ]\n      },\n      search: '',\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: (res: any) => {\n        this.isLoading = false;\n        console.log('Project permits API response:', res);\n        if (res?.isFault) {\n          console.error('Failed to load project permits:', res.faultMessage);\n          this.projectPermits = [];\n        } else {\n          const rawPermits = res.responseData?.data || res.data || [];\n          // Client-side guard: ensure only permits for this project are shown\n          this.projectPermits = (rawPermits || []).filter((p: any) => {\n            const permitProjectId = p?.projectId ?? p?.projectID ?? p?.project_id ?? p?.ProjectId ?? p?.ProjectID;\n            return this.projectId != null ? Number(permitProjectId) === Number(this.projectId) : true;\n          });\n          console.log('Project permits assigned (filtered):', this.projectPermits);\n        }\n        this.cdr.markForCheck();\n      },\n      error: (err: any) => {\n        this.isLoading = false;\n        console.error('Error loading project permits:', err);\n        this.projectPermits = [];\n        this.cdr.markForCheck();\n      }\n    });\n  }\n\n  public goBack(): void {\n    this.router.navigate(['/projects/list']);\n  }\n\n  public editProject(): void {\n    if (!this.projectId) { return; }\n    \n    const NgbModalOptions: {\n      size: string;\n      backdrop: boolean | 'static';\n      keyboard: boolean;\n      scrollable: boolean;\n    } = {\n      size: 'lg', // Large modal size\n      backdrop: 'static', // Prevents closing when clicking outside\n      keyboard: false, // Disables closing with the Escape key\n      scrollable: true, // Allows scrolling inside the modal\n    };\n\n    // Open the modal and load the ProjectPopup\n    const modalRef = this.modalService.open(\n      ProjectPopupComponent,\n      NgbModalOptions\n    );\n    \n    // Pass the selected ID to the modal component (0 for new, existing ID for edit)\n    modalRef.componentInstance.id = this.projectId;\n    modalRef.componentInstance.project = this.project;\n    \n    // Subscribe to the modal event when it closes\n    modalRef.result.then(\n      (result) => {\n        // Handle successful edit\n        if (result) {\n          console.log('Project edited successfully:', result);\n          // Refresh project details\n          this.fetchProjectDetails();\n        }\n      },\n      (reason) => {\n        // Handle modal dismissal\n        console.log('Modal dismissed:', reason);\n      }\n    );\n  }\n\n  public viewPermit(permitId: number): void {\n    this.router.navigate(['/permits/view', permitId], { \n      queryParams: { from: 'project', projectId: this.projectId } \n    });\n  }\n\n  public onStatusChange(permit: any, newStatus: string): void {\n    if (!permit?.permitId || !newStatus) { return; }\n    const allowed = [\n      'Approved',\n      'Pacifica Verification',\n      'Dis-Approved',\n      'Pending',\n      'Not Required',\n      'In Review',\n      '1 Cycle Completed'\n    ];\n    if (!allowed.includes(newStatus)) { return; }\n\n    const previous = permit.internalReviewStatus;\n    permit.internalReviewStatus = newStatus;\n    this.isLoading = true;\n    this.cdr.markForCheck();\n\n    this.permitsService\n      .updatePermitInternalReviewStatus({ permitId: permit.permitId, internalReviewStatus: newStatus })\n      .subscribe({\n        next: (res: any) => {\n          const isFault = res?.isFault || res?.responseData?.isFault;\n          if (isFault) {\n            permit.internalReviewStatus = previous;\n            this.isLoading = false;\n            this.cdr.markForCheck();\n          }\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        },\n        error: () => {\n          permit.internalReviewStatus = previous;\n          this.isLoading = false;\n          this.cdr.markForCheck();\n        }\n      });\n  }\n\n  public getStatusClass(status: string): string {\n    if (!status) return 'status-n-a';\n    return (\n      'status-' + status.toLowerCase().replace(/\\s+/g, '-').replace(/\\//g, '-')\n    );\n  }\n\n  showTab(tab: string, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n}\n", "<!-- Full Screen Loading Overlay -->\n<div *ngIf=\"isLoading\" class=\"fullscreen-loading-overlay\">\n  <div class=\"loading-content\">\n    <div class=\"custom-colored-spinner\" role=\"status\">\n      <span class=\"visually-hidden\">Loading...</span>\n    </div>\n    <div class=\"mt-4 text-primary fs-5\">Loading...</div>\n  </div>\n</div>\n\n<div class=\"project-view-container\">\n  <!-- Project Details Card -->\n  <div class=\"card shadow-sm rounded-3\" *ngIf=\"project\">\n    <!-- Project Details Header -->\n    <div class=\"project-details-header\">\n      <div class=\"header-content\">\n        <div class=\"title-wrap\">\n          <div class=\"title-line\">\n            <span class=\"project-title\">{{ project.projectName || \"Project A\" }}</span>\n            <span class=\"status-text status-active\">{{ project.projectStatus || \"In Progress\" }}</span>\n          </div>\n        </div>\n        <div class=\"button-group\">\n          <button type=\"button\" class=\"btn btn-sm btn-light-primary d-flex align-items-center mb-2\" (click)=\"goBack()\">\n            <i class=\"fas fa-arrow-left me-2\"></i>\n            Back\n          </button>\n        </div>\n      </div>\n    </div>\n\n    <!-- Project Details Section -->\n    <div class=\"project-details-section\" *ngIf=\"project\">\n      <div class=\"project-details-content\">\n        <div class=\"project-details-grid\">\n          <div class=\"project-detail-item\">\n            <label>Manager</label>\n            <span class=\"project-value\">{{ project.internalProjectManagerName || project.internalProjectManager || \"John Smith\" }}</span>\n          </div>\n          <div class=\"project-detail-item\">\n            <label>Status</label>\n            <span class=\"project-value status-bold\">{{ project.projectStatus || \"In Progress\" }}</span>\n          </div>\n          <div class=\"project-detail-item\">\n            <label>Issues</label>\n            <span class=\"project-value\">{{ project.issuesCount || \"2\" }}</span>\n          </div>\n          <div class=\"project-detail-item\">\n            <label>Comments</label>\n            <span class=\"project-value\">{{ project.comments || \"City review delayed\" }}</span>\n          </div>\n        </div>\n      </div>\n      <!-- Edit Icon on the right -->\n      <div class=\"edit-icon-container\">\n        <button type=\"button\" class=\"btn btn-link p-0\" (click)=\"editProject()\" title=\"Edit Project\">\n          <i class=\"fas fa-edit text-primary edit-pen-icon\"></i>\n        </button>\n      </div>\n    </div>\n\n    <!-- Divider Line -->\n    <hr class=\"project-divider\">\n\n    <!-- Permits Section -->\n    <div class=\"permits-section\">\n      <h5 class=\"permits-title\">Permits</h5>\n      \n      <!-- Empty State for Permits -->\n      <div class=\"d-flex justify-content-center align-items-center py-5 text-muted\" *ngIf=\"projectPermits.length === 0\">\n        <div class=\"text-center\">\n          <i class=\"fas fa-file-alt fa-3x mb-3\"></i>\n          <p>No permits found for this project.</p>\n        </div>\n      </div>\n\n      <!-- Permits Table -->\n      <div class=\"table-responsive\" *ngIf=\"projectPermits.length > 0\">\n        <table class=\"table permits-table\">\n          <thead>\n            <tr>\n              <th>Permit Type</th>\n              <th>Source</th>\n              <th>Status</th>\n              <th>Cycle (Days)</th>\n              <th>Comments</th>\n            </tr>\n          </thead>\n          <tbody>\n            <tr *ngFor=\"let permit of projectPermits\">\n              <td>\n                <a \n                  class=\"fw-bold\" \n                  (click)=\"viewPermit(permit.permitId)\" \n                  title=\"View Permit\"\n                  aria-label=\"View Permit\"\n                >\n                  {{ permit.permitName || \"\" }}  \n                </a>\n                <span class=\"badge badge-green-light ms-1\">\n                  {{ permit.permitReviewType || \"\" }}\n                </span>\n              </td>\n              <td>\n                <span>{{ permit.permitNumber || \"\" }}</span>\n              </td>\n              <td>\n                <select class=\"form-select form-select-sm w-auto\"\n                        [value]=\"permit.internalReviewStatus || ''\"\n                        (change)=\"onStatusChange(permit, $any($event.target).value)\"\n                        [disabled]=\"isLoading\">\n                  <option [value]=\"''\" disabled>Select status</option>\n                  <option value=\"Approved\">Approved</option>\n                  <option value=\"Pacifica Verification\">Pacifica Verification</option>\n                  <option value=\"Dis-Approved\">Dis-Approved</option>\n                  <option value=\"Pending\">Pending</option>\n                  <option value=\"Not Required\">Not Required</option>\n                  <option value=\"In Review\">In Review</option>\n                  <option value=\"1 Cycle Completed\">1 Cycle Completed</option>\n                </select>\n              </td>\n              <td>\n                <span>{{ permit.cycleDays || \"\" }}</span>\n              </td>\n              <td>\n                <span class=\"wrap-text\">{{ permit.attentionReason || '' }}</span>\n              </td>\n            </tr>\n          </tbody>\n        </table>\n      </div>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAEA,SAASA,YAAY,EAAEC,aAAa,QAAQ,MAAM;AAKlD,SAASC,qBAAqB,QAAQ,0CAA0C;;;;;;;;;;;ICH1EC,EAHN,CAAAC,cAAA,aAA0D,aAC3B,aACuB,cAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAElDF,EAFkD,CAAAG,YAAA,EAAM,EAChD,EACF;;;;;;IA4BMH,EAJR,CAAAC,cAAA,cAAqD,cACd,cACD,cACC,YACxB;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACtBH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAA0F;IACxHF,EADwH,CAAAG,YAAA,EAAO,EACzH;IAEJH,EADF,CAAAC,cAAA,cAAiC,YACxB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,gBAAwC;IAAAD,EAAA,CAAAE,MAAA,IAA4C;IACtFF,EADsF,CAAAG,YAAA,EAAO,EACvF;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACrBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAC9DF,EAD8D,CAAAG,YAAA,EAAO,EAC/D;IAEJH,EADF,CAAAC,cAAA,eAAiC,aACxB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACvBH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA+C;IAGjFF,EAHiF,CAAAG,YAAA,EAAO,EAC9E,EACF,EACF;IAGJH,EADF,CAAAC,cAAA,eAAiC,kBAC6D;IAA7CD,EAAA,CAAAI,UAAA,mBAAAC,oEAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,WAAA,EAAa;IAAA,EAAC;IACpEX,EAAA,CAAAY,SAAA,aAAsD;IAG5DZ,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IAtB8BH,EAAA,CAAAa,SAAA,GAA0F;IAA1Fb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAC,0BAAA,IAAAR,MAAA,CAAAO,OAAA,CAAAE,sBAAA,iBAA0F;IAI9EjB,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAG,aAAA,kBAA4C;IAIxDlB,EAAA,CAAAa,SAAA,GAAgC;IAAhCb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAI,WAAA,QAAgC;IAIhCnB,EAAA,CAAAa,SAAA,GAA+C;IAA/Cb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAK,QAAA,0BAA+C;;;;;IAqB/EpB,EADF,CAAAC,cAAA,cAAkH,cACvF;IACvBD,EAAA,CAAAY,SAAA,YAA0C;IAC1CZ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yCAAkC;IAEzCF,EAFyC,CAAAG,YAAA,EAAI,EACrC,EACF;;;;;;IAiBIH,EAFJ,CAAAC,cAAA,SAA0C,SACpC,YAMD;IAHCD,EAAA,CAAAI,UAAA,mBAAAiB,oEAAA;MAAA,MAAAC,SAAA,GAAAtB,EAAA,CAAAM,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAiB,UAAA,CAAAH,SAAA,CAAAI,QAAA,CAA2B;IAAA,EAAC;IAIrC1B,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACJH,EAAA,CAAAC,cAAA,eAA2C;IACzCD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;IAEHH,EADF,CAAAC,cAAA,SAAI,WACI;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACzC;IAEHH,EADF,CAAAC,cAAA,SAAI,kBAI6B;IADvBD,EAAA,CAAAI,UAAA,oBAAAuB,2EAAAC,MAAA;MAAA,MAAAN,SAAA,GAAAtB,EAAA,CAAAM,aAAA,CAAAiB,GAAA,EAAAC,SAAA;MAAA,MAAAhB,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAAUF,MAAA,CAAAqB,cAAA,CAAAP,SAAA,EAAAM,MAAA,CAAAE,MAAA,CAAAC,KAAA,CAAiD;IAAA,EAAC;IAElE/B,EAAA,CAAAC,cAAA,kBAA8B;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpDH,EAAA,CAAAC,cAAA,kBAAyB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC1CH,EAAA,CAAAC,cAAA,kBAAsC;IAAAD,EAAA,CAAAE,MAAA,6BAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACpEH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAAwB;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACxCH,EAAA,CAAAC,cAAA,kBAA6B;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAClDH,EAAA,CAAAC,cAAA,kBAA0B;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAC5CH,EAAA,CAAAC,cAAA,kBAAkC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAEvDF,EAFuD,CAAAG,YAAA,EAAS,EACrD,EACN;IAEHH,EADF,CAAAC,cAAA,UAAI,YACI;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IACpCF,EADoC,CAAAG,YAAA,EAAO,EACtC;IAEHH,EADF,CAAAC,cAAA,UAAI,gBACsB;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE9DF,EAF8D,CAAAG,YAAA,EAAO,EAC9D,EACF;;;;;IA9BCH,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAgC,kBAAA,MAAAV,SAAA,CAAAW,UAAA,YACF;IAEEjC,EAAA,CAAAa,SAAA,GACF;IADEb,EAAA,CAAAgC,kBAAA,MAAAV,SAAA,CAAAY,gBAAA,YACF;IAGMlC,EAAA,CAAAa,SAAA,GAA+B;IAA/Bb,EAAA,CAAAc,iBAAA,CAAAQ,SAAA,CAAAa,YAAA,OAA+B;IAI7BnC,EAAA,CAAAa,SAAA,GAA2C;IAE3Cb,EAFA,CAAAoC,UAAA,UAAAd,SAAA,CAAAe,oBAAA,OAA2C,aAAA7B,MAAA,CAAA8B,SAAA,CAErB;IACpBtC,EAAA,CAAAa,SAAA,EAAY;IAAZb,EAAA,CAAAoC,UAAA,aAAY;IAWhBpC,EAAA,CAAAa,SAAA,IAA4B;IAA5Bb,EAAA,CAAAc,iBAAA,CAAAQ,SAAA,CAAAiB,SAAA,OAA4B;IAGVvC,EAAA,CAAAa,SAAA,GAAkC;IAAlCb,EAAA,CAAAc,iBAAA,CAAAQ,SAAA,CAAAkB,eAAA,OAAkC;;;;;IA5C5DxC,EAJR,CAAAC,cAAA,cAAgE,gBAC3B,YAC1B,SACD,SACE;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpBH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAEhBF,EAFgB,CAAAG,YAAA,EAAK,EACd,EACC;IACRH,EAAA,CAAAC,cAAA,aAAO;IACLD,EAAA,CAAAyC,UAAA,KAAAC,gDAAA,kBAA0C;IAyChD1C,EAFI,CAAAG,YAAA,EAAQ,EACF,EACJ;;;;IAzCuBH,EAAA,CAAAa,SAAA,IAAiB;IAAjBb,EAAA,CAAAoC,UAAA,YAAA5B,MAAA,CAAAmC,cAAA,CAAiB;;;;;;IAvExC3C,EANV,CAAAC,cAAA,aAAsD,aAEhB,cACN,cACF,cACE,eACM;IAAAD,EAAA,CAAAE,MAAA,GAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3EH,EAAA,CAAAC,cAAA,eAAwC;IAAAD,EAAA,CAAAE,MAAA,GAA4C;IAExFF,EAFwF,CAAAG,YAAA,EAAO,EACvF,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,kBACqF;IAAnBD,EAAA,CAAAI,UAAA,mBAAAwC,6DAAA;MAAA5C,EAAA,CAAAM,aAAA,CAAAuC,GAAA;MAAA,MAAArC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAsC,MAAA,EAAQ;IAAA,EAAC;IAC1G9C,EAAA,CAAAY,SAAA,aAAsC;IACtCZ,EAAA,CAAAE,MAAA,cACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;IAGNH,EAAA,CAAAyC,UAAA,KAAAM,0CAAA,mBAAqD;IA8BrD/C,EAAA,CAAAY,SAAA,cAA4B;IAI1BZ,EADF,CAAAC,cAAA,eAA6B,cACD;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAWtCH,EARA,CAAAyC,UAAA,KAAAO,0CAAA,kBAAkH,KAAAC,0CAAA,mBAQlD;IAuDpEjD,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAlHgCH,EAAA,CAAAa,SAAA,GAAwC;IAAxCb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAmC,WAAA,gBAAwC;IAC5BlD,EAAA,CAAAa,SAAA,GAA4C;IAA5Cb,EAAA,CAAAc,iBAAA,CAAAN,MAAA,CAAAO,OAAA,CAAAG,aAAA,kBAA4C;IAatDlB,EAAA,CAAAa,SAAA,GAAa;IAAbb,EAAA,CAAAoC,UAAA,SAAA5B,MAAA,CAAAO,OAAA,CAAa;IAqC8Bf,EAAA,CAAAa,SAAA,GAAiC;IAAjCb,EAAA,CAAAoC,UAAA,SAAA5B,MAAA,CAAAmC,cAAA,CAAAQ,MAAA,OAAiC;IAQjFnD,EAAA,CAAAa,SAAA,EAA+B;IAA/Bb,EAAA,CAAAoC,UAAA,SAAA5B,MAAA,CAAAmC,cAAA,CAAAQ,MAAA,KAA+B;;;AD/DpE,OAAM,MAAOC,oBAAoB;EAUrBC,KAAA;EACAC,MAAA;EACAC,GAAA;EACAC,UAAA;EACAC,eAAA;EACAC,cAAA;EACAC,YAAA;EAfHC,SAAS,GAAkB,IAAI;EAC/B7C,OAAO,GAAQ,IAAI;EACnBuB,SAAS,GAAY,KAAK;EAC1BuB,WAAW,GAAW,SAAS;EAC/BlB,cAAc,GAAU,EAAE;EAC1BmB,SAAS,GAAQ,EAAE;EAClBC,iBAAiB,GAAiB,IAAIlE,YAAY,EAAE;EAE5DmE,YACUX,KAAqB,EACrBC,MAAc,EACdC,GAAsB,EACtBC,UAAsB,EACtBC,eAAgC,EAChCC,cAA8B,EAC9BC,YAAsB;IANtB,KAAAN,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,GAAG,GAAHA,GAAG;IACH,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,YAAY,GAAZA,YAAY;EACnB;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACH,SAAS,GAAG,IAAI,CAACN,UAAU,CAACU,eAAe,EAAE;IAElD;IACA,IAAI,CAACH,iBAAiB,GAAGjE,aAAa,CAAC,CACrC,IAAI,CAACuD,KAAK,CAACc,QAAQ,EACnB,IAAI,CAACd,KAAK,CAACe,WAAW,CACvB,CAAC,CAACC,SAAS,CAAC,CAAC,CAACF,QAAQ,EAAEC,WAAW,CAAC,KAAI;MACvC,MAAME,OAAO,GAAGH,QAAQ,CAACI,GAAG,CAAC,IAAI,CAAC;MAClC,IAAI,CAACX,SAAS,GAAGU,OAAO,GAAGE,MAAM,CAACF,OAAO,CAAC,GAAG,IAAI;MAEjDG,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE;QAAEd,SAAS,EAAE,IAAI,CAACA,SAAS;QAAEQ;MAAW,CAAE,CAAC;MAE1F;MACA,MAAMO,SAAS,GAAGP,WAAW,CAAC,WAAW,CAAC;MAC1C,IAAIO,SAAS,KAAKA,SAAS,KAAK,SAAS,IAAIA,SAAS,KAAK,SAAS,CAAC,EAAE;QACrE,IAAI,CAACd,WAAW,GAAGc,SAAS;QAC5BF,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEC,SAAS,CAAC;MAClE,CAAC,MAAM;QACLF,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAE,IAAI,CAACb,WAAW,CAAC;MAC7E;MAEA,IAAI,IAAI,CAACD,SAAS,EAAE;QAClB,IAAI,CAACgB,mBAAmB,EAAE;QAC1B,IAAI,CAACC,mBAAmB,EAAE;MAC5B;MAEA,IAAI,CAACtB,GAAG,CAACuB,YAAY,EAAE;IACzB,CAAC,CAAC;EACJ;EAEAC,WAAWA,CAAA;IACT,IAAI,IAAI,CAAChB,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACiB,WAAW,EAAE;IACtC;EACF;EAEOJ,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAAChB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACtB,SAAS,GAAG,IAAI;IACrB,IAAI,CAACmB,eAAe,CAACwB,UAAU,CAAC;MAAErB,SAAS,EAAE,IAAI,CAACA;IAAS,CAAE,CAAC,CAACS,SAAS,CAAC;MACvEa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7C,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAES,GAAG,CAAC;QACzC,IAAI,CAACA,GAAG,EAAEC,OAAO,EAAE;UACjB;UACA,IAAI,CAACrE,OAAO,GAAGoE,GAAG,CAACE,YAAY,EAAEC,OAAO,IAAIH,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACE,YAAY,IAAI,IAAI;UAC9FZ,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE,IAAI,CAAC3D,OAAO,CAAC;UACnD0D,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEc,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC1E,OAAO,IAAI,EAAE,CAAC,CAAC;UACzE;QACF,CAAC,MAAM;UACL0D,OAAO,CAACiB,KAAK,CAAC,qBAAqB,EAAEP,GAAG,CAACQ,YAAY,CAAC;UACtD,IAAI,CAAC5E,OAAO,GAAG,IAAI;QACrB;QACA,IAAI,CAACwC,GAAG,CAACuB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAG,IAAI;QACb,IAAI,CAACtD,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEE,GAAG,CAAC;QACrD,IAAI,CAACrC,GAAG,CAACuB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOD,mBAAmBA,CAAA;IACxB,IAAI,CAAC,IAAI,CAACjB,SAAS,EAAE;MAAE;IAAQ;IAC/B,IAAI,CAACtB,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACoB,cAAc,CAACmC,sBAAsB,CAAC;MACzCC,IAAI,EAAE,GAAG;MACTC,IAAI,EAAE,CAAC;MACPC,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;QACNC,KAAK,EAAE,KAAK;QACZC,OAAO,EAAE,CACP;UACEC,KAAK,EAAE,WAAW;UAClBC,QAAQ,EAAE,IAAI;UACdtE,KAAK,EAAE,IAAI,CAAC6B;SACb;OAEJ;MACD0C,MAAM,EAAE,EAAE;MACVC,cAAc,EAAE,IAAI,CAACzC,SAAS,CAAC0C;KAChC,CAAC,CAACnC,SAAS,CAAC;MACXa,IAAI,EAAGC,GAAQ,IAAI;QACjB,IAAI,CAAC7C,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAES,GAAG,CAAC;QACjD,IAAIA,GAAG,EAAEC,OAAO,EAAE;UAChBX,OAAO,CAACiB,KAAK,CAAC,iCAAiC,EAAEP,GAAG,CAACQ,YAAY,CAAC;UAClE,IAAI,CAAChD,cAAc,GAAG,EAAE;QAC1B,CAAC,MAAM;UACL,MAAM8D,UAAU,GAAGtB,GAAG,CAACE,YAAY,EAAEE,IAAI,IAAIJ,GAAG,CAACI,IAAI,IAAI,EAAE;UAC3D;UACA,IAAI,CAAC5C,cAAc,GAAG,CAAC8D,UAAU,IAAI,EAAE,EAAER,MAAM,CAAES,CAAM,IAAI;YACzD,MAAMC,eAAe,GAAGD,CAAC,EAAE9C,SAAS,IAAI8C,CAAC,EAAEE,SAAS,IAAIF,CAAC,EAAEG,UAAU,IAAIH,CAAC,EAAEI,SAAS,IAAIJ,CAAC,EAAEK,SAAS;YACrG,OAAO,IAAI,CAACnD,SAAS,IAAI,IAAI,GAAGY,MAAM,CAACmC,eAAe,CAAC,KAAKnC,MAAM,CAAC,IAAI,CAACZ,SAAS,CAAC,GAAG,IAAI;UAC3F,CAAC,CAAC;UACFa,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC/B,cAAc,CAAC;QAC1E;QACA,IAAI,CAACY,GAAG,CAACuB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAGE,GAAQ,IAAI;QAClB,IAAI,CAACtD,SAAS,GAAG,KAAK;QACtBmC,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEE,GAAG,CAAC;QACpD,IAAI,CAACjD,cAAc,GAAG,EAAE;QACxB,IAAI,CAACY,GAAG,CAACuB,YAAY,EAAE;MACzB;KACD,CAAC;EACJ;EAEOhC,MAAMA,CAAA;IACX,IAAI,CAACQ,MAAM,CAAC0D,QAAQ,CAAC,CAAC,gBAAgB,CAAC,CAAC;EAC1C;EAEOrG,WAAWA,CAAA;IAChB,IAAI,CAAC,IAAI,CAACiD,SAAS,EAAE;MAAE;IAAQ;IAE/B,MAAMqD,eAAe,GAKjB;MACFC,IAAI,EAAE,IAAI;MAAE;MACZC,QAAQ,EAAE,QAAQ;MAAE;MACpBC,QAAQ,EAAE,KAAK;MAAE;MACjBC,UAAU,EAAE,IAAI,CAAE;KACnB;IAED;IACA,MAAMC,QAAQ,GAAG,IAAI,CAAC3D,YAAY,CAAC4D,IAAI,CACrCxH,qBAAqB,EACrBkH,eAAe,CAChB;IAED;IACAK,QAAQ,CAACE,iBAAiB,CAACC,EAAE,GAAG,IAAI,CAAC7D,SAAS;IAC9C0D,QAAQ,CAACE,iBAAiB,CAACzG,OAAO,GAAG,IAAI,CAACA,OAAO;IAEjD;IACAuG,QAAQ,CAACI,MAAM,CAACC,IAAI,CACjBD,MAAM,IAAI;MACT;MACA,IAAIA,MAAM,EAAE;QACVjD,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgD,MAAM,CAAC;QACnD;QACA,IAAI,CAAC9C,mBAAmB,EAAE;MAC5B;IACF,CAAC,EACAgD,MAAM,IAAI;MACT;MACAnD,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEkD,MAAM,CAAC;IACzC,CAAC,CACF;EACH;EAEOnG,UAAUA,CAACC,QAAgB;IAChC,IAAI,CAAC4B,MAAM,CAAC0D,QAAQ,CAAC,CAAC,eAAe,EAAEtF,QAAQ,CAAC,EAAE;MAChD0C,WAAW,EAAE;QAAEyD,IAAI,EAAE,SAAS;QAAEjE,SAAS,EAAE,IAAI,CAACA;MAAS;KAC1D,CAAC;EACJ;EAEO/B,cAAcA,CAACiG,MAAW,EAAEC,SAAiB;IAClD,IAAI,CAACD,MAAM,EAAEpG,QAAQ,IAAI,CAACqG,SAAS,EAAE;MAAE;IAAQ;IAC/C,MAAMC,OAAO,GAAG,CACd,UAAU,EACV,uBAAuB,EACvB,cAAc,EACd,SAAS,EACT,cAAc,EACd,WAAW,EACX,mBAAmB,CACpB;IACD,IAAI,CAACA,OAAO,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAAE;IAAQ;IAE5C,MAAMG,QAAQ,GAAGJ,MAAM,CAACzF,oBAAoB;IAC5CyF,MAAM,CAACzF,oBAAoB,GAAG0F,SAAS;IACvC,IAAI,CAACzF,SAAS,GAAG,IAAI;IACrB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;IAEvB,IAAI,CAACpB,cAAc,CAChByE,gCAAgC,CAAC;MAAEzG,QAAQ,EAAEoG,MAAM,CAACpG,QAAQ;MAAEW,oBAAoB,EAAE0F;IAAS,CAAE,CAAC,CAChG1D,SAAS,CAAC;MACTa,IAAI,EAAGC,GAAQ,IAAI;QACjB,MAAMC,OAAO,GAAGD,GAAG,EAAEC,OAAO,IAAID,GAAG,EAAEE,YAAY,EAAED,OAAO;QAC1D,IAAIA,OAAO,EAAE;UACX0C,MAAM,CAACzF,oBAAoB,GAAG6F,QAAQ;UACtC,IAAI,CAAC5F,SAAS,GAAG,KAAK;UACtB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;QACzB;QACA,IAAI,CAACxC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;MACzB,CAAC;MACDY,KAAK,EAAEA,CAAA,KAAK;QACVoC,MAAM,CAACzF,oBAAoB,GAAG6F,QAAQ;QACtC,IAAI,CAAC5F,SAAS,GAAG,KAAK;QACtB,IAAI,CAACiB,GAAG,CAACuB,YAAY,EAAE;MACzB;KACD,CAAC;EACN;EAEOsD,cAAcA,CAACC,MAAc;IAClC,IAAI,CAACA,MAAM,EAAE,OAAO,YAAY;IAChC,OACE,SAAS,GAAGA,MAAM,CAACC,WAAW,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;EAE7E;EAEAC,OAAOA,CAACC,GAAW,EAAE7G,MAAW;IAC9B,IAAI,CAACiC,WAAW,GAAG4E,GAAG;IACtB,IAAI,CAAClF,GAAG,CAACuB,YAAY,EAAE;EACzB;;qCAxOW1B,oBAAoB,EAAApD,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAA5I,EAAA,CAAA0I,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA7I,EAAA,CAAA0I,iBAAA,CAAA1I,EAAA,CAAA8I,iBAAA,GAAA9I,EAAA,CAAA0I,iBAAA,CAAAK,EAAA,CAAAC,UAAA,GAAAhJ,EAAA,CAAA0I,iBAAA,CAAAO,EAAA,CAAAC,eAAA,GAAAlJ,EAAA,CAAA0I,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAApJ,EAAA,CAAA0I,iBAAA,CAAAW,EAAA,CAAAC,QAAA;EAAA;;UAApBlG,oBAAoB;IAAAmG,SAAA;IAAAC,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCbjC7J,EAAA,CAAAyC,UAAA,IAAAsH,mCAAA,iBAA0D;QAS1D/J,EAAA,CAAAC,cAAA,aAAoC;QAElCD,EAAA,CAAAyC,UAAA,IAAAuH,mCAAA,kBAAsD;QAyHxDhK,EAAA,CAAAG,YAAA,EAAM;;;QApIAH,EAAA,CAAAoC,UAAA,SAAA0H,GAAA,CAAAxH,SAAA,CAAe;QAWoBtC,EAAA,CAAAa,SAAA,GAAa;QAAbb,EAAA,CAAAoC,UAAA,SAAA0H,GAAA,CAAA/I,OAAA,CAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
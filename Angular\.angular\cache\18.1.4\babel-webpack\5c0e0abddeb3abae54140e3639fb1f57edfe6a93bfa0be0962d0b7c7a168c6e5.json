{"ast": null, "code": "import { FormControl } from '@angular/forms';\nimport { Subject, debounceTime, distinctUntilChanged } from 'rxjs';\nimport { AppSettings } from 'src/app/app.settings';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"../../services/exceljs.service\";\nimport * as i3 from \"../../services/http-utils.service\";\nimport * as i4 from \"../../services/app.service\";\nimport * as i5 from \"../../services/custom-layout.utils.service\";\nimport * as i6 from \"../../services/activity-log.service\";\nimport * as i7 from \"../../services/kendo-column.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@angular/forms\";\nimport * as i10 from \"@progress/kendo-angular-grid\";\nimport * as i11 from \"@progress/kendo-angular-inputs\";\nimport * as i12 from \"@progress/kendo-angular-buttons\";\nimport * as i13 from \"@progress/kendo-angular-dropdowns\";\nconst _c0 = [\"normalGrid\"];\nconst _c1 = () => [10, 15, 20, 50, 100];\nconst _c2 = a0 => ({\n  pageSizes: a0,\n  previousNext: true,\n  info: true,\n  type: \"numeric\",\n  buttonCount: 5\n});\nconst _c3 = () => ({\n  allowUnsort: true,\n  mode: \"single\"\n});\nconst _c4 = () => ({\n  checkboxOnly: true,\n  mode: \"multiple\"\n});\nconst _c5 = () => ({\n  filter: true\n});\nfunction ActivityLogListComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"div\", 26)(2, \"div\", 27)(3, \"span\", 28);\n    i0.ɵɵtext(4, \"Loading...\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 29);\n    i0.ɵɵtext(6, \"Loading...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActivityLogListComponent_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 30)(1, \"kendo-textbox\", 31);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivityLogListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.searchData, $event) || (ctx_r2.searchData = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"keydown\", function ActivityLogListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchKeyDown($event));\n    })(\"ngModelChange\", function ActivityLogListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.onSearchChange());\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(2, \"kendo-grid-spacer\");\n    i0.ɵɵelementStart(3, \"div\", 32)(4, \"span\", 33);\n    i0.ɵɵtext(5, \"Total:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 34);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_4_Template_button_click_8_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.resetTable());\n    });\n    i0.ɵɵelement(9, \"i\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_4_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.refreshGrid());\n    });\n    i0.ɵɵelement(11, \"i\", 38);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", 500, \"px\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.searchData);\n    i0.ɵɵproperty(\"clearButton\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.page.totalElements);\n  }\n}\nfunction ActivityLogListComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"div\", 41)(3, \"label\", 42);\n    i0.ɵɵtext(4, \"Status\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"kendo-dropdownlist\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivityLogListComponent_div_5_Template_kendo_dropdownlist_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.status, $event) || (ctx_r2.appliedFilters.status = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 41)(7, \"label\", 42);\n    i0.ɵɵtext(8, \"User Type\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"kendo-dropdownlist\", 43);\n    i0.ɵɵtwoWayListener(\"ngModelChange\", function ActivityLogListComponent_div_5_Template_kendo_dropdownlist_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r2.appliedFilters.userType, $event) || (ctx_r2.appliedFilters.userType = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 44)(11, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_div_5_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.applyAdvancedFilters());\n    });\n    i0.ɵɵtext(12, \" Apply Filters \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"button\", 46);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_div_5_Template_button_click_13_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.clearAdvancedFilters());\n    });\n    i0.ɵɵtext(14, \" Clear Filters \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.status)(\"textField\", \"text\")(\"valueField\", \"value\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.status);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"data\", ctx_r2.advancedFilterOptions.userTypes)(\"textField\", \"text\")(\"valueField\", \"value\");\n    i0.ɵɵtwoWayProperty(\"ngModel\", ctx_r2.appliedFilters.userType);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"themeColor\", \"primary\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"themeColor\", \"secondary\");\n  }\n}\nfunction ActivityLogListComponent_ng_template_27_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_27_Template_button_click_1_listener() {\n      const dataItem_r6 = i0.ɵɵrestoreView(_r5).dataItem;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewLog(dataItem_r6));\n    });\n    i0.ɵɵelement(2, \"i\", 49);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"themeColor\", \"primary\");\n  }\n}\nfunction ActivityLogListComponent_ng_template_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 50);\n    i0.ɵɵelement(1, \"i\", 51);\n    i0.ɵɵelementStart(2, \"span\", 34);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const dataItem_r7 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(ctx_r2.getEventIcon(dataItem_r7.activityEvent));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(dataItem_r7.activityEvent);\n  }\n}\nfunction ActivityLogListComponent_ng_template_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r8 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.truncateText(dataItem_r8.eventDescription, 40), \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 52);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r9 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r9.tableName);\n  }\n}\nfunction ActivityLogListComponent_ng_template_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 53);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r10 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngClass\", ctx_r2.getStatusClass(dataItem_r10.activityStatus));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r10.activityStatus, \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 54);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const dataItem_r11 = ctx.dataItem;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(dataItem_r11.activityUserType);\n  }\n}\nfunction ActivityLogListComponent_ng_template_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r12 = ctx.dataItem;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.formatDate(dataItem_r12.createdDate), \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtext(0);\n  }\n  if (rf & 2) {\n    const dataItem_r13 = ctx.dataItem;\n    i0.ɵɵtextInterpolate1(\" \", dataItem_r13.createdByUserFullName || \"System\", \" \");\n  }\n}\nfunction ActivityLogListComponent_ng_template_42_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r14 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 56)(1, \"div\", 57);\n    i0.ɵɵelement(2, \"i\", 58);\n    i0.ɵɵelementStart(3, \"p\", 11);\n    i0.ɵɵtext(4, \"No activity logs found\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 59);\n    i0.ɵɵlistener(\"click\", function ActivityLogListComponent_ng_template_42_div_0_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r14);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.loadTable());\n    });\n    i0.ɵɵelement(6, \"i\", 60);\n    i0.ɵɵtext(7, \"Refresh \");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction ActivityLogListComponent_ng_template_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ActivityLogListComponent_ng_template_42_div_0_Template, 8, 0, \"div\", 55);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.loading && !ctx_r2.isLoading);\n  }\n}\nfunction ActivityLogListComponent_div_43_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 61);\n    i0.ɵɵelement(1, \"i\", 62);\n    i0.ɵɵelementStart(2, \"h3\", 63);\n    i0.ɵɵtext(3, \"No Activity Logs Available\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\", 64);\n    i0.ɵɵtext(5, \"Activity logs will appear here as users perform actions in the system.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class ActivityLogListComponent {\n  cdr;\n  modalService;\n  exceljsService;\n  httpUtilService;\n  AppService;\n  layoutUtilService;\n  activityLogService;\n  kendoColumnService;\n  grid;\n  // Data\n  serverSideRowData = [];\n  gridData = [];\n  IsListHasValue = false;\n  loading = false;\n  isLoading = false;\n  loginUser = {};\n  // Search\n  searchData = '';\n  searchTerms = new Subject();\n  searchSubscription;\n  // Enhanced Filters for Kendo UI\n  filter = {\n    logic: 'and',\n    filters: []\n  };\n  gridFilter = {\n    logic: 'and',\n    filters: []\n  };\n  activeFilters = [];\n  filterOptions = [{\n    text: 'All',\n    value: null\n  }, {\n    text: 'Success',\n    value: 'SUCCESS'\n  }, {\n    text: 'Failed',\n    value: 'FAILED'\n  }];\n  // Advanced filter options\n  advancedFilterOptions = {\n    status: [{\n      text: 'All',\n      value: null\n    }, {\n      text: 'Success',\n      value: 'SUCCESS'\n    }, {\n      text: 'Failed',\n      value: 'FAILED'\n    }],\n    userTypes: [{\n      text: 'All Types',\n      value: null\n    }, {\n      text: 'Admin',\n      value: 'ADMIN'\n    }, {\n      text: 'User',\n      value: 'USER'\n    }]\n  };\n  // Filter state\n  showAdvancedFilters = false;\n  appliedFilters = {};\n  // Kendo Grid properties\n  page = {\n    size: 10,\n    pageNumber: 0,\n    totalElements: 0,\n    totalPages: 0,\n    orderBy: 'createdDate',\n    orderDir: 'desc'\n  };\n  skip = 0;\n  sort = [{\n    field: 'createdDate',\n    dir: 'desc'\n  }];\n  // Column visibility system properties\n  kendoHide;\n  hiddenData = [];\n  kendoColOrder = [];\n  kendoInitColOrder = [];\n  hiddenFields = [];\n  // Column configuration for the new system\n  gridColumns = [];\n  defaultColumns = [];\n  fixedColumns = [];\n  draggableColumns = [];\n  GRID_STATE_KEY = 'activity-logs-grid-state';\n  // Export options\n  exportOptions = [{\n    text: 'Export All',\n    value: 'all'\n  }, {\n    text: 'Export Selected',\n    value: 'selected'\n  }, {\n    text: 'Export Filtered',\n    value: 'filtered'\n  }];\n  // Selection state\n  selectedLogs = [];\n  isAllSelected = false;\n  // Manual column sort indicators for header clicks\n  columnSortStates = {};\n  // Statistics\n  logStatistics = {\n    successLogs: 0,\n    failedLogs: 0,\n    totalLogs: 0\n  };\n  // Legacy properties (keeping for backward compatibility)\n  pageSize = AppSettings.PAGE_SIZE;\n  pageSizeOptions = AppSettings.PAGE_SIZE_OPTIONS;\n  itemsPerPage = new FormControl(this.pageSize);\n  defaultOrder = 'desc';\n  defaultOrderBy = 'createdDate';\n  statusData = false;\n  selectedTab = 'All';\n  innerWidth;\n  displayMobile = false;\n  constructor(cdr, modalService, exceljsService, httpUtilService, AppService, layoutUtilService, activityLogService, kendoColumnService) {\n    this.cdr = cdr;\n    this.modalService = modalService;\n    this.exceljsService = exceljsService;\n    this.httpUtilService = httpUtilService;\n    this.AppService = AppService;\n    this.layoutUtilService = layoutUtilService;\n    this.activityLogService = activityLogService;\n    this.kendoColumnService = kendoColumnService;\n    // set the default paging options\n    this.page.pageNumber = 0;\n    this.page.size = this.pageSize;\n    this.page.orderBy = 'createdDate';\n    this.page.orderDir = 'desc';\n  }\n  ngOnInit() {\n    this.loginUser = this.AppService.getLoggedInUser();\n    this.setupSearchSubscription();\n    this.loadTable();\n    this.loadLogStatistics();\n  }\n  ngAfterViewInit() {\n    this.loadGridState();\n    this.setupGridEventHandlers();\n  }\n  // Method to handle when the component becomes visible\n  onTabActivated() {\n    // Set loading state for tab activation\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data when the tab is activated\n    this.loadTable();\n    this.loadLogStatistics();\n  }\n  // Refresh grid data - only refresh the grid with latest API call\n  refreshGrid() {\n    // Set loading state to show full-screen loader\n    this.loading = true;\n    this.isLoading = true;\n    // Refresh the data\n    this.loadTable();\n  }\n  ngOnDestroy() {\n    if (this.searchSubscription) {\n      this.searchSubscription.unsubscribe();\n    }\n    this.searchTerms.complete();\n  }\n  // Setup search subscription with debouncing\n  setupSearchSubscription() {\n    this.searchSubscription = this.searchTerms.pipe(debounceTime(500), distinctUntilChanged()).subscribe(() => {\n      // Set loading state for search\n      this.loading = true;\n      this.isLoading = true;\n      this.loadTable();\n    });\n  }\n  // Setup grid event handlers\n  setupGridEventHandlers() {\n    if (this.grid) {\n      this.grid.pageChange.subscribe(event => {\n        this.pageChange(event);\n      });\n    }\n  }\n  // Load grid state from localStorage\n  loadGridState() {\n    try {\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\n      if (savedState) {\n        const state = JSON.parse(savedState);\n        this.page.size = state.pageSize || this.page.size;\n        this.sort = state.sort || this.sort;\n        this.filter = state.filter || this.filter;\n        this.skip = state.skip || 0;\n      }\n    } catch (error) {\n      console.warn('Error loading grid state:', error);\n    }\n  }\n  // Save grid state to localStorage\n  saveGridState() {\n    try {\n      const state = {\n        pageSize: this.page.size,\n        sort: this.sort,\n        filter: this.filter,\n        skip: this.skip\n      };\n      localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\n    } catch (error) {\n      console.warn('Error saving grid state:', error);\n    }\n  }\n  // Load activity logs table\n  loadTable() {\n    this.loadTableWithKendoEndpoint();\n  }\n  // Load data using Kendo UI specific endpoint\n  loadTableWithKendoEndpoint() {\n    this.loading = true;\n    this.isLoading = true;\n    // Enable loader\n    this.httpUtilService.loadingSubject.next(true);\n    // Prepare state object for Kendo UI endpoint\n    // When sort is empty (3rd click), send default sort to backend\n    const sortForBackend = this.sort.length > 0 ? this.sort : [{\n      field: 'createdDate',\n      dir: 'desc'\n    }];\n    const state = {\n      take: this.page.size,\n      skip: this.skip,\n      sort: sortForBackend,\n      filter: this.filter,\n      search: this.searchData\n    };\n    this.activityLogService.getActivityLogsForKendoGrid(state).subscribe({\n      next: data => {\n        // Handle the new API response structure\n        if (data.isFault || data.responseData && data.responseData.errors && data.responseData.errors.length > 0) {\n          const errors = data.responseData?.errors || data.errors || [];\n          console.error('Kendo UI Grid errors:', errors);\n          this.handleEmptyResponse();\n        } else {\n          // Handle both old and new response structures\n          const responseData = data.responseData || data;\n          const logData = responseData.data || [];\n          const total = responseData.total || 0;\n          this.IsListHasValue = logData.length !== 0;\n          this.serverSideRowData = logData;\n          this.page.totalElements = total;\n          this.page.totalPages = Math.ceil(total / this.page.size);\n          // Create a data source with total count for Kendo Grid\n          this.gridData = {\n            data: logData,\n            total: total\n          };\n        }\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      error: error => {\n        console.error('Error loading data with Kendo UI endpoint:', error);\n        this.handleEmptyResponse();\n        this.loading = false;\n        this.isLoading = false;\n        this.httpUtilService.loadingSubject.next(false);\n      },\n      complete: () => {\n        this.loading = false;\n        this.isLoading = false;\n      }\n    });\n  }\n  // Handle empty response\n  handleEmptyResponse() {\n    this.IsListHasValue = false;\n    this.serverSideRowData = [];\n    this.gridData = [];\n    this.page.totalElements = 0;\n    this.page.totalPages = 0;\n  }\n  // Load log statistics\n  loadLogStatistics() {\n    // This would be implemented if there's a statistics endpoint\n    // For now, we'll calculate from the current data\n    this.updateLogStatistics();\n  }\n  // Update log statistics\n  updateLogStatistics() {\n    const logs = this.serverSideRowData;\n    this.logStatistics = {\n      successLogs: logs.filter(l => l.activityStatus === 'SUCCESS').length,\n      failedLogs: logs.filter(l => l.activityStatus === 'FAILED').length,\n      totalLogs: logs.length\n    };\n  }\n  // Search functionality\n  onSearchKeyDown(event) {\n    if (event.key === 'Enter') {\n      this.loadTable();\n    }\n  }\n  onSearchChange() {\n    console.log('Search changed:', this.searchData);\n    // Trigger search with debounce\n    this.searchTerms.next(this.searchData || '');\n  }\n  clearSearch() {\n    if (!this.searchData || this.searchData.trim() === '') {\n      this.searchTerms.next('');\n    }\n  }\n  // Advanced filters\n  toggleAdvancedFilters() {\n    this.showAdvancedFilters = !this.showAdvancedFilters;\n  }\n  applyAdvancedFilters() {\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    if (this.appliedFilters.status) {\n      this.filter.filters.push({\n        field: 'activityStatus',\n        operator: 'eq',\n        value: this.appliedFilters.status\n      });\n    }\n    if (this.appliedFilters.userType) {\n      this.filter.filters.push({\n        field: 'activityUserType',\n        operator: 'eq',\n        value: this.appliedFilters.userType\n      });\n    }\n    this.loadTable();\n  }\n  clearAdvancedFilters() {\n    this.appliedFilters = {};\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.loadTable();\n  }\n  // Kendo UI Grid event handlers\n  onSortChange(event) {\n    const sort = event.sort;\n    // Check if this is the 3rd click (dir is undefined)\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\n    if (isThirdClick) {\n      // 3rd click - clear sort and use default\n      this.sort = [];\n      this.page.orderBy = 'createdDate';\n      this.page.orderDir = 'desc';\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\n      // Valid sort with direction\n      this.sort = sort;\n      this.page.orderBy = sort[0].field || 'createdDate';\n      this.page.orderDir = sort[0].dir;\n    } else {\n      // Empty sort array or invalid sort\n      this.sort = [];\n      this.page.orderBy = 'createdDate';\n      this.page.orderDir = 'desc';\n    }\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.saveGridState();\n    // Set loading state for sorting\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  // Use native Kendo sort/unsort via sortChange\n  onSelectionChange(event) {\n    this.selectedLogs = event.selectedRows || [];\n    this.isAllSelected = this.selectedLogs.length === this.serverSideRowData.length;\n  }\n  onColumnReorder(event) {\n    // Handle column reordering\n    this.kendoColOrder = event.columns;\n  }\n  updateColumnVisibility(event) {\n    // Handle column visibility changes\n    this.kendoHide = event.columns;\n  }\n  filterChange(event) {\n    this.filter = event.filter;\n    this.skip = 0;\n    this.page.pageNumber = 0;\n    this.saveGridState();\n    // Set loading state for filtering\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  pageChange(event) {\n    // Use Kendo's provided values as source of truth\n    this.skip = event.skip;\n    this.page.size = event.take || this.page.size;\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\n    this.saveGridState();\n    // Set loading state for pagination\n    this.loading = true;\n    this.isLoading = true;\n    this.loadTable();\n  }\n  /**\n   * Reset the current state of column visibility and order in the grid to its original state.\n   */\n  resetTable() {\n    // Check if loginUser is available\n    if (!this.loginUser || !this.loginUser.userId) {\n      console.error('loginUser not available:', this.loginUser);\n      this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');\n      return;\n    }\n    // Reset all grid state to default\n    this.page.pageNumber = 0;\n    this.skip = 0;\n    this.sort = [{\n      field: 'createdDate',\n      dir: 'desc'\n    }];\n    this.filter = {\n      logic: 'and',\n      filters: []\n    };\n    this.searchData = '';\n    this.appliedFilters = {};\n    this.showAdvancedFilters = false;\n    // Reset column visibility and order\n    if (this.grid && this.grid.columns) {\n      this.grid.columns.forEach(column => {\n        const index = this.gridColumns.indexOf(column.field);\n        if (index !== -1) {\n          column.orderIndex = index;\n        }\n        // Reset column visibility - show all columns\n        if (column.field && column.field !== 'action') {\n          column.hidden = false;\n        }\n      });\n    }\n    // Clear hidden columns\n    this.hiddenData = [];\n    this.kendoColOrder = [];\n    this.hiddenFields = [];\n    // Reset the Kendo Grid's internal state\n    if (this.grid) {\n      // Clear all filters\n      this.grid.filter = {\n        logic: 'and',\n        filters: []\n      };\n      // Reset sorting\n      this.grid.sort = [{\n        field: 'createdDate',\n        dir: 'desc'\n      }];\n      // Reset to first page\n      this.grid.skip = 0;\n      this.grid.pageSize = this.page.size;\n    }\n    // Prepare reset data\n    const userData = {\n      pageName: 'ActivityLogs',\n      userID: this.loginUser.userId,\n      hiddenData: [],\n      kendoColOrder: [],\n      LoggedId: this.loginUser.userId\n    };\n    // Show loading state\n    this.httpUtilService.loadingSubject.next(true);\n    // Save reset state to backend\n    this.kendoColumnService.createHideFields(userData).subscribe({\n      next: res => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!res.isFault) {\n          // Also clear from localStorage\n          this.kendoColumnService.clearFromLocalStorage('ActivityLogs');\n          this.layoutUtilService.showSuccess(res.message || 'Column settings reset successfully.', '');\n        } else {\n          this.layoutUtilService.showError(res.message || 'Failed to reset column settings.', '');\n        }\n        // Trigger change detection and refresh grid\n        this.cdr.detectChanges();\n        // Small delay to ensure the grid is updated\n        setTimeout(() => {\n          if (this.grid) {\n            this.grid.refresh();\n          }\n        }, 100);\n        this.loadTable();\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error resetting column settings:', error);\n        // Check if it's an authentication error\n        if (error.status === 401 || error.error && error.error.status === 401) {\n          this.layoutUtilService.showError('Authentication failed. Please login again.', '');\n          // Optionally redirect to login page\n        } else {\n          this.layoutUtilService.showError('Error resetting column settings. Please try again.', '');\n        }\n      }\n    });\n  }\n  // Export functionality\n  exportData(exportType) {\n    let dataToExport = [];\n    switch (exportType) {\n      case 'all':\n        dataToExport = this.serverSideRowData;\n        break;\n      case 'selected':\n        dataToExport = this.selectedLogs;\n        break;\n      case 'filtered':\n        dataToExport = this.serverSideRowData;\n        break;\n    }\n    if (dataToExport.length === 0) {\n      return;\n    }\n    const exportData = dataToExport.map(log => ({\n      'Event': log.activityEvent,\n      'Description': log.eventDescription,\n      'Details': log.eventDetails,\n      'Table': log.tableName,\n      'Status': log.activityStatus,\n      'User Type': log.activityUserType,\n      'Created Date': this.AppService.formatDate(log.createdDate),\n      'User': log.createdByUserFullName\n    }));\n    const headers = Object.keys(exportData[0]);\n    const rows = exportData.map(item => Object.values(item));\n    const colSize = headers.map((_, index) => ({\n      id: index + 1,\n      width: 20\n    }));\n    this.exceljsService.generateExcel('Activity_Logs_Export', headers, rows, colSize);\n  }\n  // Log actions\n  viewLog(log) {\n    // Navigate to view log page\n    console.log('View log:', log);\n  }\n  // Utility methods\n  getStatusClass(status) {\n    return status === 'SUCCESS' ? 'badge-success' : 'badge-danger';\n  }\n  formatDate(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\n  }\n  truncateText(text, maxLength = 50) {\n    if (!text) return '';\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\n  }\n  getEventIcon(event) {\n    const eventIcons = {\n      'USER_CREATED': 'fas fa-user-plus',\n      'USER_UPDATED': 'fas fa-user-edit',\n      'USER_DELETED': 'fas fa-user-minus',\n      'ROLE_CREATED': 'fas fa-shield-plus',\n      'ROLE_UPDATED': 'fas fa-shield-edit',\n      'ROLE_DELETED': 'fas fa-shield-minus',\n      'LOGIN': 'fas fa-sign-in-alt',\n      'LOGOUT': 'fas fa-sign-out-alt',\n      'PASSWORD_CHANGED': 'fas fa-key',\n      'default': 'fas fa-info-circle'\n    };\n    return eventIcons[event] || eventIcons['default'];\n  }\n  static ɵfac = function ActivityLogListComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || ActivityLogListComponent)(i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.ExceljsService), i0.ɵɵdirectiveInject(i3.HttpUtilsService), i0.ɵɵdirectiveInject(i4.AppService), i0.ɵɵdirectiveInject(i5.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i6.ActivityLogService), i0.ɵɵdirectiveInject(i7.KendoColumnService));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ActivityLogListComponent,\n    selectors: [[\"app-activity-log-list\"]],\n    viewQuery: function ActivityLogListComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.grid = _t.first);\n      }\n    },\n    decls: 44,\n    vars: 43,\n    consts: [[\"normalGrid\", \"\"], [\"class\", \"fullscreen-loading-overlay\", 4, \"ngIf\"], [1, \"grid-container\"], [2, \"width\", \"auto\", \"overflow-x\", \"auto\", 3, \"columnReorder\", \"selectionChange\", \"filterChange\", \"pageChange\", \"sortChange\", \"columnVisibilityChange\", \"data\", \"pageSize\", \"sort\", \"pageable\", \"total\", \"sortable\", \"groupable\", \"selectable\", \"reorderable\", \"resizable\", \"height\", \"skip\", \"filter\", \"columnMenu\"], [\"kendoGridToolbarTemplate\", \"\"], [\"class\", \"advanced-filters-panel p-3 bg-light border-bottom\", 4, \"ngIf\"], [1, \"log-statistics\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\", \"text-center\"], [1, \"col-md-4\"], [1, \"stat-item\"], [1, \"text-success\", \"mb-0\"], [1, \"text-muted\"], [1, \"text-danger\", \"mb-0\"], [1, \"text-primary\", \"mb-0\"], [\"field\", \"action\", \"title\", \"Actions\", 3, \"width\", \"sortable\", \"filterable\"], [\"kendoGridCellTemplate\", \"\"], [\"field\", \"activityEvent\", \"title\", \"Event\", 3, \"width\", \"filterable\"], [\"field\", \"eventDescription\", \"title\", \"Description\", 3, \"width\", \"filterable\"], [\"field\", \"tableName\", \"title\", \"Table\", 3, \"width\", \"filterable\"], [\"field\", \"activityStatus\", \"title\", \"Status\", 3, \"width\", \"filterable\"], [\"field\", \"activityUserType\", \"title\", \"User Type\", 3, \"width\", \"filterable\"], [\"field\", \"createdDate\", \"title\", \"Created Date\", \"format\", \"MM/dd/yyyy\", 3, \"width\", \"filterable\"], [\"field\", \"createdByUserFullName\", \"title\", \"User\", 3, \"width\", \"filterable\"], [\"kendoGridNoRecordsTemplate\", \"\"], [\"class\", \"text-center p-5\", 4, \"ngIf\"], [1, \"fullscreen-loading-overlay\"], [1, \"loading-content\"], [\"role\", \"status\", 1, \"custom-colored-spinner\"], [1, \"visually-hidden\"], [1, \"mt-4\", \"fs-5\"], [1, \"d-flex\", \"align-items-center\", \"me-3\", \"search-section\"], [\"placeholder\", \"Search...\", 3, \"ngModelChange\", \"keydown\", \"ngModel\", \"clearButton\"], [1, \"d-flex\", \"align-items-center\", \"me-3\"], [1, \"text-muted\", \"me-2\"], [1, \"fw-bold\"], [\"type\", \"button\", \"title\", \"Reset to Default\", 1, \"btn\", \"btn-icon\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-undo\", \"text-warning\"], [\"type\", \"button\", \"title\", \"Refresh Grid Data\", 1, \"btn\", \"btn-info\", \"btn-sm\", \"me-2\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"advanced-filters-panel\", \"p-3\", \"bg-light\", \"border-bottom\"], [1, \"row\"], [1, \"col-md-3\"], [1, \"form-label\"], [1, \"w-100\", 3, \"ngModelChange\", \"data\", \"textField\", \"valueField\", \"ngModel\"], [1, \"col-md-6\", \"d-flex\", \"align-items-end\"], [\"kendoButton\", \"\", 1, \"me-2\", 3, \"click\", \"themeColor\"], [\"kendoButton\", \"\", 3, \"click\", \"themeColor\"], [1, \"btn-group\", \"btn-group-sm\"], [\"kendoButton\", \"\", 1, \"btn-sm\", 3, \"click\", \"themeColor\"], [1, \"fas\", \"fa-eye\"], [1, \"d-flex\", \"align-items-center\"], [1, \"me-2\", \"text-primary\"], [1, \"badge\", \"bg-info\"], [1, \"badge\", 3, \"ngClass\"], [1, \"badge\", \"bg-secondary\"], [\"class\", \"custom-no-records\", 4, \"ngIf\"], [1, \"custom-no-records\"], [1, \"text-center\"], [1, \"fas\", \"fa-clipboard-list\", \"text-muted\", \"mb-2\", 2, \"font-size\", \"2rem\"], [\"kendoButton\", \"\", 1, \"btn-primary\", 3, \"click\"], [1, \"fas\", \"fa-refresh\", \"me-2\"], [1, \"text-center\", \"p-5\"], [1, \"fas\", \"fa-clipboard-list\", \"fa-4x\", \"text-muted\", \"mb-4\"], [1, \"text-muted\", \"mb-3\"], [1, \"text-muted\", \"mb-4\"]],\n    template: function ActivityLogListComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        const _r1 = i0.ɵɵgetCurrentView();\n        i0.ɵɵtemplate(0, ActivityLogListComponent_div_0_Template, 7, 0, \"div\", 1);\n        i0.ɵɵelementStart(1, \"div\", 2)(2, \"kendo-grid\", 3, 0);\n        i0.ɵɵlistener(\"columnReorder\", function ActivityLogListComponent_Template_kendo_grid_columnReorder_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onColumnReorder($event));\n        })(\"selectionChange\", function ActivityLogListComponent_Template_kendo_grid_selectionChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSelectionChange($event));\n        })(\"filterChange\", function ActivityLogListComponent_Template_kendo_grid_filterChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.filterChange($event));\n        })(\"pageChange\", function ActivityLogListComponent_Template_kendo_grid_pageChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.pageChange($event));\n        })(\"sortChange\", function ActivityLogListComponent_Template_kendo_grid_sortChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.onSortChange($event));\n        })(\"columnVisibilityChange\", function ActivityLogListComponent_Template_kendo_grid_columnVisibilityChange_2_listener($event) {\n          i0.ɵɵrestoreView(_r1);\n          return i0.ɵɵresetView(ctx.updateColumnVisibility($event));\n        });\n        i0.ɵɵtemplate(4, ActivityLogListComponent_ng_template_4_Template, 12, 5, \"ng-template\", 4)(5, ActivityLogListComponent_div_5_Template, 15, 10, \"div\", 5);\n        i0.ɵɵelementStart(6, \"div\", 6)(7, \"div\", 7)(8, \"div\", 8)(9, \"div\", 9)(10, \"h4\", 10);\n        i0.ɵɵtext(11);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(12, \"small\", 11);\n        i0.ɵɵtext(13, \"Success Logs\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(14, \"div\", 8)(15, \"div\", 9)(16, \"h4\", 12);\n        i0.ɵɵtext(17);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"small\", 11);\n        i0.ɵɵtext(19, \"Failed Logs\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(20, \"div\", 8)(21, \"div\", 9)(22, \"h4\", 13);\n        i0.ɵɵtext(23);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(24, \"small\", 11);\n        i0.ɵɵtext(25, \"Total Logs\");\n        i0.ɵɵelementEnd()()()()();\n        i0.ɵɵelementStart(26, \"kendo-grid-column\", 14);\n        i0.ɵɵtemplate(27, ActivityLogListComponent_ng_template_27_Template, 3, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(28, \"kendo-grid-column\", 16);\n        i0.ɵɵtemplate(29, ActivityLogListComponent_ng_template_29_Template, 4, 3, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(30, \"kendo-grid-column\", 17);\n        i0.ɵɵtemplate(31, ActivityLogListComponent_ng_template_31_Template, 1, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(32, \"kendo-grid-column\", 18);\n        i0.ɵɵtemplate(33, ActivityLogListComponent_ng_template_33_Template, 2, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(34, \"kendo-grid-column\", 19);\n        i0.ɵɵtemplate(35, ActivityLogListComponent_ng_template_35_Template, 2, 2, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(36, \"kendo-grid-column\", 20);\n        i0.ɵɵtemplate(37, ActivityLogListComponent_ng_template_37_Template, 2, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(38, \"kendo-grid-column\", 21);\n        i0.ɵɵtemplate(39, ActivityLogListComponent_ng_template_39_Template, 1, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(40, \"kendo-grid-column\", 22);\n        i0.ɵɵtemplate(41, ActivityLogListComponent_ng_template_41_Template, 1, 1, \"ng-template\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(42, ActivityLogListComponent_ng_template_42_Template, 1, 1, \"ng-template\", 23);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵtemplate(43, ActivityLogListComponent_div_43_Template, 6, 0, \"div\", 24);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.loading || ctx.isLoading);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"data\", ctx.gridData)(\"pageSize\", ctx.page.size)(\"sort\", ctx.sort)(\"pageable\", i0.ɵɵpureFunction1(38, _c2, i0.ɵɵpureFunction0(37, _c1)))(\"total\", ctx.page.totalElements)(\"sortable\", i0.ɵɵpureFunction0(40, _c3))(\"groupable\", false)(\"selectable\", i0.ɵɵpureFunction0(41, _c4))(\"reorderable\", true)(\"resizable\", false)(\"height\", 720)(\"skip\", ctx.skip)(\"filter\", ctx.filter)(\"columnMenu\", i0.ɵɵpureFunction0(42, _c5));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.showAdvancedFilters);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.logStatistics.successLogs);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.logStatistics.failedLogs);\n        i0.ɵɵadvance(6);\n        i0.ɵɵtextInterpolate(ctx.logStatistics.totalLogs);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"width\", 100)(\"sortable\", false)(\"filterable\", false);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 180)(\"filterable\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 250)(\"filterable\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 120)(\"filterable\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 100)(\"filterable\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 120)(\"filterable\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 180)(\"filterable\", true);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"width\", 150)(\"filterable\", false);\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", !ctx.loading && !ctx.isLoading && !ctx.IsListHasValue);\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i9.NgControlStatus, i9.NgModel, i10.GridComponent, i10.ToolbarTemplateDirective, i10.GridSpacerComponent, i10.ColumnComponent, i10.CellTemplateDirective, i10.NoRecordsTemplateDirective, i11.TextBoxComponent, i12.ButtonComponent, i13.DropDownListComponent],\n    styles: [\".grid-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  display: flex;\\n  flex-direction: column;\\n  height: 100%;\\n  position: relative;\\n}\\n\\n\\n\\n.grid-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 20px;\\n}\\n.grid-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 24px;\\n  font-weight: 500;\\n}\\n.grid-header[_ngcontent-%COMP%]   .search-container[_ngcontent-%COMP%] {\\n  width: 300px;\\n}\\n\\n.grid-toolbar[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 10px;\\n  align-items: center;\\n  margin-bottom: 10px;\\n}\\n\\n\\n\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 5px;\\n  font-size: 0.875rem;\\n  font-weight: 500;\\n  border-radius: 0.375rem;\\n  transition: all 0.15s ease-in-out;\\n  min-width: 40px;\\n  height: 40px;\\n  border: 1px solid transparent;\\n  background-color: transparent;\\n  \\n\\n  \\n\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-1px);\\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n  background-color: rgba(0, 0, 0, 0.05);\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%] {\\n  padding: 0.5rem;\\n  min-width: 40px;\\n  width: 40px;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   i[_ngcontent-%COMP%], .k-grid-toolbar[_ngcontent-%COMP%]   .btn.btn-icon[_ngcontent-%COMP%]   .svg-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n}\\n.k-grid-toolbar[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%]:not(.btn-icon) {\\n  padding: 0.375rem 0.75rem;\\n  gap: 0.5rem;\\n}\\n\\n\\n\\n.advanced-filters-panel[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n\\n\\n\\n.log-statistics[_ngcontent-%COMP%] {\\n  background-color: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 8px;\\n  margin-bottom: 20px;\\n}\\n.log-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%] {\\n  padding: 15px;\\n}\\n.log-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n  margin-bottom: 5px;\\n}\\n.log-statistics[_ngcontent-%COMP%]   .stat-item[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n  font-size: 0.875rem;\\n  color: #6c757d;\\n}\\n\\n\\n\\n.search-section[_ngcontent-%COMP%]   .k-textbox[_ngcontent-%COMP%] {\\n  border-radius: 6px;\\n}\\n\\n\\n\\n.badge[_ngcontent-%COMP%] {\\n  padding: 6px 12px;\\n  border-radius: 20px;\\n  font-size: 0.75rem;\\n  font-weight: 500;\\n}\\n\\n.badge-success[_ngcontent-%COMP%] {\\n  background-color: #d4edda;\\n  color: #155724;\\n}\\n\\n.badge-danger[_ngcontent-%COMP%] {\\n  background-color: #f8d7da;\\n  color: #721c24;\\n}\\n\\n.badge-info[_ngcontent-%COMP%] {\\n  background-color: #d1ecf1;\\n  color: #0c5460;\\n}\\n\\n.badge-secondary[_ngcontent-%COMP%] {\\n  background-color: #e2e3e5;\\n  color: #383d41;\\n}\\n\\n\\n\\n.btn-group-sm[_ngcontent-%COMP%]   .btn[_ngcontent-%COMP%] {\\n  padding: 4px 8px;\\n  font-size: 0.875rem;\\n  border-radius: 4px;\\n}\\n\\n\\n\\n.custom-no-records[_ngcontent-%COMP%] {\\n  padding: 40px;\\n  text-align: center;\\n}\\n.custom-no-records[_ngcontent-%COMP%]   .spinner-border[_ngcontent-%COMP%] {\\n  width: 2rem;\\n  height: 2rem;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvbW9kdWxlcy9zZXR0aW5nL2FjdGl2aXR5LWxvZy1saXN0L2FjdGl2aXR5LWxvZy1saXN0LmNvbXBvbmVudC5zY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBQTtFQUNBLGFBQUE7RUFDQSxzQkFBQTtFQUNBLFlBQUE7RUFDQSxrQkFBQTtBQUNGOztBQUVBLDJEQUFBO0FBRUE7RUFDRSxhQUFBO0VBQ0EsOEJBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBQUY7QUFFRTtFQUNFLFNBQUE7RUFDQSxlQUFBO0VBQ0EsZ0JBQUE7QUFBSjtBQUdFO0VBQ0UsWUFBQTtBQURKOztBQUtBO0VBQ0UsYUFBQTtFQUNBLFNBQUE7RUFDQSxtQkFBQTtFQUNBLG1CQUFBO0FBRkY7O0FBS0EsMEJBQUE7QUFFRTtFQUNFLG9CQUFBO0VBQ0EsbUJBQUE7RUFDQSx1QkFBQTtFQUNBLFFBQUE7RUFDQSxtQkFBQTtFQUNBLGdCQUFBO0VBQ0EsdUJBQUE7RUFDQSxpQ0FBQTtFQUNBLGVBQUE7RUFDQSxZQUFBO0VBQ0EsNkJBQUE7RUFDQSw2QkFBQTtFQVFBLHNCQUFBO0VBV0Esa0RBQUE7QUFwQko7QUFHSTtFQUNFLDJCQUFBO0VBQ0Esd0NBQUE7RUFDQSxxQ0FBQTtBQUROO0FBS0k7RUFDRSxlQUFBO0VBQ0EsZUFBQTtFQUNBLFdBQUE7QUFITjtBQUtNO0VBQ0UsZUFBQTtBQUhSO0FBUUk7RUFDRSx5QkFBQTtFQUNBLFdBQUE7QUFOTjs7QUFXQSwyQkFBQTtBQUNBO0VBQ0UseUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7QUFSRjs7QUFXQSx1QkFBQTtBQUNBO0VBQ0UseUJBQUE7RUFDQSx5QkFBQTtFQUNBLGtCQUFBO0VBQ0EsbUJBQUE7QUFSRjtBQVVFO0VBQ0UsYUFBQTtBQVJKO0FBVUk7RUFDRSxpQkFBQTtFQUNBLGdCQUFBO0VBQ0Esa0JBQUE7QUFSTjtBQVdJO0VBQ0UsbUJBQUE7RUFDQSxjQUFBO0FBVE47O0FBY0EsbUJBQUE7QUFFRTtFQUNFLGtCQUFBO0FBWko7O0FBZ0JBLGlCQUFBO0FBQ0E7RUFDRSxpQkFBQTtFQUNBLG1CQUFBO0VBQ0Esa0JBQUE7RUFDQSxnQkFBQTtBQWJGOztBQWdCQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQWJGOztBQWdCQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQWJGOztBQWdCQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQWJGOztBQWdCQTtFQUNFLHlCQUFBO0VBQ0EsY0FBQTtBQWJGOztBQWdCQSx3QkFBQTtBQUNBO0VBQ0UsZ0JBQUE7RUFDQSxtQkFBQTtFQUNBLGtCQUFBO0FBYkY7O0FBZ0JBLCtCQUFBO0FBQ0E7RUFDRSxhQUFBO0VBQ0Esa0JBQUE7QUFiRjtBQWVFO0VBQ0UsV0FBQTtFQUNBLFlBQUE7QUFiSiIsInNvdXJjZXNDb250ZW50IjpbIi5ncmlkLWNvbnRhaW5lciB7XHJcbiAgcGFkZGluZzogMjBweDtcclxuICBkaXNwbGF5OiBmbGV4O1xyXG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XHJcbiAgaGVpZ2h0OiAxMDAlO1xyXG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcclxufVxyXG5cclxuLyogTG9hZGluZyBPdmVybGF5IFN0eWxlcyBoYW5kbGVkIGdsb2JhbGx5IGluIHN0eWxlcy5zY3NzICovXHJcblxyXG4uZ3JpZC1oZWFkZXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuXHJcbiAgaDIge1xyXG4gICAgbWFyZ2luOiAwO1xyXG4gICAgZm9udC1zaXplOiAyNHB4O1xyXG4gICAgZm9udC13ZWlnaHQ6IDUwMDtcclxuICB9XHJcblxyXG4gIC5zZWFyY2gtY29udGFpbmVyIHtcclxuICAgIHdpZHRoOiAzMDBweDtcclxuICB9XHJcbn1cclxuXHJcbi5ncmlkLXRvb2xiYXIge1xyXG4gIGRpc3BsYXk6IGZsZXg7XHJcbiAgZ2FwOiAxMHB4O1xyXG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgbWFyZ2luLWJvdHRvbTogMTBweDtcclxufVxyXG5cclxuLyogVG9vbGJhciBidXR0b24gc3R5bGVzICovXHJcbi5rLWdyaWQtdG9vbGJhciB7XHJcbiAgLmJ0biB7XHJcbiAgICBkaXNwbGF5OiBpbmxpbmUtZmxleDtcclxuICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XHJcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcclxuICAgIGdhcDogNXB4O1xyXG4gICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgIGZvbnQtd2VpZ2h0OiA1MDA7XHJcbiAgICBib3JkZXItcmFkaXVzOiAwLjM3NXJlbTtcclxuICAgIHRyYW5zaXRpb246IGFsbCAwLjE1cyBlYXNlLWluLW91dDtcclxuICAgIG1pbi13aWR0aDogNDBweDtcclxuICAgIGhlaWdodDogNDBweDtcclxuICAgIGJvcmRlcjogMXB4IHNvbGlkIHRyYW5zcGFyZW50O1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdHJhbnNwYXJlbnQ7XHJcblxyXG4gICAgJjpob3ZlciB7XHJcbiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMXB4KTtcclxuICAgICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcclxuICAgICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjA1KTtcclxuICAgIH1cclxuXHJcbiAgICAvKiBJY29uLW9ubHkgYnV0dG9ucyAqL1xyXG4gICAgJi5idG4taWNvbiB7XHJcbiAgICAgIHBhZGRpbmc6IDAuNXJlbTtcclxuICAgICAgbWluLXdpZHRoOiA0MHB4O1xyXG4gICAgICB3aWR0aDogNDBweDtcclxuXHJcbiAgICAgIGksIC5zdmctaWNvbiB7XHJcbiAgICAgICAgZm9udC1zaXplOiAxcmVtO1xyXG4gICAgICB9XHJcbiAgICB9XHJcblxyXG4gICAgLyogQnV0dG9ucyB3aXRoIHRleHQgLSBlbnN1cmUgY29uc2lzdGVudCBzcGFjaW5nICovXHJcbiAgICAmOm5vdCguYnRuLWljb24pIHtcclxuICAgICAgcGFkZGluZzogMC4zNzVyZW0gMC43NXJlbTtcclxuICAgICAgZ2FwOiAwLjVyZW07XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vKiBBZHZhbmNlZCBmaWx0ZXJzIHBhbmVsICovXHJcbi5hZHZhbmNlZC1maWx0ZXJzLXBhbmVsIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjhmOWZhO1xyXG4gIGJvcmRlcjogMXB4IHNvbGlkICNkZWUyZTY7XHJcbiAgYm9yZGVyLXJhZGl1czogOHB4O1xyXG4gIG1hcmdpbi1ib3R0b206IDIwcHg7XHJcbn1cclxuXHJcbi8qIFN0YXRpc3RpY3Mgc2VjdGlvbiAqL1xyXG4ubG9nLXN0YXRpc3RpY3Mge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmOGY5ZmE7XHJcbiAgYm9yZGVyOiAxcHggc29saWQgI2RlZTJlNjtcclxuICBib3JkZXItcmFkaXVzOiA4cHg7XHJcbiAgbWFyZ2luLWJvdHRvbTogMjBweDtcclxuXHJcbiAgLnN0YXQtaXRlbSB7XHJcbiAgICBwYWRkaW5nOiAxNXB4O1xyXG4gICAgXHJcbiAgICBoNCB7XHJcbiAgICAgIGZvbnQtc2l6ZTogMS41cmVtO1xyXG4gICAgICBmb250LXdlaWdodDogNjAwO1xyXG4gICAgICBtYXJnaW4tYm90dG9tOiA1cHg7XHJcbiAgICB9XHJcbiAgICBcclxuICAgIHNtYWxsIHtcclxuICAgICAgZm9udC1zaXplOiAwLjg3NXJlbTtcclxuICAgICAgY29sb3I6ICM2Yzc1N2Q7XHJcbiAgICB9XHJcbiAgfVxyXG59XHJcblxyXG4vKiBTZWFyY2ggc2VjdGlvbiAqL1xyXG4uc2VhcmNoLXNlY3Rpb24ge1xyXG4gIC5rLXRleHRib3gge1xyXG4gICAgYm9yZGVyLXJhZGl1czogNnB4O1xyXG4gIH1cclxufVxyXG5cclxuLyogQmFkZ2Ugc3R5bGVzICovXHJcbi5iYWRnZSB7XHJcbiAgcGFkZGluZzogNnB4IDEycHg7XHJcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcclxuICBmb250LXNpemU6IDAuNzVyZW07XHJcbiAgZm9udC13ZWlnaHQ6IDUwMDtcclxufVxyXG5cclxuLmJhZGdlLXN1Y2Nlc3Mge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNkNGVkZGE7XHJcbiAgY29sb3I6ICMxNTU3MjQ7XHJcbn1cclxuXHJcbi5iYWRnZS1kYW5nZXIge1xyXG4gIGJhY2tncm91bmQtY29sb3I6ICNmOGQ3ZGE7XHJcbiAgY29sb3I6ICM3MjFjMjQ7XHJcbn1cclxuXHJcbi5iYWRnZS1pbmZvIHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZDFlY2YxO1xyXG4gIGNvbG9yOiAjMGM1NDYwO1xyXG59XHJcblxyXG4uYmFkZ2Utc2Vjb25kYXJ5IHtcclxuICBiYWNrZ3JvdW5kLWNvbG9yOiAjZTJlM2U1O1xyXG4gIGNvbG9yOiAjMzgzZDQxO1xyXG59XHJcblxyXG4vKiBCdXR0b24gZ3JvdXAgc3R5bGVzICovXHJcbi5idG4tZ3JvdXAtc20gLmJ0biB7XHJcbiAgcGFkZGluZzogNHB4IDhweDtcclxuICBmb250LXNpemU6IDAuODc1cmVtO1xyXG4gIGJvcmRlci1yYWRpdXM6IDRweDtcclxufVxyXG5cclxuLyogQ3VzdG9tIG5vIHJlY29yZHMgdGVtcGxhdGUgKi9cclxuLmN1c3RvbS1uby1yZWNvcmRzIHtcclxuICBwYWRkaW5nOiA0MHB4O1xyXG4gIHRleHQtYWxpZ246IGNlbnRlcjtcclxuICBcclxuICAuc3Bpbm5lci1ib3JkZXIge1xyXG4gICAgd2lkdGg6IDJyZW07XHJcbiAgICBoZWlnaHQ6IDJyZW07XHJcbiAgfVxyXG59XHJcbiJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n  });\n}", "map": {"version": 3, "names": ["FormControl", "Subject", "debounceTime", "distinctUntilChanged", "AppSettings", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵtwoWayListener", "ActivityLogListComponent_ng_template_4_Template_kendo_textbox_ngModelChange_1_listener", "$event", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵtwoWayBindingSet", "searchData", "ɵɵresetView", "ɵɵlistener", "ActivityLogListComponent_ng_template_4_Template_kendo_textbox_keydown_1_listener", "onSearchKeyDown", "onSearchChange", "ɵɵelement", "ActivityLogListComponent_ng_template_4_Template_button_click_8_listener", "resetTable", "ActivityLogListComponent_ng_template_4_Template_button_click_10_listener", "refreshGrid", "ɵɵadvance", "ɵɵstyleProp", "ɵɵtwoWayProperty", "ɵɵproperty", "ɵɵtextInterpolate", "page", "totalElements", "ActivityLogListComponent_div_5_Template_kendo_dropdownlist_ngModelChange_5_listener", "_r4", "appliedFilters", "status", "ActivityLogListComponent_div_5_Template_kendo_dropdownlist_ngModelChange_9_listener", "userType", "ActivityLogListComponent_div_5_Template_button_click_11_listener", "applyAdvancedFilters", "ActivityLogListComponent_div_5_Template_button_click_13_listener", "clearAdvancedFilters", "advancedFilterOptions", "userTypes", "ActivityLogListComponent_ng_template_27_Template_button_click_1_listener", "dataItem_r6", "_r5", "dataItem", "viewLog", "ɵɵclassMap", "getEventIcon", "dataItem_r7", "activityEvent", "ɵɵtextInterpolate1", "truncateText", "dataItem_r8", "eventDescription", "dataItem_r9", "tableName", "getStatusClass", "dataItem_r10", "activityStatus", "dataItem_r11", "activityUserType", "formatDate", "dataItem_r12", "createdDate", "dataItem_r13", "createdByUserFullName", "ActivityLogListComponent_ng_template_42_div_0_Template_button_click_5_listener", "_r14", "loadTable", "ɵɵtemplate", "ActivityLogListComponent_ng_template_42_div_0_Template", "loading", "isLoading", "ActivityLogListComponent", "cdr", "modalService", "exceljsService", "httpUtilService", "AppService", "layoutUtilService", "activityLogService", "kendoColumnService", "grid", "serverSideRowData", "gridData", "IsListHasValue", "loginUser", "searchTerms", "searchSubscription", "filter", "logic", "filters", "gridFilter", "activeFilters", "filterOptions", "text", "value", "showAdvancedFilters", "size", "pageNumber", "totalPages", "orderBy", "orderDir", "skip", "sort", "field", "dir", "kendoHide", "hiddenData", "kendoColOrder", "kendoInitColOrder", "hiddenFields", "gridColumns", "defaultColumns", "fixedColumns", "draggableColumns", "GRID_STATE_KEY", "exportOptions", "selected<PERSON><PERSON>s", "isAllSelected", "columnSortStates", "logStatistics", "successLogs", "failedLogs", "totalLogs", "pageSize", "PAGE_SIZE", "pageSizeOptions", "PAGE_SIZE_OPTIONS", "itemsPerPage", "defaultOrder", "defaultOrderBy", "statusData", "selectedTab", "innerWidth", "displayMobile", "constructor", "ngOnInit", "getLoggedInUser", "setupSearchSubscription", "loadLogStatistics", "ngAfterViewInit", "loadGridState", "setupGridEventHandlers", "onTabActivated", "ngOnDestroy", "unsubscribe", "complete", "pipe", "subscribe", "pageChange", "event", "savedState", "localStorage", "getItem", "state", "JSON", "parse", "error", "console", "warn", "saveGridState", "setItem", "stringify", "loadTableWithKendoEndpoint", "loadingSubject", "next", "sortForBackend", "length", "take", "search", "getActivityLogsForKendoGrid", "data", "<PERSON><PERSON><PERSON>", "responseData", "errors", "handleEmptyResponse", "logData", "total", "Math", "ceil", "updateLogStatistics", "logs", "l", "key", "log", "clearSearch", "trim", "toggleAdvancedFilters", "push", "operator", "onSortChange", "isThirdClick", "undefined", "onSelectionChange", "selectedRows", "onColumnReorder", "columns", "updateColumnVisibility", "filterChange", "floor", "userId", "showError", "for<PERSON>ach", "column", "index", "indexOf", "orderIndex", "hidden", "userData", "pageName", "userID", "LoggedId", "createHideFields", "res", "clearFromLocalStorage", "showSuccess", "message", "detectChanges", "setTimeout", "refresh", "exportData", "exportType", "dataToExport", "map", "eventDetails", "headers", "Object", "keys", "rows", "item", "values", "colSize", "_", "id", "width", "generateExcel", "dateString", "date", "Date", "toLocaleDateString", "toLocaleTimeString", "max<PERSON><PERSON><PERSON>", "substring", "eventIcons", "ɵɵdirectiveInject", "ChangeDetectorRef", "i1", "NgbModal", "i2", "ExceljsService", "i3", "HttpUtilsService", "i4", "i5", "CustomLayoutUtilsService", "i6", "ActivityLogService", "i7", "KendoColumnService", "selectors", "viewQuery", "ActivityLogListComponent_Query", "rf", "ctx", "ActivityLogListComponent_div_0_Template", "ActivityLogListComponent_Template_kendo_grid_columnReorder_2_listener", "_r1", "ActivityLogListComponent_Template_kendo_grid_selectionChange_2_listener", "ActivityLogListComponent_Template_kendo_grid_filterChange_2_listener", "ActivityLogListComponent_Template_kendo_grid_pageChange_2_listener", "ActivityLogListComponent_Template_kendo_grid_sortChange_2_listener", "ActivityLogListComponent_Template_kendo_grid_columnVisibilityChange_2_listener", "ActivityLogListComponent_ng_template_4_Template", "ActivityLogListComponent_div_5_Template", "ActivityLogListComponent_ng_template_27_Template", "ActivityLogListComponent_ng_template_29_Template", "ActivityLogListComponent_ng_template_31_Template", "ActivityLogListComponent_ng_template_33_Template", "ActivityLogListComponent_ng_template_35_Template", "ActivityLogListComponent_ng_template_37_Template", "ActivityLogListComponent_ng_template_39_Template", "ActivityLogListComponent_ng_template_41_Template", "ActivityLogListComponent_ng_template_42_Template", "ActivityLogListComponent_div_43_Template", "ɵɵpureFunction1", "_c2", "ɵɵpureFunction0", "_c1", "_c3", "_c4", "_c5"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\setting\\activity-log-list\\activity-log-list.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\setting\\activity-log-list\\activity-log-list.component.html"], "sourcesContent": ["import { ChangeDetector<PERSON><PERSON>, Component, ElementRef, ViewChild, OnInit, OnDestroy, AfterViewInit } from '@angular/core';\r\nimport { FormControl } from '@angular/forms';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport _, { each } from 'lodash';\r\nimport { map, Subject, debounceTime, distinctUntilChanged, Subscription } from 'rxjs';\r\nimport { AppSettings } from 'src/app/app.settings';\r\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\r\nimport { HttpUtilsService } from '../../services/http-utils.service';\r\nimport { ConfirmationDialogComponent } from '../../shared/confirmation-dialog/confirmation-dialog.component';\r\nimport { Page } from '../../shared/data/pagination.module';\r\nimport { AppService } from '../../services/app.service';\r\nimport { ActivityLogService } from '../../services/activity-log.service';\r\nimport { ExceljsService } from '../../services/exceljs.service';\r\nimport { KendoColumnService } from '../../services/kendo-column.service';\r\nimport { SortDescriptor } from '@progress/kendo-data-query';\r\nimport { FilterDescriptor, CompositeFilterDescriptor } from '@progress/kendo-data-query';\r\n\r\n// Type definitions\r\ninterface ActivityLogData {\r\n  activityId: number;\r\n  activityEvent: string;\r\n  eventDescription: string;\r\n  eventDetails: string;\r\n  tableName: string;\r\n  tableId: number;\r\n  activityStatus: string;\r\n  activityUserType: string;\r\n  field1: string;\r\n  field2: string;\r\n  field3: string;\r\n  createdBy: number;\r\n  createdDate: string;\r\n  lastUpdatedBy: number;\r\n  lastUpdatedDate: string;\r\n  createdByUserFullName: string;\r\n}\r\n\r\n// Type for page configuration\r\ninterface PageConfig {\r\n  size: number;\r\n  pageNumber: number;\r\n  totalElements: number;\r\n  totalPages: number;\r\n  orderBy: string;\r\n  orderDir: string;\r\n}\r\n\r\n@Component({\r\n  selector: 'app-activity-log-list',\r\n  templateUrl: './activity-log-list.component.html',\r\n  styleUrl: './activity-log-list.component.scss'\r\n})\r\nexport class ActivityLogListComponent implements OnInit, OnDestroy, AfterViewInit {\r\n  @ViewChild('normalGrid') grid: any;\r\n\r\n  // Data\r\n  public serverSideRowData: ActivityLogData[] = [];\r\n  public gridData: any = [];\r\n  public IsListHasValue: boolean = false;\r\n\r\n  public loading: boolean = false;\r\n  public isLoading: boolean = false;\r\n\r\n  loginUser: Record<string, any> = {};\r\n\r\n  // Search\r\n  public searchData: string = '';\r\n  private searchTerms = new Subject<string>();\r\n  private searchSubscription: Subscription;\r\n\r\n  // Enhanced Filters for Kendo UI\r\n  public filter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public gridFilter: CompositeFilterDescriptor = { logic: 'and', filters: [] };\r\n  public activeFilters: Array<{\r\n    field: string;\r\n    operator: string;\r\n    value: any;\r\n  }> = [];\r\n\r\n  public filterOptions: Array<{ text: string; value: string | null }> = [\r\n    { text: 'All', value: null },\r\n    { text: 'Success', value: 'SUCCESS' },\r\n    { text: 'Failed', value: 'FAILED' }\r\n  ];\r\n\r\n  // Advanced filter options\r\n  public advancedFilterOptions = {\r\n    status: [\r\n      { text: 'All', value: null },\r\n      { text: 'Success', value: 'SUCCESS' },\r\n      { text: 'Failed', value: 'FAILED' }\r\n    ] as Array<{ text: string; value: string | null }>,\r\n    userTypes: [\r\n      { text: 'All Types', value: null },\r\n      { text: 'Admin', value: 'ADMIN' },\r\n      { text: 'User', value: 'USER' }\r\n    ] as Array<{ text: string; value: string | null }>\r\n  };\r\n\r\n  // Filter state\r\n  public showAdvancedFilters = false;\r\n  public appliedFilters: {\r\n    status?: string | null;\r\n    userType?: string | null;\r\n  } = {};\r\n\r\n  // Kendo Grid properties\r\n  public page: PageConfig = {\r\n    size: 10,\r\n    pageNumber: 0,\r\n    totalElements: 0,\r\n    totalPages: 0,\r\n    orderBy: 'createdDate',\r\n    orderDir: 'desc'\r\n  };\r\n  public skip: number = 0;\r\n  public sort: SortDescriptor[] = [\r\n    { field: 'createdDate', dir: 'desc' }\r\n  ];\r\n\r\n  // Column visibility system properties\r\n  public kendoHide: any;\r\n  public hiddenData: any = [];\r\n  public kendoColOrder: any = [];\r\n  public kendoInitColOrder: any = [];\r\n  public hiddenFields: any = [];\r\n\r\n  // Column configuration for the new system\r\n  public gridColumns: string[] = [];\r\n  public defaultColumns: string[] = [];\r\n  public fixedColumns: string[] = [];\r\n  public draggableColumns: string[] = [];\r\n\r\n  private readonly GRID_STATE_KEY = 'activity-logs-grid-state';\r\n\r\n  // Export options\r\n  public exportOptions: Array<{ text: string; value: string }> = [\r\n    { text: 'Export All', value: 'all' },\r\n    { text: 'Export Selected', value: 'selected' },\r\n    { text: 'Export Filtered', value: 'filtered' }\r\n  ];\r\n\r\n  // Selection state\r\n  public selectedLogs: ActivityLogData[] = [];\r\n  public isAllSelected: boolean = false;\r\n\r\n  // Manual column sort indicators for header clicks\r\n  public columnSortStates: { [field: string]: 'asc' | 'desc' | undefined } = {};\r\n\r\n  // Statistics\r\n  public logStatistics: {\r\n    successLogs: number;\r\n    failedLogs: number;\r\n    totalLogs: number;\r\n  } = {\r\n    successLogs: 0,\r\n    failedLogs: 0,\r\n    totalLogs: 0\r\n  };\r\n\r\n  // Legacy properties (keeping for backward compatibility)\r\n  pageSize: number = AppSettings.PAGE_SIZE;\r\n  pageSizeOptions: any = AppSettings.PAGE_SIZE_OPTIONS;\r\n  itemsPerPage = new FormControl(this.pageSize);\r\n  defaultOrder = 'desc';\r\n  defaultOrderBy = 'createdDate';\r\n  statusData: boolean = false;\r\n  selectedTab = 'All';\r\n  innerWidth: any;\r\n  displayMobile: boolean = false;\r\n\r\n  constructor(\r\n    private cdr: ChangeDetectorRef,\r\n    private modalService: NgbModal,\r\n    public exceljsService: ExceljsService,\r\n    private httpUtilService: HttpUtilsService,\r\n    public AppService: AppService,\r\n    private layoutUtilService: CustomLayoutUtilsService,\r\n    private activityLogService: ActivityLogService,\r\n    private kendoColumnService: KendoColumnService,\r\n  ) {\r\n    // set the default paging options\r\n    this.page.pageNumber = 0;\r\n    this.page.size = this.pageSize;\r\n    this.page.orderBy = 'createdDate';\r\n    this.page.orderDir = 'desc';\r\n  }\r\n\r\n  ngOnInit(): void {\r\n    this.loginUser = this.AppService.getLoggedInUser();\r\n    this.setupSearchSubscription();\r\n    this.loadTable();\r\n    this.loadLogStatistics();\r\n  }\r\n\r\n  ngAfterViewInit(): void {\r\n    this.loadGridState();\r\n    this.setupGridEventHandlers();\r\n  }\r\n\r\n  // Method to handle when the component becomes visible\r\n  onTabActivated(): void {\r\n    // Set loading state for tab activation\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    // Refresh the data when the tab is activated\r\n    this.loadTable();\r\n    this.loadLogStatistics();\r\n  }\r\n\r\n  // Refresh grid data - only refresh the grid with latest API call\r\n  refreshGrid(): void {\r\n    // Set loading state to show full-screen loader\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    // Refresh the data\r\n    this.loadTable();\r\n  }\r\n\r\n  ngOnDestroy(): void {\r\n    if (this.searchSubscription) {\r\n      this.searchSubscription.unsubscribe();\r\n    }\r\n    this.searchTerms.complete();\r\n  }\r\n\r\n  // Setup search subscription with debouncing\r\n  private setupSearchSubscription(): void {\r\n    this.searchSubscription = this.searchTerms\r\n      .pipe(\r\n        debounceTime(500),\r\n        distinctUntilChanged()\r\n      )\r\n      .subscribe(() => {\r\n        // Set loading state for search\r\n        this.loading = true;\r\n        this.isLoading = true;\r\n        this.loadTable();\r\n      });\r\n  }\r\n\r\n  // Setup grid event handlers\r\n  private setupGridEventHandlers(): void {\r\n    if (this.grid) {\r\n      this.grid.pageChange.subscribe((event: any) => {\r\n        this.pageChange(event);\r\n      });\r\n    }\r\n  }\r\n\r\n  // Load grid state from localStorage\r\n  private loadGridState(): void {\r\n    try {\r\n      const savedState = localStorage.getItem(this.GRID_STATE_KEY);\r\n      if (savedState) {\r\n        const state = JSON.parse(savedState);\r\n        this.page.size = state.pageSize || this.page.size;\r\n        this.sort = state.sort || this.sort;\r\n        this.filter = state.filter || this.filter;\r\n        this.skip = state.skip || 0;\r\n      }\r\n    } catch (error) {\r\n      console.warn('Error loading grid state:', error);\r\n    }\r\n  }\r\n\r\n  // Save grid state to localStorage\r\n  private saveGridState(): void {\r\n    try {\r\n      const state = {\r\n        pageSize: this.page.size,\r\n        sort: this.sort,\r\n        filter: this.filter,\r\n        skip: this.skip\r\n      };\r\n      localStorage.setItem(this.GRID_STATE_KEY, JSON.stringify(state));\r\n    } catch (error) {\r\n      console.warn('Error saving grid state:', error);\r\n    }\r\n  }\r\n\r\n  // Load activity logs table\r\n  loadTable(): void {\r\n    this.loadTableWithKendoEndpoint();\r\n  }\r\n\r\n  // Load data using Kendo UI specific endpoint\r\n  loadTableWithKendoEndpoint() {\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n\r\n    // Enable loader\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Prepare state object for Kendo UI endpoint\r\n    // When sort is empty (3rd click), send default sort to backend\r\n    const sortForBackend = this.sort.length > 0\r\n      ? this.sort\r\n      : [{ field: 'createdDate', dir: 'desc' }];\r\n    const state = {\r\n      take: this.page.size,\r\n      skip: this.skip,\r\n      sort: sortForBackend,\r\n      filter: this.filter,\r\n      search: this.searchData\r\n    };\r\n\r\n    this.activityLogService.getActivityLogsForKendoGrid(state).subscribe({\r\n      next: (data: {\r\n        isFault?: boolean;\r\n        responseData?: {\r\n          data: any[];\r\n          total: number;\r\n          errors?: string[];\r\n        };\r\n        data?: any[];\r\n        total?: number;\r\n        errors?: string[];\r\n      }) => {\r\n        // Handle the new API response structure\r\n        if (data.isFault || (data.responseData && data.responseData.errors && data.responseData.errors.length > 0)) {\r\n          const errors = data.responseData?.errors || data.errors || [];\r\n          console.error('Kendo UI Grid errors:', errors);\r\n          this.handleEmptyResponse();\r\n        } else {\r\n          // Handle both old and new response structures\r\n          const responseData = data.responseData || data;\r\n          const logData = responseData.data || [];\r\n          const total = responseData.total || 0;\r\n\r\n          this.IsListHasValue = logData.length !== 0;\r\n          this.serverSideRowData = logData;\r\n          this.page.totalElements = total;\r\n          this.page.totalPages = Math.ceil(total / this.page.size);\r\n          \r\n          // Create a data source with total count for Kendo Grid\r\n          this.gridData = {\r\n            data: logData,\r\n            total: total\r\n          };\r\n        }\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n      error: (error: unknown) => {\r\n        console.error('Error loading data with Kendo UI endpoint:', error);\r\n        this.handleEmptyResponse();\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n        this.httpUtilService.loadingSubject.next(false);\r\n      },\r\n      complete: () => {\r\n        this.loading = false;\r\n        this.isLoading = false;\r\n      }\r\n    });\r\n  }\r\n\r\n  // Handle empty response\r\n  private handleEmptyResponse(): void {\r\n    this.IsListHasValue = false;\r\n    this.serverSideRowData = [];\r\n    this.gridData = [];\r\n    this.page.totalElements = 0;\r\n    this.page.totalPages = 0;\r\n  }\r\n\r\n  // Load log statistics\r\n  loadLogStatistics(): void {\r\n    // This would be implemented if there's a statistics endpoint\r\n    // For now, we'll calculate from the current data\r\n    this.updateLogStatistics();\r\n  }\r\n\r\n  // Update log statistics\r\n  private updateLogStatistics(): void {\r\n    const logs = this.serverSideRowData;\r\n    this.logStatistics = {\r\n      successLogs: logs.filter(l => l.activityStatus === 'SUCCESS').length,\r\n      failedLogs: logs.filter(l => l.activityStatus === 'FAILED').length,\r\n      totalLogs: logs.length\r\n    };\r\n  }\r\n\r\n  // Search functionality\r\n  onSearchKeyDown(event: KeyboardEvent): void {\r\n    if (event.key === 'Enter') {\r\n      this.loadTable();\r\n    }\r\n  }\r\n\r\n  onSearchChange(): void {\r\n    console.log('Search changed:', this.searchData);\r\n    // Trigger search with debounce\r\n    this.searchTerms.next(this.searchData || '');\r\n  }\r\n\r\n  clearSearch(): void {\r\n    if (!this.searchData || this.searchData.trim() === '') {\r\n      this.searchTerms.next('');\r\n    }\r\n  }\r\n\r\n  // Advanced filters\r\n  toggleAdvancedFilters(): void {\r\n    this.showAdvancedFilters = !this.showAdvancedFilters;\r\n  }\r\n\r\n  applyAdvancedFilters(): void {\r\n    this.filter = { logic: 'and', filters: [] };\r\n    \r\n    if (this.appliedFilters.status) {\r\n      this.filter.filters.push({\r\n        field: 'activityStatus',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.status\r\n      });\r\n    }\r\n\r\n    if (this.appliedFilters.userType) {\r\n      this.filter.filters.push({\r\n        field: 'activityUserType',\r\n        operator: 'eq',\r\n        value: this.appliedFilters.userType\r\n      });\r\n    }\r\n\r\n    this.loadTable();\r\n  }\r\n\r\n  clearAdvancedFilters(): void {\r\n    this.appliedFilters = {};\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.loadTable();\r\n  }\r\n\r\n  // Kendo UI Grid event handlers\r\n  onSortChange(event: any): void {\r\n    const sort = event.sort;\r\n\r\n    // Check if this is the 3rd click (dir is undefined)\r\n    const isThirdClick = sort.length > 0 && sort[0] && sort[0].dir === undefined;\r\n\r\n    if (isThirdClick) {\r\n      // 3rd click - clear sort and use default\r\n      this.sort = [];\r\n      this.page.orderBy = 'createdDate';\r\n      this.page.orderDir = 'desc';\r\n    } else if (sort.length > 0 && sort[0] && sort[0].dir) {\r\n      // Valid sort with direction\r\n      this.sort = sort;\r\n      this.page.orderBy = sort[0].field || 'createdDate';\r\n      this.page.orderDir = sort[0].dir;\r\n    } else {\r\n      // Empty sort array or invalid sort\r\n      this.sort = [];\r\n      this.page.orderBy = 'createdDate';\r\n      this.page.orderDir = 'desc';\r\n    }\r\n\r\n    this.skip = 0;\r\n    this.page.pageNumber = 0;\r\n    this.saveGridState();\r\n    // Set loading state for sorting\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  // Use native Kendo sort/unsort via sortChange\r\n\r\n  onSelectionChange(event: any): void {\r\n    this.selectedLogs = event.selectedRows || [];\r\n    this.isAllSelected = this.selectedLogs.length === this.serverSideRowData.length;\r\n  }\r\n\r\n  onColumnReorder(event: any): void {\r\n    // Handle column reordering\r\n    this.kendoColOrder = event.columns;\r\n  }\r\n\r\n  updateColumnVisibility(event: any): void {\r\n    // Handle column visibility changes\r\n    this.kendoHide = event.columns;\r\n  }\r\n\r\n  filterChange(event: any): void {\r\n    this.filter = event.filter;\r\n    this.skip = 0;\r\n    this.page.pageNumber = 0;\r\n    this.saveGridState();\r\n    // Set loading state for filtering\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  pageChange(event: any): void {\r\n    // Use Kendo's provided values as source of truth\r\n    this.skip = event.skip;\r\n    this.page.size = event.take || this.page.size;\r\n    this.page.pageNumber = Math.floor(this.skip / this.page.size);\r\n    this.saveGridState();\r\n    // Set loading state for pagination\r\n    this.loading = true;\r\n    this.isLoading = true;\r\n    this.loadTable();\r\n  }\r\n\r\n  /**\r\n   * Reset the current state of column visibility and order in the grid to its original state.\r\n   */\r\n  resetTable(): void {\r\n    // Check if loginUser is available\r\n    if (!this.loginUser || !this.loginUser.userId) {\r\n      console.error('loginUser not available:', this.loginUser);\r\n      this.layoutUtilService.showError('User not logged in. Please refresh the page and try again.', '');\r\n      return;\r\n    }\r\n\r\n    // Reset all grid state to default\r\n    this.page.pageNumber = 0;\r\n    this.skip = 0;\r\n    this.sort = [{ field: 'createdDate', dir: 'desc' }];\r\n    this.filter = { logic: 'and', filters: [] };\r\n    this.searchData = '';\r\n    this.appliedFilters = {};\r\n    this.showAdvancedFilters = false;\r\n\r\n    // Reset column visibility and order\r\n    if (this.grid && this.grid.columns) {\r\n      this.grid.columns.forEach((column: any) => {\r\n        const index = this.gridColumns.indexOf(column.field);\r\n        if (index !== -1) {\r\n          column.orderIndex = index;\r\n        }\r\n        // Reset column visibility - show all columns\r\n        if (column.field && column.field !== 'action') {\r\n          column.hidden = false;\r\n        }\r\n      });\r\n    }\r\n\r\n    // Clear hidden columns\r\n    this.hiddenData = [];\r\n    this.kendoColOrder = [];\r\n    this.hiddenFields = [];\r\n\r\n    // Reset the Kendo Grid's internal state\r\n    if (this.grid) {\r\n      // Clear all filters\r\n      this.grid.filter = { logic: 'and', filters: [] };\r\n      \r\n      // Reset sorting\r\n      this.grid.sort = [{ field: 'createdDate', dir: 'desc' }];\r\n      \r\n      // Reset to first page\r\n      this.grid.skip = 0;\r\n      this.grid.pageSize = this.page.size;\r\n    }\r\n\r\n    // Prepare reset data\r\n    const userData = {\r\n      pageName: 'ActivityLogs',\r\n      userID: this.loginUser.userId,\r\n      hiddenData: [],\r\n      kendoColOrder: [],\r\n      LoggedId: this.loginUser.userId\r\n    };\r\n\r\n    // Show loading state\r\n    this.httpUtilService.loadingSubject.next(true);\r\n\r\n    // Save reset state to backend\r\n    this.kendoColumnService.createHideFields(userData).subscribe({\r\n      next: (res) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        if (!res.isFault) {\r\n          // Also clear from localStorage\r\n          this.kendoColumnService.clearFromLocalStorage('ActivityLogs');\r\n          this.layoutUtilService.showSuccess(res.message || 'Column settings reset successfully.', '');\r\n        } else {\r\n          this.layoutUtilService.showError(res.message || 'Failed to reset column settings.', '');\r\n        }\r\n        \r\n        // Trigger change detection and refresh grid\r\n        this.cdr.detectChanges();\r\n        \r\n        // Small delay to ensure the grid is updated\r\n        setTimeout(() => {\r\n          if (this.grid) {\r\n            this.grid.refresh();\r\n          }\r\n        }, 100);\r\n        \r\n        this.loadTable();\r\n      },\r\n      error: (error) => {\r\n        this.httpUtilService.loadingSubject.next(false);\r\n        console.error('Error resetting column settings:', error);\r\n\r\n        // Check if it's an authentication error\r\n        if (error.status === 401 || (error.error && error.error.status === 401)) {\r\n          this.layoutUtilService.showError('Authentication failed. Please login again.', '');\r\n          // Optionally redirect to login page\r\n        } else {\r\n          this.layoutUtilService.showError('Error resetting column settings. Please try again.', '');\r\n        }\r\n      }\r\n    });\r\n  }\r\n\r\n  // Export functionality\r\n  exportData(exportType: string): void {\r\n    let dataToExport: any[] = [];\r\n\r\n    switch (exportType) {\r\n      case 'all':\r\n        dataToExport = this.serverSideRowData;\r\n        break;\r\n      case 'selected':\r\n        dataToExport = this.selectedLogs;\r\n        break;\r\n      case 'filtered':\r\n        dataToExport = this.serverSideRowData;\r\n        break;\r\n    }\r\n\r\n    if (dataToExport.length === 0) {\r\n      return;\r\n    }\r\n\r\n    const exportData = dataToExport.map(log => ({\r\n      'Event': log.activityEvent,\r\n      'Description': log.eventDescription,\r\n      'Details': log.eventDetails,\r\n      'Table': log.tableName,\r\n      'Status': log.activityStatus,\r\n      'User Type': log.activityUserType,\r\n      'Created Date': this.AppService.formatDate(log.createdDate),\r\n      'User': log.createdByUserFullName\r\n    }));\r\n\r\n    const headers = Object.keys(exportData[0]);\r\n    const rows = exportData.map(item => Object.values(item));\r\n    const colSize = headers.map((_, index) => ({ id: index + 1, width: 20 }));\r\n    this.exceljsService.generateExcel('Activity_Logs_Export', headers, rows, colSize);\r\n  }\r\n\r\n  // Log actions\r\n  viewLog(log: ActivityLogData): void {\r\n    // Navigate to view log page\r\n    console.log('View log:', log);\r\n  }\r\n\r\n  // Utility methods\r\n  getStatusClass(status: string): string {\r\n    return status === 'SUCCESS' ? 'badge-success' : 'badge-danger';\r\n  }\r\n\r\n  formatDate(dateString: string): string {\r\n    if (!dateString) return '';\r\n    const date = new Date(dateString);\r\n    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();\r\n  }\r\n\r\n  truncateText(text: string, maxLength: number = 50): string {\r\n    if (!text) return '';\r\n    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;\r\n  }\r\n\r\n  getEventIcon(event: string): string {\r\n    const eventIcons: { [key: string]: string } = {\r\n      'USER_CREATED': 'fas fa-user-plus',\r\n      'USER_UPDATED': 'fas fa-user-edit',\r\n      'USER_DELETED': 'fas fa-user-minus',\r\n      'ROLE_CREATED': 'fas fa-shield-plus',\r\n      'ROLE_UPDATED': 'fas fa-shield-edit',\r\n      'ROLE_DELETED': 'fas fa-shield-minus',\r\n      'LOGIN': 'fas fa-sign-in-alt',\r\n      'LOGOUT': 'fas fa-sign-out-alt',\r\n      'PASSWORD_CHANGED': 'fas fa-key',\r\n      'default': 'fas fa-info-circle'\r\n    };\r\n    return eventIcons[event] || eventIcons['default'];\r\n  }\r\n}\r\n", "<!-- Full Screen Loading Overlay -->\r\n<div *ngIf=\"loading || isLoading\" class=\"fullscreen-loading-overlay\">\r\n  <div class=\"loading-content\">\r\n    <div class=\"custom-colored-spinner\" role=\"status\">\r\n      <span class=\"visually-hidden\">Loading...</span>\r\n    </div>\r\n    <div class=\"mt-4 fs-5\">Loading...</div>\r\n  </div>\r\n</div>\r\n\r\n<div class=\"grid-container\">\r\n  <kendo-grid #normalGrid [data]=\"gridData\" [pageSize]=\"page.size\" [sort]=\"sort\"\r\n    [pageable]=\"{\r\n      pageSizes: [10, 15, 20, 50, 100],\r\n      previousNext: true,\r\n      info: true,\r\n      type: 'numeric',\r\n      buttonCount: 5\r\n    }\"\r\n    [total]=\"page.totalElements\"\r\n    [sortable]=\"{ allowUnsort: true, mode: 'single' }\"\r\n    [groupable]=\"false\"\r\n    [selectable]=\"{ checkboxOnly: true, mode: 'multiple' }\"\r\n    (columnReorder)=\"onColumnReorder($event)\"\r\n    (selectionChange)=\"onSelectionChange($event)\"\r\n    [reorderable]=\"true\"\r\n    style=\"width: auto;overflow-x:auto\"\r\n    [resizable]=\"false\"\r\n    [height]=\"720\"\r\n    [skip]=\"skip\"\r\n    [filter]=\"filter\"\r\n    [columnMenu]=\"{ filter: true }\"\r\n    (filterChange)=\"filterChange($event)\"\r\n    (pageChange)=\"pageChange($event)\"\r\n    (sortChange)=\"onSortChange($event)\"\r\n    (columnVisibilityChange)=\"updateColumnVisibility($event)\">\r\n\r\n    <ng-template kendoGridToolbarTemplate>\r\n      <!-- Search Section -->\r\n      <div class=\"d-flex align-items-center me-3 search-section\">\r\n        <kendo-textbox [style.width.px]=\"500\" placeholder=\"Search...\" [(ngModel)]=\"searchData\"\r\n          [clearButton]=\"true\" (keydown)=\"onSearchKeyDown($event)\"\r\n          (ngModelChange)=\"onSearchChange()\"></kendo-textbox>\r\n      </div>\r\n\r\n      <kendo-grid-spacer></kendo-grid-spacer>\r\n\r\n      <!-- Total Count - Repositioned to the right -->\r\n      <div class=\"d-flex align-items-center me-3\">\r\n        <span class=\"text-muted me-2\">Total:</span>\r\n        <span class=\"fw-bold\">{{ page.totalElements }}</span>\r\n      </div>\r\n\r\n      <!-- Reset Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-icon btn-sm me-2\"\r\n        (click)=\"resetTable()\"\r\n        title=\"Reset to Default\"\r\n      >\r\n        <i class=\"fas fa-undo text-warning\"></i>\r\n      </button>\r\n\r\n      <!-- Refresh Button -->\r\n      <button\r\n        type=\"button\"\r\n        class=\"btn btn-info btn-sm me-2\"\r\n        (click)=\"refreshGrid()\"\r\n        title=\"Refresh Grid Data\"\r\n      >\r\n        <i class=\"fas fa-sync-alt\"></i>\r\n      </button>\r\n\r\n    </ng-template>\r\n\r\n    <!-- Advanced Filters Panel -->\r\n    <div *ngIf=\"showAdvancedFilters\" class=\"advanced-filters-panel p-3 bg-light border-bottom\">\r\n      <div class=\"row\">\r\n        <div class=\"col-md-3\">\r\n          <label class=\"form-label\">Status</label>\r\n          <kendo-dropdownlist\r\n            [data]=\"advancedFilterOptions.status\"\r\n            [textField]=\"'text'\"\r\n            [valueField]=\"'value'\"\r\n            [(ngModel)]=\"appliedFilters.status\"\r\n            class=\"w-100\">\r\n          </kendo-dropdownlist>\r\n        </div>\r\n        <div class=\"col-md-3\">\r\n          <label class=\"form-label\">User Type</label>\r\n          <kendo-dropdownlist\r\n            [data]=\"advancedFilterOptions.userTypes\"\r\n            [textField]=\"'text'\"\r\n            [valueField]=\"'value'\"\r\n            [(ngModel)]=\"appliedFilters.userType\"\r\n            class=\"w-100\">\r\n          </kendo-dropdownlist>\r\n        </div>\r\n        <div class=\"col-md-6 d-flex align-items-end\">\r\n          <button kendoButton [themeColor]=\"'primary'\" (click)=\"applyAdvancedFilters()\" class=\"me-2\">\r\n            Apply Filters\r\n          </button>\r\n          <button kendoButton [themeColor]=\"'secondary'\" (click)=\"clearAdvancedFilters()\">\r\n            Clear Filters\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Log Statistics -->\r\n    <div class=\"log-statistics p-3 bg-light border-bottom\">\r\n      <div class=\"row text-center\">\r\n        <div class=\"col-md-4\">\r\n          <div class=\"stat-item\">\r\n            <h4 class=\"text-success mb-0\">{{ logStatistics.successLogs }}</h4>\r\n            <small class=\"text-muted\">Success Logs</small>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-4\">\r\n          <div class=\"stat-item\">\r\n            <h4 class=\"text-danger mb-0\">{{ logStatistics.failedLogs }}</h4>\r\n            <small class=\"text-muted\">Failed Logs</small>\r\n          </div>\r\n        </div>\r\n        <div class=\"col-md-4\">\r\n          <div class=\"stat-item\">\r\n            <h4 class=\"text-primary mb-0\">{{ logStatistics.totalLogs }}</h4>\r\n            <small class=\"text-muted\">Total Logs</small>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Grid Columns -->\r\n    <kendo-grid-column field=\"action\" title=\"Actions\" [width]=\"100\" [sortable]=\"false\" [filterable]=\"false\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        <div class=\"btn-group btn-group-sm\">\r\n          <button kendoButton [themeColor]=\"'primary'\" (click)=\"viewLog(dataItem)\" class=\"btn-sm\">\r\n            <i class=\"fas fa-eye\"></i>\r\n          </button>\r\n        </div>\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <kendo-grid-column field=\"activityEvent\" title=\"Event\" [width]=\"180\" [filterable]=\"true\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        <div class=\"d-flex align-items-center\">\r\n          <i [class]=\"getEventIcon(dataItem.activityEvent)\" class=\"me-2 text-primary\"></i>\r\n          <span class=\"fw-bold\">{{ dataItem.activityEvent }}</span>\r\n        </div>\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <kendo-grid-column field=\"eventDescription\" title=\"Description\" [width]=\"250\" [filterable]=\"true\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        {{ truncateText(dataItem.eventDescription, 40) }}\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <kendo-grid-column field=\"tableName\" title=\"Table\" [width]=\"120\" [filterable]=\"true\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        <span class=\"badge bg-info\">{{ dataItem.tableName }}</span>\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <kendo-grid-column field=\"activityStatus\" title=\"Status\" [width]=\"100\" [filterable]=\"true\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        <span class=\"badge\" [ngClass]=\"getStatusClass(dataItem.activityStatus)\">\r\n          {{ dataItem.activityStatus }}\r\n        </span>\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <kendo-grid-column field=\"activityUserType\" title=\"User Type\" [width]=\"120\" [filterable]=\"true\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        <span class=\"badge bg-secondary\">{{ dataItem.activityUserType }}</span>\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <kendo-grid-column field=\"createdDate\" title=\"Created Date\" [width]=\"180\" [filterable]=\"true\" format=\"MM/dd/yyyy\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        {{ formatDate(dataItem.createdDate) }}\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <kendo-grid-column field=\"createdByUserFullName\" title=\"User\" [width]=\"150\" [filterable]=\"false\">\r\n      <ng-template kendoGridCellTemplate let-dataItem=\"dataItem\">\r\n        {{ dataItem.createdByUserFullName || 'System' }}\r\n      </ng-template>\r\n    </kendo-grid-column>\r\n\r\n    <!-- No Data Template -->\r\n    <ng-template kendoGridNoRecordsTemplate>\r\n      <div class=\"custom-no-records\" *ngIf=\"!loading && !isLoading\">\r\n        <div class=\"text-center\">\r\n          <i class=\"fas fa-clipboard-list text-muted mb-2\" style=\"font-size: 2rem;\"></i>\r\n          <p class=\"text-muted\">No activity logs found</p>\r\n          <button kendoButton (click)=\"loadTable()\" class=\"btn-primary\">\r\n            <i class=\"fas fa-refresh me-2\"></i>Refresh\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </ng-template>\r\n  </kendo-grid>\r\n</div>\r\n\r\n<!-- Empty State -->\r\n<div *ngIf=\"!loading && !isLoading && !IsListHasValue\" class=\"text-center p-5\">\r\n  <i class=\"fas fa-clipboard-list fa-4x text-muted mb-4\"></i>\r\n  <h3 class=\"text-muted mb-3\">No Activity Logs Available</h3>\r\n  <p class=\"text-muted mb-4\">Activity logs will appear here as users perform actions in the system.</p>\r\n</div>\r\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,gBAAgB;AAG5C,SAAcC,OAAO,EAAEC,YAAY,EAAEC,oBAAoB,QAAsB,MAAM;AACrF,SAASC,WAAW,QAAQ,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICD5CC,EAHN,CAAAC,cAAA,cAAqE,cACtC,cACuB,eAClB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAErCF,EAFqC,CAAAG,YAAA,EAAM,EACnC,EACF;;;;;;IAgCEH,EADF,CAAAC,cAAA,cAA2D,wBAGpB;IAFyBD,EAAA,CAAAI,gBAAA,2BAAAC,uFAAAC,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAG,UAAA,EAAAN,MAAA,MAAAG,MAAA,CAAAG,UAAA,GAAAN,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAwB;IAEpFN,EADqB,CAAAc,UAAA,qBAAAC,iFAAAT,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAAWJ,MAAA,CAAAO,eAAA,CAAAV,MAAA,CAAuB;IAAA,EAAC,2BAAAD,uFAAA;MAAAL,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CACvCJ,MAAA,CAAAQ,cAAA,EAAgB;IAAA,EAAC;IACtCjB,EADuC,CAAAG,YAAA,EAAgB,EACjD;IAENH,EAAA,CAAAkB,SAAA,wBAAuC;IAIrClB,EADF,CAAAC,cAAA,cAA4C,eACZ;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC3CH,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAChDF,EADgD,CAAAG,YAAA,EAAO,EACjD;IAGNH,EAAA,CAAAC,cAAA,iBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAK,wEAAA;MAAAnB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAW,UAAA,EAAY;IAAA,EAAC;IAGtBpB,EAAA,CAAAkB,SAAA,YAAwC;IAC1ClB,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAKC;IAFCD,EAAA,CAAAc,UAAA,mBAAAO,yEAAA;MAAArB,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAa,WAAA,EAAa;IAAA,EAAC;IAGvBtB,EAAA,CAAAkB,SAAA,aAA+B;IACjClB,EAAA,CAAAG,YAAA,EAAS;;;;IA/BQH,EAAA,CAAAuB,SAAA,EAAsB;IAAtBvB,EAAA,CAAAwB,WAAA,oBAAsB;IAAyBxB,EAAA,CAAAyB,gBAAA,YAAAhB,MAAA,CAAAG,UAAA,CAAwB;IACpFZ,EAAA,CAAA0B,UAAA,qBAAoB;IASA1B,EAAA,CAAAuB,SAAA,GAAwB;IAAxBvB,EAAA,CAAA2B,iBAAA,CAAAlB,MAAA,CAAAmB,IAAA,CAAAC,aAAA,CAAwB;;;;;;IA6B5C7B,EAHN,CAAAC,cAAA,cAA2F,cACxE,cACO,gBACM;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACxCH,EAAA,CAAAC,cAAA,6BAKgB;IADdD,EAAA,CAAAI,gBAAA,2BAAA0B,oFAAAxB,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAuB,cAAA,CAAAC,MAAA,EAAA3B,MAAA,MAAAG,MAAA,CAAAuB,cAAA,CAAAC,MAAA,GAAA3B,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAmC;IAGvCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,cAAsB,gBACM;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC3CH,EAAA,CAAAC,cAAA,6BAKgB;IADdD,EAAA,CAAAI,gBAAA,2BAAA8B,oFAAA5B,MAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAAV,EAAA,CAAAW,kBAAA,CAAAF,MAAA,CAAAuB,cAAA,CAAAG,QAAA,EAAA7B,MAAA,MAAAG,MAAA,CAAAuB,cAAA,CAAAG,QAAA,GAAA7B,MAAA;MAAA,OAAAN,EAAA,CAAAa,WAAA,CAAAP,MAAA;IAAA,EAAqC;IAGzCN,EADE,CAAAG,YAAA,EAAqB,EACjB;IAEJH,EADF,CAAAC,cAAA,eAA6C,kBACgD;IAA9CD,EAAA,CAAAc,UAAA,mBAAAsB,iEAAA;MAAApC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA4B,oBAAA,EAAsB;IAAA,EAAC;IAC3ErC,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAgF;IAAjCD,EAAA,CAAAc,UAAA,mBAAAwB,iEAAA;MAAAtC,EAAA,CAAAO,aAAA,CAAAwB,GAAA;MAAA,MAAAtB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA8B,oBAAA,EAAsB;IAAA,EAAC;IAC7EvC,EAAA,CAAAE,MAAA,uBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;IA1BEH,EAAA,CAAAuB,SAAA,GAAqC;IAErCvB,EAFA,CAAA0B,UAAA,SAAAjB,MAAA,CAAA+B,qBAAA,CAAAP,MAAA,CAAqC,qBACjB,uBACE;IACtBjC,EAAA,CAAAyB,gBAAA,YAAAhB,MAAA,CAAAuB,cAAA,CAAAC,MAAA,CAAmC;IAOnCjC,EAAA,CAAAuB,SAAA,GAAwC;IAExCvB,EAFA,CAAA0B,UAAA,SAAAjB,MAAA,CAAA+B,qBAAA,CAAAC,SAAA,CAAwC,qBACpB,uBACE;IACtBzC,EAAA,CAAAyB,gBAAA,YAAAhB,MAAA,CAAAuB,cAAA,CAAAG,QAAA,CAAqC;IAKnBnC,EAAA,CAAAuB,SAAA,GAAwB;IAAxBvB,EAAA,CAAA0B,UAAA,yBAAwB;IAGxB1B,EAAA,CAAAuB,SAAA,GAA0B;IAA1BvB,EAAA,CAAA0B,UAAA,2BAA0B;;;;;;IAmC9C1B,EADF,CAAAC,cAAA,cAAoC,iBACsD;IAA3CD,EAAA,CAAAc,UAAA,mBAAA4B,yEAAA;MAAA,MAAAC,WAAA,GAAA3C,EAAA,CAAAO,aAAA,CAAAqC,GAAA,EAAAC,QAAA;MAAA,MAAApC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAAqC,OAAA,CAAAH,WAAA,CAAiB;IAAA,EAAC;IACtE3C,EAAA,CAAAkB,SAAA,YAA0B;IAE9BlB,EADE,CAAAG,YAAA,EAAS,EACL;;;IAHgBH,EAAA,CAAAuB,SAAA,EAAwB;IAAxBvB,EAAA,CAAA0B,UAAA,yBAAwB;;;;;IAS9C1B,EAAA,CAAAC,cAAA,cAAuC;IACrCD,EAAA,CAAAkB,SAAA,YAAgF;IAChFlB,EAAA,CAAAC,cAAA,eAAsB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IACpDF,EADoD,CAAAG,YAAA,EAAO,EACrD;;;;;IAFDH,EAAA,CAAAuB,SAAA,EAA8C;IAA9CvB,EAAA,CAAA+C,UAAA,CAAAtC,MAAA,CAAAuC,YAAA,CAAAC,WAAA,CAAAC,aAAA,EAA8C;IAC3BlD,EAAA,CAAAuB,SAAA,GAA4B;IAA5BvB,EAAA,CAAA2B,iBAAA,CAAAsB,WAAA,CAAAC,aAAA,CAA4B;;;;;IAOpDlD,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmD,kBAAA,MAAA1C,MAAA,CAAA2C,YAAA,CAAAC,WAAA,CAAAC,gBAAA,WACF;;;;;IAKEtD,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAA/BH,EAAA,CAAAuB,SAAA,EAAwB;IAAxBvB,EAAA,CAAA2B,iBAAA,CAAA4B,WAAA,CAAAC,SAAA,CAAwB;;;;;IAMpDxD,EAAA,CAAAC,cAAA,eAAwE;IACtED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAFaH,EAAA,CAAA0B,UAAA,YAAAjB,MAAA,CAAAgD,cAAA,CAAAC,YAAA,CAAAC,cAAA,EAAmD;IACrE3D,EAAA,CAAAuB,SAAA,EACF;IADEvB,EAAA,CAAAmD,kBAAA,MAAAO,YAAA,CAAAC,cAAA,MACF;;;;;IAMA3D,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAuB,SAAA,EAA+B;IAA/BvB,EAAA,CAAA2B,iBAAA,CAAAiC,YAAA,CAAAC,gBAAA,CAA+B;;;;;IAMhE7D,EAAA,CAAAE,MAAA,GACF;;;;;IADEF,EAAA,CAAAmD,kBAAA,MAAA1C,MAAA,CAAAqD,UAAA,CAAAC,YAAA,CAAAC,WAAA,OACF;;;;;IAKEhE,EAAA,CAAAE,MAAA,GACF;;;;IADEF,EAAA,CAAAmD,kBAAA,MAAAc,YAAA,CAAAC,qBAAA,kBACF;;;;;;IAMElE,EADF,CAAAC,cAAA,cAA8D,cACnC;IACvBD,EAAA,CAAAkB,SAAA,YAA8E;IAC9ElB,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChDH,EAAA,CAAAC,cAAA,iBAA8D;IAA1CD,EAAA,CAAAc,UAAA,mBAAAqD,+EAAA;MAAAnE,EAAA,CAAAO,aAAA,CAAA6D,IAAA;MAAA,MAAA3D,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAa,WAAA,CAASJ,MAAA,CAAA4D,SAAA,EAAW;IAAA,EAAC;IACvCrE,EAAA,CAAAkB,SAAA,YAAmC;IAAAlB,EAAA,CAAAE,MAAA,eACrC;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;;IARNH,EAAA,CAAAsE,UAAA,IAAAC,sDAAA,kBAA8D;;;;IAA9BvE,EAAA,CAAA0B,UAAA,UAAAjB,MAAA,CAAA+D,OAAA,KAAA/D,MAAA,CAAAgE,SAAA,CAA4B;;;;;IAclEzE,EAAA,CAAAC,cAAA,cAA+E;IAC7ED,EAAA,CAAAkB,SAAA,YAA2D;IAC3DlB,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,iCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3DH,EAAA,CAAAC,cAAA,YAA2B;IAAAD,EAAA,CAAAE,MAAA,6EAAsE;IACnGF,EADmG,CAAAG,YAAA,EAAI,EACjG;;;AD/JN,OAAM,MAAOuE,wBAAwB;EAwHzBC,GAAA;EACAC,YAAA;EACDC,cAAA;EACCC,eAAA;EACDC,UAAA;EACCC,iBAAA;EACAC,kBAAA;EACAC,kBAAA;EA9HeC,IAAI;EAE7B;EACOC,iBAAiB,GAAsB,EAAE;EACzCC,QAAQ,GAAQ,EAAE;EAClBC,cAAc,GAAY,KAAK;EAE/Bd,OAAO,GAAY,KAAK;EACxBC,SAAS,GAAY,KAAK;EAEjCc,SAAS,GAAwB,EAAE;EAEnC;EACO3E,UAAU,GAAW,EAAE;EACtB4E,WAAW,GAAG,IAAI5F,OAAO,EAAU;EACnC6F,kBAAkB;EAE1B;EACOC,MAAM,GAA8B;IAAEC,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACjEC,UAAU,GAA8B;IAAEF,KAAK,EAAE,KAAK;IAAEC,OAAO,EAAE;EAAE,CAAE;EACrEE,aAAa,GAIf,EAAE;EAEAC,aAAa,GAAkD,CACpE;IAAEC,IAAI,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAI,CAAE,EAC5B;IAAED,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAE,EACrC;IAAED,IAAI,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAQ,CAAE,CACpC;EAED;EACOzD,qBAAqB,GAAG;IAC7BP,MAAM,EAAE,CACN;MAAE+D,IAAI,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAI,CAAE,EAC5B;MAAED,IAAI,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAS,CAAE,EACrC;MAAED,IAAI,EAAE,QAAQ;MAAEC,KAAK,EAAE;IAAQ,CAAE,CACa;IAClDxD,SAAS,EAAE,CACT;MAAEuD,IAAI,EAAE,WAAW;MAAEC,KAAK,EAAE;IAAI,CAAE,EAClC;MAAED,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAO,CAAE,EACjC;MAAED,IAAI,EAAE,MAAM;MAAEC,KAAK,EAAE;IAAM,CAAE;GAElC;EAED;EACOC,mBAAmB,GAAG,KAAK;EAC3BlE,cAAc,GAGjB,EAAE;EAEN;EACOJ,IAAI,GAAe;IACxBuE,IAAI,EAAE,EAAE;IACRC,UAAU,EAAE,CAAC;IACbvE,aAAa,EAAE,CAAC;IAChBwE,UAAU,EAAE,CAAC;IACbC,OAAO,EAAE,aAAa;IACtBC,QAAQ,EAAE;GACX;EACMC,IAAI,GAAW,CAAC;EAChBC,IAAI,GAAqB,CAC9B;IAAEC,KAAK,EAAE,aAAa;IAAEC,GAAG,EAAE;EAAM,CAAE,CACtC;EAED;EACOC,SAAS;EACTC,UAAU,GAAQ,EAAE;EACpBC,aAAa,GAAQ,EAAE;EACvBC,iBAAiB,GAAQ,EAAE;EAC3BC,YAAY,GAAQ,EAAE;EAE7B;EACOC,WAAW,GAAa,EAAE;EAC1BC,cAAc,GAAa,EAAE;EAC7BC,YAAY,GAAa,EAAE;EAC3BC,gBAAgB,GAAa,EAAE;EAErBC,cAAc,GAAG,0BAA0B;EAE5D;EACOC,aAAa,GAA2C,CAC7D;IAAEtB,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAE;EAAK,CAAE,EACpC;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,EAC9C;IAAED,IAAI,EAAE,iBAAiB;IAAEC,KAAK,EAAE;EAAU,CAAE,CAC/C;EAED;EACOsB,YAAY,GAAsB,EAAE;EACpCC,aAAa,GAAY,KAAK;EAErC;EACOC,gBAAgB,GAAoD,EAAE;EAE7E;EACOC,aAAa,GAIhB;IACFC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,SAAS,EAAE;GACZ;EAED;EACAC,QAAQ,GAAW/H,WAAW,CAACgI,SAAS;EACxCC,eAAe,GAAQjI,WAAW,CAACkI,iBAAiB;EACpDC,YAAY,GAAG,IAAIvI,WAAW,CAAC,IAAI,CAACmI,QAAQ,CAAC;EAC7CK,YAAY,GAAG,MAAM;EACrBC,cAAc,GAAG,aAAa;EAC9BC,UAAU,GAAY,KAAK;EAC3BC,WAAW,GAAG,KAAK;EACnBC,UAAU;EACVC,aAAa,GAAY,KAAK;EAE9BC,YACU9D,GAAsB,EACtBC,YAAsB,EACvBC,cAA8B,EAC7BC,eAAiC,EAClCC,UAAsB,EACrBC,iBAA2C,EAC3CC,kBAAsC,EACtCC,kBAAsC;IAPtC,KAAAP,GAAG,GAAHA,GAAG;IACH,KAAAC,YAAY,GAAZA,YAAY;IACb,KAAAC,cAAc,GAAdA,cAAc;IACb,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAAC,UAAU,GAAVA,UAAU;IACT,KAAAC,iBAAiB,GAAjBA,iBAAiB;IACjB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAClB,KAAAC,kBAAkB,GAAlBA,kBAAkB;IAE1B;IACA,IAAI,CAACtD,IAAI,CAACwE,UAAU,GAAG,CAAC;IACxB,IAAI,CAACxE,IAAI,CAACuE,IAAI,GAAG,IAAI,CAAC2B,QAAQ;IAC9B,IAAI,CAAClG,IAAI,CAAC0E,OAAO,GAAG,aAAa;IACjC,IAAI,CAAC1E,IAAI,CAAC2E,QAAQ,GAAG,MAAM;EAC7B;EAEAmC,QAAQA,CAAA;IACN,IAAI,CAACnD,SAAS,GAAG,IAAI,CAACR,UAAU,CAAC4D,eAAe,EAAE;IAClD,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACvE,SAAS,EAAE;IAChB,IAAI,CAACwE,iBAAiB,EAAE;EAC1B;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACC,aAAa,EAAE;IACpB,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEA;EACAC,cAAcA,CAAA;IACZ;IACA,IAAI,CAACzE,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACJ,SAAS,EAAE;IAChB,IAAI,CAACwE,iBAAiB,EAAE;EAC1B;EAEA;EACAvH,WAAWA,CAAA;IACT;IACA,IAAI,CAACkD,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEA6E,WAAWA,CAAA;IACT,IAAI,IAAI,CAACzD,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAAC0D,WAAW,EAAE;IACvC;IACA,IAAI,CAAC3D,WAAW,CAAC4D,QAAQ,EAAE;EAC7B;EAEA;EACQR,uBAAuBA,CAAA;IAC7B,IAAI,CAACnD,kBAAkB,GAAG,IAAI,CAACD,WAAW,CACvC6D,IAAI,CACHxJ,YAAY,CAAC,GAAG,CAAC,EACjBC,oBAAoB,EAAE,CACvB,CACAwJ,SAAS,CAAC,MAAK;MACd;MACA,IAAI,CAAC9E,OAAO,GAAG,IAAI;MACnB,IAAI,CAACC,SAAS,GAAG,IAAI;MACrB,IAAI,CAACJ,SAAS,EAAE;IAClB,CAAC,CAAC;EACN;EAEA;EACQ2E,sBAAsBA,CAAA;IAC5B,IAAI,IAAI,CAAC7D,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACoE,UAAU,CAACD,SAAS,CAAEE,KAAU,IAAI;QAC5C,IAAI,CAACD,UAAU,CAACC,KAAK,CAAC;MACxB,CAAC,CAAC;IACJ;EACF;EAEA;EACQT,aAAaA,CAAA;IACnB,IAAI;MACF,MAAMU,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACtC,cAAc,CAAC;MAC5D,IAAIoC,UAAU,EAAE;QACd,MAAMG,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;QACpC,IAAI,CAAC7H,IAAI,CAACuE,IAAI,GAAGyD,KAAK,CAAC9B,QAAQ,IAAI,IAAI,CAAClG,IAAI,CAACuE,IAAI;QACjD,IAAI,CAACM,IAAI,GAAGmD,KAAK,CAACnD,IAAI,IAAI,IAAI,CAACA,IAAI;QACnC,IAAI,CAACf,MAAM,GAAGkE,KAAK,CAAClE,MAAM,IAAI,IAAI,CAACA,MAAM;QACzC,IAAI,CAACc,IAAI,GAAGoD,KAAK,CAACpD,IAAI,IAAI,CAAC;MAC7B;IACF,CAAC,CAAC,OAAOuD,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,2BAA2B,EAAEF,KAAK,CAAC;IAClD;EACF;EAEA;EACQG,aAAaA,CAAA;IACnB,IAAI;MACF,MAAMN,KAAK,GAAG;QACZ9B,QAAQ,EAAE,IAAI,CAAClG,IAAI,CAACuE,IAAI;QACxBM,IAAI,EAAE,IAAI,CAACA,IAAI;QACff,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBc,IAAI,EAAE,IAAI,CAACA;OACZ;MACDkD,YAAY,CAACS,OAAO,CAAC,IAAI,CAAC9C,cAAc,EAAEwC,IAAI,CAACO,SAAS,CAACR,KAAK,CAAC,CAAC;IAClE,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAEF,KAAK,CAAC;IACjD;EACF;EAEA;EACA1F,SAASA,CAAA;IACP,IAAI,CAACgG,0BAA0B,EAAE;EACnC;EAEA;EACAA,0BAA0BA,CAAA;IACxB,IAAI,CAAC7F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IAErB;IACA,IAAI,CAACK,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA;IACA,MAAMC,cAAc,GAAG,IAAI,CAAC/D,IAAI,CAACgE,MAAM,GAAG,CAAC,GACvC,IAAI,CAAChE,IAAI,GACT,CAAC;MAAEC,KAAK,EAAE,aAAa;MAAEC,GAAG,EAAE;IAAM,CAAE,CAAC;IAC3C,MAAMiD,KAAK,GAAG;MACZc,IAAI,EAAE,IAAI,CAAC9I,IAAI,CAACuE,IAAI;MACpBK,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,IAAI,EAAE+D,cAAc;MACpB9E,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBiF,MAAM,EAAE,IAAI,CAAC/J;KACd;IAED,IAAI,CAACqE,kBAAkB,CAAC2F,2BAA2B,CAAChB,KAAK,CAAC,CAACN,SAAS,CAAC;MACnEiB,IAAI,EAAGM,IAUN,IAAI;QACH;QACA,IAAIA,IAAI,CAACC,OAAO,IAAKD,IAAI,CAACE,YAAY,IAAIF,IAAI,CAACE,YAAY,CAACC,MAAM,IAAIH,IAAI,CAACE,YAAY,CAACC,MAAM,CAACP,MAAM,GAAG,CAAE,EAAE;UAC1G,MAAMO,MAAM,GAAGH,IAAI,CAACE,YAAY,EAAEC,MAAM,IAAIH,IAAI,CAACG,MAAM,IAAI,EAAE;UAC7DhB,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEiB,MAAM,CAAC;UAC9C,IAAI,CAACC,mBAAmB,EAAE;QAC5B,CAAC,MAAM;UACL;UACA,MAAMF,YAAY,GAAGF,IAAI,CAACE,YAAY,IAAIF,IAAI;UAC9C,MAAMK,OAAO,GAAGH,YAAY,CAACF,IAAI,IAAI,EAAE;UACvC,MAAMM,KAAK,GAAGJ,YAAY,CAACI,KAAK,IAAI,CAAC;UAErC,IAAI,CAAC7F,cAAc,GAAG4F,OAAO,CAACT,MAAM,KAAK,CAAC;UAC1C,IAAI,CAACrF,iBAAiB,GAAG8F,OAAO;UAChC,IAAI,CAACtJ,IAAI,CAACC,aAAa,GAAGsJ,KAAK;UAC/B,IAAI,CAACvJ,IAAI,CAACyE,UAAU,GAAG+E,IAAI,CAACC,IAAI,CAACF,KAAK,GAAG,IAAI,CAACvJ,IAAI,CAACuE,IAAI,CAAC;UAExD;UACA,IAAI,CAACd,QAAQ,GAAG;YACdwF,IAAI,EAAEK,OAAO;YACbC,KAAK,EAAEA;WACR;QACH;QACA,IAAI,CAACrG,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDR,KAAK,EAAGA,KAAc,IAAI;QACxBC,OAAO,CAACD,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;QAClE,IAAI,CAACkB,mBAAmB,EAAE;QAC1B,IAAI,CAACzG,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;QACtB,IAAI,CAACK,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;MACjD,CAAC;MACDnB,QAAQ,EAAEA,CAAA,KAAK;QACb,IAAI,CAAC5E,OAAO,GAAG,KAAK;QACpB,IAAI,CAACC,SAAS,GAAG,KAAK;MACxB;KACD,CAAC;EACJ;EAEA;EACQwG,mBAAmBA,CAAA;IACzB,IAAI,CAAC3F,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACF,iBAAiB,GAAG,EAAE;IAC3B,IAAI,CAACC,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACzD,IAAI,CAACC,aAAa,GAAG,CAAC;IAC3B,IAAI,CAACD,IAAI,CAACyE,UAAU,GAAG,CAAC;EAC1B;EAEA;EACAwC,iBAAiBA,CAAA;IACf;IACA;IACA,IAAI,CAACyC,mBAAmB,EAAE;EAC5B;EAEA;EACQA,mBAAmBA,CAAA;IACzB,MAAMC,IAAI,GAAG,IAAI,CAACnG,iBAAiB;IACnC,IAAI,CAACsC,aAAa,GAAG;MACnBC,WAAW,EAAE4D,IAAI,CAAC7F,MAAM,CAAC8F,CAAC,IAAIA,CAAC,CAAC7H,cAAc,KAAK,SAAS,CAAC,CAAC8G,MAAM;MACpE7C,UAAU,EAAE2D,IAAI,CAAC7F,MAAM,CAAC8F,CAAC,IAAIA,CAAC,CAAC7H,cAAc,KAAK,QAAQ,CAAC,CAAC8G,MAAM;MAClE5C,SAAS,EAAE0D,IAAI,CAACd;KACjB;EACH;EAEA;EACAzJ,eAAeA,CAACwI,KAAoB;IAClC,IAAIA,KAAK,CAACiC,GAAG,KAAK,OAAO,EAAE;MACzB,IAAI,CAACpH,SAAS,EAAE;IAClB;EACF;EAEApD,cAAcA,CAAA;IACZ+I,OAAO,CAAC0B,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC9K,UAAU,CAAC;IAC/C;IACA,IAAI,CAAC4E,WAAW,CAAC+E,IAAI,CAAC,IAAI,CAAC3J,UAAU,IAAI,EAAE,CAAC;EAC9C;EAEA+K,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAAC/K,UAAU,IAAI,IAAI,CAACA,UAAU,CAACgL,IAAI,EAAE,KAAK,EAAE,EAAE;MACrD,IAAI,CAACpG,WAAW,CAAC+E,IAAI,CAAC,EAAE,CAAC;IAC3B;EACF;EAEA;EACAsB,qBAAqBA,CAAA;IACnB,IAAI,CAAC3F,mBAAmB,GAAG,CAAC,IAAI,CAACA,mBAAmB;EACtD;EAEA7D,oBAAoBA,CAAA;IAClB,IAAI,CAACqD,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAE3C,IAAI,IAAI,CAAC5D,cAAc,CAACC,MAAM,EAAE;MAC9B,IAAI,CAACyD,MAAM,CAACE,OAAO,CAACkG,IAAI,CAAC;QACvBpF,KAAK,EAAE,gBAAgB;QACvBqF,QAAQ,EAAE,IAAI;QACd9F,KAAK,EAAE,IAAI,CAACjE,cAAc,CAACC;OAC5B,CAAC;IACJ;IAEA,IAAI,IAAI,CAACD,cAAc,CAACG,QAAQ,EAAE;MAChC,IAAI,CAACuD,MAAM,CAACE,OAAO,CAACkG,IAAI,CAAC;QACvBpF,KAAK,EAAE,kBAAkB;QACzBqF,QAAQ,EAAE,IAAI;QACd9F,KAAK,EAAE,IAAI,CAACjE,cAAc,CAACG;OAC5B,CAAC;IACJ;IAEA,IAAI,CAACkC,SAAS,EAAE;EAClB;EAEA9B,oBAAoBA,CAAA;IAClB,IAAI,CAACP,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC0D,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAACvB,SAAS,EAAE;EAClB;EAEA;EACA2H,YAAYA,CAACxC,KAAU;IACrB,MAAM/C,IAAI,GAAG+C,KAAK,CAAC/C,IAAI;IAEvB;IACA,MAAMwF,YAAY,GAAGxF,IAAI,CAACgE,MAAM,GAAG,CAAC,IAAIhE,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACE,GAAG,KAAKuF,SAAS;IAE5E,IAAID,YAAY,EAAE;MAChB;MACA,IAAI,CAACxF,IAAI,GAAG,EAAE;MACd,IAAI,CAAC7E,IAAI,CAAC0E,OAAO,GAAG,aAAa;MACjC,IAAI,CAAC1E,IAAI,CAAC2E,QAAQ,GAAG,MAAM;IAC7B,CAAC,MAAM,IAAIE,IAAI,CAACgE,MAAM,GAAG,CAAC,IAAIhE,IAAI,CAAC,CAAC,CAAC,IAAIA,IAAI,CAAC,CAAC,CAAC,CAACE,GAAG,EAAE;MACpD;MACA,IAAI,CAACF,IAAI,GAAGA,IAAI;MAChB,IAAI,CAAC7E,IAAI,CAAC0E,OAAO,GAAGG,IAAI,CAAC,CAAC,CAAC,CAACC,KAAK,IAAI,aAAa;MAClD,IAAI,CAAC9E,IAAI,CAAC2E,QAAQ,GAAGE,IAAI,CAAC,CAAC,CAAC,CAACE,GAAG;IAClC,CAAC,MAAM;MACL;MACA,IAAI,CAACF,IAAI,GAAG,EAAE;MACd,IAAI,CAAC7E,IAAI,CAAC0E,OAAO,GAAG,aAAa;MACjC,IAAI,CAAC1E,IAAI,CAAC2E,QAAQ,GAAG,MAAM;IAC7B;IAEA,IAAI,CAACC,IAAI,GAAG,CAAC;IACb,IAAI,CAAC5E,IAAI,CAACwE,UAAU,GAAG,CAAC;IACxB,IAAI,CAAC8D,aAAa,EAAE;IACpB;IACA,IAAI,CAAC1F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEA;EAEA8H,iBAAiBA,CAAC3C,KAAU;IAC1B,IAAI,CAACjC,YAAY,GAAGiC,KAAK,CAAC4C,YAAY,IAAI,EAAE;IAC5C,IAAI,CAAC5E,aAAa,GAAG,IAAI,CAACD,YAAY,CAACkD,MAAM,KAAK,IAAI,CAACrF,iBAAiB,CAACqF,MAAM;EACjF;EAEA4B,eAAeA,CAAC7C,KAAU;IACxB;IACA,IAAI,CAAC1C,aAAa,GAAG0C,KAAK,CAAC8C,OAAO;EACpC;EAEAC,sBAAsBA,CAAC/C,KAAU;IAC/B;IACA,IAAI,CAAC5C,SAAS,GAAG4C,KAAK,CAAC8C,OAAO;EAChC;EAEAE,YAAYA,CAAChD,KAAU;IACrB,IAAI,CAAC9D,MAAM,GAAG8D,KAAK,CAAC9D,MAAM;IAC1B,IAAI,CAACc,IAAI,GAAG,CAAC;IACb,IAAI,CAAC5E,IAAI,CAACwE,UAAU,GAAG,CAAC;IACxB,IAAI,CAAC8D,aAAa,EAAE;IACpB;IACA,IAAI,CAAC1F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEAkF,UAAUA,CAACC,KAAU;IACnB;IACA,IAAI,CAAChD,IAAI,GAAGgD,KAAK,CAAChD,IAAI;IACtB,IAAI,CAAC5E,IAAI,CAACuE,IAAI,GAAGqD,KAAK,CAACkB,IAAI,IAAI,IAAI,CAAC9I,IAAI,CAACuE,IAAI;IAC7C,IAAI,CAACvE,IAAI,CAACwE,UAAU,GAAGgF,IAAI,CAACqB,KAAK,CAAC,IAAI,CAACjG,IAAI,GAAG,IAAI,CAAC5E,IAAI,CAACuE,IAAI,CAAC;IAC7D,IAAI,CAAC+D,aAAa,EAAE;IACpB;IACA,IAAI,CAAC1F,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACJ,SAAS,EAAE;EAClB;EAEA;;;EAGAjD,UAAUA,CAAA;IACR;IACA,IAAI,CAAC,IAAI,CAACmE,SAAS,IAAI,CAAC,IAAI,CAACA,SAAS,CAACmH,MAAM,EAAE;MAC7C1C,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAE,IAAI,CAACxE,SAAS,CAAC;MACzD,IAAI,CAACP,iBAAiB,CAAC2H,SAAS,CAAC,4DAA4D,EAAE,EAAE,CAAC;MAClG;IACF;IAEA;IACA,IAAI,CAAC/K,IAAI,CAACwE,UAAU,GAAG,CAAC;IACxB,IAAI,CAACI,IAAI,GAAG,CAAC;IACb,IAAI,CAACC,IAAI,GAAG,CAAC;MAAEC,KAAK,EAAE,aAAa;MAAEC,GAAG,EAAE;IAAM,CAAE,CAAC;IACnD,IAAI,CAACjB,MAAM,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,OAAO,EAAE;IAAE,CAAE;IAC3C,IAAI,CAAChF,UAAU,GAAG,EAAE;IACpB,IAAI,CAACoB,cAAc,GAAG,EAAE;IACxB,IAAI,CAACkE,mBAAmB,GAAG,KAAK;IAEhC;IACA,IAAI,IAAI,CAACf,IAAI,IAAI,IAAI,CAACA,IAAI,CAACmH,OAAO,EAAE;MAClC,IAAI,CAACnH,IAAI,CAACmH,OAAO,CAACM,OAAO,CAAEC,MAAW,IAAI;QACxC,MAAMC,KAAK,GAAG,IAAI,CAAC7F,WAAW,CAAC8F,OAAO,CAACF,MAAM,CAACnG,KAAK,CAAC;QACpD,IAAIoG,KAAK,KAAK,CAAC,CAAC,EAAE;UAChBD,MAAM,CAACG,UAAU,GAAGF,KAAK;QAC3B;QACA;QACA,IAAID,MAAM,CAACnG,KAAK,IAAImG,MAAM,CAACnG,KAAK,KAAK,QAAQ,EAAE;UAC7CmG,MAAM,CAACI,MAAM,GAAG,KAAK;QACvB;MACF,CAAC,CAAC;IACJ;IAEA;IACA,IAAI,CAACpG,UAAU,GAAG,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB,IAAI,CAACE,YAAY,GAAG,EAAE;IAEtB;IACA,IAAI,IAAI,CAAC7B,IAAI,EAAE;MACb;MACA,IAAI,CAACA,IAAI,CAACO,MAAM,GAAG;QAAEC,KAAK,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE;MAEhD;MACA,IAAI,CAACT,IAAI,CAACsB,IAAI,GAAG,CAAC;QAAEC,KAAK,EAAE,aAAa;QAAEC,GAAG,EAAE;MAAM,CAAE,CAAC;MAExD;MACA,IAAI,CAACxB,IAAI,CAACqB,IAAI,GAAG,CAAC;MAClB,IAAI,CAACrB,IAAI,CAAC2C,QAAQ,GAAG,IAAI,CAAClG,IAAI,CAACuE,IAAI;IACrC;IAEA;IACA,MAAM+G,QAAQ,GAAG;MACfC,QAAQ,EAAE,cAAc;MACxBC,MAAM,EAAE,IAAI,CAAC7H,SAAS,CAACmH,MAAM;MAC7B7F,UAAU,EAAE,EAAE;MACdC,aAAa,EAAE,EAAE;MACjBuG,QAAQ,EAAE,IAAI,CAAC9H,SAAS,CAACmH;KAC1B;IAED;IACA,IAAI,CAAC5H,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,IAAI,CAAC;IAE9C;IACA,IAAI,CAACrF,kBAAkB,CAACoI,gBAAgB,CAACJ,QAAQ,CAAC,CAAC5D,SAAS,CAAC;MAC3DiB,IAAI,EAAGgD,GAAG,IAAI;QACZ,IAAI,CAACzI,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACgD,GAAG,CAACzC,OAAO,EAAE;UAChB;UACA,IAAI,CAAC5F,kBAAkB,CAACsI,qBAAqB,CAAC,cAAc,CAAC;UAC7D,IAAI,CAACxI,iBAAiB,CAACyI,WAAW,CAACF,GAAG,CAACG,OAAO,IAAI,qCAAqC,EAAE,EAAE,CAAC;QAC9F,CAAC,MAAM;UACL,IAAI,CAAC1I,iBAAiB,CAAC2H,SAAS,CAACY,GAAG,CAACG,OAAO,IAAI,kCAAkC,EAAE,EAAE,CAAC;QACzF;QAEA;QACA,IAAI,CAAC/I,GAAG,CAACgJ,aAAa,EAAE;QAExB;QACAC,UAAU,CAAC,MAAK;UACd,IAAI,IAAI,CAACzI,IAAI,EAAE;YACb,IAAI,CAACA,IAAI,CAAC0I,OAAO,EAAE;UACrB;QACF,CAAC,EAAE,GAAG,CAAC;QAEP,IAAI,CAACxJ,SAAS,EAAE;MAClB,CAAC;MACD0F,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjF,eAAe,CAACwF,cAAc,CAACC,IAAI,CAAC,KAAK,CAAC;QAC/CP,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QAExD;QACA,IAAIA,KAAK,CAAC9H,MAAM,KAAK,GAAG,IAAK8H,KAAK,CAACA,KAAK,IAAIA,KAAK,CAACA,KAAK,CAAC9H,MAAM,KAAK,GAAI,EAAE;UACvE,IAAI,CAAC+C,iBAAiB,CAAC2H,SAAS,CAAC,4CAA4C,EAAE,EAAE,CAAC;UAClF;QACF,CAAC,MAAM;UACL,IAAI,CAAC3H,iBAAiB,CAAC2H,SAAS,CAAC,oDAAoD,EAAE,EAAE,CAAC;QAC5F;MACF;KACD,CAAC;EACJ;EAEA;EACAmB,UAAUA,CAACC,UAAkB;IAC3B,IAAIC,YAAY,GAAU,EAAE;IAE5B,QAAQD,UAAU;MAChB,KAAK,KAAK;QACRC,YAAY,GAAG,IAAI,CAAC5I,iBAAiB;QACrC;MACF,KAAK,UAAU;QACb4I,YAAY,GAAG,IAAI,CAACzG,YAAY;QAChC;MACF,KAAK,UAAU;QACbyG,YAAY,GAAG,IAAI,CAAC5I,iBAAiB;QACrC;IACJ;IAEA,IAAI4I,YAAY,CAACvD,MAAM,KAAK,CAAC,EAAE;MAC7B;IACF;IAEA,MAAMqD,UAAU,GAAGE,YAAY,CAACC,GAAG,CAACvC,GAAG,KAAK;MAC1C,OAAO,EAAEA,GAAG,CAACxI,aAAa;MAC1B,aAAa,EAAEwI,GAAG,CAACpI,gBAAgB;MACnC,SAAS,EAAEoI,GAAG,CAACwC,YAAY;MAC3B,OAAO,EAAExC,GAAG,CAAClI,SAAS;MACtB,QAAQ,EAAEkI,GAAG,CAAC/H,cAAc;MAC5B,WAAW,EAAE+H,GAAG,CAAC7H,gBAAgB;MACjC,cAAc,EAAE,IAAI,CAACkB,UAAU,CAACjB,UAAU,CAAC4H,GAAG,CAAC1H,WAAW,CAAC;MAC3D,MAAM,EAAE0H,GAAG,CAACxH;KACb,CAAC,CAAC;IAEH,MAAMiK,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACP,UAAU,CAAC,CAAC,CAAC,CAAC;IAC1C,MAAMQ,IAAI,GAAGR,UAAU,CAACG,GAAG,CAACM,IAAI,IAAIH,MAAM,CAACI,MAAM,CAACD,IAAI,CAAC,CAAC;IACxD,MAAME,OAAO,GAAGN,OAAO,CAACF,GAAG,CAAC,CAACS,CAAC,EAAE5B,KAAK,MAAM;MAAE6B,EAAE,EAAE7B,KAAK,GAAG,CAAC;MAAE8B,KAAK,EAAE;IAAE,CAAE,CAAC,CAAC;IACzE,IAAI,CAAC/J,cAAc,CAACgK,aAAa,CAAC,sBAAsB,EAAEV,OAAO,EAAEG,IAAI,EAAEG,OAAO,CAAC;EACnF;EAEA;EACA3L,OAAOA,CAAC4I,GAAoB;IAC1B;IACA1B,OAAO,CAAC0B,GAAG,CAAC,WAAW,EAAEA,GAAG,CAAC;EAC/B;EAEA;EACAjI,cAAcA,CAACxB,MAAc;IAC3B,OAAOA,MAAM,KAAK,SAAS,GAAG,eAAe,GAAG,cAAc;EAChE;EAEA6B,UAAUA,CAACgL,UAAkB;IAC3B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAC1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC,OAAOC,IAAI,CAACE,kBAAkB,EAAE,GAAG,GAAG,GAAGF,IAAI,CAACG,kBAAkB,EAAE;EACpE;EAEA9L,YAAYA,CAAC4C,IAAY,EAAEmJ,SAAA,GAAoB,EAAE;IAC/C,IAAI,CAACnJ,IAAI,EAAE,OAAO,EAAE;IACpB,OAAOA,IAAI,CAACyE,MAAM,GAAG0E,SAAS,GAAGnJ,IAAI,CAACoJ,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK,GAAGnJ,IAAI;EAC9E;EAEAhD,YAAYA,CAACwG,KAAa;IACxB,MAAM6F,UAAU,GAA8B;MAC5C,cAAc,EAAE,kBAAkB;MAClC,cAAc,EAAE,kBAAkB;MAClC,cAAc,EAAE,mBAAmB;MACnC,cAAc,EAAE,oBAAoB;MACpC,cAAc,EAAE,oBAAoB;MACpC,cAAc,EAAE,qBAAqB;MACrC,OAAO,EAAE,oBAAoB;MAC7B,QAAQ,EAAE,qBAAqB;MAC/B,kBAAkB,EAAE,YAAY;MAChC,SAAS,EAAE;KACZ;IACD,OAAOA,UAAU,CAAC7F,KAAK,CAAC,IAAI6F,UAAU,CAAC,SAAS,CAAC;EACnD;;qCAxnBW3K,wBAAwB,EAAA1E,EAAA,CAAAsP,iBAAA,CAAAtP,EAAA,CAAAuP,iBAAA,GAAAvP,EAAA,CAAAsP,iBAAA,CAAAE,EAAA,CAAAC,QAAA,GAAAzP,EAAA,CAAAsP,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA3P,EAAA,CAAAsP,iBAAA,CAAAM,EAAA,CAAAC,gBAAA,GAAA7P,EAAA,CAAAsP,iBAAA,CAAAQ,EAAA,CAAA/K,UAAA,GAAA/E,EAAA,CAAAsP,iBAAA,CAAAS,EAAA,CAAAC,wBAAA,GAAAhQ,EAAA,CAAAsP,iBAAA,CAAAW,EAAA,CAAAC,kBAAA,GAAAlQ,EAAA,CAAAsP,iBAAA,CAAAa,EAAA,CAAAC,kBAAA;EAAA;;UAAxB1L,wBAAwB;IAAA2L,SAAA;IAAAC,SAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;;;;;;QCnDrCxQ,EAAA,CAAAsE,UAAA,IAAAoM,uCAAA,iBAAqE;QAUnE1Q,EADF,CAAAC,cAAA,aAA4B,uBAyBkC;QAA1DD,EAZA,CAAAc,UAAA,2BAAA6P,sEAAArQ,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAqQ,GAAA;UAAA,OAAA5Q,EAAA,CAAAa,WAAA,CAAiB4P,GAAA,CAAApE,eAAA,CAAA/L,MAAA,CAAuB;QAAA,EAAC,6BAAAuQ,wEAAAvQ,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAqQ,GAAA;UAAA,OAAA5Q,EAAA,CAAAa,WAAA,CACtB4P,GAAA,CAAAtE,iBAAA,CAAA7L,MAAA,CAAyB;QAAA,EAAC,0BAAAwQ,qEAAAxQ,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAqQ,GAAA;UAAA,OAAA5Q,EAAA,CAAAa,WAAA,CAQ7B4P,GAAA,CAAAjE,YAAA,CAAAlM,MAAA,CAAoB;QAAA,EAAC,wBAAAyQ,mEAAAzQ,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAqQ,GAAA;UAAA,OAAA5Q,EAAA,CAAAa,WAAA,CACvB4P,GAAA,CAAAlH,UAAA,CAAAjJ,MAAA,CAAkB;QAAA,EAAC,wBAAA0Q,mEAAA1Q,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAqQ,GAAA;UAAA,OAAA5Q,EAAA,CAAAa,WAAA,CACnB4P,GAAA,CAAAzE,YAAA,CAAA1L,MAAA,CAAoB;QAAA,EAAC,oCAAA2Q,+EAAA3Q,MAAA;UAAAN,EAAA,CAAAO,aAAA,CAAAqQ,GAAA;UAAA,OAAA5Q,EAAA,CAAAa,WAAA,CACT4P,GAAA,CAAAlE,sBAAA,CAAAjM,MAAA,CAA8B;QAAA,EAAC;QAyCzDN,EAvCA,CAAAsE,UAAA,IAAA4M,+CAAA,0BAAsC,IAAAC,uCAAA,mBAuCqD;QAsCnFnR,EAJR,CAAAC,cAAA,aAAuD,aACxB,aACL,aACG,cACS;QAAAD,EAAA,CAAAE,MAAA,IAA+B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAClEH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAE,MAAA,oBAAY;QAE1CF,EAF0C,CAAAG,YAAA,EAAQ,EAC1C,EACF;QAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,cACQ;QAAAD,EAAA,CAAAE,MAAA,IAA8B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChEH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAE,MAAA,mBAAW;QAEzCF,EAFyC,CAAAG,YAAA,EAAQ,EACzC,EACF;QAGFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,cACS;QAAAD,EAAA,CAAAE,MAAA,IAA6B;QAAAF,EAAA,CAAAG,YAAA,EAAK;QAChEH,EAAA,CAAAC,cAAA,iBAA0B;QAAAD,EAAA,CAAAE,MAAA,kBAAU;QAI5CF,EAJ4C,CAAAG,YAAA,EAAQ,EACxC,EACF,EACF,EACF;QAGNH,EAAA,CAAAC,cAAA,6BAAwG;QACtGD,EAAA,CAAAsE,UAAA,KAAA8M,gDAAA,0BAA2D;QAO7DpR,EAAA,CAAAG,YAAA,EAAoB;QAEpBH,EAAA,CAAAC,cAAA,6BAAyF;QACvFD,EAAA,CAAAsE,UAAA,KAAA+M,gDAAA,0BAA2D;QAM7DrR,EAAA,CAAAG,YAAA,EAAoB;QAEpBH,EAAA,CAAAC,cAAA,6BAAkG;QAChGD,EAAA,CAAAsE,UAAA,KAAAgN,gDAAA,0BAA2D;QAG7DtR,EAAA,CAAAG,YAAA,EAAoB;QAEpBH,EAAA,CAAAC,cAAA,6BAAqF;QACnFD,EAAA,CAAAsE,UAAA,KAAAiN,gDAAA,0BAA2D;QAG7DvR,EAAA,CAAAG,YAAA,EAAoB;QAEpBH,EAAA,CAAAC,cAAA,6BAA2F;QACzFD,EAAA,CAAAsE,UAAA,KAAAkN,gDAAA,0BAA2D;QAK7DxR,EAAA,CAAAG,YAAA,EAAoB;QAEpBH,EAAA,CAAAC,cAAA,6BAAgG;QAC9FD,EAAA,CAAAsE,UAAA,KAAAmN,gDAAA,0BAA2D;QAG7DzR,EAAA,CAAAG,YAAA,EAAoB;QAEpBH,EAAA,CAAAC,cAAA,6BAAkH;QAChHD,EAAA,CAAAsE,UAAA,KAAAoN,gDAAA,0BAA2D;QAG7D1R,EAAA,CAAAG,YAAA,EAAoB;QAEpBH,EAAA,CAAAC,cAAA,6BAAiG;QAC/FD,EAAA,CAAAsE,UAAA,KAAAqN,gDAAA,0BAA2D;QAG7D3R,EAAA,CAAAG,YAAA,EAAoB;QAGpBH,EAAA,CAAAsE,UAAA,KAAAsN,gDAAA,0BAAwC;QAY5C5R,EADE,CAAAG,YAAA,EAAa,EACT;QAGNH,EAAA,CAAAsE,UAAA,KAAAuN,wCAAA,kBAA+E;;;QA9MzE7R,EAAA,CAAA0B,UAAA,SAAA+O,GAAA,CAAAjM,OAAA,IAAAiM,GAAA,CAAAhM,SAAA,CAA0B;QAUNzE,EAAA,CAAAuB,SAAA,GAAiB;QAoBvCvB,EApBsB,CAAA0B,UAAA,SAAA+O,GAAA,CAAApL,QAAA,CAAiB,aAAAoL,GAAA,CAAA7O,IAAA,CAAAuE,IAAA,CAAuB,SAAAsK,GAAA,CAAAhK,IAAA,CAAc,aAAAzG,EAAA,CAAA8R,eAAA,KAAAC,GAAA,EAAA/R,EAAA,CAAAgS,eAAA,KAAAC,GAAA,GAO1E,UAAAxB,GAAA,CAAA7O,IAAA,CAAAC,aAAA,CAC0B,aAAA7B,EAAA,CAAAgS,eAAA,KAAAE,GAAA,EACsB,oBAC/B,eAAAlS,EAAA,CAAAgS,eAAA,KAAAG,GAAA,EACoC,qBAGnC,oBAED,eACL,SAAA1B,GAAA,CAAAjK,IAAA,CACD,WAAAiK,GAAA,CAAA/K,MAAA,CACI,eAAA1F,EAAA,CAAAgS,eAAA,KAAAI,GAAA,EACc;QA6CzBpS,EAAA,CAAAuB,SAAA,GAAyB;QAAzBvB,EAAA,CAAA0B,UAAA,SAAA+O,GAAA,CAAAvK,mBAAA,CAAyB;QAsCOlG,EAAA,CAAAuB,SAAA,GAA+B;QAA/BvB,EAAA,CAAA2B,iBAAA,CAAA8O,GAAA,CAAA/I,aAAA,CAAAC,WAAA,CAA+B;QAMhC3H,EAAA,CAAAuB,SAAA,GAA8B;QAA9BvB,EAAA,CAAA2B,iBAAA,CAAA8O,GAAA,CAAA/I,aAAA,CAAAE,UAAA,CAA8B;QAM7B5H,EAAA,CAAAuB,SAAA,GAA6B;QAA7BvB,EAAA,CAAA2B,iBAAA,CAAA8O,GAAA,CAAA/I,aAAA,CAAAG,SAAA,CAA6B;QAQjB7H,EAAA,CAAAuB,SAAA,GAAa;QAAoBvB,EAAjC,CAAA0B,UAAA,cAAa,mBAAmB,qBAAqB;QAUhD1B,EAAA,CAAAuB,SAAA,GAAa;QAACvB,EAAd,CAAA0B,UAAA,cAAa,oBAAoB;QASxB1B,EAAA,CAAAuB,SAAA,GAAa;QAACvB,EAAd,CAAA0B,UAAA,cAAa,oBAAoB;QAM9C1B,EAAA,CAAAuB,SAAA,GAAa;QAACvB,EAAd,CAAA0B,UAAA,cAAa,oBAAoB;QAM3B1B,EAAA,CAAAuB,SAAA,GAAa;QAACvB,EAAd,CAAA0B,UAAA,cAAa,oBAAoB;QAQ5B1B,EAAA,CAAAuB,SAAA,GAAa;QAACvB,EAAd,CAAA0B,UAAA,cAAa,oBAAoB;QAMnC1B,EAAA,CAAAuB,SAAA,GAAa;QAACvB,EAAd,CAAA0B,UAAA,cAAa,oBAAoB;QAM/B1B,EAAA,CAAAuB,SAAA,GAAa;QAACvB,EAAd,CAAA0B,UAAA,cAAa,qBAAqB;QAsB9F1B,EAAA,CAAAuB,SAAA,GAA+C;QAA/CvB,EAAA,CAAA0B,UAAA,UAAA+O,GAAA,CAAAjM,OAAA,KAAAiM,GAAA,CAAAhM,SAAA,KAAAgM,GAAA,CAAAnL,cAAA,CAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { AppService } from '../services/app.service';
import { UserListComponent } from './user_list/user-list.component';
import { RoleListComponent } from './role-list/role-list.component';
import { EmailTemplatesListComponent } from './email-templates-list/email-templates-list.component';
import { ActivityLogListComponent } from './activity-log-list/activity-log-list.component';
import { PageInfoService } from '../../_metronic/layout/core/page-info.service';

@Component({
  selector: 'app-setting',
  templateUrl: './setting.component.html',
  styleUrls: ['./setting.component.scss']
})
export class SettingComponent implements OnInit, AfterViewInit {
  selectedTab = 'Users'; //store default selected tab
  loginUser: any;
  
  @ViewChild(UserListComponent) userListComponent: UserListComponent;
  @ViewChild(RoleListComponent) roleListComponent: RoleListComponent;
  @ViewChild(EmailTemplatesListComponent) emailTemplatesListComponent: EmailTemplatesListComponent;
  @ViewChild(ActivityLogListComponent) activityLogListComponent: ActivityLogListComponent;
  
  constructor(
    public AppService: AppService,
    private router: Router,
    private pageInfo: PageInfoService
  ) {
    // set the default paging options
  }
  
  ngOnInit() {
    this.pageInfo.updateTitle('Settings');
    //this.loginUser = this.AppService.getLocalStorageItem('metuser', true);
    // Ensure Users tab is selected by default
    this.selectedTab = 'Users';
  }
  
  ngAfterViewInit() {
    // Automatically load users data when the component is ready
    if (this.userListComponent) {
      this.userListComponent.onTabActivated();
    }
  }
  
  onNavChange(tabName: string) {
    console.log(`Switching to tab: ${tabName}`);
    this.selectedTab = tabName;
    
    // Small delay to ensure the component is rendered before calling tab activation
    setTimeout(() => {
      switch (tabName) {
        case 'Users':
          if (this.userListComponent) {
            this.userListComponent.onTabActivated();
          }
          break;
        case 'Roles':
          if (this.roleListComponent) {
            this.roleListComponent.onTabActivated();
          }
          break;
        case 'Email':
          if (this.emailTemplatesListComponent) {
            this.emailTemplatesListComponent.onTabActivated();
          }
          break;
        case 'Activity':
          if (this.activityLogListComponent) {
            this.activityLogListComponent.onTabActivated();
          }
          break;
      }
    }, 100);
  }

  goBack() {
    // Navigate back to the settings dashboard view
    this.router.navigate(['/setting/view']);
  }
}
import { Component, ViewChild, OnInit } from '@angular/core';
import { PageInfoService } from '../../_metronic/layout/core/page-info.service';
import { ModalConfig, ModalComponent } from '../../_metronic/partials';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  modalConfig: ModalConfig = {
    modalTitle: 'Modal title',
    dismissButtonLabel: 'Submit',
    closeButtonLabel: 'Cancel'
  };
  @ViewChild('modal') private modalComponent: ModalComponent;
  constructor(private pageInfo: PageInfoService) {}

  ngOnInit(): void {
    this.pageInfo.updateTitle('Dashboard');
  }

  async openModal() {
    return await this.modalComponent.open();
  }
}

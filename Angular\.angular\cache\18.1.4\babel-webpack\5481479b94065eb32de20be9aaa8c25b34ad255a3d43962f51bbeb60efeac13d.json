{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { Validators } from '@angular/forms';\nimport { distinctUntilChanged, catchError, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"../../services/projects.service\";\nimport * as i4 from \"../../services/permits.service\";\nimport * as i5 from \"../../services/app.service\";\nimport * as i6 from \"../../services/http-utils.service\";\nimport * as i7 from \"../../services/custom-layout.utils.service\";\nimport * as i8 from \"@angular/common\";\nimport * as i9 from \"@ng-select/ng-select\";\nconst _c0 = a0 => ({\n  active: a0\n});\nfunction PermitPopupComponent_div_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \"Add Permit\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\"Edit Permit - \", ctx_r0.permitNumber, \"\");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_16_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_16_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_24_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_24_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"div\", 37)(2, \"span\", 38);\n    i0.ɵɵtext(3, \"Validating...\");\n    i0.ɵɵelementEnd()()();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_34_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_34_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberValidationError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_34_div_1_Template, 2, 0, \"div\", 3)(2, PermitPopupComponent_ng_container_20_div_34_div_2_Template, 2, 1, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    let tmp_3_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_3_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_3_0.errors == null ? null : tmp_3_0.errors[\"permitNumberExists\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.permitNumberValidationError, \" \");\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_42_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_20_div_42_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_20_div_51_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 31)(1, \"div\", 39)(2, \"div\", 40);\n    i0.ɵɵtext(3, \" The syncing of permit details may take up to 3 minutes. \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\")(5, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_ng_container_20_div_51_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r0 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r0.syncPermitDetails());\n    });\n    i0.ɵɵelement(6, \"i\", 42);\n    i0.ɵɵtext(7, \" Sync \");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction PermitPopupComponent_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 8)(3, \"label\", 20);\n    i0.ɵɵtext(4, \"Project \");\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"ng-select\", 22);\n    i0.ɵɵlistener(\"change\", function PermitPopupComponent_ng_container_20_Template_ng_select_change_7_listener($event) {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.onProjectChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_20_div_8_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 19)(10, \"div\", 8)(11, \"label\", 20);\n    i0.ɵɵtext(12, \"Permit / Sub Project Name \");\n    i0.ɵɵelementStart(13, \"span\", 21);\n    i0.ɵɵtext(14, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(15, \"input\", 24);\n    i0.ɵɵtemplate(16, PermitPopupComponent_ng_container_20_div_16_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(17, \"div\", 19)(18, \"div\", 8)(19, \"label\", 20);\n    i0.ɵɵtext(20, \"Location\");\n    i0.ɵɵelementStart(21, \"span\", 21);\n    i0.ɵɵtext(22, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(23, \"input\", 25);\n    i0.ɵɵtemplate(24, PermitPopupComponent_ng_container_20_div_24_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(25, \"div\", 19)(26, \"div\", 26)(27, \"label\", 20);\n    i0.ɵɵtext(28, \"Permit # \");\n    i0.ɵɵelementStart(29, \"span\", 21);\n    i0.ɵɵtext(30, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(31, \"div\", 27)(32, \"input\", 28);\n    i0.ɵɵlistener(\"blur\", function PermitPopupComponent_ng_container_20_Template_input_blur_32_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.triggerPermitNumberValidation());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(33, PermitPopupComponent_ng_container_20_div_33_Template, 4, 0, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(34, PermitPopupComponent_ng_container_20_div_34_Template, 3, 2, \"div\", 23)(35, PermitPopupComponent_ng_container_20_div_35_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(36, \"div\", 26)(37, \"label\", 20);\n    i0.ɵɵtext(38, \"Permit Category \");\n    i0.ɵɵelementStart(39, \"span\", 21);\n    i0.ɵɵtext(40, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(41, \"ng-select\", 30);\n    i0.ɵɵtemplate(42, PermitPopupComponent_ng_container_20_div_42_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 31)(44, \"div\", 8)(45, \"div\", 32);\n    i0.ɵɵelement(46, \"i\", 33);\n    i0.ɵɵelementStart(47, \"span\");\n    i0.ɵɵtext(48, \"For External Review, permit details can be retrieved from the Municipality City website when Municipality is chosen.\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelement(49, \"div\", 19)(50, \"div\", 19);\n    i0.ɵɵtemplate(51, PermitPopupComponent_ng_container_20_div_51_Template, 8, 0, \"div\", 34);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_4_0;\n    let tmp_5_0;\n    let tmp_6_0;\n    let tmp_7_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    let tmp_14_0;\n    let tmp_15_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.projects)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.touched) && ((tmp_4_0 = ctx_r0.permitForm.get(\"projectId\")) == null ? null : tmp_4_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitName\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_6_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_6_0.touched) && ((tmp_6_0 = ctx_r0.permitForm.get(\"location\")) == null ? null : tmp_6_0.invalid));\n    i0.ɵɵadvance(8);\n    i0.ɵɵclassProp(\"is-invalid\", ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.invalid) && ((tmp_7_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_7_0.touched));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isPermitNumberValidating);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_9_0.invalid));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.permitNumberValidationError && !((tmp_10_0 = ctx_r0.permitForm.get(\"permitNumber\")) == null ? null : tmp_10_0.touched));\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"items\", ctx_r0.categories)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_14_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_14_0.touched) && ((tmp_14_0 = ctx_r0.permitForm.get(\"permitCategory\")) == null ? null : tmp_14_0.invalid));\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_15_0 = ctx_r0.permitForm.get(\"permitReviewType\")) == null ? null : tmp_15_0.value) === \"External\" && !ctx_r0.getSyncButtonDisableStatus() && ctx_r0.id === 0);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_8_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_19_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_19_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_27_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_27_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_38_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1, \" Required Field \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_ng_container_21_div_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 35);\n    i0.ɵɵtemplate(1, PermitPopupComponent_ng_container_21_div_38_div_1_Template, 2, 0, \"div\", 3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    let tmp_2_0;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", (tmp_2_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_2_0.errors == null ? null : tmp_2_0.errors[\"required\"]);\n  }\n}\nfunction PermitPopupComponent_ng_container_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 19)(2, \"div\", 43)(3, \"label\", 20);\n    i0.ɵɵtext(4, \"Review Responsible Party\");\n    i0.ɵɵelementStart(5, \"span\", 21);\n    i0.ɵɵtext(6, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(7, \"input\", 44);\n    i0.ɵɵtemplate(8, PermitPopupComponent_ng_container_21_div_8_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 43)(10, \"label\", 20);\n    i0.ɵɵtext(11, \"Primary Contact Name\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"input\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 43)(14, \"label\", 20);\n    i0.ɵɵtext(15, \"Permit Status \");\n    i0.ɵɵelementStart(16, \"span\", 21);\n    i0.ɵɵtext(17, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(18, \"ng-select\", 46);\n    i0.ɵɵtemplate(19, PermitPopupComponent_ng_container_21_div_19_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 19)(21, \"div\", 43)(22, \"label\", 20);\n    i0.ɵɵtext(23, \"Permit Type \");\n    i0.ɵɵelementStart(24, \"span\", 21);\n    i0.ɵɵtext(25, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(26, \"ng-select\", 47);\n    i0.ɵɵtemplate(27, PermitPopupComponent_ng_container_21_div_27_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 43)(29, \"label\", 20);\n    i0.ɵɵtext(30, \"Issue Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(31, \"input\", 48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"div\", 43)(33, \"label\", 20);\n    i0.ɵɵtext(34, \"Applied Date \");\n    i0.ɵɵelementStart(35, \"span\", 21);\n    i0.ɵɵtext(36, \"*\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(37, \"input\", 49);\n    i0.ɵɵtemplate(38, PermitPopupComponent_ng_container_21_div_38_Template, 2, 1, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 43)(41, \"label\", 20);\n    i0.ɵɵtext(42, \"Expiration Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(43, \"input\", 50);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(44, \"div\", 43)(45, \"label\", 20);\n    i0.ɵɵtext(46, \"Completed Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(47, \"input\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 43)(49, \"label\", 20);\n    i0.ɵɵtext(50, \"Final Date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(51, \"input\", 52);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"div\", 19)(53, \"div\", 8)(54, \"label\", 20);\n    i0.ɵɵtext(55, \"Description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(56, \"textarea\", 53);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    let tmp_1_0;\n    let tmp_5_0;\n    let tmp_9_0;\n    let tmp_10_0;\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_1_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_1_0.touched) && ((tmp_1_0 = ctx_r0.permitForm.get(\"reviewResponsibleParty\")) == null ? null : tmp_1_0.invalid));\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"items\", ctx_r0.statuses)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_5_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_5_0.touched) && ((tmp_5_0 = ctx_r0.permitForm.get(\"permitStatus\")) == null ? null : tmp_5_0.invalid));\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"items\", ctx_r0.permitTypes)(\"clearable\", false)(\"multiple\", false);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ((tmp_9_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_9_0.touched) && ((tmp_9_0 = ctx_r0.permitForm.get(\"permitType\")) == null ? null : tmp_9_0.invalid));\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ((tmp_10_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_10_0.touched) && ((tmp_10_0 = ctx_r0.permitForm.get(\"permitAppliedDate\")) == null ? null : tmp_10_0.invalid));\n  }\n}\nfunction PermitPopupComponent_button_24_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_24_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToPreviousTab());\n    });\n    i0.ɵɵtext(1, \" Previous \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction PermitPopupComponent_button_29_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 55);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_29_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.save());\n    });\n    i0.ɵɵtext(1, \" Save \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"disabled\", ctx_r0.permitForm.invalid || ctx_r0.isPermitNumberValidating);\n  }\n}\nfunction PermitPopupComponent_button_30_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function PermitPopupComponent_button_30_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r0 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r0.goToNextTab());\n    });\n    i0.ɵɵtext(1, \" Next \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PermitPopupComponent {\n  modal;\n  fb;\n  projectsService;\n  permitsService;\n  appService;\n  httpUtilService;\n  customLayoutUtilsService;\n  cdr;\n  id = 0; // 0 = Add, otherwise Edit\n  isHideInternalReviewStatus = true; // 0 = Add, otherwise Edit\n  permit; // incoming permit data (for edit)\n  passEntry = new EventEmitter();\n  permitForm;\n  projects = [];\n  reviewTypeArray = ['Internal', 'External', 'Both'];\n  loginUser = {};\n  isLoading = false;\n  muncipalities = [];\n  selectedTab = 'basic';\n  // dropdown options\n  permitTypes = ['Access Control System - Commercial', 'Addition - Commercial', 'Addition - Residential', 'Backflow - Commercial', 'Building Miscellaneous - Commercial', 'Building Move Permit - Residential', 'Building Revisions - Commercial Revision', 'Certificate of Completion', 'Certificate of Occupancy - Commercial', 'Commercial - LV Data Voice Cable Sub-Permit', 'Demolition - Commercial', 'Document Submittal - Commercial Building', 'Electrical Sub-Permit - Commercial', 'Engineering Construction Traffic & Parking Management Plan', 'Fence - Commercial', 'Fire Alarm - Fire', 'Fire Sprinkler/Fire Suppression - Fire', 'Foundation Only - Commercial', 'Gas Sub-Permit - Commercial', 'General Electrical - Commercial', 'General Paving - Paving', 'General Sign Permit', 'Generator - Commercial', 'Interceptor - Commercial', 'Interior (<5000 sq ft) - Commercial', 'Irrigation - Commercial', 'Landscape Non-Residential and Multi-Family', 'Low Voltage - Commercial', 'Mechanical Sub-Permit - Commercial', 'Monument - Sign', 'Mural - Sign', 'New Building - Commercial', 'Plumbing Sub-Permit - Commercial', 'Pool Plumbing Commercial (Sub-Permit)', 'Public Art Permit Application', 'Remodel - Commercial', 'Right-of-Way | ENG A - General', 'Sewer Cap for Demo - Commercial', 'Windows and Doors - Commercial'];\n  categories = ['Primary', 'Sub Permit', 'Industrial', 'Municipal', 'Environmental'];\n  statuses = ['Canceled', 'Complete', 'Expired', 'Fees Due', 'In Review', 'Issued', 'On Hold', 'Requires Resubmit', 'Requires Resubmit for Prescreen', 'Submitted - Online', 'Void'];\n  internalStatusArray = ['Approved', 'Pacifica Verification', 'Dis-Approved', 'Pending', 'Not Required', 'In Review', '1 Cycle Completed'];\n  permitNumber;\n  isPermitMunicipalRequired = false;\n  iscityreviewLinkMunicipalRequired = false;\n  cityReviewLink = '';\n  syncedPermitData = {};\n  permitNumberValidationError = '';\n  isPermitNumberValidating = false;\n  constructor(modal, fb, projectsService, permitsService, appService, httpUtilService, customLayoutUtilsService, cdr) {\n    this.modal = modal;\n    this.fb = fb;\n    this.projectsService = projectsService;\n    this.permitsService = permitsService;\n    this.appService = appService;\n    this.httpUtilService = httpUtilService;\n    this.customLayoutUtilsService = customLayoutUtilsService;\n    this.cdr = cdr;\n    // Subscribe to loading state\n    this.httpUtilService.loadingSubject.subscribe(loading => {\n      this.isLoading = loading;\n    });\n  }\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadMunicipalities();\n    this.loadForm();\n    this.loadProjects();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n  }\n  loadForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      permitCategory: ['', Validators.required],\n      permitType: ['', Validators.required],\n      description: [''],\n      permitReviewType: ['', Validators.required],\n      location: ['', Validators.required],\n      primaryContact: [''],\n      permitAppliedDate: ['', Validators.required],\n      permitExpirationDate: [''],\n      permitIssueDate: [''],\n      permitFinalDate: [''],\n      permitCompleteDate: [''],\n      permitStatus: ['', Validators.required],\n      internalReviewStatus: ['', Validators.required],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      reviewResponsibleParty: ['', Validators.required],\n      permitMunicipality: ['']\n      // cityReviewLink: [''],\n    });\n  }\n  setupPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    if (permitNumberControl) {\n      // Only clear server-side validation while typing; don't call API here\n      permitNumberControl.valueChanges.pipe(distinctUntilChanged()).subscribe(() => {\n        this.permitNumberValidationError = '';\n        if (permitNumberControl.hasError('permitNumberExists')) {\n          const errors = {\n            ...permitNumberControl.errors\n          };\n          delete errors['permitNumberExists'];\n          permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n        }\n      });\n    }\n  }\n  triggerPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    const projectIdControl = this.permitForm.get('projectId');\n    const permitNumber = permitNumberControl?.value;\n    const projectId = projectIdControl?.value;\n    const enteredNormalized = (permitNumber || '').toString().trim().toLowerCase();\n    const originalNormalized = (this.permitNumber || '').toString().trim().toLowerCase();\n    // If empty, or in edit mode and value hasn't changed, just clear any prior error and skip API\n    if (!enteredNormalized || this.id !== 0 && enteredNormalized === originalNormalized) {\n      this.isPermitNumberValidating = false;\n      this.permitNumberValidationError = '';\n      if (permitNumberControl?.hasError('permitNumberExists')) {\n        const errors = {\n          ...permitNumberControl.errors\n        };\n        delete errors['permitNumberExists'];\n        permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n      }\n      return;\n    }\n    if (enteredNormalized && projectId) {\n      this.isPermitNumberValidating = true;\n      this.validatePermitNumber(permitNumber, projectId).pipe(map(res => res?.responseData ?? res)).subscribe(result => {\n        this.isPermitNumberValidating = false;\n        if (result && result.exists) {\n          this.permitNumberValidationError = result.message;\n          permitNumberControl?.setErrors({\n            'permitNumberExists': true\n          });\n        } else {\n          this.permitNumberValidationError = '';\n          if (permitNumberControl?.hasError('permitNumberExists')) {\n            const errors = {\n              ...permitNumberControl.errors\n            };\n            delete errors['permitNumberExists'];\n            permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n          }\n        }\n      });\n    }\n  }\n  validatePermitNumber(permitNumber, projectId) {\n    this.isPermitNumberValidating = true;\n    this.permitNumberValidationError = '';\n    const params = {\n      permitNumber: permitNumber,\n      projectId: projectId,\n      permitId: this.id !== 0 ? this.id : null // Include permitId for edit mode\n    };\n    return this.permitsService.validatePermitNumber(params).pipe(catchError(() => {\n      this.isPermitNumberValidating = false;\n      return of({\n        exists: false,\n        message: ''\n      });\n    }));\n  }\n  loadProjects() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = {\n      paginate: false\n    };\n    this.projectsService.getAllProjectsData(params).subscribe({\n      next: response => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.projects = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.projects = [];\n      }\n    });\n  }\n  loadMunicipalities() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = {\n      loggedinUser: this.loginUser.userId\n    };\n    this.permitsService.getAllMunicipalities(params).subscribe({\n      next: response => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.muncipalities = response.responseData.data;\n        }\n      },\n      error: error => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.muncipalities = [];\n      }\n    });\n  }\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getPermit({\n      permitId: this.id,\n      loggedInUserId: this.loginUser.userId\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.data;\n          this.permitNumber = permitData.permitNumber;\n          this.permitForm.patchValue({\n            projectId: permitData.projectId,\n            permitNumber: permitData.permitNumber,\n            permitReviewType: permitData.permitReviewType,\n            permitCategory: permitData.permitCategory,\n            permitType: permitData.permitType,\n            description: permitData.description,\n            permitName: permitData.permitName,\n            location: permitData.location,\n            internalReviewStatus: permitData.internalReviewStatus,\n            primaryContact: permitData.primaryContact,\n            permitAppliedDate: permitData.permitAppliedDate ? this.formatDateForInput(permitData.permitAppliedDate) : '',\n            permitExpirationDate: permitData.permitExpirationDate ? this.formatDateForInput(permitData.permitExpirationDate) : '',\n            permitIssueDate: permitData.permitIssueDate ? this.formatDateForInput(permitData.permitIssueDate) : '',\n            permitFinalDate: permitData.permitFinalDate ? this.formatDateForInput(permitData.permitFinalDate) : '',\n            permitCompleteDate: permitData.permitCompleteDate ? this.formatDateForInput(permitData.permitCompleteDate) : '',\n            permitStatus: permitData.permitStatus,\n            attentionReason: permitData.attentionReason,\n            internalNotes: permitData.internalNotes,\n            actionTaken: permitData.actionTaken,\n            reviewResponsibleParty: permitData.reviewResponsibleParty,\n            permitMunicipality: permitData.permitMunicipality\n            // cityReviewLink: permitData.cityReviewLink,\n          });\n          this.onPermitReviewTypeChange(permitData.permitReviewType);\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n  }\n  formatDateForInput(dateString) {\n    if (!dateString) return '';\n    const date = new Date(dateString);\n    // Use local timezone to avoid date shifting\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n    return `${year}-${month}-${day}`;\n  }\n  preparePermitData() {\n    const formData = this.permitForm.value;\n    let permitRequestData = {};\n    permitRequestData.projectId = formData.projectId;\n    permitRequestData.permitNumber = formData.permitNumber;\n    permitRequestData.permitReviewType = formData.permitReviewType;\n    permitRequestData.permitCategory = formData.permitCategory;\n    permitRequestData.permitType = formData.permitType;\n    permitRequestData.description = formData.description;\n    permitRequestData.location = formData.location;\n    permitRequestData.primaryContact = formData.primaryContact;\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\n    permitRequestData.permitExpirationDate = formData.permitExpirationDate || null;\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\n    permitRequestData.permitFinalDate = formData.permitFinalDate || null;\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\n    permitRequestData.permitStatus = formData.permitStatus;\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\n    permitRequestData.permitName = formData.permitName;\n    permitRequestData.attentionReason = formData.attentionReason;\n    permitRequestData.internalNotes = formData.internalNotes;\n    permitRequestData.actionTaken = formData.actionTaken;\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\n    permitRequestData.cityReviewLink = this.cityReviewLink;\n    permitRequestData.loggedInUserId = this.loginUser.userId;\n    permitRequestData.syncedPermitData = this.syncedPermitData;\n    const caseId = this.syncedPermitData.EntityId || this.syncedPermitData.permitId || this.syncedPermitData.Id || null;\n    permitRequestData.permitEntityID = caseId;\n    if (this.id !== 0) {\n      permitRequestData.permitId = this.id;\n    }\n    return permitRequestData;\n  }\n  save() {\n    let controls = this.permitForm.controls;\n    console.log('Permit Data:', this.permitForm.value);\n    if (this.permitForm.invalid) {\n      Object.keys(controls).forEach(controlName => controls[controlName].markAsTouched());\n      this.customLayoutUtilsService.showError('Please fill all required fields', '');\n      return;\n    }\n    let permitData = this.preparePermitData();\n    console.log('Permit Data:', permitData);\n    if (this.id === 0) {\n      this.create(permitData);\n    } else {\n      this.edit(permitData);\n    }\n  }\n  create(permitData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.createPermit(permitData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  edit(permitData) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.updatePermit(permitData).subscribe(res => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  showTab(tab, $event) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'notes';\n    }\n    this.cdr.markForCheck();\n  }\n  goToPreviousTab() {\n    if (this.selectedTab === 'notes') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n    this.cdr.markForCheck();\n  }\n  onProjectChange(event) {\n    this.permitForm.patchValue({\n      location: event.projectLocation\n    });\n    // console.log(\"project loacation\",event)\n    // Auto-fill Permit / Sub Project Name for Add mode when selecting a project\n    if (this.id === 0) {\n      const permitNameControl = this.permitForm.get('permitName');\n      const currentValue = (permitNameControl?.value || '').toString().trim();\n      const projectName = event?.projectName || event?.project?.projectName || '';\n      if (permitNameControl && projectName && (currentValue === '' || permitNameControl.pristine)) {\n        permitNameControl.setValue(projectName);\n        permitNameControl.markAsDirty();\n      }\n    }\n  }\n  onPermitReviewTypeChange(event) {\n    const permitControl = this.permitForm.get('permitMunicipality');\n    // const cityReviewControl = this.permitForm.get('cityReviewLink');\n    if (event === 'External') {\n      this.isPermitMunicipalRequired = true;\n      // this.iscityreviewLinkMunicipalRequired = true;\n      permitControl?.setValidators([Validators.required]);\n      // cityReviewControl?.setValidators([Validators.required]);\n    } else {\n      this.isPermitMunicipalRequired = false;\n      // this.iscityreviewLinkMunicipalRequired = false;\n      permitControl?.clearValidators();\n      // cityReviewControl?.clearValidators();\n    }\n    permitControl?.updateValueAndValidity();\n    // cityReviewControl?.updateValueAndValidity();\n  }\n  onPermitReviewTypeClear() {\n    this.permitForm.patchValue({\n      permitReviewType: null\n    });\n    // Also clear the municipality when review type is cleared\n    this.permitForm.patchValue({\n      permitMunicipality: null\n    });\n    this.cityReviewLink = '';\n    this.isPermitMunicipalRequired = false;\n    // Clear validators for municipality\n    const permitControl = this.permitForm.get('permitMunicipality');\n    permitControl?.clearValidators();\n    permitControl?.updateValueAndValidity();\n  }\n  onPermitMunicipalityChange(event) {\n    console.log('event 0 ', event);\n    this.cityReviewLink = event.cityWebsiteLink + '#/permit/';\n    this.getSyncButtonDisableStatus();\n  }\n  onPermitMunicipalityClear() {\n    this.permitForm.patchValue({\n      permitMunicipality: null\n    });\n    this.cityReviewLink = '';\n    this.getSyncButtonDisableStatus();\n  }\n  syncPermitDetails() {\n    const formData = this.permitForm.value;\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.getPermitDetails({\n      permitNumber: formData.permitNumber,\n      municipalityId: formData.permitMunicipality\n    }).subscribe({\n      next: permitResponse => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (!permitResponse.isFault) {\n          let permitData = permitResponse.responseData.permit;\n          this.syncedPermitData = permitData;\n          this.permitForm.patchValue({\n            permitType: permitData.permitType,\n            description: permitData.description,\n            location: permitData.address,\n            permitAppliedDate: permitData.applyDate ? this.formatDateForInput(permitData.applyDate) : '',\n            permitExpirationDate: permitData.expireDate ? this.formatDateForInput(permitData.expireDate) : '',\n            permitIssueDate: permitData.issueDate ? this.formatDateForInput(permitData.issueDate) : '',\n            permitFinalDate: permitData.finalDate ? this.formatDateForInput(permitData.finalDate) : '',\n            permitCompleteDate: permitData.completeDate ? this.formatDateForInput(permitData.completeDate) : '',\n            permitStatus: permitData.permitStatus\n          });\n        } else {\n          console.warn('Permit response has isFault = true', permitResponse.responseData.message);\n        }\n      },\n      error: err => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('API call failed', err);\n      }\n    });\n  }\n  getSyncButtonDisableStatus() {\n    const reviewType = this.permitForm.get('permitReviewType')?.value;\n    const permitNumber = this.permitForm.get('permitNumber')?.value;\n    const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\n    const isExternal = reviewType === 'External';\n    const hasPermitNumber = !!permitNumber;\n    const hasPermitMunicipality = !!permitMunicipality;\n    console.log('isExternal ', isExternal);\n    console.log('hasPermitNumber ', hasPermitNumber);\n    console.log('hasPermitMunicipality ', hasPermitMunicipality);\n    // Disable if any of the conditions are not satisfied\n    return !(isExternal && hasPermitNumber && hasPermitMunicipality);\n  }\n  static ɵfac = function PermitPopupComponent_Factory(__ngFactoryType__) {\n    return new (__ngFactoryType__ || PermitPopupComponent)(i0.ɵɵdirectiveInject(i1.NgbActiveModal), i0.ɵɵdirectiveInject(i2.FormBuilder), i0.ɵɵdirectiveInject(i3.ProjectsService), i0.ɵɵdirectiveInject(i4.PermitsService), i0.ɵɵdirectiveInject(i5.AppService), i0.ɵɵdirectiveInject(i6.HttpUtilsService), i0.ɵɵdirectiveInject(i7.CustomLayoutUtilsService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: PermitPopupComponent,\n    selectors: [[\"app-permit-popup\"]],\n    inputs: {\n      id: \"id\",\n      isHideInternalReviewStatus: \"isHideInternalReviewStatus\",\n      permit: \"permit\"\n    },\n    outputs: {\n      passEntry: \"passEntry\"\n    },\n    decls: 31,\n    vars: 14,\n    consts: [[1, \"modal-content\", \"h-auto\"], [1, \"modal-header\", \"bg-light-primary\"], [1, \"modal-title\", \"h5\", \"fs-3\"], [4, \"ngIf\"], [1, \"float-right\"], [1, \"fa-solid\", \"fs-2\", \"fa-xmark\", \"text-white\", 3, \"click\"], [1, \"modal-body\"], [1, \"row\"], [1, \"col-xl-12\"], [1, \"d-flex\"], [1, \"nav\", \"nav-stretch\", \"nav-line-tabs\", \"nav-line-tabs-2x\", \"border-transparent\", \"fs-3\", \"fw-bold\", \"flex-nowrap\"], [1, \"nav-item\"], [\"data-toggle\", \"tab\", 1, \"nav-link\", \"text-active-primary\", \"me-6\", \"cursor-pointer\", 3, \"click\", \"ngClass\"], [1, \"form\", \"form-label-right\", 3, \"formGroup\"], [1, \"modal-footer\", \"justify-content-between\"], [\"type\", \"button\", \"class\", \"btn btn-secondary btn-sm btn-elevate\", 3, \"click\", 4, \"ngIf\"], [\"type\", \"button\", 1, \"btn\", \"btn-danger\", \"btn-sm\", \"btn-elevate\", \"mr-2\", 3, \"click\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"disabled\", \"click\", 4, \"ngIf\"], [\"type\", \"button\", \"class\", \"btn btn-primary btn-sm\", 3, \"click\", 4, \"ngIf\"], [1, \"row\", \"mt-4\"], [1, \"fw-bold\", \"form-label\", \"mb-2\"], [1, \"text-danger\"], [\"bindLabel\", \"projectName\", \"formControlName\", \"projectId\", \"bindValue\", \"projectId\", \"placeholder\", \"Select Project\", 3, \"change\", \"items\", \"clearable\", \"multiple\"], [\"class\", \"text-danger mt-1 small\", 4, \"ngIf\"], [\"type\", \"text\", \"formControlName\", \"permitName\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"location\", 1, \"form-control\", \"form-control-sm\"], [1, \"col-xl-6\"], [1, \"position-relative\"], [\"type\", \"text\", \"formControlName\", \"permitNumber\", 1, \"form-control\", \"form-control-sm\", 3, \"blur\"], [\"class\", \"position-absolute top-50 end-0 translate-middle-y me-2\", 4, \"ngIf\"], [\"formControlName\", \"permitCategory\", \"placeholder\", \"Select Category\", 3, \"items\", \"clearable\", \"multiple\"], [1, \"row\", \"mt-3\"], [1, \"text-muted\", \"small\", \"d-flex\", \"align-items-center\", 2, \"white-space\", \"normal\"], [1, \"fas\", \"fa-info-circle\", \"me-2\"], [\"class\", \"row mt-3\", 4, \"ngIf\"], [1, \"text-danger\", \"mt-1\", \"small\"], [1, \"position-absolute\", \"top-50\", \"end-0\", \"translate-middle-y\", \"me-2\"], [\"role\", \"status\", 1, \"spinner-border\", \"spinner-border-sm\", \"text-primary\"], [1, \"visually-hidden\"], [1, \"col-xl-12\", \"d-flex\", \"align-items-center\", \"justify-content-end\"], [1, \"small\", \"me-3\", 2, \"white-space\", \"normal\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\"], [1, \"fas\", \"fa-sync-alt\"], [1, \"col-xl-4\"], [\"type\", \"text\", \"formControlName\", \"reviewResponsibleParty\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"text\", \"formControlName\", \"primaryContact\", 1, \"form-control\", \"form-control-sm\"], [\"formControlName\", \"permitStatus\", \"placeholder\", \"Select Status\", 3, \"items\", \"clearable\", \"multiple\"], [\"formControlName\", \"permitType\", \"placeholder\", \"Select Type\", 3, \"items\", \"clearable\", \"multiple\"], [\"type\", \"date\", \"formControlName\", \"permitIssueDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitAppliedDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitExpirationDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitCompleteDate\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"date\", \"formControlName\", \"permitFinalDate\", 1, \"form-control\", \"form-control-sm\"], [\"rows\", \"3\", \"formControlName\", \"description\", 1, \"form-control\", \"form-control-sm\"], [\"type\", \"button\", 1, \"btn\", \"btn-secondary\", \"btn-sm\", \"btn-elevate\", 3, \"click\"], [\"type\", \"button\", 1, \"btn\", \"btn-primary\", \"btn-sm\", 3, \"click\", \"disabled\"]],\n    template: function PermitPopupComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2);\n        i0.ɵɵelementContainerStart(3);\n        i0.ɵɵtemplate(4, PermitPopupComponent_div_4_Template, 2, 0, \"div\", 3)(5, PermitPopupComponent_div_5_Template, 2, 1, \"div\", 3);\n        i0.ɵɵelementContainerEnd();\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(6, \"div\", 4)(7, \"i\", 5);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_i_click_7_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(8, \"div\", 6)(9, \"div\", 7)(10, \"div\", 8)(11, \"div\", 9)(12, \"ul\", 10)(13, \"li\", 11)(14, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_14_listener($event) {\n          return ctx.showTab(\"basic\", $event);\n        });\n        i0.ɵɵtext(15, \" Basic Info \");\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(16, \"li\", 11)(17, \"a\", 12);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_a_click_17_listener($event) {\n          return ctx.showTab(\"details\", $event);\n        });\n        i0.ɵɵtext(18, \" Permit Details \");\n        i0.ɵɵelementEnd()()()()()();\n        i0.ɵɵelementStart(19, \"form\", 13);\n        i0.ɵɵtemplate(20, PermitPopupComponent_ng_container_20_Template, 52, 16, \"ng-container\", 3)(21, PermitPopupComponent_ng_container_21_Template, 57, 10, \"ng-container\", 3);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(22, \"div\", 14)(23, \"div\");\n        i0.ɵɵtemplate(24, PermitPopupComponent_button_24_Template, 2, 0, \"button\", 15);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(25, \"div\")(26, \"button\", 16);\n        i0.ɵɵlistener(\"click\", function PermitPopupComponent_Template_button_click_26_listener() {\n          return ctx.modal.dismiss();\n        });\n        i0.ɵɵtext(27, \" Cancel\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵtext(28, \"\\u00A0 \");\n        i0.ɵɵtemplate(29, PermitPopupComponent_button_29_Template, 2, 1, \"button\", 17)(30, PermitPopupComponent_button_30_Template, 2, 0, \"button\", 18);\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.id === 0);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.id !== 0);\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(10, _c0, ctx.selectedTab === \"basic\"));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c0, ctx.selectedTab === \"details\"));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"formGroup\", ctx.permitForm);\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"details\");\n        i0.ɵɵadvance();\n        i0.ɵɵproperty(\"ngIf\", ctx.selectedTab == \"basic\");\n      }\n    },\n    dependencies: [i8.NgClass, i8.NgIf, i2.ɵNgNoValidate, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgControlStatusGroup, i2.FormGroupDirective, i2.FormControlName, i9.NgSelectComponent],\n    encapsulation: 2\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "Validators", "distinctUntilChanged", "catchError", "map", "of", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r0", "permitNumber", "ɵɵtemplate", "PermitPopupComponent_ng_container_20_div_8_div_1_Template", "ɵɵproperty", "tmp_2_0", "permitForm", "get", "errors", "PermitPopupComponent_ng_container_20_div_16_div_1_Template", "PermitPopupComponent_ng_container_20_div_24_div_1_Template", "permitNumberValidationError", "PermitPopupComponent_ng_container_20_div_34_div_1_Template", "PermitPopupComponent_ng_container_20_div_34_div_2_Template", "tmp_3_0", "PermitPopupComponent_ng_container_20_div_42_div_1_Template", "ɵɵlistener", "PermitPopupComponent_ng_container_20_div_51_Template_button_click_5_listener", "ɵɵrestoreView", "_r3", "ɵɵnextContext", "ɵɵresetView", "syncPermitDetails", "ɵɵelement", "ɵɵelementContainerStart", "PermitPopupComponent_ng_container_20_Template_ng_select_change_7_listener", "$event", "_r2", "onProjectChange", "PermitPopupComponent_ng_container_20_div_8_Template", "PermitPopupComponent_ng_container_20_div_16_Template", "PermitPopupComponent_ng_container_20_div_24_Template", "PermitPopupComponent_ng_container_20_Template_input_blur_32_listener", "triggerPermitNumberValidation", "PermitPopupComponent_ng_container_20_div_33_Template", "PermitPopupComponent_ng_container_20_div_34_Template", "PermitPopupComponent_ng_container_20_div_35_Template", "PermitPopupComponent_ng_container_20_div_42_Template", "PermitPopupComponent_ng_container_20_div_51_Template", "projects", "tmp_4_0", "touched", "invalid", "tmp_5_0", "tmp_6_0", "ɵɵclassProp", "tmp_7_0", "isPermitNumberValidating", "tmp_9_0", "tmp_10_0", "categories", "tmp_14_0", "tmp_15_0", "value", "getSyncButtonDisableStatus", "id", "PermitPopupComponent_ng_container_21_div_8_div_1_Template", "PermitPopupComponent_ng_container_21_div_19_div_1_Template", "PermitPopupComponent_ng_container_21_div_27_div_1_Template", "PermitPopupComponent_ng_container_21_div_38_div_1_Template", "PermitPopupComponent_ng_container_21_div_8_Template", "PermitPopupComponent_ng_container_21_div_19_Template", "PermitPopupComponent_ng_container_21_div_27_Template", "PermitPopupComponent_ng_container_21_div_38_Template", "tmp_1_0", "statuses", "permitTypes", "PermitPopupComponent_button_24_Template_button_click_0_listener", "_r4", "goToPreviousTab", "PermitPopupComponent_button_29_Template_button_click_0_listener", "_r5", "save", "PermitPopupComponent_button_30_Template_button_click_0_listener", "_r6", "goToNextTab", "PermitPopupComponent", "modal", "fb", "projectsService", "permitsService", "appService", "httpUtilService", "customLayoutUtilsService", "cdr", "isHideInternalReviewStatus", "permit", "passEntry", "reviewTypeArray", "loginUser", "isLoading", "muncipalities", "selectedTab", "internalStatusArray", "isPermitMunicipalRequired", "iscityreviewLinkMunicipalRequired", "cityReviewLink", "syncedPermitData", "constructor", "loadingSubject", "subscribe", "loading", "ngOnInit", "getLoggedInUser", "loadMunicipalities", "loadForm", "loadProjects", "setupPermitNumberValidation", "patchForm", "group", "projectId", "required", "permitName", "permitCategory", "permitType", "description", "permitReviewType", "location", "primaryContact", "permitAppliedDate", "permitExpirationDate", "permitIssueDate", "permitFinalDate", "permitCompleteDate", "permitStatus", "internalReviewStatus", "attentionReason", "internalNotes", "actionTaken", "reviewResponsibleParty", "permitMunicipality", "permitNumberControl", "valueChanges", "pipe", "<PERSON><PERSON><PERSON><PERSON>", "setErrors", "Object", "keys", "length", "projectIdControl", "enteredNormalized", "toString", "trim", "toLowerCase", "originalNormalized", "validatePermitNumber", "res", "responseData", "result", "exists", "message", "params", "permitId", "next", "paginate", "getAllProjectsData", "response", "data", "error", "console", "loggedinUser", "userId", "getAllMunicipalities", "get<PERSON><PERSON><PERSON>", "loggedInUserId", "permitResponse", "<PERSON><PERSON><PERSON>", "permitData", "patchValue", "formatDateForInput", "onPermitReviewTypeChange", "warn", "err", "dateString", "date", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "preparePermitData", "formData", "permitRequestData", "caseId", "EntityId", "Id", "permitEntityID", "controls", "log", "for<PERSON>ach", "controlName", "<PERSON><PERSON><PERSON><PERSON>ched", "showError", "create", "edit", "createPermit", "showSuccess", "emit", "close", "updatePermit", "showTab", "tab", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "projectLocation", "permitNameControl", "currentValue", "projectName", "project", "pristine", "setValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "permitControl", "setValidators", "clearValidators", "updateValueAndValidity", "onPermitReviewTypeClear", "onPermitMunicipalityChange", "cityWebsiteLink", "onPermitMunicipalityClear", "getPermitDetails", "municipalityId", "address", "applyDate", "expireDate", "issueDate", "finalDate", "completeDate", "reviewType", "isExternal", "hasPermitNumber", "hasPermitMunicipality", "ɵɵdirectiveInject", "i1", "NgbActiveModal", "i2", "FormBuilder", "i3", "ProjectsService", "i4", "PermitsService", "i5", "AppService", "i6", "HttpUtilsService", "i7", "CustomLayoutUtilsService", "ChangeDetectorRef", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "PermitPopupComponent_Template", "rf", "ctx", "PermitPopupComponent_div_4_Template", "PermitPopupComponent_div_5_Template", "PermitPopupComponent_Template_i_click_7_listener", "dismiss", "PermitPopupComponent_Template_a_click_14_listener", "PermitPopupComponent_Template_a_click_17_listener", "PermitPopupComponent_ng_container_20_Template", "PermitPopupComponent_ng_container_21_Template", "PermitPopupComponent_button_24_Template", "PermitPopupComponent_Template_button_click_26_listener", "PermitPopupComponent_button_29_Template", "PermitPopupComponent_button_30_Template", "ɵɵpureFunction1", "_c0"], "sources": ["D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.ts", "D:\\permittracker\\Angular\\src\\app\\modules\\permits\\permit-popup\\permit-popup.component.html"], "sourcesContent": ["import {\n  Component,\n  Input,\n  Output,\n  EventEmitter,\n  ChangeDetectorRef,\n  OnInit,\n} from '@angular/core';\nimport { FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';\nimport { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';\nimport { ProjectsService } from '../../services/projects.service';\nimport { PermitsService } from '../../services/permits.service';\nimport { AppService } from '../../services/app.service';\nimport { HttpUtilsService } from '../../services/http-utils.service';\nimport { CustomLayoutUtilsService } from '../../services/custom-layout.utils.service';\nimport { even } from '@rxweb/reactive-form-validators';\nimport { debounceTime, distinctUntilChanged, switchMap, catchError, map } from 'rxjs/operators';\nimport { of } from 'rxjs';\n\n@Component({\n  selector: 'app-permit-popup',\n  templateUrl: './permit-popup.component.html',\n})\nexport class PermitPopupComponent {\n  @Input() id: number = 0; // 0 = Add, otherwise Edit\n  @Input() isHideInternalReviewStatus: Boolean = true; // 0 = Add, otherwise Edit\n  @Input() permit: any; // incoming permit data (for edit)\n  @Output() passEntry: EventEmitter<any> = new EventEmitter();\n\n  permitForm: FormGroup;\n  projects: any[] = [];\n  reviewTypeArray: any[] = ['Internal', 'External', 'Both'];\n  loginUser: any = {};\n  isLoading: boolean = false;\n  muncipalities: any = [];\n  selectedTab: any = 'basic';\n  // dropdown options\n  permitTypes = [\n    'Access Control System - Commercial',\n    'Addition - Commercial',\n    'Addition - Residential',\n    'Backflow - Commercial',\n    'Building Miscellaneous - Commercial',\n    'Building Move Permit - Residential',\n    'Building Revisions - Commercial Revision',\n    'Certificate of Completion',\n    'Certificate of Occupancy - Commercial',\n    'Commercial - LV Data Voice Cable Sub-Permit',\n    'Demolition - Commercial',\n    'Document Submittal - Commercial Building',\n    'Electrical Sub-Permit - Commercial',\n    'Engineering Construction Traffic & Parking Management Plan',\n    'Fence - Commercial',\n    'Fire Alarm - Fire',\n    'Fire Sprinkler/Fire Suppression - Fire',\n    'Foundation Only - Commercial',\n    'Gas Sub-Permit - Commercial',\n    'General Electrical - Commercial',\n    'General Paving - Paving',\n    'General Sign Permit',\n    'Generator - Commercial',\n    'Interceptor - Commercial',\n    'Interior (<5000 sq ft) - Commercial',\n    'Irrigation - Commercial',\n    'Landscape Non-Residential and Multi-Family',\n    'Low Voltage - Commercial',\n    'Mechanical Sub-Permit - Commercial',\n    'Monument - Sign',\n    'Mural - Sign',\n    'New Building - Commercial',\n    'Plumbing Sub-Permit - Commercial',\n    'Pool Plumbing Commercial (Sub-Permit)',\n    'Public Art Permit Application',\n    'Remodel - Commercial',\n    'Right-of-Way | ENG A - General',\n    'Sewer Cap for Demo - Commercial',\n    'Windows and Doors - Commercial',\n  ];\n  categories = [\n    'Primary',\n    'Sub Permit',\n    'Industrial',\n    'Municipal',\n    'Environmental',\n  ];\n  statuses = [\n    'Canceled',\n    'Complete',\n    'Expired',\n    'Fees Due',\n    'In Review',\n    'Issued',\n    'On Hold',\n    'Requires Resubmit',\n    'Requires Resubmit for Prescreen',\n    'Submitted - Online',\n    'Void',\n  ];\n  internalStatusArray =['Approved','Pacifica Verification','Dis-Approved','Pending','Not Required','In Review','1 Cycle Completed']\n  permitNumber: any;\n  isPermitMunicipalRequired: boolean = false;\n  iscityreviewLinkMunicipalRequired: boolean=false;\n  cityReviewLink:any ='';\n  syncedPermitData:any ={};\n  permitNumberValidationError: string = '';\n  isPermitNumberValidating: boolean = false;\n  constructor(\n    public modal: NgbActiveModal,\n    private fb: FormBuilder,\n    private projectsService: ProjectsService,\n    private permitsService: PermitsService,\n    private appService: AppService,\n    private httpUtilService: HttpUtilsService,\n    private customLayoutUtilsService: CustomLayoutUtilsService,\n    private cdr: ChangeDetectorRef\n  ) {\n    // Subscribe to loading state\n    this.httpUtilService.loadingSubject.subscribe((loading) => {\n      this.isLoading = loading;\n    });\n  }\n\n  ngOnInit() {\n    this.loginUser = this.appService.getLoggedInUser();\n    this.loadMunicipalities();\n    this.loadForm();\n    this.loadProjects();\n    this.setupPermitNumberValidation();\n    if (this.id !== 0) {\n      this.patchForm();\n    }\n  }\n\n  loadForm() {\n    this.permitForm = this.fb.group({\n      projectId: ['', Validators.required],\n      permitName: ['', Validators.required],\n      permitNumber: ['', Validators.required],\n      permitCategory: ['', Validators.required],\n      permitType: ['', Validators.required],\n      description: [''],\n      permitReviewType: ['', Validators.required],\n      location: ['',Validators.required],\n      primaryContact: [''],\n      permitAppliedDate: ['', Validators.required],\n      permitExpirationDate: [''],\n      permitIssueDate: [''],\n      permitFinalDate: [''],\n      permitCompleteDate: [''],\n      permitStatus: ['', Validators.required],\n      internalReviewStatus: ['', Validators.required],\n      attentionReason: [''],\n      internalNotes: [''],\n      actionTaken: [''],\n      reviewResponsibleParty: ['', Validators.required],\n      permitMunicipality: [''],\n      // cityReviewLink: [''],\n    });\n  }\n\n  setupPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    if (permitNumberControl) {\n      // Only clear server-side validation while typing; don't call API here\n      permitNumberControl.valueChanges\n        .pipe(distinctUntilChanged())\n        .subscribe(() => {\n          this.permitNumberValidationError = '';\n          if (permitNumberControl.hasError('permitNumberExists')) {\n            const errors: any = { ...permitNumberControl.errors };\n            delete errors['permitNumberExists'];\n            permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n          }\n        });\n    }\n  }\n\n  triggerPermitNumberValidation() {\n    const permitNumberControl = this.permitForm.get('permitNumber');\n    const projectIdControl = this.permitForm.get('projectId');\n    const permitNumber = permitNumberControl?.value;\n    const projectId = projectIdControl?.value;\n\n    const enteredNormalized = (permitNumber || '').toString().trim().toLowerCase();\n    const originalNormalized = (this.permitNumber || '').toString().trim().toLowerCase();\n\n    // If empty, or in edit mode and value hasn't changed, just clear any prior error and skip API\n    if (!enteredNormalized || (this.id !== 0 && enteredNormalized === originalNormalized)) {\n      this.isPermitNumberValidating = false;\n      this.permitNumberValidationError = '';\n      if (permitNumberControl?.hasError('permitNumberExists')) {\n        const errors: any = { ...permitNumberControl.errors };\n        delete errors['permitNumberExists'];\n        permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n      }\n      return;\n    }\n\n    if (enteredNormalized && projectId) {\n      this.isPermitNumberValidating = true;\n      this.validatePermitNumber(permitNumber, projectId)\n        .pipe(map((res: any) => res?.responseData ?? res))\n        .subscribe((result: any) => {\n          this.isPermitNumberValidating = false;\n          if (result && result.exists) {\n            this.permitNumberValidationError = result.message;\n            permitNumberControl?.setErrors({ 'permitNumberExists': true });\n          } else {\n            this.permitNumberValidationError = '';\n            if (permitNumberControl?.hasError('permitNumberExists')) {\n              const errors: any = { ...permitNumberControl.errors };\n              delete errors['permitNumberExists'];\n              permitNumberControl.setErrors(Object.keys(errors).length > 0 ? errors : null);\n            }\n          }\n        });\n    }\n  }\n\n  validatePermitNumber(permitNumber: string, projectId: number) {\n    this.isPermitNumberValidating = true;\n    this.permitNumberValidationError = '';\n\n    const params = {\n      permitNumber: permitNumber,\n      projectId: projectId,\n      permitId: this.id !== 0 ? this.id : null // Include permitId for edit mode\n    };\n\n    return this.permitsService.validatePermitNumber(params).pipe(\n      catchError(() => {\n        this.isPermitNumberValidating = false;\n        return of({ exists: false, message: '' });\n      })\n    );\n  }\n\n  loadProjects() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = { paginate: false };\n    this.projectsService.getAllProjectsData(params).subscribe({\n      next: (response: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.projects = response.responseData.data;\n        }\n      },\n      error: (error: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.projects = [];\n      },\n    });\n  }\n\n  loadMunicipalities() {\n    this.httpUtilService.loadingSubject.next(true);\n    const params = { loggedinUser: this.loginUser.userId };\n    this.permitsService.getAllMunicipalities(params).subscribe({\n      next: (response: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        if (response && response.responseData) {\n          this.muncipalities = response.responseData.data;\n        }\n      },\n      error: (error: any) => {\n        this.httpUtilService.loadingSubject.next(false);\n        console.error('Error loading projects:', error);\n        this.muncipalities = [];\n      },\n    });\n  }\n\n  patchForm() {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService\n      .getPermit({ permitId: this.id, loggedInUserId: this.loginUser.userId })\n      .subscribe({\n        next: (permitResponse: any) => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!permitResponse.isFault) {\n            let permitData = permitResponse.responseData.data;\n            this.permitNumber = permitData.permitNumber;\n            this.permitForm.patchValue({\n              projectId: permitData.projectId,\n              permitNumber: permitData.permitNumber,\n              permitReviewType: permitData.permitReviewType,\n              permitCategory: permitData.permitCategory,\n              permitType: permitData.permitType,\n              description: permitData.description,\n              permitName: permitData.permitName,\n              location: permitData.location,\n              internalReviewStatus:permitData.internalReviewStatus,\n              primaryContact: permitData.primaryContact,\n              permitAppliedDate: permitData.permitAppliedDate\n                ? this.formatDateForInput(permitData.permitAppliedDate)\n                : '',\n              permitExpirationDate: permitData.permitExpirationDate\n                ? this.formatDateForInput(permitData.permitExpirationDate)\n                : '',\n              permitIssueDate: permitData.permitIssueDate\n                ? this.formatDateForInput(permitData.permitIssueDate)\n                : '',\n                  permitFinalDate: permitData.permitFinalDate\n                ? this.formatDateForInput(permitData.permitFinalDate)\n                : '',\n              permitCompleteDate: permitData.permitCompleteDate\n                ? this.formatDateForInput(permitData.permitCompleteDate)\n                : '',\n              permitStatus: permitData.permitStatus,\n              attentionReason: permitData.attentionReason,\n              internalNotes: permitData.internalNotes,\n              actionTaken: permitData.actionTaken,\n              reviewResponsibleParty: permitData.reviewResponsibleParty,\n              permitMunicipality: permitData.permitMunicipality,\n              // cityReviewLink: permitData.cityReviewLink,\n            });\n            this.onPermitReviewTypeChange(permitData.permitReviewType)\n          } else {\n            console.warn(\n              'Permit response has isFault = true',\n              permitResponse.responseData\n            );\n          }\n        },\n        error: (err) => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('API call failed', err);\n        },\n      });\n  }\n\n  formatDateForInput(dateString: string): string {\n    if (!dateString) return '';\n\n    const date = new Date(dateString);\n    // Use local timezone to avoid date shifting\n    const year = date.getFullYear();\n    const month = String(date.getMonth() + 1).padStart(2, '0');\n    const day = String(date.getDate()).padStart(2, '0');\n\n    return `${year}-${month}-${day}`;\n  }\n\n  preparePermitData() {\n    const formData = this.permitForm.value;\n\n    let permitRequestData: any = {};\n    permitRequestData.projectId = formData.projectId;\n    permitRequestData.permitNumber = formData.permitNumber;\n    permitRequestData.permitReviewType = formData.permitReviewType;\n    permitRequestData.permitCategory = formData.permitCategory;\n    permitRequestData.permitType = formData.permitType;\n    permitRequestData.description = formData.description;\n    permitRequestData.location = formData.location;\n    permitRequestData.primaryContact = formData.primaryContact;\n    permitRequestData.permitAppliedDate = formData.permitAppliedDate || null;\n    permitRequestData.permitExpirationDate =\n      formData.permitExpirationDate || null;\n    permitRequestData.permitIssueDate = formData.permitIssueDate || null;\n    permitRequestData.permitFinalDate =\n      formData.permitFinalDate || null;\n    permitRequestData.permitCompleteDate = formData.permitCompleteDate || null;\n    permitRequestData.permitStatus = formData.permitStatus;\n    permitRequestData.internalReviewStatus = formData.internalReviewStatus;\n    permitRequestData.permitName = formData.permitName;\n    permitRequestData.attentionReason = formData.attentionReason;\n    permitRequestData.internalNotes = formData.internalNotes;\n    permitRequestData.actionTaken = formData.actionTaken;\n    permitRequestData.reviewResponsibleParty = formData.reviewResponsibleParty;\n    permitRequestData.permitMunicipality = formData.permitMunicipality;\n    permitRequestData.cityReviewLink = this.cityReviewLink;\n    permitRequestData.loggedInUserId = this.loginUser.userId;\n    permitRequestData.syncedPermitData = this.syncedPermitData;\n    const caseId =  this.syncedPermitData.EntityId ||  this.syncedPermitData.permitId ||  this.syncedPermitData.Id || null;\n    permitRequestData.permitEntityID = caseId\n    if (this.id !== 0) {\n      permitRequestData.permitId = this.id;\n    }\n\n    return permitRequestData;\n  }\n\n  save() {\n    let controls = this.permitForm.controls;\n    console.log('Permit Data:', this.permitForm.value);\n    if (this.permitForm.invalid) {\n      Object.keys(controls).forEach((controlName) =>\n        controls[controlName].markAsTouched()\n      );\n      this.customLayoutUtilsService.showError(\n        'Please fill all required fields',\n        ''\n      );\n      return;\n    }\n    let permitData: any = this.preparePermitData();\n    console.log('Permit Data:', permitData);\n    if (this.id === 0) {\n      this.create(permitData);\n    } else {\n      this.edit(permitData);\n    }\n  }\n\n  create(permitData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.createPermit(permitData).subscribe((res: any) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n\n  edit(permitData: any) {\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService.updatePermit(permitData).subscribe((res) => {\n      this.httpUtilService.loadingSubject.next(false);\n      if (!res.isFault) {\n        this.customLayoutUtilsService.showSuccess(res.responseData.message, '');\n        this.passEntry.emit(true);\n        this.modal.close();\n      } else {\n        this.customLayoutUtilsService.showError(res.responseData.message, '');\n        this.passEntry.emit(false);\n      }\n    });\n  }\n  showTab(tab: any, $event: any) {\n    this.selectedTab = tab;\n    this.cdr.markForCheck();\n  }\n\n  goToNextTab() {\n    if (this.selectedTab === 'basic') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'notes';\n    }\n    this.cdr.markForCheck();\n  }\n\n  goToPreviousTab() {\n    if (this.selectedTab === 'notes') {\n      this.selectedTab = 'details';\n    } else if (this.selectedTab === 'details') {\n      this.selectedTab = 'basic';\n    }\n    this.cdr.markForCheck();\n  }\n\n  onProjectChange(event:any){\n     this.permitForm.patchValue({\n              location: event.projectLocation })\n    // console.log(\"project loacation\",event)\n    // Auto-fill Permit / Sub Project Name for Add mode when selecting a project\n    if (this.id === 0) {\n      const permitNameControl = this.permitForm.get('permitName');\n      const currentValue = (permitNameControl?.value || '').toString().trim();\n      const projectName = event?.projectName || event?.project?.projectName || '';\n      if (permitNameControl && projectName && (currentValue === '' || permitNameControl.pristine)) {\n        permitNameControl.setValue(projectName);\n        permitNameControl.markAsDirty();\n      }\n    }\n\n  }\n\nonPermitReviewTypeChange(event: any) {\n  const permitControl = this.permitForm.get('permitMunicipality');\n  // const cityReviewControl = this.permitForm.get('cityReviewLink');\n\n  if (event === 'External') {\n    this.isPermitMunicipalRequired = true;\n    // this.iscityreviewLinkMunicipalRequired = true;\n\n    permitControl?.setValidators([Validators.required]);\n    // cityReviewControl?.setValidators([Validators.required]);\n  } else {\n    this.isPermitMunicipalRequired = false;\n    // this.iscityreviewLinkMunicipalRequired = false;\n\n    permitControl?.clearValidators();\n    // cityReviewControl?.clearValidators();\n  }\n\n  permitControl?.updateValueAndValidity();\n  // cityReviewControl?.updateValueAndValidity();\n}\n\nonPermitReviewTypeClear() {\n  this.permitForm.patchValue({ permitReviewType: null });\n  // Also clear the municipality when review type is cleared\n  this.permitForm.patchValue({ permitMunicipality: null });\n  this.cityReviewLink = '';\n  this.isPermitMunicipalRequired = false;\n  \n  // Clear validators for municipality\n  const permitControl = this.permitForm.get('permitMunicipality');\n  permitControl?.clearValidators();\n  permitControl?.updateValueAndValidity();\n}\n\nonPermitMunicipalityChange(event:any){\n  console.log('event 0 ',event);\n  this.cityReviewLink= event.cityWebsiteLink+'#/permit/';\n  this.getSyncButtonDisableStatus()\n}\n\nonPermitMunicipalityClear(){\n  this.permitForm.patchValue({ permitMunicipality: null });\n  this.cityReviewLink = '';\n  this.getSyncButtonDisableStatus();\n}\n\nsyncPermitDetails(){\n   const formData = this.permitForm.value;\n    this.httpUtilService.loadingSubject.next(true);\n    this.permitsService\n      .getPermitDetails({ permitNumber: formData.permitNumber, municipalityId: formData.permitMunicipality })\n      .subscribe({\n        next: (permitResponse: any) => {\n          this.httpUtilService.loadingSubject.next(false);\n          if (!permitResponse.isFault) {\n            let permitData = permitResponse.responseData.permit;\n            this.syncedPermitData = permitData\n            this.permitForm.patchValue({\n              permitType: permitData.permitType,\n              description: permitData.description,\n              location: permitData.address,\n              permitAppliedDate: permitData.applyDate\n                ? this.formatDateForInput(permitData.applyDate)\n                : '',\n              permitExpirationDate: permitData.expireDate\n                ? this.formatDateForInput(permitData.expireDate)\n                : '',\n              permitIssueDate: permitData.issueDate\n                ? this.formatDateForInput(permitData.issueDate)\n                : '',\n              permitFinalDate: permitData.finalDate\n                ? this.formatDateForInput(permitData.finalDate)\n                : '',\n              permitCompleteDate: permitData.completeDate\n                ? this.formatDateForInput(permitData.completeDate)\n                : '',\n              permitStatus: permitData.permitStatus,\n\n            });\n          } else {\n            console.warn(\n              'Permit response has isFault = true',\n              permitResponse.responseData.message\n            );\n          }\n        },\n        error: (err) => {\n          this.httpUtilService.loadingSubject.next(false);\n          console.error('API call failed', err);\n        },\n      });\n}\n\ngetSyncButtonDisableStatus(): boolean {\n  const reviewType = this.permitForm.get('permitReviewType')?.value;\n  const permitNumber = this.permitForm.get('permitNumber')?.value;\n  const permitMunicipality = this.permitForm.get('permitMunicipality')?.value;\n\n  const isExternal = reviewType === 'External';\n  const hasPermitNumber = !!permitNumber;\n  const hasPermitMunicipality = !!permitMunicipality;\n\n  console.log('isExternal ', isExternal)\n  console.log('hasPermitNumber ', hasPermitNumber)\n  console.log('hasPermitMunicipality ', hasPermitMunicipality)\n  // Disable if any of the conditions are not satisfied\n  return !(isExternal && hasPermitNumber && hasPermitMunicipality);\n}\n}\n", "<div class=\"modal-content h-auto\">\n  <div class=\"modal-header bg-light-primary\">\n    <div class=\"modal-title h5 fs-3\">\n      <ng-container> \n        <div *ngIf=\"id === 0\">Add Permit</div>\n        <div *ngIf=\"id !== 0\">Edit Permit - {{ permitNumber }}</div>\n      </ng-container>\n    </div>\n    <div class=\"float-right\">\n      <i class=\"fa-solid fs-2 fa-xmark text-white\" (click)=\"modal.dismiss()\"></i>\n    </div>\n  </div>\n\n  <div class=\"modal-body\">\n    <!-- Loading overlay removed; global loader handles this -->\n\n    <div class=\"row\">\n      <div class=\"col-xl-12\">\n        <div class=\"d-flex\">\n          <ul class=\"nav nav-stretch nav-line-tabs nav-line-tabs-2x border-transparent fs-3 fw-bold flex-nowrap\">\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'basic' }\" (click)=\"showTab('basic', $event)\">\n                Basic Info\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'details' }\" (click)=\"showTab('details', $event)\">\n                Permit Details\n              </a>\n            </li>\n            <!-- <li class=\"nav-item\">\n              <a class=\"nav-link text-active-primary me-6 cursor-pointer\" data-toggle=\"tab\"\n                [ngClass]=\"{ active: selectedTab === 'notes' }\" (click)=\"showTab('notes', $event)\">\n                Notes/Actions\n              </a>\n            </li> -->\n          </ul>\n        </div>\n      </div>\n    </div>\n\n    <form class=\"form form-label-right\" [formGroup]=\"permitForm\">\n      <ng-container *ngIf=\"selectedTab == 'basic'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Project <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"projects\" [clearable]=\"false\" [multiple]=\"false\" bindLabel=\"projectName\"\n              formControlName=\"projectId\" bindValue=\"projectId\" placeholder=\"Select Project\"\n              (change)=\"onProjectChange($event)\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('projectId')?.touched && permitForm.get('projectId')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('projectId')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Permit / Sub Project Name <span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitName\" />\n            <div *ngIf=\"permitForm.get('permitName')?.touched && permitForm.get('permitName')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitName')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n                <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Location<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"location\" />\n            <div *ngIf=\"permitForm.get('location')?.touched && permitForm.get('location')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('location')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Permit # <span class=\"text-danger\">*</span></label>\n            <div class=\"position-relative\">\n              <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"permitNumber\" \n                     (blur)=\"triggerPermitNumberValidation()\"\n                     [class.is-invalid]=\"permitForm.get('permitNumber')?.invalid && permitForm.get('permitNumber')?.touched\" />\n              <div *ngIf=\"isPermitNumberValidating\" class=\"position-absolute top-50 end-0 translate-middle-y me-2\">\n                <div class=\"spinner-border spinner-border-sm text-primary\" role=\"status\">\n                  <span class=\"visually-hidden\">Validating...</span>\n                </div>\n              </div>\n            </div>\n            <!-- Validation error messages -->\n            <div *ngIf=\"permitForm.get('permitNumber')?.touched && permitForm.get('permitNumber')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['required']\">\n                Required Field\n              </div>\n              <div *ngIf=\"permitForm.get('permitNumber')?.errors?.['permitNumberExists']\">\n                {{ permitNumberValidationError }}\n              </div>\n            </div>\n            <!-- Show validation error even if field is not touched but has error (e.g., after blur) -->\n            <div *ngIf=\"permitNumberValidationError && !permitForm.get('permitNumber')?.touched\"\n              class=\"text-danger mt-1 small\">\n              {{ permitNumberValidationError }}\n            </div>\n          </div>\n          <!-- <div  class=\"col-xl-4\">\n            <ng-container *ngIf=\"isHideInternalReviewStatus\">\n\n            <label class=\"fw-bold form-label mb-2\">Internal Review Status <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"internalStatusArray\" [clearable]=\"false\" [multiple]=\"false\"\n              formControlName=\"internalReviewStatus\" placeholder=\"Select Status\">\n            </ng-select>\n            <div\n              *ngIf=\"permitForm.get('internalReviewStatus')?.touched && permitForm.get('internalReviewStatus')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('internalReviewStatus')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n            </ng-container>\n\n          </div> -->\n          <div class=\"col-xl-6\">\n            <label class=\"fw-bold form-label mb-2\">Permit Category <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"categories\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitCategory\"\n              placeholder=\"Select Category\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitCategory')?.touched && permitForm.get('permitCategory')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitCategory')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n\n        </div>\n                <div class=\"row mt-3\">\n          <div class=\"col-xl-12\">\n            <div class=\"text-muted small d-flex align-items-center\" style=\"white-space: normal;\">\n              <i class=\"fas fa-info-circle me-2\"></i>\n              <span>For External Review, permit details can be retrieved from the Municipality City website when Municipality is chosen.</span>\n            </div>\n          </div>\n        </div>\n         <div class=\"row mt-4\">\n           \n         </div>\n         <div  class=\"row mt-4\">\n            \n         </div>\n         <div *ngIf=\"permitForm.get('permitReviewType')?.value === 'External' && !getSyncButtonDisableStatus() && id === 0\" class=\"row mt-3\">\n           <div class=\"col-xl-12 d-flex align-items-center justify-content-end\">\n             <div class=\"small me-3\" style=\"white-space: normal;\">\n               The syncing of permit details may take up to 3 minutes.\n             </div>\n             <div>\n               <button type=\"button\" class=\"btn btn-primary btn-sm\"\n                       (click)=\"syncPermitDetails()\">\n                 <i class=\"fas fa-sync-alt\"></i> Sync\n               </button>\n             </div>\n           </div>\n         </div>\n      </ng-container>\n      <ng-container *ngIf=\"selectedTab == 'details'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Review Responsible Party<span class=\"text-danger\">*</span></label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"reviewResponsibleParty\" />\n            <div\n              *ngIf=\"permitForm.get('reviewResponsibleParty')?.touched && permitForm.get('reviewResponsibleParty')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('reviewResponsibleParty')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Primary Contact Name</label>\n            <input type=\"text\" class=\"form-control form-control-sm\" formControlName=\"primaryContact\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Status <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"statuses\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitStatus\"\n              placeholder=\"Select Status\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitStatus')?.touched && permitForm.get('permitStatus')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitStatus')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Permit Type <span class=\"text-danger\">*</span></label>\n            <ng-select [items]=\"permitTypes\" [clearable]=\"false\" [multiple]=\"false\" formControlName=\"permitType\"\n              placeholder=\"Select Type\">\n            </ng-select>\n            <div *ngIf=\"permitForm.get('permitType')?.touched && permitForm.get('permitType')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitType')?.errors?.['required']\">\n                Required Field\n              </div>\n            </div>\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Issue Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitIssueDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Applied Date <span class=\"text-danger\">*</span></label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitAppliedDate\" />\n            <div *ngIf=\"permitForm.get('permitAppliedDate')?.touched && permitForm.get('permitAppliedDate')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('permitAppliedDate')?.errors?.['required']\">\n                 Required Field\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n\n\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Expiration Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitExpirationDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Completed Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitCompleteDate\" />\n          </div>\n          <div class=\"col-xl-4\">\n            <label class=\"fw-bold form-label mb-2\">Final Date</label>\n            <input type=\"date\" class=\"form-control form-control-sm\" formControlName=\"permitFinalDate\" />\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Description</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"description\"></textarea>\n          </div>\n        </div>\n      </ng-container>\n      <!-- <ng-container *ngIf=\"selectedTab == 'role'\">\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Permit City Review Link<span *ngIf=\"isPermitMunicipalRequired\"\n                class=\"text-danger\">*</span></label>\n            <input type=\"url\" class=\"form-control form-control-sm\" formControlName=\"cityReviewLink\" />\n            <div *ngIf=\"permitForm.get('cityReviewLink')?.touched && permitForm.get('cityReviewLink')?.invalid\"\n              class=\"text-danger mt-1 small\">\n              <div *ngIf=\"permitForm.get('cityReviewLink')?.errors?.['required']\">\n                Permit City Review Link is required\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n\n        </div>\n\n\n      </ng-container> -->\n      <!-- <ng-container *ngIf=\"selectedTab == 'notes'\">\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Attention Reason</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"attentionReason\"></textarea>\n          </div>\n        </div>\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Internal Notes</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"internalNotes\"></textarea>\n          </div>\n        </div>\n\n        <div class=\"row mt-4\">\n          <div class=\"col-xl-12\">\n            <label class=\"fw-bold form-label mb-2\">Action Taken</label>\n            <textarea class=\"form-control form-control-sm\" rows=\"3\" formControlName=\"actionTaken\"></textarea>\n          </div>\n        </div>\n      </ng-container> -->\n    </form>\n  </div>\n\n  <div class=\"modal-footer justify-content-between\">\n    <div>\n      <button *ngIf=\"selectedTab == 'details'\" type=\"button\" class=\"btn btn-secondary btn-sm btn-elevate\"\n        (click)=\"goToPreviousTab()\">\n        Previous\n      </button>\n    </div>\n    <div>\n      <button type=\"button\" class=\"btn btn-danger btn-sm btn-elevate mr-2\" (click)=\"modal.dismiss()\">\n        Cancel</button>&nbsp;\n      <!-- *ngIf=\"selectedTab == 'notes'\"  -->\n      <button type=\"button\" class=\"btn btn-primary btn-sm\" *ngIf=\"selectedTab == 'details'\"\n        [disabled]=\"permitForm.invalid || isPermitNumberValidating\" (click)=\"save()\">\n        Save\n      </button>\n      <button *ngIf=\"selectedTab == 'basic'\" type=\"button\" class=\"btn btn-primary btn-sm\" (click)=\"goToNextTab()\">\n        Next\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAAA,SAIEA,YAAY,QAGP,eAAe;AACtB,SAAiCC,UAAU,QAAyB,gBAAgB;AAQpF,SAAuBC,oBAAoB,EAAaC,UAAU,EAAEC,GAAG,QAAQ,gBAAgB;AAC/F,SAASC,EAAE,QAAQ,MAAM;;;;;;;;;;;;;;;;ICbjBC,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACtCH,EAAA,CAAAC,cAAA,UAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAtCH,EAAA,CAAAI,SAAA,EAAgC;IAAhCJ,EAAA,CAAAK,kBAAA,mBAAAC,MAAA,CAAAC,YAAA,KAAgC;;;;;IAiDhDP,EAAA,CAAAC,cAAA,UAA+D;IAC7DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAC,yDAAA,iBAA+D;IAGjET,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAuD;IAAvDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAuD;;;;;IAa7Dd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAO,0DAAA,iBAAgE;IAGlEf,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAY9Dd,EAAA,CAAAC,cAAA,UAA8D;IAC3DD,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAQ,0DAAA,iBAA8D;IAGhEhB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAsD;IAAtDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAsD;;;;;IAexDd,EAFJ,CAAAC,cAAA,cAAqG,cAC1B,eACzC;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAE/CF,EAF+C,CAAAG,YAAA,EAAO,EAC9C,EACF;;;;;IAKNH,EAAA,CAAAC,cAAA,UAAkE;IAChED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IACNH,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAW,2BAAA,MACF;;;;;IAPFjB,EAAA,CAAAC,cAAA,cACiC;IAI/BD,EAHA,CAAAQ,UAAA,IAAAU,0DAAA,iBAAkE,IAAAC,0DAAA,iBAGU;IAG9EnB,EAAA,CAAAG,YAAA,EAAM;;;;;;IANEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;IAG1Dd,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAU,OAAA,GAAAd,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAO,OAAA,CAAAN,MAAA,kBAAAM,OAAA,CAAAN,MAAA,uBAAoE;;;;;IAK5Ed,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAW,2BAAA,MACF;;;;;IA0BEjB,EAAA,CAAAC,cAAA,UAAoE;IACjED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAa,0DAAA,iBAAoE;IAGtErB,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA4D;IAA5DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA4D;;;;;;IAuBnEd,EAFJ,CAAAC,cAAA,cAAoI,cAC7D,cACd;IACnDD,EAAA,CAAAE,MAAA,gEACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,UAAK,iBAEmC;IAA9BD,EAAA,CAAAsB,UAAA,mBAAAC,6EAAA;MAAAvB,EAAA,CAAAwB,aAAA,CAAAC,GAAA;MAAA,MAAAnB,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASrB,MAAA,CAAAsB,iBAAA,EAAmB;IAAA,EAAC;IACnC5B,EAAA,CAAA6B,SAAA,YAA+B;IAAC7B,EAAA,CAAAE,MAAA,aAClC;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACF,EACF;;;;;;IA/HTH,EAAA,CAAA8B,uBAAA,GAA6C;IAGvC9B,EAFJ,CAAAC,cAAA,cAAsB,aACG,gBACkB;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAAC,cAAA,oBAEqC;IAAnCD,EAAA,CAAAsB,UAAA,oBAAAS,0EAAAC,MAAA;MAAAhC,EAAA,CAAAwB,aAAA,CAAAS,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAAUrB,MAAA,CAAA4B,eAAA,CAAAF,MAAA,CAAuB;IAAA,EAAC;IACpChC,EAAA,CAAAG,YAAA,EAAY;IACZH,EAAA,CAAAQ,UAAA,IAAA2B,mDAAA,kBACiC;IAMrCnC,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,cAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC3GH,EAAA,CAAA6B,SAAA,iBAAuF;IACvF7B,EAAA,CAAAQ,UAAA,KAAA4B,oDAAA,kBACiC;IAMrCpC,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFI,CAAAC,cAAA,eAAsB,cACL,iBACkB;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzFH,EAAA,CAAA6B,SAAA,iBAAqF;IACrF7B,EAAA,CAAAQ,UAAA,KAAA6B,oDAAA,kBACiC;IAMrCrC,EADE,CAAAG,YAAA,EAAM,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAExFH,EADF,CAAAC,cAAA,eAA+B,iBAGoF;IAD1GD,EAAA,CAAAsB,UAAA,kBAAAgB,qEAAA;MAAAtC,EAAA,CAAAwB,aAAA,CAAAS,GAAA;MAAA,MAAA3B,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAAQrB,MAAA,CAAAiC,6BAAA,EAA+B;IAAA,EAAC;IAD/CvC,EAAA,CAAAG,YAAA,EAEiH;IACjHH,EAAA,CAAAQ,UAAA,KAAAgC,oDAAA,kBAAqG;IAKvGxC,EAAA,CAAAG,YAAA,EAAM;IAYNH,EAVA,CAAAQ,UAAA,KAAAiC,oDAAA,kBACiC,KAAAC,oDAAA,kBAUA;IAGnC1C,EAAA,CAAAG,YAAA,EAAM;IAmBJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACjGH,EAAA,CAAA6B,SAAA,qBAEY;IACZ7B,EAAA,CAAAQ,UAAA,KAAAmC,oDAAA,kBACiC;IAOrC3C,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFI,CAAAC,cAAA,eAAsB,cACL,eACgE;IACnFD,EAAA,CAAA6B,SAAA,aAAuC;IACvC7B,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,4HAAoH;IAGhIF,EAHgI,CAAAG,YAAA,EAAO,EAC7H,EACF,EACF;IAILH,EAHA,CAAA6B,SAAA,eAEM,eAGA;IACN7B,EAAA,CAAAQ,UAAA,KAAAoC,oDAAA,kBAAoI;;;;;;;;;;;;;IA/GtH5C,EAAA,CAAAI,SAAA,GAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAAuC,QAAA,CAAkB,oBAAoB,mBAAmB;IAI9D7C,EAAA,CAAAI,SAAA,EAAkF;IAAlFJ,EAAA,CAAAU,UAAA,WAAAoC,OAAA,GAAAxC,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAiC,OAAA,CAAAC,OAAA,OAAAD,OAAA,GAAAxC,MAAA,CAAAM,UAAA,CAAAC,GAAA,gCAAAiC,OAAA,CAAAE,OAAA,EAAkF;IAalFhD,EAAA,CAAAI,SAAA,GAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAAuC,OAAA,GAAA3C,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAoC,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA3C,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAoC,OAAA,CAAAD,OAAA,EAAoF;IAYpFhD,EAAA,CAAAI,SAAA,GAAgF;IAAhFJ,EAAA,CAAAU,UAAA,WAAAwC,OAAA,GAAA5C,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAqC,OAAA,CAAAH,OAAA,OAAAG,OAAA,GAAA5C,MAAA,CAAAM,UAAA,CAAAC,GAAA,+BAAAqC,OAAA,CAAAF,OAAA,EAAgF;IAc7EhD,EAAA,CAAAI,SAAA,GAAuG;IAAvGJ,EAAA,CAAAmD,WAAA,iBAAAC,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAJ,OAAA,OAAAI,OAAA,GAAA9C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAuC,OAAA,CAAAL,OAAA,EAAuG;IACxG/C,EAAA,CAAAI,SAAA,EAA8B;IAA9BJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAA+C,wBAAA,CAA8B;IAOhCrD,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAA4C,OAAA,GAAAhD,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAyC,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAhD,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAyC,OAAA,CAAAN,OAAA,EAAwF;IAUxFhD,EAAA,CAAAI,SAAA,EAA6E;IAA7EJ,EAAA,CAAAU,UAAA,SAAAJ,MAAA,CAAAW,2BAAA,OAAAsC,QAAA,GAAAjD,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAA0C,QAAA,CAAAR,OAAA,EAA6E;IAwBxE/C,EAAA,CAAAI,SAAA,GAAoB;IAAqBJ,EAAzC,CAAAU,UAAA,UAAAJ,MAAA,CAAAkD,UAAA,CAAoB,oBAAoB,mBAAmB;IAGhExD,EAAA,CAAAI,SAAA,EAA4F;IAA5FJ,EAAA,CAAAU,UAAA,WAAA+C,QAAA,GAAAnD,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA4C,QAAA,CAAAV,OAAA,OAAAU,QAAA,GAAAnD,MAAA,CAAAM,UAAA,CAAAC,GAAA,qCAAA4C,QAAA,CAAAT,OAAA,EAA4F;IAuB/FhD,EAAA,CAAAI,SAAA,GAA2G;IAA3GJ,EAAA,CAAAU,UAAA,WAAAgD,QAAA,GAAApD,MAAA,CAAAM,UAAA,CAAAC,GAAA,uCAAA6C,QAAA,CAAAC,KAAA,qBAAArD,MAAA,CAAAsD,0BAAA,MAAAtD,MAAA,CAAAuD,EAAA,OAA2G;;;;;IAsB5G7D,EAAA,CAAAC,cAAA,UAA4E;IAC1ED,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IALRH,EAAA,CAAAC,cAAA,cAEiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAsD,yDAAA,iBAA4E;IAG9E9D,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAoE;IAApEJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAoE;;;;;IAgB1Ed,EAAA,CAAAC,cAAA,UAAkE;IAC/DD,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAuD,0DAAA,iBAAkE;IAGpE/D,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA0D;;;;;IAehEd,EAAA,CAAAC,cAAA,UAAgE;IAC9DD,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAwD,0DAAA,iBAAgE;IAGlEhE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAAwD;IAAxDJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAAwD;;;;;IAc9Dd,EAAA,CAAAC,cAAA,UAAuE;IACpED,EAAA,CAAAE,MAAA,uBACH;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;;IAJRH,EAAA,CAAAC,cAAA,cACiC;IAC/BD,EAAA,CAAAQ,UAAA,IAAAyD,0DAAA,iBAAuE;IAGzEjE,EAAA,CAAAG,YAAA,EAAM;;;;;IAHEH,EAAA,CAAAI,SAAA,EAA+D;IAA/DJ,EAAA,CAAAU,UAAA,UAAAC,OAAA,GAAAL,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAAF,OAAA,CAAAG,MAAA,kBAAAH,OAAA,CAAAG,MAAA,aAA+D;;;;;IArD7Ed,EAAA,CAAA8B,uBAAA,GAA+C;IAGzC9B,EAFJ,CAAAC,cAAA,cAAsB,cACE,gBACmB;IAAAD,EAAA,CAAAE,MAAA,+BAAwB;IAAAF,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IACzGH,EAAA,CAAA6B,SAAA,gBAAmG;IACnG7B,EAAA,CAAAQ,UAAA,IAAA0D,mDAAA,kBAEiC;IAKnClE,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACnEH,EAAA,CAAA6B,SAAA,iBAA2F;IAC7F7B,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC/FH,EAAA,CAAA6B,SAAA,qBAEY;IACZ7B,EAAA,CAAAQ,UAAA,KAAA2D,oDAAA,kBACiC;IAOrCnE,EAFE,CAAAG,YAAA,EAAM,EAEF;IAGFH,EAFJ,CAAAC,cAAA,eAAsB,eACE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC7FH,EAAA,CAAA6B,SAAA,qBAEY;IACZ7B,EAAA,CAAAQ,UAAA,KAAA4D,oDAAA,kBACiC;IAKnCpE,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAA6B,SAAA,iBAA4F;IAC9F7B,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAC,cAAA,gBAA0B;IAAAD,EAAA,CAAAE,MAAA,SAAC;IAAOF,EAAP,CAAAG,YAAA,EAAO,EAAQ;IAC9FH,EAAA,CAAA6B,SAAA,iBAA8F;IAC9F7B,EAAA,CAAAQ,UAAA,KAAA6D,oDAAA,kBACiC;IAMrCrE,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAJJ,CAAAC,cAAA,eAAsB,eAGE,iBACmB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC9DH,EAAA,CAAA6B,SAAA,iBAAiG;IACnG7B,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC7DH,EAAA,CAAA6B,SAAA,iBAA+F;IACjG7B,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAAsB,iBACmB;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IACzDH,EAAA,CAAA6B,SAAA,iBAA4F;IAEhG7B,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAAsB,cACG,iBACkB;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAQ;IAC1DH,EAAA,CAAA6B,SAAA,oBAAiG;IAErG7B,EADE,CAAAG,YAAA,EAAM,EACF;;;;;;;;;IA3ECH,EAAA,CAAAI,SAAA,GAA4G;IAA5GJ,EAAA,CAAAU,UAAA,WAAA4D,OAAA,GAAAhE,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAyD,OAAA,CAAAvB,OAAA,OAAAuB,OAAA,GAAAhE,MAAA,CAAAM,UAAA,CAAAC,GAAA,6CAAAyD,OAAA,CAAAtB,OAAA,EAA4G;IAapGhD,EAAA,CAAAI,SAAA,IAAkB;IAAqBJ,EAAvC,CAAAU,UAAA,UAAAJ,MAAA,CAAAiE,QAAA,CAAkB,oBAAoB,mBAAmB;IAG9DvE,EAAA,CAAAI,SAAA,EAAwF;IAAxFJ,EAAA,CAAAU,UAAA,WAAAuC,OAAA,GAAA3C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAoC,OAAA,CAAAF,OAAA,OAAAE,OAAA,GAAA3C,MAAA,CAAAM,UAAA,CAAAC,GAAA,mCAAAoC,OAAA,CAAAD,OAAA,EAAwF;IAYnFhD,EAAA,CAAAI,SAAA,GAAqB;IAAqBJ,EAA1C,CAAAU,UAAA,UAAAJ,MAAA,CAAAkE,WAAA,CAAqB,oBAAoB,mBAAmB;IAGjExE,EAAA,CAAAI,SAAA,EAAoF;IAApFJ,EAAA,CAAAU,UAAA,WAAA4C,OAAA,GAAAhD,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAyC,OAAA,CAAAP,OAAA,OAAAO,OAAA,GAAAhD,MAAA,CAAAM,UAAA,CAAAC,GAAA,iCAAAyC,OAAA,CAAAN,OAAA,EAAoF;IAcpFhD,EAAA,CAAAI,SAAA,IAAkG;IAAlGJ,EAAA,CAAAU,UAAA,WAAA6C,QAAA,GAAAjD,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAA0C,QAAA,CAAAR,OAAA,OAAAQ,QAAA,GAAAjD,MAAA,CAAAM,UAAA,CAAAC,GAAA,wCAAA0C,QAAA,CAAAP,OAAA,EAAkG;;;;;;IAgF9GhD,EAAA,CAAAC,cAAA,iBAC8B;IAA5BD,EAAA,CAAAsB,UAAA,mBAAAmD,gEAAA;MAAAzE,EAAA,CAAAwB,aAAA,CAAAkD,GAAA;MAAA,MAAApE,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASrB,MAAA,CAAAqE,eAAA,EAAiB;IAAA,EAAC;IAC3B3E,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;;IAMTH,EAAA,CAAAC,cAAA,iBAC+E;IAAjBD,EAAA,CAAAsB,UAAA,mBAAAsD,gEAAA;MAAA5E,EAAA,CAAAwB,aAAA,CAAAqD,GAAA;MAAA,MAAAvE,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASrB,MAAA,CAAAwE,IAAA,EAAM;IAAA,EAAC;IAC5E9E,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;IAFPH,EAAA,CAAAU,UAAA,aAAAJ,MAAA,CAAAM,UAAA,CAAAoC,OAAA,IAAA1C,MAAA,CAAA+C,wBAAA,CAA2D;;;;;;IAG7DrD,EAAA,CAAAC,cAAA,iBAA4G;IAAxBD,EAAA,CAAAsB,UAAA,mBAAAyD,gEAAA;MAAA/E,EAAA,CAAAwB,aAAA,CAAAwD,GAAA;MAAA,MAAA1E,MAAA,GAAAN,EAAA,CAAA0B,aAAA;MAAA,OAAA1B,EAAA,CAAA2B,WAAA,CAASrB,MAAA,CAAA2E,WAAA,EAAa;IAAA,EAAC;IACzGjF,EAAA,CAAAE,MAAA,aACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;ADxSf,OAAM,MAAO+E,oBAAoB;EAoFtBC,KAAA;EACCC,EAAA;EACAC,eAAA;EACAC,cAAA;EACAC,UAAA;EACAC,eAAA;EACAC,wBAAA;EACAC,GAAA;EA1FD7B,EAAE,GAAW,CAAC,CAAC,CAAC;EAChB8B,0BAA0B,GAAY,IAAI,CAAC,CAAC;EAC5CC,MAAM,CAAM,CAAC;EACZC,SAAS,GAAsB,IAAInG,YAAY,EAAE;EAE3DkB,UAAU;EACViC,QAAQ,GAAU,EAAE;EACpBiD,eAAe,GAAU,CAAC,UAAU,EAAE,UAAU,EAAE,MAAM,CAAC;EACzDC,SAAS,GAAQ,EAAE;EACnBC,SAAS,GAAY,KAAK;EAC1BC,aAAa,GAAQ,EAAE;EACvBC,WAAW,GAAQ,OAAO;EAC1B;EACA1B,WAAW,GAAG,CACZ,oCAAoC,EACpC,uBAAuB,EACvB,wBAAwB,EACxB,uBAAuB,EACvB,qCAAqC,EACrC,oCAAoC,EACpC,0CAA0C,EAC1C,2BAA2B,EAC3B,uCAAuC,EACvC,6CAA6C,EAC7C,yBAAyB,EACzB,0CAA0C,EAC1C,oCAAoC,EACpC,4DAA4D,EAC5D,oBAAoB,EACpB,mBAAmB,EACnB,wCAAwC,EACxC,8BAA8B,EAC9B,6BAA6B,EAC7B,iCAAiC,EACjC,yBAAyB,EACzB,qBAAqB,EACrB,wBAAwB,EACxB,0BAA0B,EAC1B,qCAAqC,EACrC,yBAAyB,EACzB,4CAA4C,EAC5C,0BAA0B,EAC1B,oCAAoC,EACpC,iBAAiB,EACjB,cAAc,EACd,2BAA2B,EAC3B,kCAAkC,EAClC,uCAAuC,EACvC,+BAA+B,EAC/B,sBAAsB,EACtB,gCAAgC,EAChC,iCAAiC,EACjC,gCAAgC,CACjC;EACDhB,UAAU,GAAG,CACX,SAAS,EACT,YAAY,EACZ,YAAY,EACZ,WAAW,EACX,eAAe,CAChB;EACDe,QAAQ,GAAG,CACT,UAAU,EACV,UAAU,EACV,SAAS,EACT,UAAU,EACV,WAAW,EACX,QAAQ,EACR,SAAS,EACT,mBAAmB,EACnB,iCAAiC,EACjC,oBAAoB,EACpB,MAAM,CACP;EACD4B,mBAAmB,GAAE,CAAC,UAAU,EAAC,uBAAuB,EAAC,cAAc,EAAC,SAAS,EAAC,cAAc,EAAC,WAAW,EAAC,mBAAmB,CAAC;EACjI5F,YAAY;EACZ6F,yBAAyB,GAAY,KAAK;EAC1CC,iCAAiC,GAAU,KAAK;EAChDC,cAAc,GAAM,EAAE;EACtBC,gBAAgB,GAAM,EAAE;EACxBtF,2BAA2B,GAAW,EAAE;EACxCoC,wBAAwB,GAAY,KAAK;EACzCmD,YACSrB,KAAqB,EACpBC,EAAe,EACfC,eAAgC,EAChCC,cAA8B,EAC9BC,UAAsB,EACtBC,eAAiC,EACjCC,wBAAkD,EAClDC,GAAsB;IAPvB,KAAAP,KAAK,GAALA,KAAK;IACJ,KAAAC,EAAE,GAAFA,EAAE;IACF,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,UAAU,GAAVA,UAAU;IACV,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,wBAAwB,GAAxBA,wBAAwB;IACxB,KAAAC,GAAG,GAAHA,GAAG;IAEX;IACA,IAAI,CAACF,eAAe,CAACiB,cAAc,CAACC,SAAS,CAAEC,OAAO,IAAI;MACxD,IAAI,CAACX,SAAS,GAAGW,OAAO;IAC1B,CAAC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACb,SAAS,GAAG,IAAI,CAACR,UAAU,CAACsB,eAAe,EAAE;IAClD,IAAI,CAACC,kBAAkB,EAAE;IACzB,IAAI,CAACC,QAAQ,EAAE;IACf,IAAI,CAACC,YAAY,EAAE;IACnB,IAAI,CAACC,2BAA2B,EAAE;IAClC,IAAI,IAAI,CAACpD,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAACqD,SAAS,EAAE;IAClB;EACF;EAEAH,QAAQA,CAAA;IACN,IAAI,CAACnG,UAAU,GAAG,IAAI,CAACwE,EAAE,CAAC+B,KAAK,CAAC;MAC9BC,SAAS,EAAE,CAAC,EAAE,EAAEzH,UAAU,CAAC0H,QAAQ,CAAC;MACpCC,UAAU,EAAE,CAAC,EAAE,EAAE3H,UAAU,CAAC0H,QAAQ,CAAC;MACrC9G,YAAY,EAAE,CAAC,EAAE,EAAEZ,UAAU,CAAC0H,QAAQ,CAAC;MACvCE,cAAc,EAAE,CAAC,EAAE,EAAE5H,UAAU,CAAC0H,QAAQ,CAAC;MACzCG,UAAU,EAAE,CAAC,EAAE,EAAE7H,UAAU,CAAC0H,QAAQ,CAAC;MACrCI,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,gBAAgB,EAAE,CAAC,EAAE,EAAE/H,UAAU,CAAC0H,QAAQ,CAAC;MAC3CM,QAAQ,EAAE,CAAC,EAAE,EAAChI,UAAU,CAAC0H,QAAQ,CAAC;MAClCO,cAAc,EAAE,CAAC,EAAE,CAAC;MACpBC,iBAAiB,EAAE,CAAC,EAAE,EAAElI,UAAU,CAAC0H,QAAQ,CAAC;MAC5CS,oBAAoB,EAAE,CAAC,EAAE,CAAC;MAC1BC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,kBAAkB,EAAE,CAAC,EAAE,CAAC;MACxBC,YAAY,EAAE,CAAC,EAAE,EAAEvI,UAAU,CAAC0H,QAAQ,CAAC;MACvCc,oBAAoB,EAAE,CAAC,EAAE,EAAExI,UAAU,CAAC0H,QAAQ,CAAC;MAC/Ce,eAAe,EAAE,CAAC,EAAE,CAAC;MACrBC,aAAa,EAAE,CAAC,EAAE,CAAC;MACnBC,WAAW,EAAE,CAAC,EAAE,CAAC;MACjBC,sBAAsB,EAAE,CAAC,EAAE,EAAE5I,UAAU,CAAC0H,QAAQ,CAAC;MACjDmB,kBAAkB,EAAE,CAAC,EAAE;MACvB;KACD,CAAC;EACJ;EAEAvB,2BAA2BA,CAAA;IACzB,MAAMwB,mBAAmB,GAAG,IAAI,CAAC7H,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC;IAC/D,IAAI4H,mBAAmB,EAAE;MACvB;MACAA,mBAAmB,CAACC,YAAY,CAC7BC,IAAI,CAAC/I,oBAAoB,EAAE,CAAC,CAC5B8G,SAAS,CAAC,MAAK;QACd,IAAI,CAACzF,2BAA2B,GAAG,EAAE;QACrC,IAAIwH,mBAAmB,CAACG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;UACtD,MAAM9H,MAAM,GAAQ;YAAE,GAAG2H,mBAAmB,CAAC3H;UAAM,CAAE;UACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;UACnC2H,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAACjI,MAAM,CAAC,CAACkI,MAAM,GAAG,CAAC,GAAGlI,MAAM,GAAG,IAAI,CAAC;QAC/E;MACF,CAAC,CAAC;IACN;EACF;EAEAyB,6BAA6BA,CAAA;IAC3B,MAAMkG,mBAAmB,GAAG,IAAI,CAAC7H,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC;IAC/D,MAAMoI,gBAAgB,GAAG,IAAI,CAACrI,UAAU,CAACC,GAAG,CAAC,WAAW,CAAC;IACzD,MAAMN,YAAY,GAAGkI,mBAAmB,EAAE9E,KAAK;IAC/C,MAAMyD,SAAS,GAAG6B,gBAAgB,EAAEtF,KAAK;IAEzC,MAAMuF,iBAAiB,GAAG,CAAC3I,YAAY,IAAI,EAAE,EAAE4I,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE;IAC9E,MAAMC,kBAAkB,GAAG,CAAC,IAAI,CAAC/I,YAAY,IAAI,EAAE,EAAE4I,QAAQ,EAAE,CAACC,IAAI,EAAE,CAACC,WAAW,EAAE;IAEpF;IACA,IAAI,CAACH,iBAAiB,IAAK,IAAI,CAACrF,EAAE,KAAK,CAAC,IAAIqF,iBAAiB,KAAKI,kBAAmB,EAAE;MACrF,IAAI,CAACjG,wBAAwB,GAAG,KAAK;MACrC,IAAI,CAACpC,2BAA2B,GAAG,EAAE;MACrC,IAAIwH,mBAAmB,EAAEG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;QACvD,MAAM9H,MAAM,GAAQ;UAAE,GAAG2H,mBAAmB,CAAC3H;QAAM,CAAE;QACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;QACnC2H,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAACjI,MAAM,CAAC,CAACkI,MAAM,GAAG,CAAC,GAAGlI,MAAM,GAAG,IAAI,CAAC;MAC/E;MACA;IACF;IAEA,IAAIoI,iBAAiB,IAAI9B,SAAS,EAAE;MAClC,IAAI,CAAC/D,wBAAwB,GAAG,IAAI;MACpC,IAAI,CAACkG,oBAAoB,CAAChJ,YAAY,EAAE6G,SAAS,CAAC,CAC/CuB,IAAI,CAAC7I,GAAG,CAAE0J,GAAQ,IAAKA,GAAG,EAAEC,YAAY,IAAID,GAAG,CAAC,CAAC,CACjD9C,SAAS,CAAEgD,MAAW,IAAI;QACzB,IAAI,CAACrG,wBAAwB,GAAG,KAAK;QACrC,IAAIqG,MAAM,IAAIA,MAAM,CAACC,MAAM,EAAE;UAC3B,IAAI,CAAC1I,2BAA2B,GAAGyI,MAAM,CAACE,OAAO;UACjDnB,mBAAmB,EAAEI,SAAS,CAAC;YAAE,oBAAoB,EAAE;UAAI,CAAE,CAAC;QAChE,CAAC,MAAM;UACL,IAAI,CAAC5H,2BAA2B,GAAG,EAAE;UACrC,IAAIwH,mBAAmB,EAAEG,QAAQ,CAAC,oBAAoB,CAAC,EAAE;YACvD,MAAM9H,MAAM,GAAQ;cAAE,GAAG2H,mBAAmB,CAAC3H;YAAM,CAAE;YACrD,OAAOA,MAAM,CAAC,oBAAoB,CAAC;YACnC2H,mBAAmB,CAACI,SAAS,CAACC,MAAM,CAACC,IAAI,CAACjI,MAAM,CAAC,CAACkI,MAAM,GAAG,CAAC,GAAGlI,MAAM,GAAG,IAAI,CAAC;UAC/E;QACF;MACF,CAAC,CAAC;IACN;EACF;EAEAyI,oBAAoBA,CAAChJ,YAAoB,EAAE6G,SAAiB;IAC1D,IAAI,CAAC/D,wBAAwB,GAAG,IAAI;IACpC,IAAI,CAACpC,2BAA2B,GAAG,EAAE;IAErC,MAAM4I,MAAM,GAAG;MACbtJ,YAAY,EAAEA,YAAY;MAC1B6G,SAAS,EAAEA,SAAS;MACpB0C,QAAQ,EAAE,IAAI,CAACjG,EAAE,KAAK,CAAC,GAAG,IAAI,CAACA,EAAE,GAAG,IAAI,CAAC;KAC1C;IAED,OAAO,IAAI,CAACyB,cAAc,CAACiE,oBAAoB,CAACM,MAAM,CAAC,CAAClB,IAAI,CAC1D9I,UAAU,CAAC,MAAK;MACd,IAAI,CAACwD,wBAAwB,GAAG,KAAK;MACrC,OAAOtD,EAAE,CAAC;QAAE4J,MAAM,EAAE,KAAK;QAAEC,OAAO,EAAE;MAAE,CAAE,CAAC;IAC3C,CAAC,CAAC,CACH;EACH;EAEA5C,YAAYA,CAAA;IACV,IAAI,CAACxB,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMF,MAAM,GAAG;MAAEG,QAAQ,EAAE;IAAK,CAAE;IAClC,IAAI,CAAC3E,eAAe,CAAC4E,kBAAkB,CAACJ,MAAM,CAAC,CAACnD,SAAS,CAAC;MACxDqD,IAAI,EAAGG,QAAa,IAAI;QACtB,IAAI,CAAC1E,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIG,QAAQ,IAAIA,QAAQ,CAACT,YAAY,EAAE;UACrC,IAAI,CAAC5G,QAAQ,GAAGqH,QAAQ,CAACT,YAAY,CAACU,IAAI;QAC5C;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC5E,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACvH,QAAQ,GAAG,EAAE;MACpB;KACD,CAAC;EACJ;EAEAiE,kBAAkBA,CAAA;IAChB,IAAI,CAACtB,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,MAAMF,MAAM,GAAG;MAAES,YAAY,EAAE,IAAI,CAACvE,SAAS,CAACwE;IAAM,CAAE;IACtD,IAAI,CAACjF,cAAc,CAACkF,oBAAoB,CAACX,MAAM,CAAC,CAACnD,SAAS,CAAC;MACzDqD,IAAI,EAAGG,QAAa,IAAI;QACtB,IAAI,CAAC1E,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAIG,QAAQ,IAAIA,QAAQ,CAACT,YAAY,EAAE;UACrC,IAAI,CAACxD,aAAa,GAAGiE,QAAQ,CAACT,YAAY,CAACU,IAAI;QACjD;MACF,CAAC;MACDC,KAAK,EAAGA,KAAU,IAAI;QACpB,IAAI,CAAC5E,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,IAAI,CAACnE,aAAa,GAAG,EAAE;MACzB;KACD,CAAC;EACJ;EAEAiB,SAASA,CAAA;IACP,IAAI,CAAC1B,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzE,cAAc,CAChBmF,SAAS,CAAC;MAAEX,QAAQ,EAAE,IAAI,CAACjG,EAAE;MAAE6G,cAAc,EAAE,IAAI,CAAC3E,SAAS,CAACwE;IAAM,CAAE,CAAC,CACvE7D,SAAS,CAAC;MACTqD,IAAI,EAAGY,cAAmB,IAAI;QAC5B,IAAI,CAACnF,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACY,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAAClB,YAAY,CAACU,IAAI;UACjD,IAAI,CAAC5J,YAAY,GAAGsK,UAAU,CAACtK,YAAY;UAC3C,IAAI,CAACK,UAAU,CAACkK,UAAU,CAAC;YACzB1D,SAAS,EAAEyD,UAAU,CAACzD,SAAS;YAC/B7G,YAAY,EAAEsK,UAAU,CAACtK,YAAY;YACrCmH,gBAAgB,EAAEmD,UAAU,CAACnD,gBAAgB;YAC7CH,cAAc,EAAEsD,UAAU,CAACtD,cAAc;YACzCC,UAAU,EAAEqD,UAAU,CAACrD,UAAU;YACjCC,WAAW,EAAEoD,UAAU,CAACpD,WAAW;YACnCH,UAAU,EAAEuD,UAAU,CAACvD,UAAU;YACjCK,QAAQ,EAAEkD,UAAU,CAAClD,QAAQ;YAC7BQ,oBAAoB,EAAC0C,UAAU,CAAC1C,oBAAoB;YACpDP,cAAc,EAAEiD,UAAU,CAACjD,cAAc;YACzCC,iBAAiB,EAAEgD,UAAU,CAAChD,iBAAiB,GAC3C,IAAI,CAACkD,kBAAkB,CAACF,UAAU,CAAChD,iBAAiB,CAAC,GACrD,EAAE;YACNC,oBAAoB,EAAE+C,UAAU,CAAC/C,oBAAoB,GACjD,IAAI,CAACiD,kBAAkB,CAACF,UAAU,CAAC/C,oBAAoB,CAAC,GACxD,EAAE;YACNC,eAAe,EAAE8C,UAAU,CAAC9C,eAAe,GACvC,IAAI,CAACgD,kBAAkB,CAACF,UAAU,CAAC9C,eAAe,CAAC,GACnD,EAAE;YACFC,eAAe,EAAE6C,UAAU,CAAC7C,eAAe,GAC3C,IAAI,CAAC+C,kBAAkB,CAACF,UAAU,CAAC7C,eAAe,CAAC,GACnD,EAAE;YACNC,kBAAkB,EAAE4C,UAAU,CAAC5C,kBAAkB,GAC7C,IAAI,CAAC8C,kBAAkB,CAACF,UAAU,CAAC5C,kBAAkB,CAAC,GACtD,EAAE;YACNC,YAAY,EAAE2C,UAAU,CAAC3C,YAAY;YACrCE,eAAe,EAAEyC,UAAU,CAACzC,eAAe;YAC3CC,aAAa,EAAEwC,UAAU,CAACxC,aAAa;YACvCC,WAAW,EAAEuC,UAAU,CAACvC,WAAW;YACnCC,sBAAsB,EAAEsC,UAAU,CAACtC,sBAAsB;YACzDC,kBAAkB,EAAEqC,UAAU,CAACrC;YAC/B;WACD,CAAC;UACF,IAAI,CAACwC,wBAAwB,CAACH,UAAU,CAACnD,gBAAgB,CAAC;QAC5D,CAAC,MAAM;UACL2C,OAAO,CAACY,IAAI,CACV,oCAAoC,EACpCN,cAAc,CAAClB,YAAY,CAC5B;QACH;MACF,CAAC;MACDW,KAAK,EAAGc,GAAG,IAAI;QACb,IAAI,CAAC1F,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEc,GAAG,CAAC;MACvC;KACD,CAAC;EACN;EAEAH,kBAAkBA,CAACI,UAAkB;IACnC,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;IAE1B,MAAMC,IAAI,GAAG,IAAIC,IAAI,CAACF,UAAU,CAAC;IACjC;IACA,MAAMG,IAAI,GAAGF,IAAI,CAACG,WAAW,EAAE;IAC/B,MAAMC,KAAK,GAAGC,MAAM,CAACL,IAAI,CAACM,QAAQ,EAAE,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAC1D,MAAMC,GAAG,GAAGH,MAAM,CAACL,IAAI,CAACS,OAAO,EAAE,CAAC,CAACF,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC;IAEnD,OAAO,GAAGL,IAAI,IAAIE,KAAK,IAAII,GAAG,EAAE;EAClC;EAEAE,iBAAiBA,CAAA;IACf,MAAMC,QAAQ,GAAG,IAAI,CAACnL,UAAU,CAAC+C,KAAK;IAEtC,IAAIqI,iBAAiB,GAAQ,EAAE;IAC/BA,iBAAiB,CAAC5E,SAAS,GAAG2E,QAAQ,CAAC3E,SAAS;IAChD4E,iBAAiB,CAACzL,YAAY,GAAGwL,QAAQ,CAACxL,YAAY;IACtDyL,iBAAiB,CAACtE,gBAAgB,GAAGqE,QAAQ,CAACrE,gBAAgB;IAC9DsE,iBAAiB,CAACzE,cAAc,GAAGwE,QAAQ,CAACxE,cAAc;IAC1DyE,iBAAiB,CAACxE,UAAU,GAAGuE,QAAQ,CAACvE,UAAU;IAClDwE,iBAAiB,CAACvE,WAAW,GAAGsE,QAAQ,CAACtE,WAAW;IACpDuE,iBAAiB,CAACrE,QAAQ,GAAGoE,QAAQ,CAACpE,QAAQ;IAC9CqE,iBAAiB,CAACpE,cAAc,GAAGmE,QAAQ,CAACnE,cAAc;IAC1DoE,iBAAiB,CAACnE,iBAAiB,GAAGkE,QAAQ,CAAClE,iBAAiB,IAAI,IAAI;IACxEmE,iBAAiB,CAAClE,oBAAoB,GACpCiE,QAAQ,CAACjE,oBAAoB,IAAI,IAAI;IACvCkE,iBAAiB,CAACjE,eAAe,GAAGgE,QAAQ,CAAChE,eAAe,IAAI,IAAI;IACpEiE,iBAAiB,CAAChE,eAAe,GAC/B+D,QAAQ,CAAC/D,eAAe,IAAI,IAAI;IAClCgE,iBAAiB,CAAC/D,kBAAkB,GAAG8D,QAAQ,CAAC9D,kBAAkB,IAAI,IAAI;IAC1E+D,iBAAiB,CAAC9D,YAAY,GAAG6D,QAAQ,CAAC7D,YAAY;IACtD8D,iBAAiB,CAAC7D,oBAAoB,GAAG4D,QAAQ,CAAC5D,oBAAoB;IACtE6D,iBAAiB,CAAC1E,UAAU,GAAGyE,QAAQ,CAACzE,UAAU;IAClD0E,iBAAiB,CAAC5D,eAAe,GAAG2D,QAAQ,CAAC3D,eAAe;IAC5D4D,iBAAiB,CAAC3D,aAAa,GAAG0D,QAAQ,CAAC1D,aAAa;IACxD2D,iBAAiB,CAAC1D,WAAW,GAAGyD,QAAQ,CAACzD,WAAW;IACpD0D,iBAAiB,CAACzD,sBAAsB,GAAGwD,QAAQ,CAACxD,sBAAsB;IAC1EyD,iBAAiB,CAACxD,kBAAkB,GAAGuD,QAAQ,CAACvD,kBAAkB;IAClEwD,iBAAiB,CAAC1F,cAAc,GAAG,IAAI,CAACA,cAAc;IACtD0F,iBAAiB,CAACtB,cAAc,GAAG,IAAI,CAAC3E,SAAS,CAACwE,MAAM;IACxDyB,iBAAiB,CAACzF,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC1D,MAAM0F,MAAM,GAAI,IAAI,CAAC1F,gBAAgB,CAAC2F,QAAQ,IAAK,IAAI,CAAC3F,gBAAgB,CAACuD,QAAQ,IAAK,IAAI,CAACvD,gBAAgB,CAAC4F,EAAE,IAAI,IAAI;IACtHH,iBAAiB,CAACI,cAAc,GAAGH,MAAM;IACzC,IAAI,IAAI,CAACpI,EAAE,KAAK,CAAC,EAAE;MACjBmI,iBAAiB,CAAClC,QAAQ,GAAG,IAAI,CAACjG,EAAE;IACtC;IAEA,OAAOmI,iBAAiB;EAC1B;EAEAlH,IAAIA,CAAA;IACF,IAAIuH,QAAQ,GAAG,IAAI,CAACzL,UAAU,CAACyL,QAAQ;IACvChC,OAAO,CAACiC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC1L,UAAU,CAAC+C,KAAK,CAAC;IAClD,IAAI,IAAI,CAAC/C,UAAU,CAACoC,OAAO,EAAE;MAC3B8F,MAAM,CAACC,IAAI,CAACsD,QAAQ,CAAC,CAACE,OAAO,CAAEC,WAAW,IACxCH,QAAQ,CAACG,WAAW,CAAC,CAACC,aAAa,EAAE,CACtC;MACD,IAAI,CAAChH,wBAAwB,CAACiH,SAAS,CACrC,iCAAiC,EACjC,EAAE,CACH;MACD;IACF;IACA,IAAI7B,UAAU,GAAQ,IAAI,CAACiB,iBAAiB,EAAE;IAC9CzB,OAAO,CAACiC,GAAG,CAAC,cAAc,EAAEzB,UAAU,CAAC;IACvC,IAAI,IAAI,CAAChH,EAAE,KAAK,CAAC,EAAE;MACjB,IAAI,CAAC8I,MAAM,CAAC9B,UAAU,CAAC;IACzB,CAAC,MAAM;MACL,IAAI,CAAC+B,IAAI,CAAC/B,UAAU,CAAC;IACvB;EACF;EAEA8B,MAAMA,CAAC9B,UAAe;IACpB,IAAI,CAACrF,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzE,cAAc,CAACuH,YAAY,CAAChC,UAAU,CAAC,CAACnE,SAAS,CAAE8C,GAAQ,IAAI;MAClE,IAAI,CAAChE,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACP,GAAG,CAACoB,OAAO,EAAE;QAChB,IAAI,CAACnF,wBAAwB,CAACqH,WAAW,CAACtD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC/D,SAAS,CAACkH,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC5H,KAAK,CAAC6H,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACvH,wBAAwB,CAACiH,SAAS,CAAClD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC/D,SAAS,CAACkH,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EAEAH,IAAIA,CAAC/B,UAAe;IAClB,IAAI,CAACrF,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzE,cAAc,CAAC2H,YAAY,CAACpC,UAAU,CAAC,CAACnE,SAAS,CAAE8C,GAAG,IAAI;MAC7D,IAAI,CAAChE,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;MAC/C,IAAI,CAACP,GAAG,CAACoB,OAAO,EAAE;QAChB,IAAI,CAACnF,wBAAwB,CAACqH,WAAW,CAACtD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACvE,IAAI,CAAC/D,SAAS,CAACkH,IAAI,CAAC,IAAI,CAAC;QACzB,IAAI,CAAC5H,KAAK,CAAC6H,KAAK,EAAE;MACpB,CAAC,MAAM;QACL,IAAI,CAACvH,wBAAwB,CAACiH,SAAS,CAAClD,GAAG,CAACC,YAAY,CAACG,OAAO,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC/D,SAAS,CAACkH,IAAI,CAAC,KAAK,CAAC;MAC5B;IACF,CAAC,CAAC;EACJ;EACAG,OAAOA,CAACC,GAAQ,EAAEnL,MAAW;IAC3B,IAAI,CAACkE,WAAW,GAAGiH,GAAG;IACtB,IAAI,CAACzH,GAAG,CAAC0H,YAAY,EAAE;EACzB;EAEAnI,WAAWA,CAAA;IACT,IAAI,IAAI,CAACiB,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACR,GAAG,CAAC0H,YAAY,EAAE;EACzB;EAEAzI,eAAeA,CAAA;IACb,IAAI,IAAI,CAACuB,WAAW,KAAK,OAAO,EAAE;MAChC,IAAI,CAACA,WAAW,GAAG,SAAS;IAC9B,CAAC,MAAM,IAAI,IAAI,CAACA,WAAW,KAAK,SAAS,EAAE;MACzC,IAAI,CAACA,WAAW,GAAG,OAAO;IAC5B;IACA,IAAI,CAACR,GAAG,CAAC0H,YAAY,EAAE;EACzB;EAEAlL,eAAeA,CAACmL,KAAS;IACtB,IAAI,CAACzM,UAAU,CAACkK,UAAU,CAAC;MAClBnD,QAAQ,EAAE0F,KAAK,CAACC;KAAiB,CAAC;IAC5C;IACA;IACA,IAAI,IAAI,CAACzJ,EAAE,KAAK,CAAC,EAAE;MACjB,MAAM0J,iBAAiB,GAAG,IAAI,CAAC3M,UAAU,CAACC,GAAG,CAAC,YAAY,CAAC;MAC3D,MAAM2M,YAAY,GAAG,CAACD,iBAAiB,EAAE5J,KAAK,IAAI,EAAE,EAAEwF,QAAQ,EAAE,CAACC,IAAI,EAAE;MACvE,MAAMqE,WAAW,GAAGJ,KAAK,EAAEI,WAAW,IAAIJ,KAAK,EAAEK,OAAO,EAAED,WAAW,IAAI,EAAE;MAC3E,IAAIF,iBAAiB,IAAIE,WAAW,KAAKD,YAAY,KAAK,EAAE,IAAID,iBAAiB,CAACI,QAAQ,CAAC,EAAE;QAC3FJ,iBAAiB,CAACK,QAAQ,CAACH,WAAW,CAAC;QACvCF,iBAAiB,CAACM,WAAW,EAAE;MACjC;IACF;EAEF;EAEF7C,wBAAwBA,CAACqC,KAAU;IACjC,MAAMS,aAAa,GAAG,IAAI,CAAClN,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC/D;IAEA,IAAIwM,KAAK,KAAK,UAAU,EAAE;MACxB,IAAI,CAACjH,yBAAyB,GAAG,IAAI;MACrC;MAEA0H,aAAa,EAAEC,aAAa,CAAC,CAACpO,UAAU,CAAC0H,QAAQ,CAAC,CAAC;MACnD;IACF,CAAC,MAAM;MACL,IAAI,CAACjB,yBAAyB,GAAG,KAAK;MACtC;MAEA0H,aAAa,EAAEE,eAAe,EAAE;MAChC;IACF;IAEAF,aAAa,EAAEG,sBAAsB,EAAE;IACvC;EACF;EAEAC,uBAAuBA,CAAA;IACrB,IAAI,CAACtN,UAAU,CAACkK,UAAU,CAAC;MAAEpD,gBAAgB,EAAE;IAAI,CAAE,CAAC;IACtD;IACA,IAAI,CAAC9G,UAAU,CAACkK,UAAU,CAAC;MAAEtC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IACxD,IAAI,CAAClC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACF,yBAAyB,GAAG,KAAK;IAEtC;IACA,MAAM0H,aAAa,GAAG,IAAI,CAAClN,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC;IAC/DiN,aAAa,EAAEE,eAAe,EAAE;IAChCF,aAAa,EAAEG,sBAAsB,EAAE;EACzC;EAEAE,0BAA0BA,CAACd,KAAS;IAClChD,OAAO,CAACiC,GAAG,CAAC,UAAU,EAACe,KAAK,CAAC;IAC7B,IAAI,CAAC/G,cAAc,GAAE+G,KAAK,CAACe,eAAe,GAAC,WAAW;IACtD,IAAI,CAACxK,0BAA0B,EAAE;EACnC;EAEAyK,yBAAyBA,CAAA;IACvB,IAAI,CAACzN,UAAU,CAACkK,UAAU,CAAC;MAAEtC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IACxD,IAAI,CAAClC,cAAc,GAAG,EAAE;IACxB,IAAI,CAAC1C,0BAA0B,EAAE;EACnC;EAEAhC,iBAAiBA,CAAA;IACd,MAAMmK,QAAQ,GAAG,IAAI,CAACnL,UAAU,CAAC+C,KAAK;IACrC,IAAI,CAAC6B,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,IAAI,CAAC;IAC9C,IAAI,CAACzE,cAAc,CAChBgJ,gBAAgB,CAAC;MAAE/N,YAAY,EAAEwL,QAAQ,CAACxL,YAAY;MAAEgO,cAAc,EAAExC,QAAQ,CAACvD;IAAkB,CAAE,CAAC,CACtG9B,SAAS,CAAC;MACTqD,IAAI,EAAGY,cAAmB,IAAI;QAC5B,IAAI,CAACnF,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/C,IAAI,CAACY,cAAc,CAACC,OAAO,EAAE;UAC3B,IAAIC,UAAU,GAAGF,cAAc,CAAClB,YAAY,CAAC7D,MAAM;UACnD,IAAI,CAACW,gBAAgB,GAAGsE,UAAU;UAClC,IAAI,CAACjK,UAAU,CAACkK,UAAU,CAAC;YACzBtD,UAAU,EAAEqD,UAAU,CAACrD,UAAU;YACjCC,WAAW,EAAEoD,UAAU,CAACpD,WAAW;YACnCE,QAAQ,EAAEkD,UAAU,CAAC2D,OAAO;YAC5B3G,iBAAiB,EAAEgD,UAAU,CAAC4D,SAAS,GACnC,IAAI,CAAC1D,kBAAkB,CAACF,UAAU,CAAC4D,SAAS,CAAC,GAC7C,EAAE;YACN3G,oBAAoB,EAAE+C,UAAU,CAAC6D,UAAU,GACvC,IAAI,CAAC3D,kBAAkB,CAACF,UAAU,CAAC6D,UAAU,CAAC,GAC9C,EAAE;YACN3G,eAAe,EAAE8C,UAAU,CAAC8D,SAAS,GACjC,IAAI,CAAC5D,kBAAkB,CAACF,UAAU,CAAC8D,SAAS,CAAC,GAC7C,EAAE;YACN3G,eAAe,EAAE6C,UAAU,CAAC+D,SAAS,GACjC,IAAI,CAAC7D,kBAAkB,CAACF,UAAU,CAAC+D,SAAS,CAAC,GAC7C,EAAE;YACN3G,kBAAkB,EAAE4C,UAAU,CAACgE,YAAY,GACvC,IAAI,CAAC9D,kBAAkB,CAACF,UAAU,CAACgE,YAAY,CAAC,GAChD,EAAE;YACN3G,YAAY,EAAE2C,UAAU,CAAC3C;WAE1B,CAAC;QACJ,CAAC,MAAM;UACLmC,OAAO,CAACY,IAAI,CACV,oCAAoC,EACpCN,cAAc,CAAClB,YAAY,CAACG,OAAO,CACpC;QACH;MACF,CAAC;MACDQ,KAAK,EAAGc,GAAG,IAAI;QACb,IAAI,CAAC1F,eAAe,CAACiB,cAAc,CAACsD,IAAI,CAAC,KAAK,CAAC;QAC/CM,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEc,GAAG,CAAC;MACvC;KACD,CAAC;EACR;EAEAtH,0BAA0BA,CAAA;IACxB,MAAMkL,UAAU,GAAG,IAAI,CAAClO,UAAU,CAACC,GAAG,CAAC,kBAAkB,CAAC,EAAE8C,KAAK;IACjE,MAAMpD,YAAY,GAAG,IAAI,CAACK,UAAU,CAACC,GAAG,CAAC,cAAc,CAAC,EAAE8C,KAAK;IAC/D,MAAM6E,kBAAkB,GAAG,IAAI,CAAC5H,UAAU,CAACC,GAAG,CAAC,oBAAoB,CAAC,EAAE8C,KAAK;IAE3E,MAAMoL,UAAU,GAAGD,UAAU,KAAK,UAAU;IAC5C,MAAME,eAAe,GAAG,CAAC,CAACzO,YAAY;IACtC,MAAM0O,qBAAqB,GAAG,CAAC,CAACzG,kBAAkB;IAElD6B,OAAO,CAACiC,GAAG,CAAC,aAAa,EAAEyC,UAAU,CAAC;IACtC1E,OAAO,CAACiC,GAAG,CAAC,kBAAkB,EAAE0C,eAAe,CAAC;IAChD3E,OAAO,CAACiC,GAAG,CAAC,wBAAwB,EAAE2C,qBAAqB,CAAC;IAC5D;IACA,OAAO,EAAEF,UAAU,IAAIC,eAAe,IAAIC,qBAAqB,CAAC;EAClE;;qCA/iBa/J,oBAAoB,EAAAlF,EAAA,CAAAkP,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAApP,EAAA,CAAAkP,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAtP,EAAA,CAAAkP,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAxP,EAAA,CAAAkP,iBAAA,CAAAO,EAAA,CAAAC,cAAA,GAAA1P,EAAA,CAAAkP,iBAAA,CAAAS,EAAA,CAAAC,UAAA,GAAA5P,EAAA,CAAAkP,iBAAA,CAAAW,EAAA,CAAAC,gBAAA,GAAA9P,EAAA,CAAAkP,iBAAA,CAAAa,EAAA,CAAAC,wBAAA,GAAAhQ,EAAA,CAAAkP,iBAAA,CAAAlP,EAAA,CAAAiQ,iBAAA;EAAA;;UAApB/K,oBAAoB;IAAAgL,SAAA;IAAAC,MAAA;MAAAtM,EAAA;MAAA8B,0BAAA;MAAAC,MAAA;IAAA;IAAAwK,OAAA;MAAAvK,SAAA;IAAA;IAAAwK,KAAA;IAAAC,IAAA;IAAAC,MAAA;IAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;QCrB7B1Q,EAFJ,CAAAC,cAAA,aAAkC,aACW,aACR;QAC/BD,EAAA,CAAA8B,uBAAA,GAAc;QAEZ9B,EADA,CAAAQ,UAAA,IAAAoQ,mCAAA,iBAAsB,IAAAC,mCAAA,iBACA;;QAE1B7Q,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,aAAyB,WACgD;QAA1BD,EAAA,CAAAsB,UAAA,mBAAAwP,iDAAA;UAAA,OAASH,GAAA,CAAAxL,KAAA,CAAA4L,OAAA,EAAe;QAAA,EAAC;QAE1E/Q,EAF2E,CAAAG,YAAA,EAAI,EACvE,EACF;QAUMH,EARZ,CAAAC,cAAA,aAAwB,aAGL,cACQ,cACD,cACqF,cAChF,aAEkE;QAAnCD,EAAA,CAAAsB,UAAA,mBAAA0P,kDAAAhP,MAAA;UAAA,OAAS2O,GAAA,CAAAzD,OAAA,CAAQ,OAAO,EAAAlL,MAAA,CAAS;QAAA,EAAC;QAClFhC,EAAA,CAAAE,MAAA,oBACF;QACFF,EADE,CAAAG,YAAA,EAAI,EACD;QAEHH,EADF,CAAAC,cAAA,cAAqB,aAEsE;QAArCD,EAAA,CAAAsB,UAAA,mBAAA2P,kDAAAjP,MAAA;UAAA,OAAS2O,GAAA,CAAAzD,OAAA,CAAQ,SAAS,EAAAlL,MAAA,CAAS;QAAA,EAAC;QACtFhC,EAAA,CAAAE,MAAA,wBACF;QAWVF,EAXU,CAAAG,YAAA,EAAI,EACD,EAOF,EACD,EACF,EACF;QAENH,EAAA,CAAAC,cAAA,gBAA6D;QAkI3DD,EAjIA,CAAAQ,UAAA,KAAA0Q,6CAAA,4BAA6C,KAAAC,6CAAA,4BAiIE;QA+HnDnR,EADE,CAAAG,YAAA,EAAO,EACH;QAGJH,EADF,CAAAC,cAAA,eAAkD,WAC3C;QACHD,EAAA,CAAAQ,UAAA,KAAA4Q,uCAAA,qBAC8B;QAGhCpR,EAAA,CAAAG,YAAA,EAAM;QAEJH,EADF,CAAAC,cAAA,WAAK,kBAC4F;QAA1BD,EAAA,CAAAsB,UAAA,mBAAA+P,uDAAA;UAAA,OAASV,GAAA,CAAAxL,KAAA,CAAA4L,OAAA,EAAe;QAAA,EAAC;QAC5F/Q,EAAA,CAAAE,MAAA,eAAM;QAAAF,EAAA,CAAAG,YAAA,EAAS;QAAAH,EAAA,CAAAE,MAAA,eACjB;QAKAF,EAJA,CAAAQ,UAAA,KAAA8Q,uCAAA,qBAC+E,KAAAC,uCAAA,qBAG6B;QAKlHvR,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;;;QA9TQH,EAAA,CAAAI,SAAA,GAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAiQ,GAAA,CAAA9M,EAAA,OAAc;QACd7D,EAAA,CAAAI,SAAA,EAAc;QAAdJ,EAAA,CAAAU,UAAA,SAAAiQ,GAAA,CAAA9M,EAAA,OAAc;QAiBZ7D,EAAA,CAAAI,SAAA,GAA+C;QAA/CJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAwR,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAAzK,WAAA,cAA+C;QAM/ClG,EAAA,CAAAI,SAAA,GAAiD;QAAjDJ,EAAA,CAAAU,UAAA,YAAAV,EAAA,CAAAwR,eAAA,KAAAC,GAAA,EAAAd,GAAA,CAAAzK,WAAA,gBAAiD;QAezBlG,EAAA,CAAAI,SAAA,GAAwB;QAAxBJ,EAAA,CAAAU,UAAA,cAAAiQ,GAAA,CAAA/P,UAAA,CAAwB;QAC3CZ,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAiQ,GAAA,CAAAzK,WAAA,YAA4B;QAiI5BlG,EAAA,CAAAI,SAAA,EAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAiQ,GAAA,CAAAzK,WAAA,cAA8B;QAmIpClG,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAiQ,GAAA,CAAAzK,WAAA,cAA8B;QASelG,EAAA,CAAAI,SAAA,GAA8B;QAA9BJ,EAAA,CAAAU,UAAA,SAAAiQ,GAAA,CAAAzK,WAAA,cAA8B;QAI3ElG,EAAA,CAAAI,SAAA,EAA4B;QAA5BJ,EAAA,CAAAU,UAAA,SAAAiQ,GAAA,CAAAzK,WAAA,YAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}